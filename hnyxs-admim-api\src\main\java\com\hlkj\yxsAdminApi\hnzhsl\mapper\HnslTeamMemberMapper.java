package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamMember;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamMemberParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队成员表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslTeamMemberMapper extends BaseMapper<HnslTeamMember> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTeamMember>
     */
    List<HnslTeamMember> selectPageRel(@Param("page") IPage<HnslTeamMember> page,
                             @Param("param") HnslTeamMemberParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTeamMember> selectListRel(@Param("param") HnslTeamMemberParam param);

}
