package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBlackList;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslBlackListParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslBlackListService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 二维码黑名单
 *
 * @Author: zwk
 * @Since: 2024/10/21
 * @return: null
 **/
@Api(tags = "二维码黑名单管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslblacklist")
public class HnslBlackListController extends BaseController {

    @Autowired
    private HnslBlackListService hnslBlackListService;

    @Autowired
    private HnslUserService hnslUserService;

    @Autowired
    private HnslSchoolService hnslSchoolService;

    @Autowired
    private ConfigProperties config;

    @Autowired
    private AmazonS3 amazonS3;

    /**
     * 分页查询二维码黑名单
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslblacklist:list')")
    @OperationLog
    @ApiOperation("分页查询二维码黑名单")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslBlackList>> page(@RequestBody HnslBlackListParam param) {
        PageParam<HnslBlackList, HnslBlackListParam> page = new PageParam<>(param);
        logger.info("分页查询二维码黑名单===》");
        return success(hnslBlackListService.page(page, page.getWrapper()));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslblacklist::remove')")
    @OperationLog
    @ApiOperation("删除用户")
    @PostMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslBlackListService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }


    /**
     * 通过合伙人账号查询合伙人信息
     * @param userPhone
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslblacklist:list')")
    @OperationLog
    @ApiOperation("通过合伙人账号查询合伙人信息")
    @PostMapping("/getUserPhone/{userPhone}")
    public ApiResult<List<HnslUser>> getUserPhone(@PathVariable("userPhone") String userPhone) {
        LambdaQueryWrapper<HnslUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(HnslUser::getUserPhone, userPhone);

        // 如果有多条则过滤有效的返回
        List<HnslUser> list = hnslUserService.list(lambdaQueryWrapper).stream()
                .filter(user -> user.getStatus() == 1)
                .collect(Collectors.toList());;
        return success(list);
    }


    /**
     * 保存新增合伙人信息
     * @param userList
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslblacklist::save')")
    @OperationLog
    @ApiOperation("保存新增合伙人信息")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody List<HnslBlackListParam> userList) {
        if (null == userList) {
            return fail("保存失败，未获取到合伙人信息");
        }
        for (HnslBlackListParam userParam : userList) {
            LambdaQueryWrapper<HnslBlackList> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HnslBlackList::getUserPhone, userParam.getUserPhone());
            HnslBlackList one = hnslBlackListService.getOne(queryWrapper);
            if (null != one) {
                return fail("合伙人已存在黑名单库，请勿重复添加");
            }
            HnslBlackList list = new HnslBlackList();
            list.setUserName(userParam.getUserName());
            list.setUserPhone(userParam.getUserPhone());
            list.setCityCode(userParam.getCityCode());
            list.setCreateTime(new Date());
            list.setCreateUser(getLoginUser().getUsername());
            hnslBlackListService.save(list);
        }
        return success("保存合伙人信息成功");
    }

    /***
     * 导出二维码黑名单
     * @param params
     * @param request
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslblacklist:output')")
    @OperationLog
    @ApiOperation("导出二维码黑名单")
    @PostMapping("/outputBlackListTable")
    public ApiResult<?> outputBlackListTable(@RequestBody Map<String, Object> params, HttpServletRequest request) {

        LambdaQueryWrapper<HnslBlackList> queryWrapper = new LambdaQueryWrapper();
        String userPhone = (String) params.get("userPhone");
        if (userPhone != null && !userPhone.trim().isEmpty()) {
            queryWrapper.eq(HnslBlackList::getUserPhone, userPhone.trim());
        }
        String userName = (String) params.get("userName");
        if (userName != null && !userName.trim().isEmpty()) {
            queryWrapper.eq(HnslBlackList::getUserName, userName.trim());
        }
        String cityCode = (String) params.get("cityCode");
        if (cityCode != null && !cityCode.trim().isEmpty()) {
            queryWrapper.eq(HnslBlackList::getCityCode, cityCode.trim());
        }
        List<HnslBlackList> blackList = hnslBlackListService.list(queryWrapper);

        // 创建一个映射，将数字映射到文本描述
        Map<Integer, String> statusMap = new HashMap<>();
        statusMap.put(1, "开启");
        statusMap.put(2, "关闭");

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        for (int i = 0; i <= 12; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("ID");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("合伙人姓名");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("合伙人账号");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("线下预约二维码");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("线上预约二维码");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("线上激活二维码");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("自助激活二维码");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != blackList && blackList.size() != 0) {

            for (int i = 0; i < blackList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(blackList.get(i).getId()));

                row.createCell(1).setCellValue(String.valueOf(blackList.get(i).getUserName()));
                row.createCell(2).setCellValue(String.valueOf(blackList.get(i).getUserPhone()));
                row.createCell(3).setCellValue(String.valueOf(blackList.get(i).getCityCode()));

                Integer offlineQrcode = blackList.get(i).getOfflineQrcode();
                Integer onlineQrcode = blackList.get(i).getOnlineQrcode();
                Integer onlineActiveQrcode = blackList.get(i).getOnlineActiveQrcode();
                Integer selfActiveQrcode = blackList.get(i).getSelfActiveQrcode();
                String offlineName = statusMap.get(offlineQrcode);
                String onlineName = statusMap.get(onlineQrcode);
                String onlineActiveName = statusMap.get(onlineActiveQrcode);
                String selfActiveName = statusMap.get(selfActiveQrcode);

                row.createCell(4).setCellValue(offlineName);
                row.createCell(5).setCellValue(onlineName);
                row.createCell(6).setCellValue(onlineActiveName);
                row.createCell(7).setCellValue(selfActiveName);
            }

            // 将文件上传到S3
            String filePath = "qrcode/" + "二维码黑名单" + ".xls";
            ByteArrayOutputStream baos;
            PutObjectRequest putRequest;

            try {
                baos = new ByteArrayOutputStream();
                wb.write(baos);
                byte[] data = baos.toByteArray();

                // 检查桶是否存在
                boolean isBucketExist = amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_NAME);
                if (!isBucketExist) {
                    try {
                        amazonS3.createBucket(ConstantUtil.BUCKET_NAME);
                        logger.info("智慧扫楼二维码管理-创建桶成功" + ConstantUtil.BUCKET_NAME);
                    } catch (AmazonS3Exception e) {
                        logger.error("智慧扫楼二维码管理-创建桶失败 " + ConstantUtil.BUCKET_NAME + ": " + e.getMessage());
                    }
                }
                if (!amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_NAME)) {
                    amazonS3.createBucket(ConstantUtil.BUCKET_NAME);
                }

                putRequest = new PutObjectRequest(ConstantUtil.BUCKET_NAME, filePath,
                        new ByteArrayInputStream(data), null);
                PutObjectResult result = amazonS3.putObject(putRequest);
                logger.info("上传结果" + result.toString());

                Map<String, Object> map = new HashedMap<>();
                map.put("code", "6");
                return success("导出成功", map);
            } catch (IOException e) {
                e.printStackTrace();
                logger.error("文件上传到S3时发生错误：" + e.getMessage());
                return fail("文件上传到S3时发生错误：" + e.getMessage());
            }
        }
        return success("导出成功");
    }


    /**
     * 导入二维码黑名单
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslblacklist:import')")
    @OperationLog
    @ApiOperation("导入二维码黑名单")
    @PostMapping("/decode/importBlackList")
    public ApiResult<?> importBlackList(MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        logger.info("导入二维码黑名单开始===》");
        JSONObject result = new JSONObject();
        String image = "xls,xlsx";
        User loginUser = getLoginUser();
        if (!file.isEmpty()) {
            String uploadPath = config.getHnzhslFilePath()  + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确", 2);
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                logger.error("文件上传格式不正确");
                return fail("resultCode", 2);
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    logger.error("文件所在目录创建失败");
                    return fail("resultCode", 3);
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 1, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 1, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                saveFile.delete();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
                String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
                if (numberList != null && numberList.size() != 0) {
                    //将数据转化成user对象
                    List<HnslBlackList> blackLists = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslBlackList hnslBlackList = new HnslBlackList();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            // 合伙人账号
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                if (!StringUtil.trimString(userMap.get(name)).isEmpty()) {
                                    hnslBlackList.setUserPhone(StringUtil.trimString(userMap.get(name)));
                                }
                            }
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                if (!StringUtil.trimString(userMap.get(name)).isEmpty()) {
                                    hnslBlackList.setXlsName(StringUtil.trimString(userMap.get(name)));
                                }
                            }
                        }
                        hnslBlackList.setCreateTime(new Date());
                        hnslBlackList.setCreateUser(loginUser.getUsername());
                        hnslBlackList.setOfflineQrcode(2);
                        hnslBlackList.setOnlineQrcode(2);
                        hnslBlackList.setOnlineActiveQrcode(2);
                        hnslBlackList.setSelfActiveQrcode(2);
                        blackLists.add(hnslBlackList);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + blackLists);
                        Map<String, String> saveBlackListArray = hnslBlackListService.saveBlackList(blackLists, request);
                        if (null != saveBlackListArray & saveBlackListArray.get("resultCode").equals("1")) {
                            System.out.println("saveBlackListArray"+ saveBlackListArray);
                            HashMap<Object, Object> r = new HashMap<>();
                            r.put("mes", "exportDaoUser");
                            r.put("resultCode", "6");
                            r.put("fileName", saveBlackListArray.get("fileName"));
                            return success("导入成功", r);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                        logger.error("批量插入用户信息失败:" + e.getMessage());
                        return fail("resultCode", 5);
                    }
                } else {
                    logger.error("文件内容为空，或者解析失败");
                    result.put("resultCode", "4");
                    return fail("resultCode", 4);
                }
                HashMap<Object, Object> r = new HashMap<>();
                r.put("mes", "exportDaoUser");
                r.put("resultCode", 0);
                return success("导入成功", r);
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("文件上传接口上传异常:" + e.getMessage());
                saveFile.delete();
                return fail("文件上传接口上传异常" + e.getMessage());
            } finally {
                saveFile.delete();
            }
        } else {
            logger.error("上传文件为空");
            return fail("上传文件为空");
        }
    }


    /**
     * 下载白名单导入情况文件
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/exportDaoUser")
    public void exportDaoUser(HttpServletResponse response, HttpServletRequest request,String name) throws Exception {
        String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName  = name+".xls".toString(); // 文件的默认保存名

        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("黑名单导入情况表", "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("下载文件异常" + e.getMessage());
        } finally {
            inStream.close();
            file.delete();
        }
    }
}
