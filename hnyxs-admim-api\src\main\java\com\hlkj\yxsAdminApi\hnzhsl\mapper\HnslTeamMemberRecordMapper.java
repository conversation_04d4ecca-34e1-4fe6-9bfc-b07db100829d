package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamMemberRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamMemberRecordParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslTeamMemberRecordMapper extends BaseMapper<HnslTeamMemberRecord> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTeamMemberRecord>
     */
    List<HnslTeamMemberRecord> selectPageRel(@Param("page") IPage<HnslTeamMemberRecord> page,
                             @Param("param") HnslTeamMemberRecordParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTeamMemberRecord> selectListRel(@Param("param") HnslTeamMemberRecordParam param);

}
