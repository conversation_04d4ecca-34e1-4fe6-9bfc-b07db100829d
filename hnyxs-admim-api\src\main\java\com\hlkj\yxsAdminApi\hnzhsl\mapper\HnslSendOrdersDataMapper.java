package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersData;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSendOrdersDataParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 派单详情表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslSendOrdersDataMapper extends BaseMapper<HnslSendOrdersData> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSendOrdersData>
     */
    List<HnslSendOrdersData> selectPageRel(@Param("page") IPage<HnslSendOrdersData> page,
                             @Param("param") HnslSendOrdersDataParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSendOrdersData> selectListRel(@Param("param") HnslSendOrdersDataParam param);

}
