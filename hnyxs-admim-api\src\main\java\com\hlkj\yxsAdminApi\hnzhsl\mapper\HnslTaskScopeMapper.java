package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskScope;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskScopeParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务范围表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslTaskScopeMapper extends BaseMapper<HnslTaskScope> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTaskScope>
     */
    List<HnslTaskScope> selectPageRel(@Param("page") IPage<HnslTaskScope> page,
                             @Param("param") HnslTaskScopeParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTaskScope> selectListRel(@Param("param") HnslTaskScopeParam param);

}
