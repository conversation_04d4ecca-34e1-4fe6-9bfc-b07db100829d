package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMessageNotification;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslMessageNotificationParam;

import java.util.List;

/**
 * 扫楼消息通知表Service
 *
 * <AUTHOR>
 * @since 2023-07-21 11:31:45
 */
public interface HnslMessageNotificationService extends IService<HnslMessageNotification> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslMessageNotification>
     */
    PageResult<HnslMessageNotification> pageRel(HnslMessageNotificationParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslMessageNotification>
     */
    List<HnslMessageNotification> listRel(HnslMessageNotificationParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslMessageNotification
     */
    HnslMessageNotification getByIdRel(Integer id);

}
