<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslLoginWhiteMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_login_white a
        <where>
            <if test="param.id != null">
                a.id = #{param.id}
            </if>
            <if test="param.hhrName != null and param.hhrName != ''">
                AND a.hhr_name LIKE CONCAT('%', #{param.hhrName}, '%')
            </if>
            <if test="param.hhrMobile != null and param.hhrMobile != ''">
                AND a.hhr_mobile = #{param.hhrMobile}
            </if>
            <if test="param.cityCode != null and param.cityCode != ''">
                AND a.city_code = #{param.cityCode}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslLoginWhite">
        <include refid="selectSql"></include>
    </select>

<!--    <select id="queryUserPhone" resultType="java.util.HashMap">-->
<!--        SELECT * FROM `hnsl_black_list` t-->
<!--        <where>-->
<!--            <if test="userName != null and userName !='' ">-->
<!--                AND t.user_name = #{userName}-->
<!--            </if>-->
<!--            <if test="userPhone != null and userPhone !='' ">-->
<!--                AND t.user_phone = #{userPhone}-->
<!--            </if>-->
<!--            <if test="cityCode != null and cityCode != '' ">-->
<!--                AND t.city_code = #{cityCode}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->


    <select id="queryListTable" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBlackList">
        SELECT * FROM `hnsl_login_white` t where 1 = 1 and t.hhr_mobile = #{hhrMobile}
    </select>

    <update id="updateHhr" parameterType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslLoginWhite">
        update hnsl_login_white
        <set>
            <if test="allowDate != null">allow_date = #{allowDate} </if>
        </set>
        where id = #{id}
    </update>
</mapper>
