package com.hlkj.yxsAdminApi.common.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.common.system.entity.Tenant;
import com.hlkj.yxsAdminApi.common.system.param.TenantParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户Mapper
 *
 * <AUTHOR>
 * @since 2023-02-16 12:27:16
 */
public interface TenantMapper extends BaseMapper<Tenant> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<Tenant>
     */
    List<Tenant> selectPageRel(@Param("page") IPage<Tenant> page,
                             @Param("param") TenantParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<Tenant> selectListRel(@Param("param") TenantParam param);

    /**
     * 根据账户名查询所有的租户信息
     * @param userName
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<Tenant> getLoginAllTenantList(String userName);
}
