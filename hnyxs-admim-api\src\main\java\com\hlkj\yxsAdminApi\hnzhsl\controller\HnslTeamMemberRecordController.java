package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTeamMemberRecordService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamMemberRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamMemberRecordParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-team-member-record")
public class HnslTeamMemberRecordController extends BaseController {
    @Autowired
    private HnslTeamMemberRecordService hnslTeamMemberRecordService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:list')")
    @OperationLog
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTeamMemberRecord>> page(@RequestBody HnslTeamMemberRecordParam param) {
        PageParam<HnslTeamMemberRecord, HnslTeamMemberRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamMemberRecordService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTeamMemberRecordService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:list')")
    @OperationLog
    @ApiOperation("查询全部")
    @PostMapping("/list")
    public ApiResult<List<HnslTeamMemberRecord>> list(@RequestBody HnslTeamMemberRecordParam param) {
        PageParam<HnslTeamMemberRecord, HnslTeamMemberRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamMemberRecordService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTeamMemberRecordService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:list')")
    @OperationLog
    @ApiOperation("根据id查询")
    @GetMapping("/{id}")
    public ApiResult<HnslTeamMemberRecord> get(@PathVariable("id") Integer id) {
        return success(hnslTeamMemberRecordService.getById(id));
        // 使用关联查询
        //return success(hnslTeamMemberRecordService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:save')")
    @OperationLog
    @ApiOperation("添加")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTeamMemberRecord hnslTeamMemberRecord) {
        if (hnslTeamMemberRecordService.save(hnslTeamMemberRecord)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:update')")
    @OperationLog
    @ApiOperation("修改")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTeamMemberRecord hnslTeamMemberRecord) {
        if (hnslTeamMemberRecordService.updateById(hnslTeamMemberRecord)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:remove')")
    @OperationLog
    @ApiOperation("删除")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTeamMemberRecordService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:save')")
    @OperationLog
    @ApiOperation("批量添加")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTeamMemberRecord> list) {
        if (hnslTeamMemberRecordService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:update')")
    @OperationLog
    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTeamMemberRecord> batchParam) {
        if (batchParam.update(hnslTeamMemberRecordService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMemberRecord:remove')")
    @OperationLog
    @ApiOperation("批量删除")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTeamMemberRecordService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
