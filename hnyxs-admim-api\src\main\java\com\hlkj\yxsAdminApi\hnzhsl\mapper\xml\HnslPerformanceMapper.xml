<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslPerformanceMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">

        SELECT pp.user_phone,pp.id,pp.user_name,pp.status_sf,pp.levels,pp.school_code,pp.school_name
        ,pp.friends_jie_tu,pp.qq_jie_tu,pp.service_jie_tu FROM (SELECT
        tt.user_phone,tt.id,tt.user_name,tt.status_sf,dd.levels,tt.school_code,tt.school_name
        ,ee.friends_jie_tu,ee.qq_jie_tu,ee.service_jie_tu
        FROM (
        <if test="param.conditionStatus==1">
            select distinct(aa.user_phone) ,aa.user_name ,aa.status_sf ,aa.id,bb.school_code,bb.school_name from (
        </if>
        <if test="param.conditionStatus==2">
            select distinct(aa.user_phone) ,aa.user_name ,aa.status_sf ,aa.id,us.school_code,us.school_name from (
        </if>
        select u.* from HNSL_USER u
        <if test="param.building != null and param.building != ''">
            INNER JOIN HNSL_BUILDING_USER b ON u.USER_PHONE = b.USER_ID
        </if>
        where 1=1
        <if test="param.userName !=null and param.userName !=''">
            and u.USER_NAME like concat(concat('%',#{param.userName}),'%')
        </if>
        <if test="param.userManager!=null and param.userManager!=''">
            and (u.USER_MANGER is null or u.USER_MANGER=#{param.userManager})
        </if>
        <if test="param.userNameOrUserPhone !=null and param.userNameOrUserPhone !=''">
            and (u.USER_NAME like concat(concat('%',#{param.userNameOrUserPhone}),'%') or
            u.USER_PHONE like concat(concat('%',#{param.userNameOrUserPhone}),'%'))
        </if>
        <if test="param.statusSf !=null and param.statusSf!=''">
            and u.STATUS_SF = #{param.statusSf}
        </if>
        <if test="param.status !=null and param.status !=''">
            and u.STATUS = #{param.status}
        </if>
        <if test="param.cityCode !=null and param.cityCode !=''">
            and u.CITY_CODE =#{param.cityCode}
        </if>
        <if test="param.numbers !=null and param.numbers!=''">
            and u.NUMBERS =#{param.numbers}
        </if>
        <if test="param.userSfz !=null and param.userSfz!=''">
            and u.USER_SFZ =#{param.userSfz}
        </if>
        <if test="param.userPhone !=null and param.userPhone!=''">
            and u.USER_PHONE =#{param.userPhone}
        </if>
        <if test="param.beginTime != null and param.beginTime != ''">
            and u.CREATED_DATE between
            str_to_date(#{param.beginTime},'%Y-%m-%d %T')and
            str_to_date(#{param.endTime},'%Y-%m-%d %T')
        </if>
        order by u.ID desc
        <if test="param.conditionStatus==1">
            ) aa left join ( select s2.* from(
            select t.user_phone,group_concat(t.school_code,',') as school_code,
            group_concat(t1.school_name,',') as school_name
            from hnsl_user_school t left join hnsl_school t1 on t.school_code=t1.school_code
            where t.status=1
            <if test="param.userManager!=null and param.userManager!=''">
                and user_phone=#{param.userManager}
            </if>
            group by t.user_phone ) s2 where 1=1
            <if test="param.schoolName !=null and param.schoolName !=''">
                and s2.school_name  like concat(concat('%',#{param.schoolName}),'%')
            </if>
            ) bb on aa.user_phone=bb.user_phone where 1=1
            <if test="param.loginUserSf==1">
                and (aa.status_sf!='1' and aa.status_sf!='5' and aa.status_sf!='6')
            </if>
            <if test="param.loginUserSf==5">
                and (aa.status_sf!='5')
            </if>
            <if test="param.loginUserSf==6">
                and (aa.status_sf!='5' and aa.status_sf!='6')
            </if>
        </if>
        <if test="param.conditionStatus==2">
            ) aa ,(select * from(
            select t.user_phone,group_concat(t.school_code,',') as school_code,
            group_concat(t1.school_name,',') as school_name
            from hnsl_user_school t left join hnsl_school t1 on t.school_code=t1.school_code
            where t.status=1
            group by t.user_phone
            ) bb where 1=1
            <if test="param.schoolName !=null and param.schoolName !=''">
                and bb.school_name like concat(concat('%',#{param.schoolName}),'%'))
            </if>
            ) us where aa.user_phone=us.user_phone
            <if test="param.loginUserSf==1">
                and (aa.status_sf!='1' and aa.status_sf!='5' and aa.status_sf!='6')
            </if>
            <if test="param.loginUserSf==5">
                and (aa.status_sf!='5')
            </if>
            <if test="param.loginUserSf==6">
                and (aa.status_sf!='5' and aa.status_sf!='6')
            </if>
        </if>
        ) tt
        left join (select t.user_id,max(t.levels) as levels from (select * from hnsl_performance order by created_date desc) t where t.status=1
        and date_format(t.created_date,'%Y-%m')= date_format(sysdate(),'%Y-%m')  group by t.user_id) dd on tt.user_phone=dd.user_id
        left join (select t.user_phone,max( CASE WHEN t.upload_type=1 THEN t.upload_image END) as friends_jie_tu
        ,max( CASE WHEN t.upload_type=2 THEN t.upload_image END) as qq_jie_tu,max( CASE WHEN t.upload_type=3 THEN t.upload_image END) as service_jie_tu
        from (select * from hnsl_task_upload_pictures order by upload_date desc) t
        where t.upload_status=1 and t.user_phone is not null and t.UPLOAD_DATE between
        str_to_date(#{param.firstDay},'%Y-%m-%d %T')and
        str_to_date(#{param.lastDay},'%Y-%m-%d %T')
        group by t.user_phone) ee on tt.user_phone=ee.user_phone
        <where>
            <if test="param.performanceStatus==1">
                and dd.levels is not null
            </if>
            <if test="param.performanceStatus==2">
                and dd.levels is null
            </if>
        </where>
        ) pp
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPerformance">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPerformance">
        <include refid="selectSql"></include>
    </select>

    <select id="queryTotal" resultType="int">
        select count(*) from HNSL_PERFORMANCE where STATUS=1
        <if test="userId!=null and userId!=''">
            AND USER_ID=#{userId}
        </if>
        <if test="dates!=null and dates!=''">
            and DATE_FORMAT(CREATED_DATE,'%Y-%m')=#{dates}
        </if>
    </select>

</mapper>
