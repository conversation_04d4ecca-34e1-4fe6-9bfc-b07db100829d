package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSchoolParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 学校表Mapper
 *
 * <AUTHOR>
 * @since 2023-05-04 14:46:39
 */
public interface HnslSchoolMapper extends BaseMapper<HnslSchool> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSchool>
     */
    List<HnslSchool> selectPageRel(@Param("page") IPage<HnslSchool> page,
                             @Param("param") HnslSchoolParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSchool> selectListRel(@Param("param") HnslSchoolParam param);

    /**
     * 查询学校学子信息
     * @param map
     * @return
     */
    List<HnslSchoolUser> queryTaskUserDetailsByTaskCode(Map<String, Object> map);

    /**
     * 查询学校包含里面楼栋信息
     * @param map
     * @return
     */
    List<HnslSchool> querySchoolDownBuildingList(Map<String, Object> map);

}
