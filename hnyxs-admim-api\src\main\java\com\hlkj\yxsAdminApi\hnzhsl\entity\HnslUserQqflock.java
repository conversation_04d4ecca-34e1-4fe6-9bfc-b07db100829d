package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 楼栋服务QQ群
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUserQqflock对象", description = "楼栋服务QQ群")
public class HnslUserQqflock implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合伙人第一联系电话")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "楼栋编码")
    @TableField("BUILDING_ID")
    private String buildingId;

    @ApiModelProperty(value = "QQ群号")
    @TableField("QQFLOCK")
    private String qqflock;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private LocalDate createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private LocalDate updatedDate;

    @ApiModelProperty(value = "QQ群昵称")
    @TableField("QQFLOCK_NAME")
    private String qqflockName;

    @ApiModelProperty(value = "QQ群人数")
    @TableField("QQFLOCK_NUMBER")
    private Integer qqflockNumber;

}
