package com.hlkj.yxsAdminApi.hnzhsl.controller;

import cn.hutool.core.util.ObjectUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.constant.SysLogConstant;
import com.hlkj.yxsAdminApi.common.core.utils.DesensitizationUtil;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.utils.ValidatorUtils;
import com.hlkj.yxsAdminApi.common.core.validator.group.AddGroup;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.PortalPersonnelInfo;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.PortalPersonnelInfoService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.hnslApproverSettingEntity;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslApproverSettingParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslApproverSettingService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("api/hnslapproversetting")
public class HnslApproverSettingController extends BaseController {

    Logger logger = LogManager.getLogger(HnslApproverSettingController.class);
    @Autowired
    private RedisUtil redisUtils;
    @Autowired
    private HnslApproverSettingService hnslApproverSettingService;
    @Autowired
    private PortalPersonnelInfoService personnelInfoService;


    /**
     * 列表
     */
    @OperationLog(value = "查询审批人列表", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @PostMapping("/page")
    @PreAuthorize("hasAuthority('hnslsetting:list')")
    @ApiOperation("查询审批人列表")
    public ApiResult<PageResult<hnslApproverSettingEntity>> page(@RequestBody HnslApproverSettingParam param) {
        PageParam<hnslApproverSettingEntity, HnslApproverSettingParam> page = new PageParam<>(param);
        return success(hnslApproverSettingService.page(page, page.getWrapper()));
    }

    /**
     * 查询信息
     */
    @OperationLog(value = "查询信息", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @RequestMapping("/info/{id}")
    @PreAuthorize("hasAuthority('hnslsetting:info')")
    @ApiOperation("查询审批人信息")
    public ApiResult<hnslApproverSettingEntity> info(@PathVariable Long id, HttpServletResponse response) {
        return ApiResult.success(hnslApproverSettingService.getById(id));
    }

    /**
     * 新增审批人
     */
    @OperationLog(value = "新增审批人", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @RequestMapping("/save")
    @PreAuthorize("hasAuthority('hnslsetting:save')")
    @ApiOperation("新增审批人")
    public ApiResult<String> save(@RequestBody hnslApproverSettingEntity entity, HttpServletResponse response) {
        logger.info("新建审批人入参->{}" + entity);
        ValidatorUtils.validateEntity(entity, AddGroup.class);

        String thisVision = InterfaceUtil.otherWSInterface(entity.getUserPhone());
        if (StringUtils.isBlank(thisVision)) {
            return ApiResult.error("必须是电信本网号码才能成为审批人");
        }

        // 重复信息无法重新创建
        LambdaQueryWrapper<hnslApproverSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(hnslApproverSettingEntity::getUserPhone, entity.getUserPhone())
                .or()
                .eq(hnslApproverSettingEntity::getIdCard, entity.getIdCard())
                .or()
                .eq(hnslApproverSettingEntity::getUserName, entity.getUserName());
        if (hnslApproverSettingService.count(queryWrapper) > 0) {
            return ApiResult.error("当前审批人信息已存在，请勿重复创建");
        }
        
        ApiResult<String> result = getR(entity);
        if (result != null) {
            return result;
        }
        
        entity.setSchoolCode("");
        entity.setCreateTime(new Date());
        long idCodeIncr = redisUtils.incr("idCodeIncr", 1);
        hnslApproverSettingEntity service = hnslApproverSettingService.getById(idCodeIncr);
        if (ObjectUtil.isNotNull(service)) {
            idCodeIncr = redisUtils.incr("idCodeIncr", 1);
        }
        entity.setId(idCodeIncr);
        hnslApproverSettingService.save(entity);
        return ApiResult.success("0");
    }


    /**
     * 修改审批人
     */
    @OperationLog(value = "修改审批人", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @RequestMapping("/update")
    @PreAuthorize("hasAuthority('hnslsetting:update')")
    @ApiOperation("修改审批人")
    public ApiResult<String> update(@RequestBody hnslApproverSettingEntity entity, HttpServletResponse response) {
        logger.info("新建审批人入参->{}" + entity);
        ValidatorUtils.validateEntity(entity, AddGroup.class);

        String thisVision = InterfaceUtil.otherWSInterface(entity.getUserPhone());
        if (StringUtils.isBlank(thisVision)) {
            return ApiResult.error("必须是电信本网号码才能成为审批人");
        }

        ApiResult<String> result = getR(entity);
        if (result != null) {
            return result;
        }
        hnslApproverSettingService.updateById(entity);
        return ApiResult.success("0");
    }

    @Nullable
    private ApiResult<String> getR(hnslApproverSettingEntity entity) {
        LambdaQueryWrapper<PortalPersonnelInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PortalPersonnelInfo::getCertNum,entity.getIdCard());
        PortalPersonnelInfo portalPersonnelInfoEntity = personnelInfoService.getOne(wrapper);
        if (null == portalPersonnelInfoEntity) {
            return ApiResult.error("新建失败，该人员未在门户信息系统存在资料，不可设为审批人！");
        }
        if (!entity.getUserPhone().equals(portalPersonnelInfoEntity.getMobilephone())) {
            return ApiResult.error("新建失败，手机号企业信息门户系统内数据不一致！");
        }
        if (!entity.getUserName().equals(portalPersonnelInfoEntity.getUsername())) {
            return ApiResult.error("新建失败，联系人姓名与企业信息门户系统内数据不一致！");
        }
        return null;
    }

    /**
     * 删除审批人
     */
    @OperationLog(value = "删除审批人", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @RequestMapping("/delete/{id}")
    @PreAuthorize("hasAuthority('hnslsetting:delete')")
    @ApiOperation("删除审批人")
    public ApiResult<String> delete(@PathVariable Long id, HttpServletResponse response) {
        if (hnslApproverSettingService.removeById(id)) {
            return ApiResult.success("0");
        }
        return ApiResult.error("删除失败");
    }

    /**
     * 查询审批人列表
     *
     * @return
     */
    @OperationLog(value = "查询审批人列表", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @RequestMapping("/queryApprover")
    @PreAuthorize("hasAuthority('hnslsetting:queryApprover')")
    @ApiOperation("查询审批人列表")
    public ApiResult<List<hnslApproverSettingEntity>> queryApprover(@RequestParam(value = "userName", required = false) String userName) {
        User user = DesensitizationUtil.getUser();
       /* String cityCode = (String) redisUtils.get("cityCode" + user.getPhone());
        String statusSf = (String) redisUtils.get("thisUserSf" + user.getPhone());
        String hnslType = (String) redisUtils.get("hnslChannel" + user.getPhone());*/

        LambdaQueryWrapper<hnslApproverSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        List<hnslApproverSettingEntity> list = new ArrayList<>();
        // 排除自己
        queryWrapper.ne(hnslApproverSettingEntity::getUserPhone, user.getPhone());
        if (userName != null && !userName.trim().isEmpty()) {
            queryWrapper.like(hnslApproverSettingEntity::getUserName, userName);
        }
        list = hnslApproverSettingService.list(queryWrapper);
        return ApiResult.success(list);
    }
}
