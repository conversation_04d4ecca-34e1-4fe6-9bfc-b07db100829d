package com.hlkj.yxsAdminApi.common.system.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * 返回数据封装类
 * <p>
 * Title: R.java
 * </p>
 * 
 * @<NAME_EMAIL>
 * @date 2025-03-07 03:32:05
 * @version 1.0
 */
public class R extends JSONObject {
	private static final long serialVersionUID = 1L;

	public R() {
		put("code", 0);
		put("message", "success");
	}

	public R(String data) {
		put("data", data);
	}
	
	public R(JSONObject json) {
		this.put("code", json.getInteger("code"));
		this.put("message", json.getString("message"));
		if (json.get("data") instanceof JSONArray) {
			this.put("data", json.getJSONArray("data"));
		} else if (json.get("data") instanceof String) {
			this.put("data", json.getString("data"));
		} else if (json.get("data") instanceof JSONObject) {
			this.put("data", json.getJSONObject("data"));
		} else {
			this.put("data", json.get("data"));
		}
	}

	public static R error() {
		return error(500, "未知异常，请联系管理员");
	}

	public static R error(String message) {
		return error(500, message);
	}

	public static R error(int code, String message) {
		R r = new R();
		r.put("code", code);
		r.put("message", message);
		return r;
	}

	public static R ok(String message) {
		R r = new R();
		r.put("message", message);
		return r;
	}

	public static R ok(Map<String, Object> map) {
		R r = new R();
		r.putAll(map);
		return r;
	}

	public static R ok() {
		return new R();
	}

	@Override
	public R put(String key, Object value) {
		super.put(key, value);
		return this;
	}
}
