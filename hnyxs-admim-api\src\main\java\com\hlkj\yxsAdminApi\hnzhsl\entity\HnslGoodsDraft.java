package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫楼商品草稿表
 *
 * <AUTHOR>
 * @since 2023-06-17 16:07:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslGoodsDraft对象", description = "扫楼商品草稿表")
public class HnslGoodsDraft implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "商品编码")
    @TableField("GOODS_NUMBER")
    private String goodsNumber;

    @ApiModelProperty(value = "主套餐销售品编码")
    @TableField("GOODS_MIAN_NUMBER")
    private String goodsMianNumber;

    @ApiModelProperty(value = "商品名称")
    @TableField("GOODS_NAME")
    private String goodsName;

    @ApiModelProperty(value = "商品类型（SAFL_TYPE为1和2时，1号卡 2：宽带 3：融合 4：合约分期 ）（SAFL_TYPE为3时，1:权益 2：预存 3：续费  4:流量 5:宽带）")
    @TableField("GOODS_TYPE")
    private String goodsType;

    @ApiModelProperty(value = "商品子类别（1:正常类型 2:绑定加装 3:新加装类型 4:可重复加装 5:促销包）")
    @TableField("GOODS_CHILDTYPE_NUMBER")
    private String goodsChildtypeNumber;

    @ApiModelProperty(value = "商品价格")
    @TableField("GOODS_PRICE")
    private Integer goodsPrice;

    @ApiModelProperty(value = "商品头图")
    @TableField("GOODS_IMG")
    private String goodsImg;

    @ApiModelProperty(value = "商品详情图片")
    @TableField("GOODS_DETAILS_IMG")
    private String goodsDetailsImg;

    @ApiModelProperty(value = "预存话费")
    @TableField("PRESTORE")
    private String prestore;

    @ApiModelProperty(value = "保底消费")
    @TableField("MIN_PRICE")
    private String minPrice;

    @ApiModelProperty(value = "制卡费")
    @TableField("PRODUCTION_PRICE")
    private String productionPrice;

    @ApiModelProperty(value = "安装费连接费")
    @TableField("INSTALL_PRICE")
    private String installPrice;

    @ApiModelProperty(value = "商品描述")
    @TableField("GOOD_INTEGRAL")
    private String goodIntegral;

    @ApiModelProperty(value = "带宽")
    @TableField("BANDWIDTH")
    private String bandwidth;

    @ApiModelProperty(value = "宽带类型（1：电视宽带  2：单宽带 ）")
    @TableField("BANDWIDTH_TYPE")
    private String bandwidthType;

    @ApiModelProperty(value = "号卡类型（1：天翼畅想  2：乐享4G  3天翼大流量 4其他）")
    @TableField("CARD_TYPE")
    private String cardType;

    @ApiModelProperty(value = "同一个客户该产品最大办理次数")
    @TableField("TRANSACTION_NUM")
    private String transactionNum;

    @ApiModelProperty(value = "状态(1在架 0：下架)")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "排序")
    @TableField("ORDER_NUMBER")
    private String orderNumber;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "号卡主套餐名称")
    @TableField("CARD_GOODS_NAME")
    private String cardGoodsName;

    @ApiModelProperty(value = "号卡主套餐编码")
    @TableField("CARD_GOODS_NUMBER")
    private String cardGoodsNumber;

    @ApiModelProperty(value = "宽带主套餐名称")
    @TableField("BANDWIDTH_GOODS_NAME")
    private String bandwidthGoodsName;

    @ApiModelProperty(value = "宽带主套餐编码")
    @TableField("BANDWIDTH_GOODS_NUMBER")
    private String bandwidthGoodsNumber;

    @ApiModelProperty(value = "ITV销售品编码")
    @TableField("ITV_NUMBER")
    private String itvNumber;

    @ApiModelProperty(value = "1：号卡新装 ，2：一人一码 ， 3：加装商品 4:存费送费")
    @TableField("SAFL_TYPE")
    private Integer saflType;

    @ApiModelProperty(value = "商品详情")
    @TableField("GOODS_DETALT")
    private String goodsDetalt;

    @ApiModelProperty(value = "最小年龄限制（默认为0）")
    @TableField("MIN_AGE")
    private Integer minAge;

    @ApiModelProperty(value = "最大年龄限制（默认为0）")
    @TableField("MAX_AGE")
    private Integer maxAge;

    @ApiModelProperty(value = "商品限制套餐编码（,隔开）")
    @TableField("ASTRICT_GOODS")
    private String astrictGoods;

    @ApiModelProperty(value = "加装商品说明字段")
    @TableField("GOODS_EXPLAIN")
    private String goodsExplain;

    @ApiModelProperty(value = "商品是否上架翼店展示（0:默认不显示 1:展示）")
    @TableField("GOODS_TYPE_SHOW")
    private Integer goodsTypeShow;

    @ApiModelProperty(value = "一人一码跳转H5地址URL")
    @TableField("GOODS_HTML_URL")
    private String goodsHtmlUrl;

    @ApiModelProperty(value = "套餐类型 1:校园套餐 2：公共套餐")
    @TableField("GOODS_PACKAGE_TYPE")
    private Integer goodsPackageType;

    @ApiModelProperty(value = "传给BPS的CPS1字段 JSON格式保存")
    @TableField("CPS_LIST")
    private String cpsList;

    @ApiModelProperty(value = "是否上传证件 默认 0:否   1:是")
    @TableField("CERTIFICATE_SWITCH")
    private Integer certificateSwitch;

    @ApiModelProperty(value = "客服二维码URL")
    @TableField("GOODS_SERVICE_URL")
    private String goodsServiceUrl;

    @ApiModelProperty(value = "商品分享图片")
    @TableField("GOODS_SHARE_IMG")
    private String goodsShareImg;

    @ApiModelProperty(value = "是否展示分享 默认 2:否   1:是")
    @TableField("GOODS_SHARE_SWITCH")
    private String goodsShareSwitch;

    @ApiModelProperty(value = "配置人姓名")
    @TableField("CONFIGURATION_NAME")
    private String configurationName;

    @ApiModelProperty(value = "配置人工号")
    @TableField("CONFIGURATION_NUMBER")
    private String configurationNumber;

    @ApiModelProperty(value = "提交类型")
    @TableField("SUBMIT_TYPE")
    private String submitType;

    @ApiModelProperty(value = "提交时间")
    @TableField("SUBMIT_DATE")
    private Date submitDate;

    @ApiModelProperty(value = "审批时间")
    @TableField("APPROVAL_DATE")
    private Date approvalDate;

    @ApiModelProperty(value = "审批状态(1:待提交 2:通过 3:驳回)")
    @TableField("APPROVAL_STATUS")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审批人")
    @TableField("APPROVAL_USER")
    private String approvalUser;

}
