package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUserOrdersData对象", description = "")
public class HnslUserOrdersData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识 主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID")
    private Integer userId;

    @ApiModelProperty(value = "活动标识")
    @TableField("ACTIVITY_LOGO")
    private String activityLogo;

    @ApiModelProperty(value = "接触单标识 每条数据的唯一标识 回单使用")
    @TableField("TOUCH_ID")
    private String touchId;

    @ApiModelProperty(value = "订单ID")
    @TableField("ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "外呼状态 1;待外呼 2：已外呼待回单   6:已回单 7:已逾期 8:已回收")
    @TableField("SEND_ORDERS_STATUS")
    private Integer sendOrdersStatus;

    @ApiModelProperty(value = "回执时间")
    @TableField("ORDER_RECEIPT_DATE")
    private Date orderReceiptDate;

    @ApiModelProperty(value = "回执结果类型 1;已接触-成功办理 2:已接触-有需求 3:已接触-犹豫中 4:拒绝-已转网 5:拒绝-资费不满意 6:接触失败-联系号码错误 7:接触失败-未接听")
    @TableField("ORDER_RECEIPT_RESULT")
    private String orderReceiptResult;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "分派时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "学校6级编码")
    @TableField("SCHOOL_SIX_ID")
    private String schoolSixId;

}
