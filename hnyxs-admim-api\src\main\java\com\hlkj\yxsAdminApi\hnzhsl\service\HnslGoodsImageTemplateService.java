package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsImageTemplate;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsImageTemplateParam;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2023-06-16 10:28:05
 */
public interface HnslGoodsImageTemplateService extends IService<HnslGoodsImageTemplate> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslGoodsImageTemplate>
     */
    PageResult<HnslGoodsImageTemplate> pageRel(HnslGoodsImageTemplateParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslGoodsImageTemplate>
     */
    List<HnslGoodsImageTemplate> listRel(HnslGoodsImageTemplateParam param);

    /**
     * 根据id查询
     *
     * @param id 自增ID
     * @return HnslGoodsImageTemplate
     */
    HnslGoodsImageTemplate getByIdRel(Integer id);

}
