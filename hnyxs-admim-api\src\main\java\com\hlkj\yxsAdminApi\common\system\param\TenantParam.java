package com.hlkj.yxsAdminApi.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.hlkj.yxsAdminApi.common.core.annotation.QueryField;
import com.hlkj.yxsAdminApi.common.core.annotation.QueryType;
import com.hlkj.yxsAdminApi.common.core.web.BaseParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户查询参数
 *
 * <AUTHOR>
 * @since 2023-02-16 12:27:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "TenantParam对象", description = "租户查询参数")
public class TenantParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户名称")
    private String tenantName;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "是否删除, 0否, 1是")
    @QueryField(type = QueryType.EQ)
    private Integer deleted;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;
}
