package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 派单总表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSendOrders对象", description = "派单总表")
public class HnslSendOrders implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识 主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动名称")
    @TableField("ORDERS_NAME")
    private String ordersName;

    @ApiModelProperty(value = "活动账期")
    @TableField("ORDERS_DATE")
    private String ordersDate;

    @ApiModelProperty(value = "活动可发布数量")
    @TableField("ORDERS_COUNT")
    private Integer ordersCount;

    @ApiModelProperty(value = "活动过期时间")
    @TableField("ORDERS_EXPIRED_DATE")
    private Date ordersExpiredDate;

    @ApiModelProperty(value = "活动主套餐")
    @TableField("ORDERS_MAIN_MEAL")
    private String ordersMainMeal;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "活动标识")
    @TableField("ACTIVITY_ID")
    private String activityId;

    @ApiModelProperty(value = "活动生效时间")
    @TableField("ORDERS_EFFECT_DATE")
    private Date ordersEffectDate;

    @ApiModelProperty(value = "活动主套餐编码")
    @TableField("ORDERS_MAIN_NUMBER")
    private String ordersMainNumber;

}
