<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslGoodsRelMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods_rel a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.goodsNumberRel != null">
                AND a.GOODS_NUMBER_REL LIKE CONCAT('%', #{param.goodsNumberRel}, '%')
            </if>
            <if test="param.relType != null">
                AND a.REL_TYPE LIKE CONCAT('%', #{param.relType}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.goodsNumberRelMsg != null">
                AND a.GOODS_NUMBER_REL_MSG LIKE CONCAT('%', #{param.goodsNumberRelMsg}, '%')
            </if>
            <if test="param.goodsSchoolDiscern != null">
                AND a.GOODS_SCHOOL_DISCERN = #{param.goodsSchoolDiscern}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsRel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsRel">
        <include refid="selectSql"></include>
    </select>

</mapper>
