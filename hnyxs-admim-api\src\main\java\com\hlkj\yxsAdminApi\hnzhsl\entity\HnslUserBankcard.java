package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户银行卡表
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUserBankcard对象", description = "用户银行卡表")
public class HnslUserBankcard implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "所在银行")
    @TableField("BANK_NAME")
    private String bankName;

    @ApiModelProperty(value = "支行名称")
    @TableField("BANK_SUB_NAME")
    private String bankSubName;

    @ApiModelProperty(value = "银行卡号")
    @TableField("BANK_NUMBER")
    private String bankNumber;

    @ApiModelProperty(value = "持卡人姓名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private LocalDate createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private LocalDate updatedDate;

    @ApiModelProperty(value = "所在银行编码")
    @TableField("BANK_CODE")
    private String bankCode;

}
