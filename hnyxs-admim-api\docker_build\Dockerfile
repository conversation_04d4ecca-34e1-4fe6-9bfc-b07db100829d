# tomcat项目镜像构建
FROM yd-artifact.srdcloud.cn/hnx_zsxh5-hndx_zsxh5_prod-release-docker-virtual/hnx-ctyunos-images/tomcat8:8.5.98_8G
#使用root用户启动
USER root

# 维护者信息
MAINTAINER liufahong

#设置要运行的war包名称
ENV JAR_FILE_PATH=""
ENV JAR_FILE="hnyxs-admim-api-1.0.0.war"
ENV BASE_DIR="/usr/local/tomcat"
ENV APPLICATIONNAME="hnyxsadmimapi"
# 设置程序服务端口
ENV SERVER_NAME="hnyxsadmimapi"
ENV SERVER_PORT="18090"

# 设置云眼参数
ENV PINPOINT_PACK="pinpoint-agent-2.3.3.zip"
ENV PINPOINT_FILE="pinpoint-bootstrap-2.3.3.jar"
ENV PINPOINT_DIR="pinpoint-agent-2.3.3"
ENV AGENTLOCAL="$BASE_DIR/$PINPOINT_DIR/$PINPOINT_FILE"
ENV LICENCE=8431100031
# 将pinpoint包放入base_dir目录
ADD $PINPOINT_PACK $BASE_DIR/$PINPOINT_PACK

# 应用工作目录
WORKDIR /usr/local/tomcat

# 复制所需文件，从宿主机拷贝到镜像中
COPY hnyxs-admim-api-1.0.0.war /usr/local/tomcat/webapps/hnyxsadmimapi.war
# COPY context.xml $BASE_DIR/conf
# COPY server.xml $BASE_DIR/conf
COPY web.xml $BASE_DIR/conf

# 修改docker时区为东八区，规避应用程序和北京时间相差8小时问题
ENV TZ=Asia/Shanghai

#设置java运行参数
# ENV JAVA_OPTS="-Xms2g -Xmx2g  -XX:+HeapDumpOnOutOfMemoryError -XX:MetaspaceSize=1g -XX:MaxMetaspaceSize=1g"
ENV JAVA_OPTS_SET=" -Xmn2048m -XX:+HeapDumpOnOutOfMemoryError -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m -XX:SurvivorRatio=8 -XX:MaxTenuringThreshold=12 -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:CMSInitiatingOccupancyFraction=70 -XX:+DisableExplicitGC"


# 添加系统变量
RUN unzip $BASE_DIR/$PINPOINT_PACK -d $BASE_DIR \
&& echo '#!/bin/sh' >> $BASE_DIR/bin/setenv.sh \
&& echo 'AGENTID=`ip addr | grep -o -e "inet [0-9]\{1,3\}.[0-9]\{1,3\}.[0-9]\{1,3\}.[0-9]\{1,3\}" | grep -v "127.0.0" | grep -o -e "[0-9]\{1,3\}.[0-9]\{1,3\}.[0-9]\{1,3\}.[0-9]\{1,3\}"`' >> $BASE_DIR/bin/setenv.sh \
&& echo 'hostname=`hostname`' >> $BASE_DIR/bin/setenv.sh \
&& echo 'applicationname=`hostname| rev|cut -d "-" -f 3-$n|rev`' >> $BASE_DIR/bin/setenv.sh \
&& echo 'podhash=`hostname |rev|cut -d "-" -f -2|rev`' >> $BASE_DIR/bin/setenv.sh \
&& echo 'CATALINA_OPTS="$CATALINA_OPTS -javaagent:$AGENTLOCAL -Dsnc.hostname=$hostname -Dsnc.applicationName=$applicationname -Dsnc.podhash=$podhash -Dpinpoint.applicationName=$applicationname -Dpinpoint.agentId=$AGENTID-$SERVER_PORT -Dpinpoint.licence=$LICENCE"' >> $BASE_DIR/bin/setenv.sh \
&& echo 'JAVA_OPTS="$JAVA_OPTS $JAVA_OPTS_SET"' >> $BASE_DIR/bin/setenv.sh \
&& chmod 755 $BASE_DIR/bin/setenv.sh \
&& sudo sed -i 's/tomcat,role1/manager/' /usr/local/tomcat/conf/tomcat-users.xml

# 声明服务要暴露的端口
# EXPOSE 8081

# 服务启动入口
# CMD ["/usr/local/tomcat/bin/catalina.sh","run"]
ENTRYPOINT ["/usr/local/tomcat/bin/catalina.sh", "run"]
# ENTRYPOINT ["sh", "/usr/local/tomcat/bin/startup.sh"]