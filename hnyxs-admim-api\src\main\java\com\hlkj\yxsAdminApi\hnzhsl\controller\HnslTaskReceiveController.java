package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTask;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslIntegralService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskReceiveService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskReceive;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskReceiveParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务接取表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "任务接取表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-task-receive")
public class HnslTaskReceiveController extends BaseController {
    @Autowired
    private HnslTaskReceiveService hnslTaskReceiveService;

    @Autowired
    private HnslTaskService hnslTaskService;

    @Autowired
    private HnslUserService hnslUserService;

    @Autowired
    private HnslIntegralService hnslIntegralService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:list')")
    @OperationLog
    @ApiOperation("分页查询任务接取表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTaskReceive>> page(@RequestBody HnslTaskReceiveParam param) {
        PageParam<HnslTaskReceive, HnslTaskReceiveParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskReceiveService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTaskReceiveService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:list')")
    @OperationLog
    @ApiOperation("查询全部任务接取表")
    @PostMapping("/list")
    public ApiResult<List<HnslTaskReceive>> list(@RequestBody HnslTaskReceiveParam param) {
        PageParam<HnslTaskReceive, HnslTaskReceiveParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskReceiveService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTaskReceiveService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:list')")
    @OperationLog
    @ApiOperation("根据id查询任务接取表")
    @GetMapping("/{id}")
    public ApiResult<HnslTaskReceive> get(@PathVariable("id") Integer id) {
        return success(hnslTaskReceiveService.getById(id));
        // 使用关联查询
        //return success(hnslTaskReceiveService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:save')")
    @OperationLog
    @ApiOperation("添加任务接取表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTaskReceive hnslTaskReceive) {
        if (hnslTaskReceiveService.save(hnslTaskReceive)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:update')")
    @OperationLog
    @ApiOperation("修改任务接取表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTaskReceive hnslTaskReceive) {
        User loginUser = getLoginUser();
        if (hnslTaskReceiveService.updateById(hnslTaskReceive)) {
            if(hnslTaskReceive.getTaskStatusRel()==2){//添加积分
                LambdaQueryWrapper<HnslTask> hnslTaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
                hnslTaskLambdaQueryWrapper.eq(HnslTask::getTaskCode,hnslTaskReceive.getTaskCode())
                        .eq(HnslTask::getStatus,1);
                HnslTask task=hnslTaskService.getOne(hnslTaskLambdaQueryWrapper);
                HnslIntegral jf=new HnslIntegral();
                jf.setIntegralOperation(task.getTaskAward().doubleValue());
                jf.setUserId(hnslTaskReceive.getUserPhone());
                jf.setCreatedUser(loginUser.getUsername());
                jf.setIntegralRemark("完成任务{"+task.getTaskTitle()+"}");
                jf.setIntegralType(4);
                jf.setOrderId(hnslTaskReceive.getTaskCode());
                hnslIntegralService.save(jf);
                HnslUser user=new HnslUser();
                user.setUserPhone(jf.getUserId());
                user.setIntegral(jf.getIntegralOperation());
                hnslUserService.updateUserByIntegral(user);
            }
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:remove')")
    @OperationLog
    @ApiOperation("删除任务接取表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTaskReceiveService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:save')")
    @OperationLog
    @ApiOperation("批量添加任务接取表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTaskReceive> list) {
        if (hnslTaskReceiveService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:update')")
    @OperationLog
    @ApiOperation("批量修改任务接取表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTaskReceive> batchParam) {
        if (batchParam.update(hnslTaskReceiveService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskReceive:remove')")
    @OperationLog
    @ApiOperation("批量删除任务接取表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTaskReceiveService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
