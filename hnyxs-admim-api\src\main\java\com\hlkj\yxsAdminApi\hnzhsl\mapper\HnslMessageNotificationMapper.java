package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMessageNotification;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslMessageNotificationParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 扫楼消息通知表Mapper
 *
 * <AUTHOR>
 * @since 2023-07-21 11:31:45
 */
public interface HnslMessageNotificationMapper extends BaseMapper<HnslMessageNotification> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslMessageNotification>
     */
    List<HnslMessageNotification> selectPageRel(@Param("page") IPage<HnslMessageNotification> page,
                             @Param("param") HnslMessageNotificationParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslMessageNotification> selectListRel(@Param("param") HnslMessageNotificationParam param);

}
