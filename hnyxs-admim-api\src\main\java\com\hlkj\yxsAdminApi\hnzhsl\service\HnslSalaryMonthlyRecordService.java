package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryMonthlyRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSalaryMonthlyRecordParam;

import java.util.List;

/**
 * 扫楼薪资每月记录表Service
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslSalaryMonthlyRecordService extends IService<HnslSalaryMonthlyRecord> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslSalaryMonthlyRecord>
     */
    PageResult<HnslSalaryMonthlyRecord> pageRel(HnslSalaryMonthlyRecordParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslSalaryMonthlyRecord>
     */
    List<HnslSalaryMonthlyRecord> listRel(HnslSalaryMonthlyRecordParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslSalaryMonthlyRecord
     */
    HnslSalaryMonthlyRecord getByIdRel(Integer id);

}
