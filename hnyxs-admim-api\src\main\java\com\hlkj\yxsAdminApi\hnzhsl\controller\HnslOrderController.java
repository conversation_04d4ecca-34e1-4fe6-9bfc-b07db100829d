package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRechargeOrder;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslOrderService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslOrderParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import com.hlkj.yxsAdminApi.hnzhsl.utils.ExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扫楼工具订单表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "扫楼工具订单表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslOrder")
public class HnslOrderController extends BaseController {
    @Autowired
    private HnslOrderService hnslOrderService;

    @Autowired
    private HnslUserService hnslUserService;

    @Autowired
    private ConfigProperties config;

    @Autowired
    private QueryUserManagerUtil queryUserManagerUtil;

    @Autowired
    private AwsS3Utils awsS3Utils;

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼工具订单表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslOrder>> page(@RequestBody HnslOrderParam param) {
        //PageParam<HnslOrder, HnslOrderParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        if(StringUtil.isNull(String.valueOf(param.getBeginTime()))
                && StringUtil.isNull(String.valueOf(param.getEndTime()))){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cd = Calendar.getInstance();
            cd.add(Calendar.DATE,-7);
            param.setBeginTime(simpleDateFormat.format(cd.getTime()));
            cd = Calendar.getInstance();
            cd.add(Calendar.DATE,1);
            param.setEndTime(simpleDateFormat.format(cd.getTime()));
        }
        return success(hnslOrderService.pageRel(param));
        // 使用关联查询
        //return success(hnslOrderService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼工具订单表")
    @PostMapping("/list")
    public ApiResult<List<HnslOrder>> list(@RequestBody HnslOrderParam param) {
        PageParam<HnslOrder, HnslOrderParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslOrderService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslOrderService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼工具订单表")
    @GetMapping("/{id}")
    public ApiResult<HnslOrder> get(@PathVariable("id") Integer id) {
        LambdaQueryWrapper<HnslOrderParam> orderParamLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderParamLambdaQueryWrapper.eq(HnslOrderParam::getId,id);
        HnslOrder hnslOrder = hnslOrderService.queryObject(orderParamLambdaQueryWrapper);
        String codeToName = InterfaceUtil.getCodeToName(hnslOrder.getCitycode());
        hnslOrder.setCityName(codeToName);
        return success(hnslOrder);
        // 使用关联查询
        //return success(hnslOrderService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:save')")
    @OperationLog
    @ApiOperation("添加扫楼工具订单表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslOrder hnslOrder) {
        if (hnslOrderService.save(hnslOrder)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:update')")
    @OperationLog
    @ApiOperation("修改扫楼工具订单表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslOrder hnslOrder) {
        if (hnslOrderService.updateById(hnslOrder)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:remove')")
    @OperationLog
    @ApiOperation("删除扫楼工具订单表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslOrderService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:save')")
    @OperationLog
    @ApiOperation("批量添加扫楼工具订单表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslOrder> list) {
        if (hnslOrderService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:update')")
    @OperationLog
    @ApiOperation("批量修改扫楼工具订单表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslOrder> batchParam) {
        if (batchParam.update(hnslOrderService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:remove')")
    @OperationLog
    @ApiOperation("批量删除扫楼工具订单表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslOrderService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:outputOrder')")
    @OperationLog
    @ApiOperation("导出订单")
    @PostMapping("/outputOrder")
    public ApiResult<?> outputOrder(@RequestBody HnslOrderParam hnslOrder, HttpServletRequest request,
                         HttpServletResponse response) {
        logger.info("导出订单入参" + hnslOrder);

        ServletContext servletContext = request.getSession().getServletContext();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //查询列表数据
        List<HnslOrder> hnslOrderList = hnslOrderService.listRel(hnslOrder);

        // 生成一条记录
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "订单表" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath + filePath);
        File file = new File(filePath + ".xls");
        Integer activeEvent = 4;
        Integer appSettingId = hnslOrder.getAppSettingId();
        Integer list = hnslOrderList.size();
        User user = getLoginUser();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, list, appSettingId);

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        for (int i = 0; i <= 26; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("订单编号");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("CRM订单编号");
        cell.setCellStyle(style);
        cell = row.createCell(25);
        cell.setCellValue("订单时间");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("订单状态");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("ICCID卡号");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("套餐名称");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("订单积分");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("签名状态");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("销售人员编码");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("揽机工号");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("所属地市");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("所属学校");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("订单类型");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("合伙人姓名");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("合伙人号码");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("合伙人级别");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("订购号码");
        cell.setCellStyle(style);
        cell = row.createCell(16);
        cell.setCellValue("客户联系电话");
        cell.setCellStyle(style);
        cell = row.createCell(17);
        cell.setCellValue("客户姓名");
        cell.setCellStyle(style);
        cell = row.createCell(18);
        cell.setCellValue("支付价格");
        cell.setCellStyle(style);
        cell = row.createCell(19);
        cell.setCellValue("是否有效");
        cell.setCellStyle(style);
        cell = row.createCell(20);
        cell.setCellValue("上级合伙人姓名");
        cell.setCellStyle(style);
        cell = row.createCell(21);
        cell.setCellValue("上级合伙人销售员编码");
        cell.setCellStyle(style);
        cell = row.createCell(22);
        cell.setCellValue("上上级合伙人");
        cell.setCellStyle(style);
        cell = row.createCell(23);
        cell.setCellValue("上上级合伙人销售员编码");
        cell.setCellStyle(style);
        cell = row.createCell(24);
        cell.setCellValue("入学年级");
        cell.setCellStyle(style);
        cell = row.createCell(26);
        cell.setCellValue("来源渠道");
        cell.setCellStyle(style);
        cell = row.createCell(27);
        cell.setCellValue("渠道类型");
        cell.setCellStyle(style);
        cell = row.createCell(28);
        cell.setCellValue("下单地址");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != hnslOrderList && hnslOrderList.size() != 0) {

            for (int i = 0; i < hnslOrderList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(hnslOrderList.get(i).getOrderId());
                row.createCell(1).setCellValue(hnslOrderList.get(i).getCrmOrderId());
                row.createCell(25).setCellValue(simpleDateFormat.format(hnslOrderList.get(i).getCreatedDate()));
                if("4".equals(hnslOrderList.get(i).getSaflType()) && "1".equals(hnslOrderList.get(i).getOrderStatus())){
                    row.createCell(2).setCellValue("加装成功");
                }else if ("1".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("实名提交");
                } else if ("2".equals(hnslOrderList.get(i).getOrderStatus())) {
                    if("8".equals(hnslOrderList.get(i).getSaflType()) || "9".equals(hnslOrderList.get(i).getSaflType()) ){
                        row.createCell(2).setCellValue("审核提交");
                    }else{
                        row.createCell(2).setCellValue("支付成功");
                    }
                } else if ("3".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("激活成功");
                } else if ("10".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("待支付");
                } else if ("11".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("预约成功");
                } else if ("12".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("制卡");
                } else if ("13".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("发货");
                } else if ("14".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("已支付");
                }else if ("15".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("激活成功");
                }else if ("16".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("激活失败");
                }else if ("17".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("激活异常");
                }else if ("21".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("待加装");
                }else if ("22".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("加装失败");
                }else if ("23".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("加装成功");
                }else if ("9".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("订单超时");
                }else if ("24".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(2).setCellValue("已退款");
                }
                row.createCell(3).setCellValue(hnslOrderList.get(i).getIccid());
                row.createCell(4).setCellValue(hnslOrderList.get(i).getGoodsName());
                row.createCell(5).setCellValue(hnslOrderList.get(i).getIntegralOperation());
                row.createCell(6).setCellValue(hnslOrderList.get(i).getSignatureStatus()==1?"已签名":"未签名");
                row.createCell(7).setCellValue(hnslOrderList.get(i).getSalesCode());
                row.createCell(8).setCellValue(hnslOrderList.get(i).getNumbers());
                row.createCell(9).setCellValue(InterfaceUtil.getCodeToName(hnslOrderList.get(i).getCitycode()));
                row.createCell(10).setCellValue(hnslOrderList.get(i).getSchoolName());
                if ("1".equals(hnslOrderList.get(i).getBroadBand())) { // 先去判断订单是否有宽带 即校园融合
                    row.createCell(11).setCellValue("校园融合");
                } else if ("1".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("号卡新装");
                } else if ("2".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("一人一码（本地入口）");
                } else if ("3".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("一人一码（飞young入口）");
                }else if ("4".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("校园加装");
                }else if ("6".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("校园未成年加装");
                }else if ("7".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("驿站开卡");
                }else if ("8".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("熟卡");
                }else if ("9".equals(hnslOrderList.get(i).getSaflType())) {
                    row.createCell(11).setCellValue("未成年熟卡");
                }else{
                    row.createCell(11).setCellValue("未知");
                }
                row.createCell(12).setCellValue(hnslOrderList.get(i).getUserName());
                row.createCell(13).setCellValue(hnslOrderList.get(i).getHhid());
                if ("1".equals(hnslOrderList.get(i).getStatusSf())) {
                    row.createCell(14).setCellValue("校园经理");
                } else if ("2".equals(hnslOrderList.get(i).getStatusSf())) {
                    row.createCell(14).setCellValue("一级");
                } else if ("3".equals(hnslOrderList.get(i).getStatusSf())) {
                    row.createCell(14).setCellValue("二级");
                } else if ("4".equals(hnslOrderList.get(i).getStatusSf())) {
                    row.createCell(14).setCellValue("三级");
                } else if ("6".equals(hnslOrderList.get(i).getStatusSf())) {
                    row.createCell(14).setCellValue("地市");
                } else if ("5".equals(hnslOrderList.get(i).getStatusSf())) {
                    row.createCell(14).setCellValue("省级");
                } else {
                    row.createCell(14).setCellValue("无");
                }
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerPhone())){
                    row.createCell(15).setCellValue(hnslOrderList.get(i).getCustomerPhone());
                }else{
                    row.createCell(15).setCellValue("");
                }
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerContactPhone())){
                    row.createCell(16).setCellValue(hnslOrderList.get(i).getCustomerContactPhone());
                }else{
                    row.createCell(16).setCellValue("");
                }
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerName())){
                    row.createCell(17).setCellValue(hnslOrderList.get(i).getCustomerName());
                }else{
                    row.createCell(17).setCellValue("");
                }
                if(!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getOrderPrice()))){
                    row.createCell(18).setCellValue(hnslOrderList.get(i).getOrderPrice());
                }else{
                    row.createCell(18).setCellValue("");
                }

                if ("1".equals(String.valueOf(hnslOrderList.get(i).getStatus()))) {
                    row.createCell(19).setCellValue("有效");
                } else if ("0".equals(String.valueOf(hnslOrderList.get(i).getStatus()))) {
                    row.createCell(19).setCellValue("无效");
                } else {
                    row.createCell(19).setCellValue("  ");
                }
                // 如果订单中的合伙人id不为空
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getHhid())) {
                    // 查询上级合伙人和上上级合伙人信息
                    List<HnslUser> userList = new ArrayList<>();
                    try {
                        userList = hnslUserService.queryIsEmptyStatusSuperior(hnslOrderList.get(i).getHhid());
                    }catch (Exception e){
                        logger.info(hnslOrderList.get(i).getHhid());
                        logger.info(e.getMessage());
                    }
                    if (userList.size() > 0) {
                        if (userList.size() == 1) {
                            row.createCell(20).setCellValue(userList.get(0).getUserName());
                            row.createCell(21).setCellValue(userList.get(0).getSalesCode());
                        } else if (userList.size() == 2) {
                            if (Integer.valueOf(userList.get(0).getStatusSf()) > Integer.valueOf(userList.get(1).getStatusSf())) {
                                row.createCell(20).setCellValue(userList.get(0).getUserName());
                                row.createCell(21).setCellValue(userList.get(0).getSalesCode());
                                row.createCell(22).setCellValue(userList.get(1).getUserName());
                                row.createCell(23).setCellValue(userList.get(1).getSalesCode());
                            } else {
                                row.createCell(20).setCellValue(userList.get(1).getUserName());
                                row.createCell(21).setCellValue(userList.get(1).getSalesCode());
                                row.createCell(22).setCellValue(userList.get(0).getUserName());
                                row.createCell(23).setCellValue(userList.get(0).getSalesCode());
                            }
                        }
                    }
                }

                if(!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getEnroltime()))){
                    row.createCell(24).setCellValue(hnslOrderList.get(i).getEnroltime());
                }else{
                    row.createCell(24).setCellValue("");
                }
                if(!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getChannelType()))){
                    String channelType = String.valueOf(hnslOrderList.get(i).getChannelType());
                    if("1".equals(channelType)){
                        row.createCell(26).setCellValue("中小学");
                    }else if("2".equals(channelType)){
                        row.createCell(26).setCellValue("高校");
                    }else if("3".equals(channelType)){
                        row.createCell(26).setCellValue("学子公司");
                    }else if("4".equals(channelType)){
                        row.createCell(26).setCellValue("校园店");
                    }else if("5".equals(channelType)){
                        row.createCell(26).setCellValue("泛渠道");
                    }
                }else{
                    row.createCell(26).setCellValue("");
                }
                if(!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getSchoolChannelType()))){
                    String channelType = String.valueOf(hnslOrderList.get(i).getSchoolChannelType());
                    if("1".equals(channelType)){
                        row.createCell(27).setCellValue("高校");
                    }else if("2".equals(channelType)){
                        row.createCell(27).setCellValue("中小学");
                    }else if("3".equals(channelType)){
                        row.createCell(27).setCellValue("公众");
                    }
                }else{
                    row.createCell(27).setCellValue("");
                }
                row.createCell(28).setCellValue(hnslOrderList.get(i).getPreventionControlAddress());
            }
            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("订单列表导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:update')")
    @OperationLog
    @ApiOperation("替换图片上传功能")
    @PostMapping("/replaceImage")
    public ApiResult<?> replaceImage(HttpServletRequest request, String basedata1, String basedata2, String basedata3,
                          String basedata4, String basedata5, String basedata6,String basedata7, String basedata8, String basedata9,
                          String imgUrl1, String imgUrl2, String imgUrl3,String imgUrl4, String imgUrl5, String imgUrl6
            ,String imgUrl7, String imgUrl8, String imgUrl9) {
        Map<String, String> basedataMap = new HashMap<>();
        basedataMap.put("0",basedata1);
        basedataMap.put("1",basedata2);
        basedataMap.put("2",basedata3);
        basedataMap.put("3",basedata4);
        basedataMap.put("4",basedata5);
        basedataMap.put("5",basedata6);
        basedataMap.put("6",basedata7);
        basedataMap.put("7",basedata8);
        basedataMap.put("8",basedata9);
        Map<String, String> imgUrlMap = new HashMap<>();
        imgUrlMap.put("0",imgUrl1);
        imgUrlMap.put("1",imgUrl2);
        imgUrlMap.put("2",imgUrl3);
        imgUrlMap.put("3",imgUrl4);
        imgUrlMap.put("4",imgUrl5);
        imgUrlMap.put("5",imgUrl6);
        imgUrlMap.put("6",imgUrl7);
        imgUrlMap.put("7",imgUrl8);
        imgUrlMap.put("8",imgUrl9);

        for (int i = 0; i < 9; i++) {
            if(!StringUtil.isEmpty(basedataMap.get(String.valueOf(i)))){ // 如果有图片 则替换
                String newname = imgUrlMap.get(String.valueOf(i)).replaceAll("uploads/picture/","");
                BufferedOutputStream outputStream = null;
                FileOutputStream fileOutputStream = null;
                try {
                    String url = ConstantUtil.SAVE_IMAGE_PATH;
                    String uploadPath = url + "uploads" + File.separator + "picture" + File.separator;
                    logger.info("文件路径:" + uploadPath + " 原有图片: "+i+" 名称" + newname);
                    // 检查上传文件的目录
                    File uploadDir = new File(uploadPath);
                    if (!uploadDir.isDirectory()) {
                        if (!uploadDir.mkdir()) {
                            return fail("文件所在目录创建失败");
                        }
                    }

                    File saveFile = new File(uploadPath, newname);

                    BASE64Decoder decoder = new BASE64Decoder();
                    byte[] bytes = decoder.decodeBuffer(basedataMap.get(String.valueOf(i))); //解码
                    for (int j = 0; j < bytes.length; ++j) {
                        if (bytes[j] < 0) {//调整异常数据
                            bytes[j] += 256;
                        }
                    }
                    File ocrFile = new File(uploadPath, newname);
                    fileOutputStream = new FileOutputStream(ocrFile);
                    outputStream = new BufferedOutputStream(fileOutputStream);
                    outputStream.write(bytes);
//                    resultMap.put("name", newname);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("文件上传接口上传异常:" + e.getMessage());
                    return fail("上传失败");
                } finally {
                    if (outputStream != null) {
                        try {
                            outputStream.close();
                        } catch (IOException e) {
                            logger.info("outputStream关闭异常" + e);
                        }
                    }
                    if (fileOutputStream != null) {
                        try {
                            fileOutputStream.close();
                        } catch (IOException e) {
                            logger.info("fileOutputStream关闭异常" + e);
                        }
                    }
                }
            }
        }
        return success();
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:update')")
    @OperationLog
    @ApiOperation("撤单功能")
    @PostMapping("/cancellations")
    public ApiResult<?> cancellations(HttpServletRequest request, String id) {
        HnslOrder hnslOrderEntity = new HnslOrder(); //用于撤单订单实体
        HnslOrder orderEntity = new HnslOrder(); //用于修改数据库数据实体
        // 查询订单信息
        if (!StringUtil.isEmpty(id)) {
            LambdaQueryWrapper<HnslOrderParam> orderParamLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderParamLambdaQueryWrapper.eq(HnslOrderParam::getId,id);
            hnslOrderEntity = hnslOrderService.queryObject(orderParamLambdaQueryWrapper);
            logger.info("当前撤单订单信息 :" + hnslOrderEntity.toString());
        }
        //根据撤单订单信息 判断是否可以进行撤单 限制boolean
        //1.超过24小时未支付的订单 先获取订单状态是否未支付
        Boolean flag = true; //限制该订单最终是否能进行撤单流程
        String msg = "";
        if (!StringUtil.isEmpty(hnslOrderEntity.toString())) {

            //判断下单时间是否超过24小时
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date submitDate = hnslOrderEntity.getCreatedDate(); //提交时间=创建时间
            Date currentDate = new Date();
            Boolean isSuccess = true;
            long millTime = currentDate.getTime() - submitDate.getTime();
            if (millTime < 0) {
                isSuccess = false;
            } else if (millTime * 1.0 / (60 * 60 * 1000) >= 24) { //超过24小时
                isSuccess = false;
            }else {
                isSuccess = true;
            }
            String orderType = hnslOrderEntity.getOrderStatus();
            if("1".equalsIgnoreCase(orderType)){ //未支付的订单
              /*  if(!isSuccess){ //如果超过24小时 不允许撤单
                    flag = false;
                    msg = "超过24小时未支付的订单不允许撤单";
                }*/
            }else{ //支付成功

                //如果订单超过24小时已经支付但过费失败且未手动过费的订单
                if(!isSuccess && hnslOrderEntity.getErrorStatus() == 1L && hnslOrderEntity.getDispose() == 0L ){
                    flag = false;
                    msg = "超过24小时过费失败且未手动过费的订单不允许撤单";
                }

                //如果订单支付成功并手动过费失败或者报错 不允许撤单
                if(hnslOrderEntity.getErrorStatus() == 1L && hnslOrderEntity.getDispose() == 1L){
                    flag = false;
                    msg = "支付成功并手动过费失败的订单不允许撤单";
                }
            }

            // 最终执行撤单流程
            Map<String, String> resultMap = new HashMap<>();
            if(flag){
                try {
                    resultMap = InterfaceUtil.cancellationsCrmOrder(hnslOrderEntity);
                    if (resultMap.get("code").equals("0")) {
                        //撤单成功 修改订单DISPOSE = 3(已撤单) 并禁用订单
                        UpdateWrapper<HnslOrder> hnslOrderUpdateWrapper = new UpdateWrapper<>();
                        hnslOrderUpdateWrapper.eq("ID",id)
                                .set("DISPOSE",3)
                                .set("ORDER_STATUS",9)
                                .set("STATUS",0);
                        hnslOrderService.update(hnslOrderUpdateWrapper);
                    }else{
                        return fail(resultMap.get("message"));
                    }
                    return success();
                }catch (Exception e){
                    logger.info("id " + id +" 撤单失败 :" + e);
                    return fail(resultMap.get("message"));
                }
            }
        }
        return fail("撤单失败 :" + msg);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:outputOrder')")
    @OperationLog
    @ApiOperation("一人一码订单导出")
    @PostMapping("/outQrUserOrder")
    public ApiResult<?> outQrUserOrder(@RequestBody HnslOrderParam hnslOrder, HttpServletRequest request,
                            HttpServletResponse response) {
        logger.info("导出订单入参" + hnslOrder);
        User user = getLoginUser();

        ServletContext servletContext = request.getSession().getServletContext();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //查询列表数据
        List<HnslOrder> hnslOrderList = hnslOrderService.listRel(hnslOrder);
        // 生成一条记录
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "一人一码订单" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath);
        Integer activeEvent = 7;
        Integer appSettingId = hnslOrder.getAppSettingId();
        Integer list = hnslOrderList.size();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, list, appSettingId);
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        for (int i = 0; i <= 15; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }
        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("订单编码");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("CRM订单号");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("订单时间");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("订单状态");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("套餐名称");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("销售人员编码");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("揽机工号");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("所属学校");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("合伙人姓名");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("订购号码");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("ICCID卡号");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("客户姓名");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("用户身份证号码");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("邮寄地址");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("客户联系电话");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("支付价格");
        cell.setCellStyle(style);
        cell = row.createCell(16);
        cell.setCellValue("是否有效");
        cell.setCellStyle(style);
        cell = row.createCell(17);
        cell.setCellValue("渠道类型");
        cell.setCellStyle(style);
        cell = row.createCell(18);
        cell.setCellValue("下单地址");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != hnslOrderList && hnslOrderList.size() != 0) {
            for (int i = 0; i < hnslOrderList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(hnslOrderList.get(i).getOrderId());
                row.createCell(1).setCellValue(hnslOrderList.get(i).getCrmOrderId());
                row.createCell(2).setCellValue(simpleDateFormat.format(hnslOrderList.get(i).getCreatedDate()));
                if ("10".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("待支付");
                } else if ("11".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("预约成功");
                } else if ("12".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("制卡");
                } else if ("13".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("发货");
                } else if ("14".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("已支付");
                }else if ("15".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("激活成功");
                }else if ("16".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("激活失败");
                }else if ("17".equals(hnslOrderList.get(i).getOrderStatus())) {
                    row.createCell(3).setCellValue("激活异常");
                }
                row.createCell(4).setCellValue(hnslOrderList.get(i).getGoodsName());
                row.createCell(5).setCellValue(hnslOrderList.get(i).getSalesCode());
                row.createCell(6).setCellValue(hnslOrderList.get(i).getNumbers());
                row.createCell(7).setCellValue(hnslOrderList.get(i).getSchoolName());
                row.createCell(8).setCellValue(hnslOrderList.get(i).getUserName());
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerPhone())){
                    row.createCell(9).setCellValue(hnslOrderList.get(i).getCustomerPhone());
                }else{
                    row.createCell(9).setCellValue("");
                }
                row.createCell(10).setCellValue(hnslOrderList.get(i).getIccid());
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerName())){
                    row.createCell(11).setCellValue(hnslOrderList.get(i).getCustomerName());
                }else{
                    row.createCell(11).setCellValue("");
                }
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerCard())){
                    String cert_num=hnslOrderList.get(i).getCustomerCard();
                    String num = cert_num.substring(0, 4) + "**********" + cert_num.substring(14, 18);
                    row.createCell(12).setCellValue(num);
                }else{
                    row.createCell(12).setCellValue("");
                }
                row.createCell(13).setCellValue(hnslOrderList.get(i).getMailingAddress()+hnslOrderList.get(i).getMailingDetailedAddress());
                if(!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerContactPhone())){
                    row.createCell(14).setCellValue(hnslOrderList.get(i).getCustomerContactPhone());
                }else{
                    row.createCell(14).setCellValue("");
                }
                if(!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getOrderPrice()))){
                    row.createCell(15).setCellValue(hnslOrderList.get(i).getOrderPrice());
                }else{
                    row.createCell(15).setCellValue("");
                }
                if(hnslOrderList.get(i).getStatus()==0){
                    row.createCell(16).setCellValue("否");
                }else{
                    row.createCell(16).setCellValue("是");
                }
                String schoolChannelType="";
                if(hnslOrderList.get(i).getSchoolChannelType()==1){
                    schoolChannelType="高校";
                }else if(hnslOrderList.get(i).getSchoolChannelType()==2){
                    schoolChannelType="中小学";
                }else if(hnslOrderList.get(i).getSchoolChannelType()==3){
                    schoolChannelType="公众";
                }
                row.createCell(17).setCellValue(schoolChannelType);
                row.createCell(18).setCellValue(hnslOrderList.get(i).getPreventionControlAddress());
            }
            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("一人一码订单导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }


    /**
     * 重新同步未同步成功的接口  订单类型不同 同步的接口不一样
     */
/*    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:outputOrder')")
    @OperationLog
    @ApiOperation("一人一码订单导出")
    @PostMapping("/resynchronization")
    public ApiResult<?> resynchronization(@RequestBody HnslOrder hnslOrder) {
        try{
            if(Objects.equals(hnslOrder.getSaflType(),"1")
                    || Objects.equals(hnslOrder.getSaflType(),"6")
                    || Objects.equals(hnslOrder.getSaflType(),"10")){
                Map<String, String> resultMap=hnslOrderService.synchronizeBPS(hnslOrder);
                if(!Objects.equals(resultMap.get("resultCode"),"0")){
                    return fail("同步失败!");
                }
            }else if(Objects.equals(hnslOrder.getSaflType(),"2") || Objects.equals(hnslOrder.getSaflType(),"3")){
                // 一人一码类型 基本不存在不会同步问题
            }else if(Objects.equals(hnslOrder.getSaflType(),"4")){
                Map<String,String> resultMap=hnslOrderService.synchronizeBPSCode(hnslOrder);
                if(!Objects.equals(resultMap.get("resultCode"),"0")){
                    return fail("同步失败!");
                }
            }else{
                return fail("未知订单类型！");
            }
        }catch (Exception e){
            return fail(e+"");
        }
        return success();
    }*/
}
