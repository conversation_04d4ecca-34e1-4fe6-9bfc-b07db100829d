package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 派单详情表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSendOrdersData对象", description = "派单详情表")
public class HnslSendOrdersData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识 主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "公共号码")
    @TableField("PUBLIC_NUMBER")
    private String publicNumber;

    @ApiModelProperty(value = "接触单标识 每条数据的唯一标识 回单使用")
    @TableField("TOUCH_ID")
    private String touchId;

    @ApiModelProperty(value = "活动标识")
    @TableField("ACTIVITY_LOGO")
    private String activityLogo;

    @ApiModelProperty(value = "外呼联系号码1")
    @TableField("OUTGOING_NUMBER1")
    private String outgoingNumber1;

    @ApiModelProperty(value = "外呼联系号码2")
    @TableField("OUTGOING_NUMBER2")
    private String outgoingNumber2;

    @ApiModelProperty(value = "外呼联系号码3")
    @TableField("OUTGOING_NUMBER3")
    private String outgoingNumber3;

    @ApiModelProperty(value = "推荐套餐")
    @TableField("RECOMMENDED_PACKAGE")
    private String recommendedPackage;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "分派状态 0:未分派 1:已被分派 ")
    @TableField("ORDERS_STATUS")
    private Integer ordersStatus;

    @ApiModelProperty(value = "派单地市ID 需要对应文档表格获取具体地市编码")
    @TableField("ORDERS_LAN_ID")
    private String ordersLanId;

    @ApiModelProperty(value = "学校6级编码")
    @TableField("SCHOOL_SIX_ID")
    private String schoolSixId;

    @ApiModelProperty(value = "最新主套餐名称")
    @TableField("MAIN_PACKAGE_NAME")
    private String mainPackageName;

    @ApiModelProperty(value = "当月余额")
    @TableField("MONTH_BALANCE")
    private String monthBalance;

    @ApiModelProperty(value = "用户性别")
    @TableField("USER_SEX")
    private String userSex;

    @ApiModelProperty(value = "用户年龄")
    @TableField("USER_AGE")
    private String userAge;

    @ApiModelProperty(value = "欠费金额")
    @TableField("AMOUNT_OWED")
    private String amountOwed;

    @ApiModelProperty(value = "套餐档次")
    @TableField("PACKAGE_GRADE")
    private String packageGrade;

    @ApiModelProperty(value = "上次预存时间")
    @TableField("TIME_STORAGE")
    private String timeStorage;

    @ApiModelProperty(value = "上次流量使用情况")
    @TableField("TRAFFIC_USAGE")
    private String trafficUsage;

    @ApiModelProperty(value = "上月语音使用情况")
    @TableField("VOICE_USAGE")
    private String voiceUsage;

    @ApiModelProperty(value = "六级名称")
    @TableField("SIX_LEVEL_NAME")
    private String sixLevelName;

    @ApiModelProperty(value = "用户发展认领人归属六级组织")
    @TableField("SIX_LEVEL_ORGANIZATION")
    private String sixLevelOrganization;

    @ApiModelProperty(value = "入网揽机人")
    @TableField("OPERATOR_AIRCRAFT")
    private String operatorAircraft;

    @ApiModelProperty(value = "入网揽机工号")
    @TableField("MACHINE_NUMBE")
    private String machineNumbe;

    @ApiModelProperty(value = "入网日期")
    @TableField("ACCESS_DATE")
    private String accessDate;

    @ApiModelProperty(value = "用户姓名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "活动账期")
    @TableField("ORDERS_DATE")
    private String ordersDate;

}
