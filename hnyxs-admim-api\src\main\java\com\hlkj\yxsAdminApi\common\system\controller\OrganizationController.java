package com.hlkj.yxsAdminApi.common.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.Organization;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.param.OrganizationParam;
import com.hlkj.yxsAdminApi.common.system.service.OrganizationService;
import com.hlkj.yxsAdminApi.common.system.service.PermissionChangeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组织机构控制器
 *
 * <AUTHOR>
 * @since 2020-03-14 11:29:04
 */
@Api(tags = "组织机构管理")
@RestController
@RequestMapping("/api/system/organization")
public class OrganizationController extends BaseController {
    @Autowired
    private OrganizationService organizationService;
    
    @Autowired
    private PermissionChangeLogService permissionChangeLogService;

    @PreAuthorize("hasAuthority('sys:org:list')")
    @OperationLog
    @ApiOperation("分页查询组织机构")
    @PostMapping("/page")
    public ApiResult<PageResult<Organization>> page(OrganizationParam param) {
        return success(organizationService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('sys:org:list')")
    @OperationLog
    @ApiOperation("查询全部组织机构")
    @GetMapping()
    public ApiResult<List<Organization>> list(OrganizationParam param) {
        return success(organizationService.listRel(param));
    }

    @PreAuthorize("hasAuthority('sys:org:list')")
    @OperationLog
    @ApiOperation("根据id查询组织机构")
    @GetMapping("/{id}")
    public ApiResult<Organization> get(@PathVariable("id") Integer id) {
        return success(organizationService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('sys:org:save')")
    @OperationLog
    @ApiOperation("添加组织机构")
    @PostMapping("/add")
    public ApiResult<?> add(HttpServletRequest request, @RequestBody Organization organization) {
        if (organization.getParentId() == null) {
            organization.setParentId(0);
        }
        if (organizationService.count(new LambdaQueryWrapper<Organization>()
                .eq(Organization::getOrganizationName, organization.getOrganizationName())
                .eq(Organization::getParentId, organization.getParentId())) > 0) {
            return fail("机构名称已存在");
        }
        if (organizationService.save(organization)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "新增";
            String operationModule = "组织机构管理";
            String targetId = String.valueOf(organization.getOrganizationId());
            String targetName = organization.getOrganizationName();
            String changeDetails = String.format("管理员{%s}新增机构{%s}", operator.getNickname(), organization.getOrganizationName());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, null, organization.toString(), null);
                    
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('sys:org:update')")
    @OperationLog
    @ApiOperation("修改组织机构")
    @PostMapping("/update")
    public ApiResult<?> update(HttpServletRequest request, @RequestBody Organization organization) {
        // 获取修改前的机构信息
        Organization beforeOrg = organizationService.getById(organization.getOrganizationId());
        if (beforeOrg == null) {
            return fail("机构不存在");
        }
        
        if (organization.getOrganizationName() != null) {
            if (organization.getParentId() == null) {
                organization.setParentId(0);
            }
            if (organizationService.count(new LambdaQueryWrapper<Organization>()
                    .eq(Organization::getOrganizationName, organization.getOrganizationName())
                    .eq(Organization::getParentId, organization.getParentId())
                    .ne(Organization::getOrganizationId, organization.getOrganizationId())) > 0) {
                return fail("机构名称已存在");
            }
        }
        if (organizationService.updateById(organization)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "修改";
            String operationModule = "组织机构管理";
            String targetId = String.valueOf(organization.getOrganizationId());
            String targetName = beforeOrg.getOrganizationName();
            
            // 生成变更详情
            StringBuilder detailsBuilder = new StringBuilder();
            detailsBuilder.append(String.format("管理员{%s}修改机构{%s}：", operator.getNickname(), targetName));
            
            if (organization.getOrganizationName() != null && !organization.getOrganizationName().equals(beforeOrg.getOrganizationName())) {
                detailsBuilder.append(permissionChangeLogService.formatOrgChangeDetails("organizationName", beforeOrg.getOrganizationName(), organization.getOrganizationName())).append("；");
            }
            
            if (organization.getOrganizationFullName() != null && !organization.getOrganizationFullName().equals(beforeOrg.getOrganizationFullName())) {
                detailsBuilder.append(permissionChangeLogService.formatOrgChangeDetails("organizationFullName", beforeOrg.getOrganizationFullName(), organization.getOrganizationFullName())).append("；");
            }
            
            if (organization.getParentId() != null && !organization.getParentId().equals(beforeOrg.getParentId())) {
                detailsBuilder.append(permissionChangeLogService.formatOrgChangeDetails("parentId", beforeOrg.getParentId(), organization.getParentId())).append("；");
            }
            
            if (organization.getSortNumber() != null && !organization.getSortNumber().equals(beforeOrg.getSortNumber())) {
                detailsBuilder.append(permissionChangeLogService.formatOrgChangeDetails("sortNumber", beforeOrg.getSortNumber(), organization.getSortNumber())).append("；");
            }
            
            String changeDetails = detailsBuilder.toString();
            if (changeDetails.endsWith("；")) {
                changeDetails = changeDetails.substring(0, changeDetails.length() - 1);
            }
            
            // 获取修改后的机构信息
            Organization afterOrg = organizationService.getById(organization.getOrganizationId());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeOrg.toString(), afterOrg.toString(), null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:org:remove')")
    @OperationLog
    @ApiOperation("删除组织机构")
    @PostMapping("/{id}")
    public ApiResult<?> remove(HttpServletRequest request, @PathVariable("id") Integer id) {
        // 获取删除前的机构信息
        Organization beforeOrg = organizationService.getById(id);
        if (beforeOrg == null) {
            return fail("机构不存在");
        }
        
        if (organizationService.removeById(id)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "删除";
            String operationModule = "组织机构管理";
            String targetId = String.valueOf(id);
            String targetName = beforeOrg.getOrganizationName();
            String changeDetails = String.format("管理员{%s}删除机构{%s}", operator.getNickname(), targetName);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeOrg.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('sys:org:save')")
    @OperationLog
    @ApiOperation("批量添加组织机构")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(HttpServletRequest request, @RequestBody List<Organization> organizationList) {
        if (organizationService.saveBatch(organizationList)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量新增";
            String operationModule = "组织机构管理";
            String targetNames = organizationList.stream()
                    .map(Organization::getOrganizationName)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量新增机构{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    null, targetNames, changeDetails, null, organizationList.toString(), null);
                    
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('sys:org:update')")
    @OperationLog
    @ApiOperation("批量修改组织机构")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(HttpServletRequest request, @RequestBody BatchParam<Organization> batchParam) {
        if (batchParam.update(organizationService, Organization::getOrganizationId)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量修改";
            String operationModule = "组织机构管理";
            String changeDetails = String.format("管理员{%s}批量修改机构", operator.getNickname());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    null, null, changeDetails, batchParam.getIds().toString(), null, null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:org:remove')")
    @OperationLog
    @ApiOperation("批量删除组织机构")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(HttpServletRequest request, @RequestBody List<Integer> ids) {
        // 获取删除前的机构信息
        List<Organization> beforeOrgs = organizationService.listByIds(ids);
        if (beforeOrgs.isEmpty()) {
            return fail("机构不存在");
        }
        
        if (organizationService.removeByIds(ids)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量删除";
            String operationModule = "组织机构管理";
            String targetNames = beforeOrgs.stream()
                    .map(Organization::getOrganizationName)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量删除机构{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    ids.toString(), targetNames, changeDetails, beforeOrgs.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
