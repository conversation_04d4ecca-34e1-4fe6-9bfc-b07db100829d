package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslIntegralDescriptionService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegralDescription;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslIntegralDescriptionParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 熟卡积分说明表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "熟卡积分说明表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-integral-description")
public class HnslIntegralDescriptionController extends BaseController {
    @Autowired
    private HnslIntegralDescriptionService hnslIntegralDescriptionService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:list')")
    @OperationLog
    @ApiOperation("分页查询熟卡积分说明表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslIntegralDescription>> page(@RequestBody HnslIntegralDescriptionParam param) {
        PageParam<HnslIntegralDescription, HnslIntegralDescriptionParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslIntegralDescriptionService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslIntegralDescriptionService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:list')")
    @OperationLog
    @ApiOperation("查询全部熟卡积分说明表")
    @PostMapping("/list")
    public ApiResult<List<HnslIntegralDescription>> list(@RequestBody HnslIntegralDescriptionParam param) {
        PageParam<HnslIntegralDescription, HnslIntegralDescriptionParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslIntegralDescriptionService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslIntegralDescriptionService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:list')")
    @OperationLog
    @ApiOperation("根据id查询熟卡积分说明表")
    @GetMapping("/{id}")
    public ApiResult<HnslIntegralDescription> get(@PathVariable("id") Integer id) {
        return success(hnslIntegralDescriptionService.getById(id));
        // 使用关联查询
        //return success(hnslIntegralDescriptionService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:save')")
    @OperationLog
    @ApiOperation("添加熟卡积分说明表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslIntegralDescription hnslIntegralDescription) {
        if (hnslIntegralDescriptionService.save(hnslIntegralDescription)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:update')")
    @OperationLog
    @ApiOperation("修改熟卡积分说明表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslIntegralDescription hnslIntegralDescription) {
        if (hnslIntegralDescriptionService.updateById(hnslIntegralDescription)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:remove')")
    @OperationLog
    @ApiOperation("删除熟卡积分说明表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslIntegralDescriptionService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:save')")
    @OperationLog
    @ApiOperation("批量添加熟卡积分说明表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslIntegralDescription> list) {
        if (hnslIntegralDescriptionService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:update')")
    @OperationLog
    @ApiOperation("批量修改熟卡积分说明表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslIntegralDescription> batchParam) {
        if (batchParam.update(hnslIntegralDescriptionService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegralDescription:remove')")
    @OperationLog
    @ApiOperation("批量删除熟卡积分说明表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslIntegralDescriptionService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
