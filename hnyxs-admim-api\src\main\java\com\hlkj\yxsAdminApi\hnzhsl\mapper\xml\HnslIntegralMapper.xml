<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslIntegralMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_integral a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userId != null">
                AND a.USER_ID LIKE CONCAT('%', #{param.userId}, '%')
            </if>
            <if test="param.integralOperation != null">
                AND a.INTEGRAL_OPERATION = #{param.integralOperation}
            </if>
            <if test="param.integralRemark != null">
                AND a.INTEGRAL_REMARK LIKE CONCAT('%', #{param.integralRemark}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.orderId != null">
                AND a.ORDER_ID LIKE CONCAT('%', #{param.orderId}, '%')
            </if>
            <if test="param.integralType != null">
                AND a.INTEGRAL_TYPE = #{param.integralType}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询每周订单数量 -->
    <select id="queryOrderWeekNum" resultType="java.lang.Long">
		select count(1) from HNSL_ORDER where HHID=#{userPhone}
				and YEARWEEK(date_format(CREATED_DATE,'%Y-%m-%d')) = YEARWEEK(now())
	</select>
    <!-- 查询每月订单数量 -->
    <select id="queryOrderMonthNum" resultType="java.lang.Long">
		select count(1) from HNSL_ORDER where HHID=#{userPhone}
				and date_format(CREATED_DATE,'%Y%m')=date_format(sysdate(),'%Y%m')
	</select>
    <!-- 查询每年订单数量 -->
    <select id="queryOrderYearNum" resultType="java.lang.Long">
		select count(1) from HNSL_ORDER where HHID=#{userPhone}
				and YEAR(CREATED_DATE)=YEAR(sysdate())
	</select>

    <!-- 查询积分条数 -->
    <select id="queryIntegralWeekNum" resultType="java.lang.Long">
        select sum(INTEGRAL_OPERATION) from HNSL_INTEGRAL where 1=1
                                                            AND USER_ID=#{userId}
                                                            and YEARWEEK(date_format(CREATED_DATE,'%Y-%m-%d')) = YEARWEEK(now())
    </select>
    <select id="queryIntegralMonthNum" resultType="java.lang.Long">
        select sum(INTEGRAL_OPERATION) from HNSL_INTEGRAL where 1=1
                                                            AND USER_ID=#{userId}
                                                            and date_format(CREATED_DATE,'%Y%m')=date_format(sysdate(),'%Y%m')
    </select>
    <select id="queryIntegralyearNum" resultType="java.lang.Long">
        select sum(INTEGRAL_OPERATION) from HNSL_INTEGRAL where 1=1
                                                            AND USER_ID=#{userId}
                                                            and YEAR(CREATED_DATE)=YEAR(sysdate())
    </select>

    <select id="queryIntegralDetailsList" parameterType="Map" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral">
        select * from HNSL_INTEGRAL where 1=1
                                      AND USER_ID=#{userPhone}
        order by created_date desc LIMIT #{num}
    </select>

    <select id="queryIntegral" parameterType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral"
            resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral">
        select * from HNSL_INTEGRAL where 1=1
        <if test="userId!=null and userId!=''">
            AND USER_ID=#{userId}
        </if>
        order by created_date desc LIMIT 10
    </select>


    <select id="queryList" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral">
        SELECT tt.*
        FROM (
        select * from HNSL_INTEGRAL
        <where>
            <if test="userPhone != null and userPhone.trim() != ''">
                USER_ID=#{userPhone}
            </if>
            <if test="beginTime != null and beginTime.trim() != ''">
                and CREATED_DATE between
                #{beginTime} and #{endTime}
            </if>
            <if test="integralType != null and integralType != ''">
                and INTEGRAL_TYPE=#{integralType}
            </if>
        </where>

        <choose>
            <when test="sidx != null and sidx.trim() != ''">
                order by ${sidx} ${order}
            </when>
            <otherwise>
                order by ID desc
            </otherwise>
        </choose>
        ) tt
        <choose>
            <when test="page != null and limit != null">
                LIMIT #{page},#{limit}
            </when>
            <otherwise>

            </otherwise>
        </choose>

    </select>

    <select id="queryTotal" resultType="int">
        select count(*) from HNSL_INTEGRAL
        <where>
            <if test="orderId!=null and orderId!=''">
                AND order_id=#{orderId}
            </if>
            <if test="userPhone!=null and userPhone!=''">
                AND USER_ID=#{userPhone}
            </if>
            <if test="beginTime != null and beginTime.trim() != ''">
                and CREATED_DATE between
                #{beginTime} and
                #{endTime}
            </if>
        </where>
    </select>


    <!-- 导出用户详细积分表 -->
    <select id="queryExportIntegralList" resultType="java.util.HashMap">
        select t.user_name,t.status_sf,t1.bl,t.user_sfz,t.user_phone,t.integral,t.status_superior,t2.user_name as name_superior,t3.s1,t3.s3,t3.s4,t3.s5,
        (t3.s3+t3.s4+t3.s5+t3.s1) as zj,t4.s1p,t4.s3p,t4.s4p,t4.s5p,t.CHANNEL_TYPE
        from hnsl_user t left join ( select t.user_id,count(*) bl from hnsl_building_user t group by t.user_id ) t1 on t.user_phone=t1.user_id
        left join hnsl_user t2 on t.status_superior=t2.user_phone
        left join (select t.user_id,SUM(case t.integral_type when 1 then t.integral_operation else 0 end) as s1,
        SUM(case t.integral_type when 3 then t.integral_operation else 0 end) as s3,
        SUM(case t.integral_type when 4 then t.integral_operation else 0 end) as s4,
        SUM(case t.integral_type when 5 then t.integral_operation else 0 end) as s5
        from hnsl_integral t where 1=1
        <if test="beginTime != null and beginTime.trim() != ''">
            and t.created_date between
            #{beginTime} and
            #{endTime}
        </if>
        <if test="beginTime == null or beginTime == '' ">
            and date_format(t.created_date,'%Y-%m')=date_format(sysdate(),'%Y-%m')
        </if>
        group by t.user_id) t3 on t.user_phone=t3.user_id
        left join (select t.user_id,SUM(case t.integral_type when 1 then t.integral_operation else 0 end) as s1p,
        SUM(case t.integral_type when 3 then t.integral_operation else 0 end) as s3p,
        SUM(case t.integral_type when 4 then t.integral_operation else 0 end) as s4p,
        SUM(case t.integral_type when 5 then t.integral_operation else 0 end) as s5p
        from hnsl_integral t group by t.user_id) t4 on t.user_phone=t4.user_id
        where t.status=1
        <if test="statusSf !=null and statusSf!=''">
            and t.STATUS_SF = #{statusSf}
        </if>
        <if test="status !=null and status !=''">
            and t.STATUS = #{status}
        </if>
        <if test="cityCode !=null and cityCode !=''">
            and t.CITY_CODE =#{cityCode}
        </if>
        <if test="numbers !=null and numbers!=''">
            and t.NUMBERS =#{numbers}
        </if>
        <if test="userSfz !=null and userSfz!=''">
            and t.USER_SFZ =#{userSfz}
        </if>
        <if test="userPhone !=null and userPhone!=''">
            and t.USER_PHONE =#{userPhone}
        </if>
        <!-- 根据登陆用户身份权限查询身份以下数据 -->
        <if test="loginUserSf==1">
            and (t.status_sf!='1' and t.status_sf!='5' and t.status_sf!='6')
        </if>
        <if test="loginUserSf==5">
            and (t.status_sf!='5')
        </if>
        <if test="loginUserSf==6">
            and (t.status_sf!='5' and t.status_sf!='6')
        </if>
    </select>
</mapper>
