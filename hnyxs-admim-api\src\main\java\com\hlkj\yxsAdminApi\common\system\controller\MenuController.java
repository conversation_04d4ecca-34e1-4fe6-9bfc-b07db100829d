package com.hlkj.yxsAdminApi.common.system.controller;

import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.web.*;
import com.hlkj.yxsAdminApi.common.system.entity.Menu;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.param.MenuParam;
import com.hlkj.yxsAdminApi.common.system.service.MenuService;
import com.hlkj.yxsAdminApi.common.system.service.PermissionChangeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:23
 */
@Api(tags = "菜单管理")
@RestController
@RequestMapping("/api/system/menu")
public class MenuController extends BaseController {
    @Autowired
    private MenuService menuService;
    
    @Autowired
    private PermissionChangeLogService permissionChangeLogService;

    @PreAuthorize("hasAuthority('sys:menu:list')")
    @OperationLog
    @ApiOperation("分页查询菜单")
    @PostMapping("/page")
    public ApiResult<PageResult<Menu>> page(MenuParam param) {
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort_number");
        return success(menuService.page(page, page.getWrapper()));
    }

    @PreAuthorize("hasAuthority('sys:menu:list')")
    @OperationLog
    @ApiOperation("查询全部菜单")
    @GetMapping()
    public ApiResult<List<Menu>> list(MenuParam param) {
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort_number");
        return success(menuService.list(page.getOrderWrapper()));
    }

    @PreAuthorize("hasAuthority('sys:menu:list')")
    @OperationLog
    @ApiOperation("根据id查询菜单")
    @GetMapping("/{id}")
    public ApiResult<Menu> get(@PathVariable("id") Integer id) {
        return success(menuService.getById(id));
    }

    @PreAuthorize("hasAuthority('sys:menu:save')")
    @OperationLog
    @ApiOperation("添加菜单")
    @PostMapping("/add")
    public ApiResult<?> add(HttpServletRequest request, @RequestBody Menu menu) {
        if (menu.getParentId() == null) {
            menu.setParentId(0);
        }
        if (menuService.save(menu)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "新增";
            String operationModule = "菜单管理";
            String targetId = String.valueOf(menu.getMenuId());
            String targetName = menu.getTitle();
            String changeDetails = String.format("管理员{%s}新增菜单{%s}", operator.getNickname(), menu.getTitle());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, null, menu.toString(), null);
                    
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('sys:menu:update')")
    @OperationLog
    @ApiOperation("修改菜单")
    @PostMapping("/update")
    public ApiResult<?> update(HttpServletRequest request, @RequestBody Menu menu) {
        // 获取修改前的菜单信息
        Menu beforeMenu = menuService.getById(menu.getMenuId());
        if (beforeMenu == null) {
            return fail("菜单不存在");
        }
        
        if (menuService.updateById(menu)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "修改";
            String operationModule = "菜单管理";
            String targetId = String.valueOf(menu.getMenuId());
            String targetName = beforeMenu.getTitle();
            
            // 生成变更详情
            StringBuilder detailsBuilder = new StringBuilder();
            detailsBuilder.append(String.format("管理员{%s}修改菜单{%s}：", operator.getNickname(), targetName));
            
            if (menu.getTitle() != null && !menu.getTitle().equals(beforeMenu.getTitle())) {
                detailsBuilder.append(permissionChangeLogService.formatMenuChangeDetails("menuName", beforeMenu.getTitle(), menu.getTitle())).append("；");
            }
            
            if (menu.getAuthority() != null && !menu.getAuthority().equals(beforeMenu.getAuthority())) {
                detailsBuilder.append(permissionChangeLogService.formatMenuChangeDetails("authority", beforeMenu.getAuthority(), menu.getAuthority())).append("；");
            }
            
            if (menu.getParentId() != null && !menu.getParentId().equals(beforeMenu.getParentId())) {
                detailsBuilder.append(permissionChangeLogService.formatMenuChangeDetails("parentId", beforeMenu.getParentId(), menu.getParentId())).append("；");
            }
            
            if (menu.getPath() != null && !menu.getPath().equals(beforeMenu.getPath())) {
                detailsBuilder.append(permissionChangeLogService.formatMenuChangeDetails("path", beforeMenu.getPath(), menu.getPath())).append("；");
            }
            
            String changeDetails = detailsBuilder.toString();
            if (changeDetails.endsWith("；")) {
                changeDetails = changeDetails.substring(0, changeDetails.length() - 1);
            }
            
            // 获取修改后的菜单信息
            Menu afterMenu = menuService.getById(menu.getMenuId());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeMenu.toString(), afterMenu.toString(), null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:menu:remove')")
    @OperationLog
    @ApiOperation("删除菜单")
    @PostMapping("/{id}")
    public ApiResult<?> remove(HttpServletRequest request, @PathVariable("id") Integer id) {
        // 获取删除前的菜单信息
        Menu beforeMenu = menuService.getById(id);
        if (beforeMenu == null) {
            return fail("菜单不存在");
        }
        
        if (menuService.removeById(id)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "删除";
            String operationModule = "菜单管理";
            String targetId = String.valueOf(id);
            String targetName = beforeMenu.getTitle();
            String changeDetails = String.format("管理员{%s}删除菜单{%s}", operator.getNickname(), targetName);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeMenu.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('sys:menu:save')")
    @OperationLog
    @ApiOperation("批量添加菜单")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(HttpServletRequest request, @RequestBody List<Menu> menus) {
        if (menuService.saveBatch(menus)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量新增";
            String operationModule = "菜单管理";
            String targetNames = menus.stream()
                    .map(Menu::getTitle)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量新增菜单{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    null, targetNames, changeDetails, null, menus.toString(), null);
                    
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('sys:menu:update')")
    @OperationLog
    @ApiOperation("批量修改菜单")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(HttpServletRequest request, @RequestBody BatchParam<Menu> batchParam) {
        if (batchParam.update(menuService, "menu_id")) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量修改";
            String operationModule = "菜单管理";
            String changeDetails = String.format("管理员{%s}批量修改菜单", operator.getNickname());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    null, null, changeDetails, batchParam.getIds().toString(), null, null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:menu:remove')")
    @OperationLog
    @ApiOperation("批量删除菜单")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(HttpServletRequest request, @RequestBody List<Integer> ids) {
        // 获取删除前的菜单信息
        List<Menu> beforeMenus = menuService.listByIds(ids);
        if (beforeMenus.isEmpty()) {
            return fail("菜单不存在");
        }
        
        if (menuService.removeByIds(ids)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量删除";
            String operationModule = "菜单管理";
            String targetNames = beforeMenus.stream()
                    .map(Menu::getTitle)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量删除菜单{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    ids.toString(), targetNames, changeDetails, beforeMenus.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
