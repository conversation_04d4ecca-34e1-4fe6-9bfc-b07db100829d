package com.hlkj.yxsAdminApi.common.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hlkj.yxsAdminApi.common.system.entity.Dictionary;
import com.hlkj.yxsAdminApi.common.system.mapper.DictionaryMapper;
import com.hlkj.yxsAdminApi.common.system.param.DictionaryParam;
import com.hlkj.yxsAdminApi.common.system.service.DictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典Service实现
 *
 * <AUTHOR>
 * @since 2020-03-14 11:29:03
 */
@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, Dictionary> implements DictionaryService {

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Override
    public List<Dictionary> queryDictionary(DictionaryParam param) {
        return dictionaryMapper.queryDictionary(param);
    }
}
