package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrders;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSendOrdersParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 派单总表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslSendOrdersMapper extends BaseMapper<HnslSendOrders> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSendOrders>
     */
    List<HnslSendOrders> selectPageRel(@Param("page") IPage<HnslSendOrders> page,
                             @Param("param") HnslSendOrdersParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSendOrders> selectListRel(@Param("param") HnslSendOrdersParam param);

    /**
     * 分页查询派单下的统计数据
     * @param page  分页对象
     * @param param 查询参数
     * @return
     */
    List<Map<String,Object>> selectPageMapRel(@Param("page") IPage<HnslSendOrders> page,
                                              @Param("param") HnslSendOrdersParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    JSONArray selectListMapRel(@Param("param") HnslSendOrdersParam param);

    /**
     * 查询派单下的所有统计数据
     * @param map
     * @return
     */
    public JSONArray queryAllList(Map<String,Object> map);
}
