package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskPlanService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskPlan;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskPlanParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务完成进度表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "任务完成进度表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-task-plan")
public class HnslTaskPlanController extends BaseController {
    @Autowired
    private HnslTaskPlanService hnslTaskPlanService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:list')")
    @OperationLog
    @ApiOperation("分页查询任务完成进度表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTaskPlan>> page(@RequestBody HnslTaskPlanParam param) {
        PageParam<HnslTaskPlan, HnslTaskPlanParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskPlanService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTaskPlanService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:list')")
    @OperationLog
    @ApiOperation("查询全部任务完成进度表")
    @PostMapping("/list")
    public ApiResult<List<HnslTaskPlan>> list(@RequestBody HnslTaskPlanParam param) {
        PageParam<HnslTaskPlan, HnslTaskPlanParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskPlanService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTaskPlanService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:list')")
    @OperationLog
    @ApiOperation("根据id查询任务完成进度表")
    @GetMapping("/{id}")
    public ApiResult<HnslTaskPlan> get(@PathVariable("id") Integer id) {
        return success(hnslTaskPlanService.getById(id));
        // 使用关联查询
        //return success(hnslTaskPlanService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:save')")
    @OperationLog
    @ApiOperation("添加任务完成进度表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTaskPlan hnslTaskPlan) {
        if (hnslTaskPlanService.save(hnslTaskPlan)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:update')")
    @OperationLog
    @ApiOperation("修改任务完成进度表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTaskPlan hnslTaskPlan) {
        if (hnslTaskPlanService.updateById(hnslTaskPlan)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:remove')")
    @OperationLog
    @ApiOperation("删除任务完成进度表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTaskPlanService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:save')")
    @OperationLog
    @ApiOperation("批量添加任务完成进度表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTaskPlan> list) {
        if (hnslTaskPlanService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:update')")
    @OperationLog
    @ApiOperation("批量修改任务完成进度表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTaskPlan> batchParam) {
        if (batchParam.update(hnslTaskPlanService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskPlan:remove')")
    @OperationLog
    @ApiOperation("批量删除任务完成进度表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTaskPlanService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
