package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-16 10:28:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslGoodsImageTemplate对象", description = "")
public class HnslGoodsImageTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "商品类型(1:号卡新装  2:校园融合  3:业务加装  4:熟卡)")
    @TableField("GOODS_TYPE")
    private Integer goodsType;

    @ApiModelProperty(value = "商品子类型(1:权益包 2:预存包 3:续费吧 4:流量包 5:宽带包 6:语音吧 7:增值包)")
    @TableField("GOODS_CHILD_TYPE")
    private Integer goodsChildType;

    @ApiModelProperty(value = "模板类型(1:橱窗图 2:详情图 3:分享图 4:海报图)")
    @TableField("TEMPLATE_TYPE")
    private Integer templateType;

    @ApiModelProperty(value = "模板名称")
    @TableField("TEMPLATE_NAME")
    private String templateName;

    @ApiModelProperty(value = "模板编码")
    @TableField("TEMPLATE_CODE")
    private String templateCode;

    @ApiModelProperty(value = "模板图片地址")
    @TableField("TEMPLATE_URL")
    private String templateUrl;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "商品状态（1：正常 2:禁用）")
    @TableField("STATUS")
    private Integer status;

}
