package com.hlkj.yxsAdminApi.common.system.controller;

import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.ConstantUtil;
import com.hlkj.yxsAdminApi.common.core.utils.FileServerUtil;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.FileRecord;
import com.hlkj.yxsAdminApi.common.system.param.FileRecordParam;
import com.hlkj.yxsAdminApi.common.system.service.FileRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 下载文件控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:24
 */


@Api(tags = "下载文件")
@RestController
@RequestMapping("/api/download")
public class DownloadController extends BaseController {


    @Autowired
    private ConfigProperties config;

    @Autowired
    private AmazonS3 amazonS3;

   // @PreAuthorize("hasAuthority('sys:file:download')")
    @OperationLog
    @ApiOperation("下载文件")
    @GetMapping("/exportDaoUsers")
    public void exportDaoUsers(HttpServletResponse response, HttpServletRequest request,String name) throws Exception {

        String parameter = request.getParameter("fileName");
        //下载
        String filepath = config.getHnzhslFilePath()  + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName = null;
        if(StringUtil.isNotNull(parameter)){
            fileName = parameter+".xls".toString(); // 文件的默认保存名
        }else{
            fileName = name + ".xls".toString(); // 文件的默认保存名
        }
        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inStream.close();
            file.delete();
        }
    }

    /**
     * 下载导入模板文件
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @OperationLog
    @ApiOperation("下载模板")
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        String parameter = request.getParameter("fileName");
        String bucketName = "hnsladmintemplate";
        String key = parameter;
        S3Object s3Object = amazonS3.getObject(bucketName, key);
        InputStream inputStream = s3Object.getObjectContent();
        // 设置输出的格式
        response.reset();
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition",
                "attachment;filename=" + URLEncoder.encode(parameter, "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inputStream.close();
            logger.info("扫楼导入模板下载结束，关闭流");
            // file.delete();
        } catch (IOException e) {
            logger.info("扫楼导入模板下载异常",e);
        } finally {
            inputStream.close();
        }
    }

    /**
     * 从S3获取二维码文件
     * @param request
     * @param response
     * @return
     */
    @OperationLog
    @ApiOperation("从S3获取二维码文件")
    @GetMapping("/downloadFile")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response) {
        String filePath = "qrcode/" + "熟卡二维码" + ".xls";
        S3Object amazonS3Object = amazonS3.getObject(ConstantUtil.BUCKET_NAME, filePath);
        S3ObjectInputStream inputStream = null;
        try {
            inputStream = amazonS3Object.getObjectContent();
            response.reset();
            //设置输出文件格式
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("熟卡二维码信息", "UTF-8") + ".xls");
            ServletOutputStream outputStream = response.getOutputStream();

            byte[] buff = new byte[1024];
            int length;
            while ((length = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, length);
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }

            // 下载后删除文件
            amazonS3.deleteObject(ConstantUtil.BUCKET_NAME, filePath);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 从S3获取二维码黑名单文件
     * @param request
     * @param response
     * @return
     */
    @OperationLog
    @ApiOperation("从S3获取二维码黑名单")
    @GetMapping("/downloadBlackFile")
    public void downloadBlackFile(HttpServletRequest request, HttpServletResponse response) {
        String filePath = "qrcode/" + "二维码黑名单" + ".xls";
        S3Object amazonS3Object = amazonS3.getObject(ConstantUtil.BUCKET_NAME, filePath);
        S3ObjectInputStream inputStream = null;
        try {
            inputStream = amazonS3Object.getObjectContent();
            response.reset();
            //设置输出文件格式
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("二维码黑名单信息", "UTF-8") + ".xls");
            ServletOutputStream outputStream = response.getOutputStream();

            byte[] buff = new byte[1024];
            int length;
            while ((length = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, length);
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }

            // 下载后删除文件
            amazonS3.deleteObject(ConstantUtil.BUCKET_NAME, filePath);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 手动上传模版到s3
     * @param request
     * @return
     */
    @OperationLog
    @ApiOperation("手动上传模版到s3")
    @GetMapping("/uploadTemplate")
    public ApiResult<?> uploadTemplate(HttpServletRequest request) {
        String bucketName = "hnsladmintemplate";
        String key = "黑名单导入模板";
        String filePath = "E:\\hlkj-java\\test-hnyxs-admim-api\\src\\main\\webapps\\uploads\\黑名单导入模板.xls";
        // 检查上传文件的目录
        File xlsFile = new File(filePath);
        try {
            if (!amazonS3.doesBucketExistV2(bucketName)) {
                amazonS3.createBucket(bucketName);
            }
            PutObjectResult result = amazonS3.putObject(bucketName, key, xlsFile);
        }catch (Exception e){
            e.printStackTrace();
        }
        System.out.println("File uploaded successfully. ETag: " );
        return null;
    }

    /**
     * 获取电子围栏登录白名单
     * @param request
     * @param response
     * @return
     */
    @OperationLog
    @ApiOperation("获取电子围栏登录白名单")
    @GetMapping("/downloadLoginWhiteFile")
    public void downloadLoginWhiteFile(HttpServletRequest request, HttpServletResponse response) {
        String filePath = "file/" + "电子围栏登录白名单" + ".xls";
        S3Object amazonS3Object = amazonS3.getObject(ConstantUtil.BUCKET_NAME, filePath);
        S3ObjectInputStream inputStream = null;
        try {
            inputStream = amazonS3Object.getObjectContent();
            response.reset();
            //设置输出文件格式
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("电子围栏登录白名单信息", "UTF-8") + ".xls");
            ServletOutputStream outputStream = response.getOutputStream();

            byte[] buff = new byte[1024];
            int length;
            while ((length = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, length);
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }

            // 下载后删除文件
            amazonS3.deleteObject(ConstantUtil.BUCKET_NAME, filePath);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
