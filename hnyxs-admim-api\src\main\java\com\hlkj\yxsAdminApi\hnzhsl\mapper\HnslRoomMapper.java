package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRoom;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslRoomParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 房间表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
public interface HnslRoomMapper extends BaseMapper<HnslRoom> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslRoom>
     */
    List<HnslRoom> selectPageRel(@Param("page") IPage<HnslRoom> page,
                             @Param("param") HnslRoomParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslRoom> selectListRel(@Param("param") HnslRoomParam param);

}
