package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫楼工具订单表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslOrder对象", description = "扫楼工具订单表")
public class HnslOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "订单号")
    @TableField("ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "商品编码")
    @TableField("GOODS_NUMBER")
    private String goodsNumber;

    @ApiModelProperty(value = "揽机工号")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "订单金额")
    @TableField("ORDER_PRICE")
    private Double orderPrice;

    @ApiModelProperty(value = "身份证正面照")
    @TableField("IDENTITY_CARD_IMAGE1")
    private String identityCardImage1;

    @ApiModelProperty(value = "身份证反面照")
    @TableField("IDENTITY_CARD_IMAGE2")
    private String identityCardImage2;

    @ApiModelProperty(value = "身份证免冠照")
    @TableField("IDENTITY_CARD_IMAGE3")
    private String identityCardImage3;

    @ApiModelProperty(value = "活体视频")
    @TableField("LIVINGBODY_VIDEO")
    private String livingbodyVideo;

    @ApiModelProperty(value = "客户姓名")
    @TableField("CUSTOMER_NAME")
    private String customerName;

    @ApiModelProperty(value = "客户身份证号")
    @TableField("CUSTOMER_CARD")
    private String customerCard;

    @ApiModelProperty(value = "客户身份证号有效期")
    @TableField("CUSTOMER_CARD_VALIDITY")
    private String customerCardValidity;

    @ApiModelProperty(value = "客户联系电话")
    @TableField("CUSTOMER_CONTACT_PHONE")
    private String customerContactPhone;

    @ApiModelProperty(value = "客户选择的号码")
    @TableField("CUSTOMER_PHONE")
    private String customerPhone;

    @ApiModelProperty(value = "客户身份证地址")
    @TableField("CUSTOMER_ADDRESS")
    private String customerAddress;

    @ApiModelProperty(value = "ICCID号")
    @TableField("ICCID")
    private String iccid;

    @ApiModelProperty(value = "本地网")
    @TableField("CITYCODE")
    private String citycode;

    @ApiModelProperty(value = "保底消费（主卡）")
    @TableField("PHONE_NBRPRICE")
    private String phoneNbrprice;

    @ApiModelProperty(value = "预存话费（主卡）")
    @TableField("PHONE_PREPRICE")
    private String phonePreprice;

    @ApiModelProperty(value = "预存方案（4：100元预存 5200元预存 6300元预存）")
    @TableField("PRE_TYPE")
    private String preType;

    @ApiModelProperty(value = "安装地址：市")
    @TableField("INSTALL_CITY")
    private String installCity;

    @ApiModelProperty(value = "安装地址：区")
    @TableField("INSTALL_AREA")
    private String installArea;

    @ApiModelProperty(value = "预约安装日期（YYYY-MM-DD）")
    @TableField("INSTALL_DAY")
    private String installDay;

    @ApiModelProperty(value = "是否配送（1：未配送 2：已配送）")
    @TableField("INSTALL_TIME_INTERVAL")
    private String installTimeInterval;

    @ApiModelProperty(value = "用户侧 送货方式（1：送货上门 2：联系合伙人）")
    @TableField("STB_TYPE")
    private String stbType;

    @ApiModelProperty(value = "支付方式（微信、支付宝）")
    @TableField("PAY_CHANNEL")
    private String payChannel;

    @ApiModelProperty(value = "活体视频上传类型（1：腾讯认证 2:手机录制）")
    @TableField("INVOICES_TYPE")
    private String invoicesType;

    @ApiModelProperty(value = "活体结果说明")
    @TableField("INVOICES_EMAIL")
    private String invoicesEmail;

    @ApiModelProperty(value = "业务底单1、推送 2、不推送")
    @TableField("BUSINESS_ORDER")
    private String businessOrder;

    @ApiModelProperty(value = "备注")
    @TableField("ORDER_REMARK")
    private String orderRemark;

    @ApiModelProperty(value = "订单类型（1:实名提交2:审核通过 3：激活 4:已受理 5：未受理）（10.待支付14.已支付11.预约成功12.制卡13.发货 15 激活成功 16 激活失败 17 激活异常）（9:订单超时）(21:待加装 22:待支付 23:已加载 24:已退款)")
    @TableField("ORDER_STATUS")
    private String orderStatus;

    @ApiModelProperty(value = "实名提交时间")
    @TableField("ORDER_SUBMIT_DATE")
    private Date orderSubmitDate;

    @ApiModelProperty(value = "审核通过时间")
    @TableField("ORDER_REVIEWED_DATE")
    private Date orderReviewedDate;

    @ApiModelProperty(value = "激活成功时间")
    @TableField("ORDER_ACTIVATE_DATE")
    private Date orderActivateDate;

    @ApiModelProperty(value = "CRM系统orderId")
    @TableField("CRM_ORDER_ID")
    private String crmOrderId;

    @ApiModelProperty(value = "BPS系统orderId")
    @TableField("BPS_ORDER_ID")
    private String bpsOrderId;

    @ApiModelProperty(value = "客户custId")
    @TableField("CUST_ID")
    private String custId;

    @ApiModelProperty(value = "同步bps正式单（1：成功 0：失败）")
    @TableField("BPS_FORMAL_SYNCHRO")
    private String bpsFormalSynchro;

    @ApiModelProperty(value = "同步bps实名单（1：成功 0：失败）")
    @TableField("BPS_REALNAME_SYNCHRO")
    private String bpsRealnameSynchro;

    @ApiModelProperty(value = "CRM系统订单确认(1成功 0：失败)")
    @TableField("CRM_SURE_SYNCHRO")
    private String crmSureSynchro;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "合伙人id")
    @TableField("HHID")
    private String hhid;

    @ApiModelProperty(value = "客户(学生)编码")
    @TableField("CLIENT_NUMBER")
    private String clientNumber;

    @ApiModelProperty(value = "1:号卡新装  2 一人一码（本地入口） 3.一人一码（ 飞young入口）4.销售品加装 6:未成年开卡 7:驿站用户自助下单 8 熟卡 9:二维码熟卡下单 10 预约单")
    @TableField("SAFL_TYPE")
    private Integer saflType;

    @ApiModelProperty(value = "邮寄地址")
    @TableField("MAILING_ADDRESS")
    private String mailingAddress;

    @ApiModelProperty(value = "邮寄详细地址")
    @TableField("MAILING_DETAILED_ADDRESS")
    private String mailingDetailedAddress;

    @ApiModelProperty(value = "客户性别 性别（0:女  1:男 2:未知")
    @TableField("CUSTOMER_SEX")
    private Integer customerSex;

    @ApiModelProperty(value = "关联学校编码")
    @TableField("HHID_SCHOOL")
    private String hhidSchool;

    @ApiModelProperty(value = "普通宽带CRM系统orderId")
    @TableField("BAND_ORDER_ID")
    private String bandOrderId;

    @ApiModelProperty(value = "是否有宽带(0:无 1:有)")
    @TableField("BROAD_BAND")
    private Integer broadBand;

    @ApiModelProperty(value = "订单状态(0:无 1:有)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "订单是否已发送报错(0:无 1:有)")
    @TableField("ERROR_STATUS")
    private Integer errorStatus;

    @ApiModelProperty(value = "CRM返回的ask_id值")
    @TableField("CRM_ASK_ID")
    private String crmAskId;

    @ApiModelProperty(value = "是否处理(1:已处理 0 未处理 3:已撤单)")
    @TableField("DISPOSE")
    private Integer dispose;

    @ApiModelProperty(value = "同步BPS处理 （1:已处理 0 未处理） 针对指定类型同步")
    @TableField("SYNCHRONIZATION_BPS")
    private Integer synchronizationBps;

    @ApiModelProperty(value = "加装类型(1:合伙人加装 2 :一人一码加装 3:营销加装) 号码类型(1:正常 2:携号转网) 熟卡类型(1:正常 2:营销熟卡指定积分)")
    @TableField("INSTALLATION_TYPE")
    private Integer installationType;

    @ApiModelProperty(value = "经办人身份证正面照")
    @TableField("IDENTITY_CARD_IMAGE4")
    private String identityCardImage4;

    @ApiModelProperty(value = "经办人身份证反面照")
    @TableField("IDENTITY_CARD_IMAGE5")
    private String identityCardImage5;

    @ApiModelProperty(value = "经办人身份证免冠照")
    @TableField("IDENTITY_CARD_IMAGE6")
    private String identityCardImage6;

    @ApiModelProperty(value = "订单所属年级如 大一，大二，大三，大四，高中，校外人员")
    @TableField("GRADE")
    private String grade;

    @ApiModelProperty(value = "子客户姓名")
    @TableField("CHILD_CUSTOMER_NAME")
    private String childCustomerName;

    @ApiModelProperty(value = "子客户身份证号")
    @TableField("CHILD_CUSTOMER_CARD")
    private String childCustomerCard;

    @ApiModelProperty(value = "子客户身份证地址")
    @TableField("CHILD_CUSTOMER_ADDRESS")
    private String childCustomerAddress;

    @ApiModelProperty(value = "子客户custId")
    @TableField("CHILD_CUST_ID")
    private String childCustId;

    @ApiModelProperty(value = "经办人身份证号有效期")
    @TableField("CHILD_CUSTOMER_CARD_VALIDITY")
    private String childCustomerCardValidity;

    @ApiModelProperty(value = "经办人活体视频")
    @TableField("LIVINGBODY_VIDEO1")
    private String livingbodyVideo1;

    @ApiModelProperty(value = "审核类型 0：无 1:活体视频 2：图片评分审核")
    @TableField("REVIEW_TYPES")
    private Integer reviewTypes;

    @ApiModelProperty(value = "支付流水")
    @TableField("PAY_ORDERNO")
    private String payOrderno;

    @ApiModelProperty(value = "激活通知结果（0：未接收 1：审核成功 2：审核失败 3：激活成功 4：激活失败）")
    @TableField("BPS_ACTIVATE_NOTIFICATION")
    private Integer bpsActivateNotification;

    @ApiModelProperty(value = "激活通知结果说明")
    @TableField("BPS_ACTIVATE_REMARK")
    private String bpsActivateRemark;

    @ApiModelProperty(value = "激活通知时间")
    @TableField("BPS_ACTIVATE_DATE")
    private Date bpsActivateDate;

    @ApiModelProperty(value = "同步bps激活图片单（1：成功 0：失败）")
    @TableField("BPS_FORMAL_ACTIVATE")
    private Integer bpsFormalActivate;

    @ApiModelProperty(value = "同步bps激活活体视频单（1：成功 0：失败）")
    @TableField("BPS_REALNAME_ACTIVATE")
    private Integer bpsRealnameActivate;

    @ApiModelProperty(value = "正式单 签名状态 0:未签名 1：已签名 ")
    @TableField("SIGNATURE_STATUS")
    private Integer signatureStatus;

    @ApiModelProperty(value = "正式单 拍照状态 0:未拍照 1：已拍照 ")
    @TableField("PHOTOGRAPH_STATUS")
    private Integer photographStatus;

    @ApiModelProperty(value = "CRM 要求支付金额")
    @TableField("CRM_ORDER_PRICE")
    private String crmOrderPrice;

    @ApiModelProperty(value = "订单支付用到的openid")
    @TableField("ORDER_OPENID")
    private String orderOpenid;

    @ApiModelProperty(value = "接触单标识 每条数据的唯一标识 回单使用")
    @TableField("TOUCH_ID")
    private String touchId;

    @ApiModelProperty(value = "活体视频截图1")
    @TableField("IDENTITY_VIDEO_IMAGE1")
    private String identityVideoImage1;

    @ApiModelProperty(value = "活体视频截图2")
    @TableField("IDENTITY_VIDEO_IMAGE2")
    private String identityVideoImage2;

    @ApiModelProperty(value = "学校渠道类型 1:高校 2：中小学 3：公众")
    @TableField("SCHOOL_CHANNEL_TYPE")
    private Integer schoolChannelType;

    @ApiModelProperty(value = "网厅系统orderId")
    @TableField("WT_ORDER_ID")
    private String wtOrderId;

    @ApiModelProperty(value = "工作证图片集合")
    @TableField("WORK_PERMIT_LIST")
    private String workPermitList;

    @ApiModelProperty(value = "支付商户号")
    @TableField("MERCHANT_CODE")
    private String merchantCode;

    @ApiModelProperty(value = "FTP文件获取激活时间")
    @TableField("FTP_ACTIVATE_DATE")
    private Date ftpActivateDate;

    @ApiModelProperty(value = "防控地址")
    @TableField("PREVENTION_CONTROL_ADDRESS")
    private String preventionControlAddress;

    @ApiModelProperty(value = "预约单号")
    @TableField("RESERVATION_ORDER_ID")
    private String reservationOrderId;

    @ApiModelProperty(value = "预约客户姓名")
    @TableField("RESERVATION_CUSTOMER_NAME")
    private String reservationCustomerName;

    @ApiModelProperty(value = "预约联系号码")
    @TableField("RESERVATION_CONTACT_PHONE")
    private String reservationContactPhone;

    @ApiModelProperty(value = "预约时间")
    @TableField("RESERVATION_DATE")
    private Date reservationDate;

    @ApiModelProperty(value = "预约地址")
    @TableField("RESERVATION_CONTACT_ADDRESS")
    private String reservationContactAddress;

    @ApiModelProperty(value = "预约客户身份证")
    @TableField("RESERVATION_CUSTOMER_CARD")
    private String reservationCustomerCard;

    @ApiModelProperty(value = "是否点击受理:1、已点击 2、未处理")
    @TableField("RESERVATION_STATUS")
    private Integer reservationStatus;

    @ApiModelProperty(value = "学校名称")
    @TableField(exist = false)
    private String schoolName;
    @ApiModelProperty(value = "合伙人姓名")
    @TableField(exist = false)
    private String userName;
    @ApiModelProperty(value = "合伙人身份")
    @TableField(exist = false)
    private String statusSf;
    @ApiModelProperty(value = "套餐名称")
    @TableField(exist = false)
    private String goodsName;
    @ApiModelProperty(value = "订单创建开始日期")
    @TableField(exist = false)
    private String beginTime;
    @ApiModelProperty(value = "订单创建结束日期")
    @TableField(exist = false)
    private String endTime;
    @ApiModelProperty(value = "销售员编码")
    @TableField(exist = false)
    private String salesCode;
    @ApiModelProperty(value = "所属楼栋")
    @TableField(exist = false)
    private String buildingName;
    @ApiModelProperty(value = "所属宿舍")
    @TableField(exist = false)
    private String roomName;
    @ApiModelProperty(value = "CRM返回的ask_id值")
    @TableField(exist = false)
    private String crmaskid;
    @ApiModelProperty(value = "入学时间")
    @TableField(exist = false)
    private String enroltime;
    @ApiModelProperty(value = "已添加的积分")
    @TableField(exist = false)
    private String integralOperation;
    @ApiModelProperty(value = "渠道类型")
    @TableField(exist = false)
    private String channelType;
    @ApiModelProperty(value = "	揽机工号")
    @TableField(exist = false)
    private String numbers;
    @ApiModelProperty(value = "带宽")
    @TableField(exist = false)
    private String bandwidth;
    @ApiModelProperty(value = "靓号等级")
    @TableField(exist = false)
    private String numberLevel;
    @ApiModelProperty(value = "本地网名称")
    @TableField(exist = false)
    private String cityName;
    @ApiModelProperty(value = "靓号审批金额")
    @TableField(exist = false)
    private String phoneApprovePrice;
}
