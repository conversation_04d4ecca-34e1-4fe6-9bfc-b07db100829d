package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.ConstantUtil;
import com.hlkj.yxsAdminApi.common.core.utils.DateUtils;
import com.hlkj.yxsAdminApi.common.core.utils.ObjectExcelRead;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMatureCardQrcode;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslMatureCardQrcodeParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslMatureCardQrcodeService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 熟卡二维码管理
 *
 * @Author: zwk
 * @Since: 2024/10/21
 * @return: null
 **/
@Api(tags = "熟卡二维码管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslmaturecardqrcode")
public class HnslMatureCardQrcodeController extends BaseController {

	@Autowired
	private HnslMatureCardQrcodeService hnslMatureCardQrcodeService;

	@Autowired
	private HnslSchoolService hnslSchoolService;

	@Autowired
	private ConfigProperties config;

	@Autowired
	private AmazonS3 amazonS3;

	/**
	 * 分页查询熟卡二维码表
	 *
	 * @param param
	 * @return
	 */
	@PreAuthorize("hasAuthority('hnzhsl:hnslmaturecardqrcode:list')")
	@OperationLog
	@ApiOperation("分页查询熟卡二维码表")
	@PostMapping("/page")
	public ApiResult<PageResult<HnslMatureCardQrcode>> page(@RequestBody HnslMatureCardQrcodeParam param) {
		PageParam<HnslMatureCardQrcode, HnslMatureCardQrcodeParam> page = new PageParam<>(param);
		User loginUser = getLoginUser();
		logger.info("分页查询熟卡二维码===》");
//        // 获取所有学校数据新增到熟卡二维码表中
		List<HnslSchool> schools = hnslSchoolService.list();
		List<String> schoolCodes = schools.stream().map(HnslSchool::getSchoolCode).collect(Collectors.toList());

		List<HnslMatureCardQrcode> existingQrcodes = hnslMatureCardQrcodeService.list(
				new LambdaQueryWrapper<HnslMatureCardQrcode>()
						.in(HnslMatureCardQrcode::getSchoolCode, schoolCodes)
		);

		Map<String, HnslMatureCardQrcode> qrcodeMap = existingQrcodes.stream()
				.collect(Collectors.toMap(HnslMatureCardQrcode::getSchoolCode, Function.identity()));

		List<HnslMatureCardQrcode> toSave = new ArrayList<>();
		for (HnslSchool school : schools) {
			if (school.getSchoolGradeType() != null && school.getStatus() != null) {
				if (school.getSchoolGradeType() != 3 && school.getStatus() == 1) {
					if (!qrcodeMap.containsKey(school.getSchoolCode())) {
						HnslMatureCardQrcode qrcode = new HnslMatureCardQrcode();
						qrcode.setSchoolName(school.getSchoolName());
						qrcode.setSchoolCode(school.getSchoolCode());
						qrcode.setCityCode(school.getSchoolCity());
						qrcode.setCreateTime(new Date());
						qrcode.setCreateUser(loginUser.getUsername());
						toSave.add(qrcode);
					}
				}
			}
		}
		hnslMatureCardQrcodeService.saveBatch(toSave);

		return success(hnslMatureCardQrcodeService.pageRel(param));
	}


	/**
	 * 编辑二维码预约开关
	 *
	 * @param hnslMatureCardQrcode
	 * @return
	 */
	@PreAuthorize("hasAuthority('hnzhsl:hnslmaturecardqrcode:update')")
	@OperationLog
	@ApiOperation("编辑二维码预约开关")
	@PostMapping("/update")
	public ApiResult<?> update(@RequestBody HnslMatureCardQrcode hnslMatureCardQrcode) {
		User loginUser = getLoginUser();
		hnslMatureCardQrcode.setUpdateUser(loginUser.getUsername());
		hnslMatureCardQrcode.setUpdateTime(new Date());
		if (hnslMatureCardQrcodeService.updateById(hnslMatureCardQrcode)) {
			return success("编辑成功");
		}
		return fail("编辑失败");
	}


	/**
	 * 导出熟卡二维码
	 *
	 * @param params
	 * @param request
	 * @return
	 */
	@PreAuthorize("hasAuthority('hnzhsl:hnslmaturecardqrcode:output')")
	@OperationLog
	@ApiOperation("导出熟卡二维码")
	@PostMapping("/outputMatureCardQrcodeTable")
	public ApiResult<?> outputMatureCardQrcodeTable(@RequestBody Map<String, Object> params, HttpServletRequest request,
													HttpServletResponse response) {
		logger.info("导出熟卡二维码开始===》");

		LambdaQueryWrapper<HnslMatureCardQrcode> queryWrapper = new LambdaQueryWrapper();
		String schoolName = (String) params.get("schoolName");
		if (schoolName != null && !schoolName.trim().isEmpty()) {
			queryWrapper.eq(HnslMatureCardQrcode::getSchoolName, schoolName.trim());
		}
		String schoolCode = (String) params.get("schoolCode");
		if (schoolCode != null && !schoolCode.trim().isEmpty()) {
			queryWrapper.eq(HnslMatureCardQrcode::getSchoolCode, schoolCode.trim());
		}
		String cityCode = (String) params.get("cityCode");
		if (cityCode != null && !cityCode.trim().isEmpty()) {
			queryWrapper.eq(HnslMatureCardQrcode::getCityCode, cityCode.trim());
		}
		List<HnslMatureCardQrcode> matureList = hnslMatureCardQrcodeService.list(queryWrapper);

		// 创建一个映射，将数字映射到文本描述
		Map<Integer, String> statusMap = new HashMap<>();
		statusMap.put(1, "开启");
		statusMap.put(2, "关闭");
		// 创建一个映射，将数字映射到文本描述
		Map<Integer, String> typeMap = new HashMap<>();
		typeMap.put(1, "允许");
		typeMap.put(2, "不允许");

		HSSFWorkbook wb = new HSSFWorkbook();
		HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
		//设置列宽
		for (int i = 0; i <= 12; i++) {
			sheet.setColumnWidth(i, 20 * 346);
		}

		// 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
		HSSFRow row = sheet.createRow((int) 0);
		// 4.创建单元格，设置值表头，设置表头居中
		HSSFCellStyle style = wb.createCellStyle();
		// 居中格式
		style.setAlignment(HorizontalAlignment.CENTER);
		// 设置表头
		HSSFCell cell = row.createCell(0);
		cell.setCellValue("ID");
		cell.setCellStyle(style);
		cell = row.createCell(1);
		cell.setCellValue("学校名称");
		cell.setCellStyle(style);
		cell = row.createCell(2);
		cell.setCellValue("学校ID");
		cell.setCellStyle(style);
		cell = row.createCell(3);
		cell.setCellValue("地市");
		cell.setCellStyle(style);
		cell = row.createCell(4);
		cell.setCellValue("线下预约二维码");
		cell.setCellStyle(style);
		cell = row.createCell(5);
		cell.setCellValue("省外使用线下预约二维码");
		cell.setCellStyle(style);
		cell = row.createCell(6);
		cell.setCellValue("跨地市使用");
		cell.setCellStyle(style);
		cell = row.createCell(7);
		cell.setCellValue("线上预约二维码");
		cell.setCellStyle(style);
		cell = row.createCell(8);
		cell.setCellValue("省外使用线上预约二维码");
		cell.setCellStyle(style);
		cell = row.createCell(9);
		cell.setCellValue("跨地市使用");
		cell.setCellStyle(style);
		cell = row.createCell(10);
		cell.setCellValue("线上激活二维码");
		cell.setCellStyle(style);
		cell = row.createCell(11);
		cell.setCellValue("省外使用线上激活二维码");
		cell.setCellStyle(style);
		cell = row.createCell(12);
		cell.setCellValue("跨地市使用");
		cell.setCellStyle(style);
		cell = row.createCell(13);
		cell.setCellValue("自助激活二维码");
		cell.setCellStyle(style);
		cell = row.createCell(14);
		cell.setCellValue("省外使用自助激活二维码");
		cell.setCellStyle(style);
		cell = row.createCell(15);
		cell.setCellValue("跨地市使用");
		cell.setCellStyle(style);
		// 循环将数据写入Excel
		if (null != matureList && matureList.size() != 0) {

			for (int i = 0; i < matureList.size(); i++) {
				row = sheet.createRow((int) i + 1);
				// 创建单元格，设置值
				row.createCell(0).setCellValue(String.valueOf(matureList.get(i).getId()));

				row.createCell(1).setCellValue(String.valueOf(matureList.get(i).getSchoolName()));
				row.createCell(2).setCellValue(String.valueOf(matureList.get(i).getSchoolCode()));
				row.createCell(3).setCellValue(String.valueOf(matureList.get(i).getCityCode()));

				Integer offlineQrcode = matureList.get(i).getOfflineQrcode();
				Integer offlineQrcodeOut = matureList.get(i).getOfflineQrcodeOut();
				Integer offlineCrossCityUse = matureList.get(i).getOfflineCrossCityUse();

				Integer onlineQrcode = matureList.get(i).getOnlineQrcode();
				Integer onlineQrcodeOut = matureList.get(i).getOnlineQrcodeOut();
				Integer onlineCrossCityUse = matureList.get(i).getOnlineCrossCityUse();

				Integer onlineActiveQrcode = matureList.get(i).getOnlineActiveQrcode();
				Integer onlineActiveQrcodeOut = matureList.get(i).getOnlineActiveQrcodeOut();
				Integer onlineActiveCrossCityUse = matureList.get(i).getOnlineActiveCrossCityUse();

				Integer selfActiveQrcode = matureList.get(i).getSelfActiveQrcode();
				Integer selfActiveQrcodeOut = matureList.get(i).getSelfActiveQrcodeOut();
				Integer selfActiveCrossCityUse = matureList.get(i).getSelfActiveCrossCityUse();

				Integer offlineFaceReservation = matureList.get(i).getOfflineFaceReservation();

				String offlineName = statusMap.get(offlineQrcode);
				String offlineName1 = typeMap.get(offlineQrcodeOut);
				String offlineName2 = typeMap.get(offlineCrossCityUse);

				String onlineName = statusMap.get(onlineQrcode);
				String onlineName1 = typeMap.get(onlineQrcodeOut);
				String onlineName2 = typeMap.get(onlineCrossCityUse);

				String onlineActiveName = statusMap.get(onlineActiveQrcodeOut);
				String onlineActiveName1 = typeMap.get(onlineActiveQrcode);
				String onlineActiveName2 = typeMap.get(onlineActiveCrossCityUse);

				String selfActiveName = statusMap.get(selfActiveQrcode);
				String selfActiveName1 = typeMap.get(selfActiveQrcodeOut);
				String selfActiveName2 = typeMap.get(selfActiveCrossCityUse);

				String offlineFaceReservationName = typeMap.get(offlineFaceReservation);

				row.createCell(4).setCellValue(offlineName);
				row.createCell(5).setCellValue(offlineName1);
				row.createCell(6).setCellValue(offlineName2);

				row.createCell(7).setCellValue(onlineName);
				row.createCell(8).setCellValue(onlineName1);
				row.createCell(9).setCellValue(onlineName2);

				row.createCell(10).setCellValue(onlineActiveName);
				row.createCell(11).setCellValue(onlineActiveName1);
				row.createCell(12).setCellValue(onlineActiveName2);

				row.createCell(13).setCellValue(selfActiveName);
				row.createCell(14).setCellValue(selfActiveName1);
				row.createCell(15).setCellValue(selfActiveName2);

				row.createCell(16).setCellValue(offlineFaceReservationName);
			}

			// 将文件上传到S3
			String filePath = "qrcode/" + "熟卡二维码" + ".xls";
			ByteArrayOutputStream baos;
			PutObjectRequest putRequest;

			try {
				baos = new ByteArrayOutputStream();
				wb.write(baos);
				byte[] data = baos.toByteArray();

				// 检查桶是否存在
				boolean isBucketExist = amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_NAME);
				if (!isBucketExist) {
					try {
						amazonS3.createBucket(ConstantUtil.BUCKET_NAME);
						logger.info("智慧扫楼二维码管理-创建桶成功" + ConstantUtil.BUCKET_NAME);
					} catch (AmazonS3Exception e) {
						logger.error("智慧扫楼二维码管理-创建桶失败 " + ConstantUtil.BUCKET_NAME + ": " + e.getMessage());
					}
				}
				if (!amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_NAME)) {
					amazonS3.createBucket(ConstantUtil.BUCKET_NAME);
				}

				putRequest = new PutObjectRequest(ConstantUtil.BUCKET_NAME, filePath,
						new ByteArrayInputStream(data), null);
				PutObjectResult result = amazonS3.putObject(putRequest);
				logger.info("上传结果" + result.toString());

				Map<String, Object> map = new HashedMap<>();
				map.put("code", "6");
				return success("导出成功", map);
			} catch (IOException e) {
				e.printStackTrace();
				logger.error("文件上传到S3时发生错误：" + e.getMessage());
				return fail("文件上传到S3时发生错误：" + e.getMessage());
			}
		}
		return fail("文件上传到S3时失败");
	}


	/**
	 * 导入熟卡二维码
	 *
	 * @param file
	 * @param request
	 * @return
	 */
	@PreAuthorize("hasAuthority('hnzhsl:hnslmaturecardqrcode:importMature')")
	@OperationLog
	@ApiOperation("导入熟卡二维码")
	@PostMapping("/decode/importMature")
	public ApiResult<?> importMature(MultipartFile file, HttpServletRequest request) {
		logger.info("导入熟卡二维码开始===》");
		JSONObject result = new JSONObject();
		String image = "xls,xlsx";
		User loginUser = getLoginUser();
		if (!file.isEmpty()) {
			String uploadPath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
			logger.info("uploadPath:  " + uploadPath);
			// 文件上传大小5M
			long fileSize = 5 * 1024 * 1024;
			if (file.getSize() > fileSize) {
				return fail("上传文件大小大于5M");
			}
			String OriginalFilename = file.getOriginalFilename();
			String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
			if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
				return fail("上传文件格式不正确", 2);
			}

			if (!ServletFileUpload.isMultipartContent(request)) {
				logger.error("文件上传格式不正确");
				return fail("resultCode", 2);
			}

			// 检查上传文件的目录
			File uploadDir = new File(uploadPath);
			if (!uploadDir.isDirectory()) {
				if (!uploadDir.mkdirs()) {
					logger.error("文件所在目录创建失败");
					return fail("resultCode", 3);
				}
			}
			SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
			String times = sf_.format(new Date());
			String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
			File saveFile = new File(uploadPath, newname);
			try {
				file.transferTo(saveFile);
				List<Map<String, String>> numberList = null;
				if (fileSuffix.endsWith("xls")) {
					numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 1, 0, 0);
				} else {
					numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 1, 0, 0);
				}
				logger.info("文件解析结果numberList：" + numberList);
				saveFile.delete();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
				String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));

				if (numberList != null && numberList.size() != 0) {
					//将数据转化成user对象
					List<HnslMatureCardQrcode> hnslMatureCardQrcodes = new ArrayList<>();
					for (int i = 0; i < numberList.size(); i++) {
						HnslMatureCardQrcode listPojo = new HnslMatureCardQrcode();
						Map<String, String> userMap = numberList.get(i);
						Set<String> nameset = userMap.keySet();
						Iterator<String> namelist = nameset.iterator();
						while (namelist.hasNext()) {
							String name = namelist.next();
							if (StringUtil.isEmpty(name)) {
								continue;
							}
							//学校名称-必填
							else if ("0".equalsIgnoreCase(name.trim())) {
								listPojo.setSchoolName(StringUtil.trimString(userMap.get(name)));
							}
							//学校编码-必填
							else if ("1".equalsIgnoreCase(name.trim())) {
								listPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
							}
							//线上预约二维码-非必填
							else if ("2".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOfflineQrcode(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							}
							//省外使用线上预约二维码-非必填
							else if ("3".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOfflineQrcodeOut(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("4".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOfflineCrossCityUse(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("5".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOnlineQrcode(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("6".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOnlineQrcodeOut(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("7".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOnlineCrossCityUse(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("8".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOnlineActiveQrcode(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("9".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOnlineActiveQrcodeOut(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("10".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOnlineActiveCrossCityUse(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("11".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setSelfActiveQrcode(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("12".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setSelfActiveQrcodeOut(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("13".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setSelfActiveCrossCityUse(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							} else if ("14".equalsIgnoreCase(name.trim())) {
								if ("".equals(StringUtil.trimString(userMap.get(name)))) {
									continue;
								}
								listPojo.setOfflineFaceReservation(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
							}
						}
						listPojo.setCreateTime(new Date());
						listPojo.setCreateUser(loginUser.getUsername());
						hnslMatureCardQrcodes.add(listPojo);
					}
					try {
						logger.info("解析结果装入用户集合" + hnslMatureCardQrcodes);
						Map<String, String> saveMatureCardArray = hnslMatureCardQrcodeService.saveMatureCardArray(hnslMatureCardQrcodes, request);
						if (null != saveMatureCardArray & saveMatureCardArray.get("resultCode").equals("1")) {
							HashMap<Object, Object> r = new HashMap<>();
							r.put("mes", "exportDaoUsers");
							r.put("resultCode", "6");
							r.put("fileName", saveMatureCardArray.get("fileName"));
							return success("导入成功", r);
						}
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						logger.error("批量插入用户信息失败:" + e.getMessage());
						return fail("resultCode", 5);
					}
				} else {
					logger.info("文件内容为空，或者解析失败");
					result.put("resultCode", "4");
					return fail("resultCode", 4);
				}
				HashMap<Object, Object> r = new HashMap<>();
				r.put("mes", "exportDaoUsers");
				r.put("resultCode", 0);
				return success("导入成功", r);
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("文件上传接口上传异常:" + e.getMessage());
				saveFile.delete();
				return fail("文件上传接口上传异常" + e.getMessage());
			} finally {
				saveFile.delete();
			}
		} else {
			logger.info("上传文件为空");
			return fail("上传文件为空");
		}
	}
}
