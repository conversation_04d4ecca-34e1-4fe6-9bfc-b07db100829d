package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Value;
import org.apache.commons.lang3.Validate;

import java.io.Serializable;
import java.util.Date;

/**
 * 导出审批表
 *
 * @Author: zwk
 * @Since: 2025/04/21
 * @return: null
 **/
@Data
@TableName("hnsl_export_approval")
public class HnslExportApprovalEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    //唯一标识
    private Integer id;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件名称路径
     */
    private String filePath;

    /** 文件事件类型
     * 1: 白名单清单数据
     * 2: 白名单报表
     * 3: 合伙人信息
     * 4: 订单数据
     * 5: 派单数据
     * 6: 学校数据
     * 7: 积分数据
     * 8: 学子薪资清单
     * 9: 薪资审批
     * 10: 薪资总表
     * 11: 薪资清单
     */
    private Integer activeEvent;

    /**
     * 导出数量
     */
    private Integer exportNumber;

    /**
     * 状态 (1待同意，2已拒绝，3已同意)
     */
    private Integer status;

    /**
     * 提交人姓名
     */
    private String submitUser;

    /**
     * 提交人ID
     */
    private Long submitUserId;

    /**
     * 提交时间
     */
    private Date submitDate;

    /**
     * 审批人姓名
     */
    private String approveUser;

    /**
     * 审批人手机号
     */
    private String approveUserPhone;

    /**
     * 审批时间
     */
    private Date approveDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /***
     * 数据原状态(1. 待生成 2 已生成)
     */
    private Integer dataSourceStatus;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 导出条件请求参数
     */
    private String filterConditions;

    /**
     * 自定义短信子码
     */
    private Integer smsCode;

    @TableField(exist = false)
    private Integer showDownload = 0;

    @TableField(exist = false)
    private Integer showApproval = 0;
}
