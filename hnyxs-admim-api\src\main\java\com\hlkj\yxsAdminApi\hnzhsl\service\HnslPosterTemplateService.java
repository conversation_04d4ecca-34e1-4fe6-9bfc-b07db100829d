package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPosterTemplate;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslPosterTemplateParam;

import java.util.List;

/**
 * 海报模板表Service
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslPosterTemplateService extends IService<HnslPosterTemplate> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslPosterTemplate>
     */
    PageResult<HnslPosterTemplate> pageRel(HnslPosterTemplateParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslPosterTemplate>
     */
    List<HnslPosterTemplate> listRel(HnslPosterTemplateParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识 主键

     * @return HnslPosterTemplate
     */
    HnslPosterTemplate getByIdRel(Integer id);

}
