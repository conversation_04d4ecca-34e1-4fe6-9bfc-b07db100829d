package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTask;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 任务信息表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslTaskMapper extends BaseMapper<HnslTask> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTask>
     */
    List<HnslTask> selectPageRel(@Param("page") IPage<HnslTask> page,
                             @Param("param") HnslTaskParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTask> selectListRel(@Param("param") HnslTaskParam param);


    /**
     * 导出指定任务模块
     * @param taskCode
     * @return
     */
    @Select("select DISTINCT u.user_name,u.status_sf,u.city_code,us.school_code,sc.school_name,p.user_phone as upload_status\n" +
            "\t\tfrom\n" +
            "\t\t(select r.user_phone,r.task_code from hnsl_task_receive r where r.task_code = #{taskCode}) b\n" +
            "\t\tleft join\n" +
            "\t\thnsl_user u\n" +
            "\t\ton u.user_phone = b.user_phone\n" +
            "\t\tleft join hnsl_task_plan p\n" +
            "\t\ton p.task_code = b.task_code\n" +
            "\t\tand p.user_phone = b.user_phone\n" +
            "\t\tLEFT JOIN hnsl_user_school us ON b.USER_PHONE = us.USER_PHONE\n" +
            "\t\tLEFT JOIN HNSL_SCHOOL sc ON us.SCHOOL_CODE = sc.SCHOOL_CODE")
    List<Map<String,String>> exportTask(@Param("taskCode") String taskCode);
}
