package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserSwitchService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSwitch;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserSwitchParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.hlkj.yxsAdminApi.common.core.utils.DesensitizationUtil;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.io.Serializable;

/**
 * 用户数据开关表控制器-反诈图片表
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "用户数据开关表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslUserSwitch")
public class HnslUserSwitchController extends BaseController {
    @Autowired
    private HnslUserSwitchService hnslUserSwitchService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:list')")
    @OperationLog
    @ApiOperation("分页查询用户数据开关表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserSwitch>> page(@RequestBody HnslUserSwitchParam param) {
        //PageParam<HnslUserSwitch, HnslUserSwitchParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        if(StringUtil.isNotNull(param.getUserPhone())){
            if(param.getUserPhone().length()!=11){
                param.setUserName(param.getUserPhone());
                param.setUserPhone("");
            }
        }
        
        PageResult<HnslUserSwitch> pageResult = hnslUserSwitchService.pageRel(param);
        
        // 对敏感数据进行脱敏处理
        if (pageResult != null && pageResult.getList() != null) {
            for (HnslUserSwitch userSwitch : pageResult.getList()) {
                // 敏感数据脱敏
                userSwitch.setUserName(DesensitizationUtil.desensitizedName(userSwitch.getUserName()));
                userSwitch.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(userSwitch.getUserPhone()));
            }
        }
        
        return success(pageResult);
        // 使用关联查询
        //return success(hnslUserSwitchService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:list')")
    @OperationLog
    @ApiOperation("查询全部用户数据开关表")
    @PostMapping("/list")
    public ApiResult<List<HnslUserSwitch>> list(@RequestBody HnslUserSwitchParam param) {
        PageParam<HnslUserSwitch, HnslUserSwitchParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<HnslUserSwitch> list = hnslUserSwitchService.list(page.getOrderWrapper());
        
        // 对敏感数据进行脱敏处理
        if (list != null && !list.isEmpty()) {
            for (HnslUserSwitch userSwitch : list) {
                // 敏感数据脱敏
                userSwitch.setUserName(DesensitizationUtil.desensitizedName(userSwitch.getUserName()));
                userSwitch.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(userSwitch.getUserPhone()));
            }
        }
        
        return success(list);
        // 使用关联查询
        //return success(hnslUserSwitchService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:list')")
    @OperationLog
    @ApiOperation("根据id查询用户数据开关表")
    @GetMapping("/{id}")
    public ApiResult<HnslUserSwitch> get(@PathVariable("id") Integer id) {
        HnslUserSwitch userSwitch = hnslUserSwitchService.getById(id);
        
        // 对敏感数据进行脱敏处理
        if (userSwitch != null) {
            userSwitch.setUserName(DesensitizationUtil.desensitizedName(userSwitch.getUserName()));
            userSwitch.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(userSwitch.getUserPhone()));
        }
        
        return success(userSwitch);
        // 使用关联查询
        //return success(hnslUserSwitchService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:save')")
    @OperationLog
    @ApiOperation("添加用户数据开关表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserSwitch hnslUserSwitch) {
        if (hnslUserSwitchService.save(hnslUserSwitch)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:update')")
    @OperationLog
    @ApiOperation("修改用户数据开关表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserSwitch hnslUserSwitch) {
        User loginUser = getLoginUser();
        hnslUserSwitch.setCreatedDate(new Date());
        hnslUserSwitch.setCreatedUser(loginUser.getUsername());
        
        // 获取原始数据，用于替换脱敏字段
        HnslUserSwitch originalSwitch = hnslUserSwitchService.getById(hnslUserSwitch.getId());
        
        // 处理敏感字段脱敏问题
        // 1. 检查用户姓名
        if (hnslUserSwitch.getUserName() != null && hnslUserSwitch.getUserName().contains("*")) {
            hnslUserSwitch.setUserName(originalSwitch.getUserName());
        }
        
        // 2. 检查手机号码
        if (hnslUserSwitch.getUserPhone() != null && hnslUserSwitch.getUserPhone().contains("*")) {
            hnslUserSwitch.setUserPhone(originalSwitch.getUserPhone());
        }
        
        if (hnslUserSwitchService.updateById(hnslUserSwitch)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:remove')")
    @OperationLog
    @ApiOperation("删除用户数据开关表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserSwitchService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:save')")
    @OperationLog
    @ApiOperation("批量添加用户数据开关表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserSwitch> list) {
        if (hnslUserSwitchService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:update')")
    @OperationLog
    @ApiOperation("批量修改用户数据开关表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUserSwitch> batchParam) {
        // 获取要更新的数据
        HnslUserSwitch userSwitchData = batchParam.getData();
        List<Serializable> ids = batchParam.getIds();
        
        if (userSwitchData != null && ids != null && !ids.isEmpty()) {
            // 检查敏感字段是否含有星号
            boolean hasUserNameStars = userSwitchData.getUserName() != null && userSwitchData.getUserName().contains("*");
            boolean hasUserPhoneStars = userSwitchData.getUserPhone() != null && userSwitchData.getUserPhone().contains("*");
            
            // 如果有脱敏字段，则不更新这些字段
            if (hasUserNameStars || hasUserPhoneStars) {
                // 从数据库获取第一条记录作为参考
                HnslUserSwitch originalSwitch = hnslUserSwitchService.getById(ids.get(0));
                if (originalSwitch != null) {
                    // 如果姓名包含星号，则不更新姓名字段
                    if (hasUserNameStars) {
                        userSwitchData.setUserName(null);
                    }
                    
                    // 如果手机号包含星号，则不更新手机号字段
                    if (hasUserPhoneStars) {
                        userSwitchData.setUserPhone(null);
                    }
                }
            }
        }
        
        if (batchParam.update(hnslUserSwitchService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSwitch:remove')")
    @OperationLog
    @ApiOperation("批量删除用户数据开关表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserSwitchService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
