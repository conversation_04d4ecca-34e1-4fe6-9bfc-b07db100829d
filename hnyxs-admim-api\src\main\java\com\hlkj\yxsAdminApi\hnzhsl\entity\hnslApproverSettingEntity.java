package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.hlkj.yxsAdminApi.common.core.validator.group.AddGroup;
import com.hlkj.yxsAdminApi.common.core.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 审核人设置表
 * @Author: zwk
 * @Since: 2025/04/25
 * @return: null
 **/
@Data
@TableName("hnsl_approver_setting")
public class hnslApproverSettingEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@TableId(value = "ID",type = IdType.AUTO)
	private long id;

	/** 姓名 */
	@NotBlank(message="姓名不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String userName;

	/** 手机号 */
	@NotBlank(message="手机号不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String userPhone;

	/** 身份证 */
	@NotBlank(message="身份证不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String idCard;

	/** 渠道类型 1:全渠道 2:校园渠道 3：电渠互联网卡渠道 4：其他 '*/
	@NotBlank(message="渠道不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String channelType;

	/** 地市 */
	@NotBlank(message="地市不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String cityCode;

	/** 审核角色身份 （1.校园经理 2.一级合伙人 3.二级合伙人 4.三级合伙人 5省级管理员 6 地市管理员'*/
	@NotBlank(message="审核角色身份不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String auditRole;

	/** 学校编码 */
	private String schoolCode;

	/** 创建时间 */
	private Date createTime;

	@TableField(exist = false)
	private String schoolName;
}
