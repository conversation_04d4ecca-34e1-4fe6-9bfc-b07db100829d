package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolUserService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSchoolUserParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 学生信息表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "学生信息表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-school-user")
public class HnslSchoolUserController extends BaseController {
    @Autowired
    private HnslSchoolUserService hnslSchoolUserService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:list')")
    @OperationLog
    @ApiOperation("分页查询学生信息表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSchoolUser>> page(@RequestBody HnslSchoolUserParam param) {
        PageParam<HnslSchoolUser, HnslSchoolUserParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSchoolUserService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslSchoolUserService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:list')")
    @OperationLog
    @ApiOperation("查询全部学生信息表")
    @PostMapping("/list")
    public ApiResult<List<HnslSchoolUser>> list(@RequestBody HnslSchoolUserParam param) {
        PageParam<HnslSchoolUser, HnslSchoolUserParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSchoolUserService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSchoolUserService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:list')")
    @OperationLog
    @ApiOperation("根据id查询学生信息表")
    @GetMapping("/{id}")
    public ApiResult<HnslSchoolUser> get(@PathVariable("id") Integer id) {
        return success(hnslSchoolUserService.getById(id));
        // 使用关联查询
        //return success(hnslSchoolUserService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:save')")
    @OperationLog
    @ApiOperation("添加学生信息表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSchoolUser hnslSchoolUser) {
        if (hnslSchoolUserService.save(hnslSchoolUser)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:update')")
    @OperationLog
    @ApiOperation("修改学生信息表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSchoolUser hnslSchoolUser) {
        if (hnslSchoolUserService.updateById(hnslSchoolUser)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:remove')")
    @OperationLog
    @ApiOperation("删除学生信息表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSchoolUserService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:save')")
    @OperationLog
    @ApiOperation("批量添加学生信息表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSchoolUser> list) {
        if (hnslSchoolUserService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:update')")
    @OperationLog
    @ApiOperation("批量修改学生信息表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSchoolUser> batchParam) {
        if (batchParam.update(hnslSchoolUserService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolUser:remove')")
    @OperationLog
    @ApiOperation("批量删除学生信息表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSchoolUserService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
