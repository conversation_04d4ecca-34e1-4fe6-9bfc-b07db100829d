package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUserOrders对象", description = "")
public class HnslUserOrders implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID")
    private Integer userId;

    @ApiModelProperty(value = "任务成功数量(已回单状态)")
    @TableField("ORDERS_TRUE_NUMBER")
    private Integer ordersTrueNumber;

    @ApiModelProperty(value = "任务失败数量(逾期状态)")
    @TableField("ORDERS_FALSE_NUMBER")
    private Integer ordersFalseNumber;

    @ApiModelProperty(value = "分派数量")
    @TableField("ORDERS_NUMBER")
    private Integer ordersNumber;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "回收订单数量")
    @TableField("ORDERS_RECYCLE_NUMBER")
    private Integer ordersRecycleNumber;

    @ApiModelProperty(value = "活动标识")
    @TableField("ACTIVITY_LOGO")
    private String activityLogo;

    @ApiModelProperty(value = "学校6级编码")
    @TableField("SCHOOL_SIX_ID")
    private String schoolSixId;

}
