package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrders;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserOrdersParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
public interface HnslUserOrdersMapper extends BaseMapper<HnslUserOrders> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserOrders>
     */
    List<HnslUserOrders> selectPageRel(@Param("page") IPage<HnslUserOrders> page,
                             @Param("param") HnslUserOrdersParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserOrders> selectListRel(@Param("param") HnslUserOrdersParam param);

}
