package com.hlkj.yxsAdminApi.hnzhsl.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.hlkj.yxsAdminApi.common.core.annotation.QueryField;
import com.hlkj.yxsAdminApi.common.core.annotation.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作积分记录表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslIntegral对象", description = "操作积分记录表")
public class HnslIntegral implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识 ")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "手机号码（第一联系人）")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "操作积分")
    @TableField("INTEGRAL_OPERATION")
    private Double integralOperation;

    @ApiModelProperty(value = "备注 （原因）")
    @TableField("INTEGRAL_REMARK")
    private String integralRemark;

    @ApiModelProperty(value = "操作时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "操作人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "所关联编码")
    @TableField("ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "积分类型1:业务类型（新装、一码、融合） 2：加装 3、扫楼 4、任务 5、奖励 6:宽带积分  7 熟卡 8:充值积分 9:添加合伙人")
    @TableField("INTEGRAL_TYPE")
    private Integer integralType;

}
