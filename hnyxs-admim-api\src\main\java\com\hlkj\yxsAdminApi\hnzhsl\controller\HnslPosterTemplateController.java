package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslPosterTemplateService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPosterTemplate;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslPosterTemplateParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 海报模板表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "海报模板表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslPosterTemplate")
public class HnslPosterTemplateController extends BaseController {
    @Autowired
    private HnslPosterTemplateService hnslPosterTemplateService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:list')")
    @OperationLog
    @ApiOperation("分页查询海报模板表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslPosterTemplate>> page(@RequestBody HnslPosterTemplateParam param) {
        PageParam<HnslPosterTemplate, HnslPosterTemplateParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslPosterTemplateService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslPosterTemplateService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:list')")
    @OperationLog
    @ApiOperation("查询全部海报模板表")
    @PostMapping("/list")
    public ApiResult<List<HnslPosterTemplate>> list(@RequestBody HnslPosterTemplateParam param) {
        PageParam<HnslPosterTemplate, HnslPosterTemplateParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslPosterTemplateService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslPosterTemplateService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:list')")
    @OperationLog
    @ApiOperation("根据id查询海报模板表")
    @GetMapping("/{id}")
    public ApiResult<HnslPosterTemplate> get(@PathVariable("id") Integer id) {
        return success(hnslPosterTemplateService.getById(id));
        // 使用关联查询
        //return success(hnslPosterTemplateService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:save')")
    @OperationLog
    @ApiOperation("添加海报模板表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslPosterTemplate hnslPosterTemplate) {
        User loginUser = getLoginUser();
        hnslPosterTemplate.setCreatedDate(new Date());
        hnslPosterTemplate.setCreatedUser(loginUser.getUsername());
        if (hnslPosterTemplateService.save(hnslPosterTemplate)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:update')")
    @OperationLog
    @ApiOperation("修改海报模板表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslPosterTemplate hnslPosterTemplate) {
        User loginUser = getLoginUser();
        hnslPosterTemplate.setUpdatedDate(new Date());
        hnslPosterTemplate.setUpdatedUser(loginUser.getUsername());
        if (hnslPosterTemplateService.updateById(hnslPosterTemplate)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:remove')")
    @OperationLog
    @ApiOperation("删除海报模板表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslPosterTemplateService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:save')")
    @OperationLog
    @ApiOperation("批量添加海报模板表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslPosterTemplate> list) {
        if (hnslPosterTemplateService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:update')")
    @OperationLog
    @ApiOperation("批量修改海报模板表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslPosterTemplate> batchParam) {
        if (batchParam.update(hnslPosterTemplateService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPosterTemplate:remove')")
    @OperationLog
    @ApiOperation("批量删除海报模板表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslPosterTemplateService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
