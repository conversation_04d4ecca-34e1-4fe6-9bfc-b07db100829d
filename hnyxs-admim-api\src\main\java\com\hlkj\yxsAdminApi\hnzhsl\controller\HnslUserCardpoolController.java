package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserCardpoolService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserCardpool;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserCardpoolParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 私人号池控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "私人号池管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslUserCardpool")
public class HnslUserCardpoolController extends BaseController {
    @Autowired
    private HnslUserCardpoolService hnslUserCardpoolService;

    @Autowired
    private HnslSchoolService hnslSchoolService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:list')")
    @OperationLog
    @ApiOperation("分页查询私人号池")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserCardpool>> page(@RequestBody HnslUserCardpoolParam param) {
        PageParam<HnslUserCardpool, HnslUserCardpoolParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
       // return success(hnslUserCardpoolService.page(page, page.getWrapper()));
        // 使用关联查询
        return success(hnslUserCardpoolService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:list')")
    @OperationLog
    @ApiOperation("查询全部私人号池")
    @PostMapping("/list")
    public ApiResult<List<HnslUserCardpool>> list(@RequestBody HnslUserCardpoolParam param) {
        PageParam<HnslUserCardpool, HnslUserCardpoolParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserCardpoolService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslUserCardpoolService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:list')")
    @OperationLog
    @ApiOperation("根据id查询私人号池")
    @GetMapping("/{id}")
    public ApiResult<JSONObject> get(@PathVariable("id") Integer id) {
        JSONObject jsonObject = new JSONObject();
        HnslUserCardpool hnslUserCardpoolServiceById = hnslUserCardpoolService.getById(id);
        jsonObject.put("hnslUserCardpool",hnslUserCardpoolServiceById);

        //查询号池下的所有学校
        LambdaQueryWrapper<HnslUserCardpool> cardpoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cardpoolLambdaQueryWrapper.eq(HnslUserCardpool::getCardPoolNumber,hnslUserCardpoolServiceById.getCardPoolNumber());
        List<HnslUserCardpool> hnslUserCardpools = hnslUserCardpoolService.list(cardpoolLambdaQueryWrapper);
        List<String> collect = hnslUserCardpools.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());

        //查询学校
        LambdaQueryWrapper<HnslSchool> schoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<HnslSchool> list = hnslSchoolService.list(schoolLambdaQueryWrapper);
        jsonObject.put("hnslSchoolList",list);

        return success(jsonObject);

        // 使用关联查询
        //return success(hnslUserCardpoolService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:save')")
    @OperationLog
    @ApiOperation("添加私人号池")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserCardpool hnslUserCardpool) {
        if (hnslUserCardpoolService.save(hnslUserCardpool)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:update')")
    @OperationLog
    @ApiOperation("修改私人号池")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserCardpool hnslUserCardpool) {
        User loginUser = getLoginUser();
        hnslUserCardpool.setUpdatedDate(new Date());
        hnslUserCardpool.setUpdatedUser(loginUser.getUsername());
        if (hnslUserCardpoolService.updateById(hnslUserCardpool)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:remove')")
    @OperationLog
    @ApiOperation("删除私人号池")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserCardpoolService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:save')")
    @OperationLog
    @ApiOperation("批量添加私人号池")
    @PostMapping("/decode/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserCardpool> list) {
        User loginUser = getLoginUser();
        list.forEach(w ->{
            w.setCreatedDate(new Date());
            w.setCreatedUser(loginUser.getUsername());
        });
        if (hnslUserCardpoolService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:update')")
    @OperationLog
    @ApiOperation("批量修改私人号池")
    @PostMapping("/decode/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody List<HnslUserCardpool> batchParam) {
        if (hnslUserCardpoolService.updateBatchById(batchParam)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpool:remove')")
    @OperationLog
    @ApiOperation("批量删除私人号池")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserCardpoolService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
