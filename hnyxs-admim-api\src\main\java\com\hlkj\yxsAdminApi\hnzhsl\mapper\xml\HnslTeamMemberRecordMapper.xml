<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTeamMemberRecordMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_team_member_record a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.teamDate != null">
                AND a.TEAM_DATE LIKE CONCAT('%', #{param.teamDate}, '%')
            </if>
            <if test="param.userCode != null">
                AND a.USER_CODE LIKE CONCAT('%', #{param.userCode}, '%')
            </if>
            <if test="param.teamCode != null">
                AND a.TEAM_CODE LIKE CONCAT('%', #{param.teamCode}, '%')
            </if>
            <if test="param.teamIdentity != null">
                AND a.TEAM_IDENTITY = #{param.teamIdentity}
            </if>
            <if test="param.teamReferrer != null">
                AND a.TEAM_REFERRER LIKE CONCAT('%', #{param.teamReferrer}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.teamIdentityLevel != null">
                AND a.TEAM_IDENTITY_LEVEL = #{param.teamIdentityLevel}
            </if>
            <if test="param.teamName != null">
                AND a.TEAM_NAME LIKE CONCAT('%', #{param.teamName}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamMemberRecord">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamMemberRecord">
        <include refid="selectSql"></include>
    </select>

</mapper>
