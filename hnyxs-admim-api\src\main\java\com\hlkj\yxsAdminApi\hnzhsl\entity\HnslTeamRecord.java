package com.hlkj.yxsAdminApi.hnzhsl.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 团队月记录表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTeamRecord对象", description = "团队月记录表")
public class HnslTeamRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "团队编码")
    @TableField("TEAM_CODE")
    private String teamCode;

    @ApiModelProperty(value = "团队记录月（2020-1）")
    @TableField("TEAM_DATE")
    private String teamDate;

    @ApiModelProperty(value = "单卡类型数量")
    @TableField("ORDER_TYPE1")
    private BigDecimal orderType1;

    @ApiModelProperty(value = "融合类型数量")
    @TableField("ORDER_TYPE2")
    private BigDecimal orderType2;

    @ApiModelProperty(value = "维系类型数量")
    @TableField("ORDER_TYPE3")
    private BigDecimal orderType3;

    @ApiModelProperty(value = "总订单数量")
    @TableField("ORDER_SUM")
    private BigDecimal orderSum;

    @ApiModelProperty(value = "本月团队积分")
    @TableField("TEAM_INTEGRAL")
    private BigDecimal teamIntegral;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private BigDecimal status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

}
