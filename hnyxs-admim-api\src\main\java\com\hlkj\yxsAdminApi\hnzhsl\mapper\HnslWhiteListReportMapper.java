package com.hlkj.yxsAdminApi.hnzhsl.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteReport;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteReportParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface HnslWhiteListReportMapper extends BaseMapper<HnslWhiteReport> {

    List<Map<String, Object>> queryListTable(Map<String,Object> map);

    List<HnslWhiteReport> selectPageRel(@Param("page") IPage<HnslWhiteReport> page,
                                        @Param("param") HnslWhiteReportParam param);
}
