package com.hlkj.yxsAdminApi.common.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hlkj.yxsAdminApi.common.core.utils.IPUtils;
import com.hlkj.yxsAdminApi.common.system.entity.PermissionChangeLog;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.mapper.PermissionChangeLogMapper;
import com.hlkj.yxsAdminApi.common.system.service.PermissionChangeLogService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

/**
 * 权限变更日志Service实现
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class PermissionChangeLogServiceImpl extends ServiceImpl<PermissionChangeLogMapper, PermissionChangeLog>
        implements PermissionChangeLogService {

    @Override
    public boolean recordLog(HttpServletRequest request, User operatorUser, String operationType, 
                          String operationModule, String targetId, String targetName, 
                          String changeDetails, String beforeChange, String afterChange, 
                          String approvalOrderNo) {
        PermissionChangeLog log = new PermissionChangeLog();
        log.setOperatorId(operatorUser.getUserId().toString());
        log.setOperatorName(operatorUser.getNickname());
        log.setOperationIp(IPUtils.getIpAddr(request));
        log.setOperationTime(new Date());
        log.setOperationType(operationType);
        log.setOperationModule(operationModule);
        log.setTargetId(targetId);
        log.setTargetName(targetName);
        log.setChangeDetails(changeDetails);
        //log.setBeforeChange(beforeChange);
        //log.setAfterChange(afterChange);
        log.setApprovalOrderNo(approvalOrderNo);
        log.setStatus(1);
        return save(log);
    }

    @Override
    public String formatUserChangeDetails(String field, Object before, Object after) {
        if (before == null && after == null) {
            return "";
        }
        
        String beforeStr = before == null ? "无" : before.toString();
        String afterStr = after == null ? "无" : after.toString();
        
        switch (field) {
            case "username":
                return String.format("修改用户账号：%s → %s", beforeStr, afterStr);
            case "nickname":
                return String.format("修改用户昵称：%s → %s", beforeStr, afterStr);
            case "phone":
                return String.format("修改手机号码：%s → %s", beforeStr, afterStr);
            case "email":
                return String.format("修改邮箱地址：%s → %s", beforeStr, afterStr);
            case "status":
                String beforeStatus = "0".equals(beforeStr) ? "正常" : "禁用";
                String afterStatus = "0".equals(afterStr) ? "正常" : "禁用";
                return String.format("修改用户状态：%s → %s", beforeStatus, afterStatus);
            case "roles":
                return String.format("修改用户角色：%s → %s", beforeStr, afterStr);
            case "organizationId":
                return String.format("修改用户所属机构：%s → %s", beforeStr, afterStr);
            case "password":
                return "重置用户密码";
            default:
                return String.format("修改用户%s：%s → %s", field, beforeStr, afterStr);
        }
    }
    
    @Override
    public String formatRoleChangeDetails(String field, Object before, Object after) {
        if (before == null && after == null) {
            return "";
        }
        
        String beforeStr = before == null ? "无" : before.toString();
        String afterStr = after == null ? "无" : after.toString();
        
        switch (field) {
            case "roleName":
                return String.format("修改角色名称：%s → %s", beforeStr, afterStr);
            case "roleCode":
                return String.format("修改角色标识：%s → %s", beforeStr, afterStr);
            case "comments":
                return String.format("修改角色备注：%s → %s", beforeStr, afterStr);
            case "menus":
                return String.format("修改角色菜单权限：%s → %s", beforeStr, afterStr);
            default:
                return String.format("修改角色%s：%s → %s", field, beforeStr, afterStr);
        }
    }
    
    @Override
    public String formatMenuChangeDetails(String field, Object before, Object after) {
        if (before == null && after == null) {
            return "";
        }
        
        String beforeStr = before == null ? "无" : before.toString();
        String afterStr = after == null ? "无" : after.toString();
        
        switch (field) {
            case "menuName":
                return String.format("修改菜单名称：%s → %s", beforeStr, afterStr);
            case "authority":
                return String.format("修改菜单权限标识：%s → %s", beforeStr, afterStr);
            case "parentId":
                return String.format("修改上级菜单：%s → %s", beforeStr, afterStr);
            case "path":
                return String.format("修改菜单路径：%s → %s", beforeStr, afterStr);
            case "sortNumber":
                return String.format("修改菜单排序：%s → %s", beforeStr, afterStr);
            case "menuType":
                return String.format("修改菜单类型：%s → %s", beforeStr, afterStr);
            default:
                return String.format("修改菜单%s：%s → %s", field, beforeStr, afterStr);
        }
    }
    
    @Override
    public String formatOrgChangeDetails(String field, Object before, Object after) {
        if (before == null && after == null) {
            return "";
        }
        
        String beforeStr = before == null ? "无" : before.toString();
        String afterStr = after == null ? "无" : after.toString();
        
        switch (field) {
            case "organizationName":
                return String.format("修改机构名称：%s → %s", beforeStr, afterStr);
            case "organizationFullName":
                return String.format("修改机构全称：%s → %s", beforeStr, afterStr);
            case "parentId":
                return String.format("修改上级机构：%s → %s", beforeStr, afterStr);
            case "sortNumber":
                return String.format("修改机构排序：%s → %s", beforeStr, afterStr);
            default:
                return String.format("修改机构%s：%s → %s", field, beforeStr, afterStr);
        }
    }
} 