package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteReport;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteReportParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteReportService;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 白名单报表
 *
 * @date 2024-10-09
 */
@RestController
@RequestMapping("/api/hnzhsl/hnslwhitereport")
public class HnslWhiteReportController extends BaseController {

    @Autowired
    private HnslWhiteReportService hnslWhiteReportService;

    @Autowired
    private RedisUtil redisUtils;

	@Autowired
	private ConfigProperties config;

    /**
     * 列表
     */
    @PostMapping("/list")
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhitereport:list')")
    public ApiResult<PageResult<HnslWhiteReport>> list(@RequestBody HnslWhiteReportParam param) {
        //查询列表数据
        User user = getLoginUser();
        String hnslType = redisUtils.getString("hnslChannel" + user.getPhone());
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        param.setHnslType(hnslType);
        param.setQueryCityCode(cityCode);
        param.setQueryType(statusSf);
        param.setSchoolCode(param.getSchoolName());
        return success(hnslWhiteReportService.pageRel(param));
    }


    /**
     * 导出报表
     *
     * @return
     */
    @RequestMapping("/outputWhitelTable")
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhitereport:output')")
    public ApiResult<?> outputWhitelTable(@RequestBody Map<String, Object> params, HttpServletRequest request,
                                          HttpServletResponse response) {
        User user = getLoginUser();
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        params.put("queryCityCode", cityCode);
        params.put("queryType", statusSf);

        List<Map<String, Object>> hnslOrderList = hnslWhiteReportService.queryListTable(params);

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        XSSFRow row = sheet.createRow((int) 0);
        XSSFCellStyle style = wb.createCellStyle();
        XSSFCell cell = row.createCell(0);
        // 设置字体
        XSSFFont font = wb.createFont();
        //设置列宽
        for (int i = 0; i <= 12; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 居中格式
//        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 设置表头
        cell = row.createCell(0);
        cell.setCellValue("账期");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("学校");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("客户群类型");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("熟卡号码数量");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("有效期内未激活熟卡");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("有效期内已激活熟卡");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("7天内到期的熟卡数量");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != hnslOrderList && hnslOrderList.size() != 0) {

            for (int i = 0; i < hnslOrderList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(hnslOrderList.get(i).get("CREATED_DATE")));
                row.createCell(1).setCellValue(InterfaceUtil.city_code.get(String.valueOf(hnslOrderList.get(i).get("CITY_CODE"))));
                row.createCell(2).setCellValue(String.valueOf(hnslOrderList.get(i).get("SCHOOL_NAME")));

                if ("1".equals(String.valueOf(hnslOrderList.get(i).get("SCHOOL_CHANNEL")))) {
                    row.createCell(3).setCellValue("高校");
                } else if ("2".equals(String.valueOf(hnslOrderList.get(i).get("SCHOOL_CHANNEL")))) {
                    row.createCell(3).setCellValue("中小学");
                } else {
                    row.createCell(3).setCellValue("未知");
                }

                row.createCell(4).setCellValue(String.valueOf(hnslOrderList.get(i).get("totalPhones")));
                row.createCell(5).setCellValue(String.valueOf(hnslOrderList.get(i).get("unactivatedCount")));
                row.createCell(6).setCellValue(String.valueOf(hnslOrderList.get(i).get("activatedCount")));
                row.createCell(7).setCellValue(String.valueOf(hnslOrderList.get(i).get("expiringCount")));
            }

//            try {
//                WaterMarkUtil.insertWaterMarkTextToXlsxEntrance(wb, user.getUsername() + " " + user.getUsername());
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
            String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("file文件" + filepath);
            String fileName = "白名单报表";
            File file = new File(filepath + fileName + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) { //判断是否是文件
                    Map<String, Object> map = new HashedMap();
                    map.put("code", "6");
                    map.put("fileName", fileName);
                    map.put("msg", "exportDaoUsers");
                    return success(map);
                }
            }
        }
        return success();
    }


    /**
     * 下载白名单导出文件
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/exportDaoUsers")
    public void exportDaoUsers(HttpServletResponse response, HttpServletRequest request, String name) throws Exception {

        String parameter = request.getParameter("fileName");
        //下载
        String filepath = ConstantUtil.SAVE_FILE_PATH + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName = null;
        if (StringUtil.isNotNull(parameter)) {
            fileName = parameter + ".xls".toString(); // 文件的默认保存名
        } else {
            fileName = name + ".xls".toString(); // 文件的默认保存名
        }
        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inStream.close();
            file.delete();
        }
    }
}
