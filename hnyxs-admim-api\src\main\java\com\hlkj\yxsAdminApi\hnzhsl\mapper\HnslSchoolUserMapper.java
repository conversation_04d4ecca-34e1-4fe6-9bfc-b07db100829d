package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSchoolUserParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生信息表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslSchoolUserMapper extends BaseMapper<HnslSchoolUser> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSchoolUser>
     */
    List<HnslSchoolUser> selectPageRel(@Param("page") IPage<HnslSchoolUser> page,
                             @Param("param") HnslSchoolUserParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSchoolUser> selectListRel(@Param("param") HnslSchoolUserParam param);

}
