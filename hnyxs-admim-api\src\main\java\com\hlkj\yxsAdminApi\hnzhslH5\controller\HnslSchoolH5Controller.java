package com.hlkj.yxsAdminApi.hnzhslH5.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserSchool;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslBuildingH5Service;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslSchoolH5Service;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School;
import com.hlkj.yxsAdminApi.hnzhslH5.param.HnslSchoolH5Param;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslUserH5Service;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslUserSchoolH5Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 渠道表控制器
 */
@Api(tags = "渠道表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslSchoolH5")
public class HnslSchoolH5Controller extends BaseController {
    @Resource
    private HnslSchoolH5Service hnslSchoolService;

    @Resource
    private HnslBuildingH5Service hnslBuildingService;
    @Resource
    private HnslUserH5Service hnslUserService;
    @Autowired
    private HnslUserSchoolH5Service hnslUserSchoolService;
    @Autowired
    private RedisUtil redisUtils;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolH5:list')")
    @OperationLog
    @ApiOperation("分页查询渠道表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslH5School>> page(@RequestBody HnslSchoolH5Param param) {
        return success(hnslSchoolService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolH5:list')")
    @OperationLog
    @ApiOperation("查询全部学校表")
    @PostMapping("/list")
    public ApiResult<List<HnslH5School>> list(@RequestBody HnslSchoolH5Param param) {
        User loginUser = getLoginUser();
        String statusSf = redisUtils.getString("thisUserSf" + loginUser.getPhone());
        String cityCode = redisUtils.getString("cityCodeH5" + loginUser.getPhone());
        PageParam<HnslH5School, HnslSchoolH5Param> page = new PageParam<>(param);
        List<HnslH5School> list = null;
        // 省级管理员查询所有学校
        if (("5").equals(statusSf)) {
            list = hnslSchoolService.list();

            // 地市管理员只查询当前地市
        } else if (("4").equals(statusSf)) {
            LambdaQueryWrapper<HnslH5School> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HnslH5School::getSchoolCity, cityCode);
            list = hnslSchoolService.list(queryWrapper);
        }
        // 否则只查询当前所关联的学校
        else {
            HnslH5UserSchool hnslH5UserSchool = new HnslH5UserSchool();
            hnslH5UserSchool.setUserPhone(loginUser.getPhone());
            List<HnslH5UserSchool> hnslH5UserSchools = hnslUserSchoolService.queryObjectBy(hnslH5UserSchool);
            List<String> collect = hnslH5UserSchools.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());

            LambdaQueryWrapper<HnslH5School> queryWrapper = new LambdaQueryWrapper<>();
            if (param.getSchoolName() != null && !param.getSchoolName().isEmpty()) {
                queryWrapper.like(HnslH5School::getSchoolName, param.getSchoolName());
            }
            queryWrapper.in(HnslH5School::getSchoolCode, collect);
            list = hnslSchoolService.list(queryWrapper);
        }
        return success(list);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolH5:list')")
    @OperationLog
    @ApiOperation("根据id查询学校表")
    @GetMapping("/info/{id}")
    public ApiResult<JSONObject> get(@PathVariable("id") Integer id) {
        JSONObject jsonObject = new JSONObject();
        HnslH5School schoolServiceById = hnslSchoolService.getById(id);
        jsonObject.put("hnslSchool", schoolServiceById);
        //获取学校对应的校园经理
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("schoolCode", schoolServiceById.getSchoolCode());
        List<HnslH5User> managerList = hnslUserService.queryManager(hashMap);
        jsonObject.put("managerList", managerList);
        return success(jsonObject);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolH5:save')")
    @OperationLog
    @ApiOperation("添加学校表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslH5School hnslSchool) {
        User loginUser = getLoginUser();
        LambdaQueryWrapper<HnslH5School> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslH5School::getSchoolCode, hnslSchool.getSchoolCode());
        int count = hnslSchoolService.count(queryWrapper);
        if (count > 0) {
            return fail("渠道编码已存在");
        }
        hnslSchool.setStatus(1);
        hnslSchool.setCreatedUser(loginUser.getUsername());
        hnslSchool.setUserId(loginUser.getUserId());
        hnslSchool.setCreatedDate(new Date());
        if (hnslSchoolService.save(hnslSchool)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolH5:update')")
    @OperationLog
    @ApiOperation("修改学校表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslH5School hnslSchool) {
        User loginUser = getLoginUser();
        hnslSchool.setUpdatedUser(loginUser.getUsername());
        hnslSchool.setUpdatedDate(new Date());
        if (hnslSchoolService.updateById(hnslSchool)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolH5:remove')")
    @OperationLog
    @ApiOperation("删除学校表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSchoolService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @OperationLog
    @ApiOperation("批量添加学校表")
    @PostMapping("/batchSave")
    public ApiResult<?> saveBatch(@RequestBody List<HnslH5School> list) {
        if (hnslSchoolService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @OperationLog
    @ApiOperation("批量修改学校表")
    @PostMapping("/batchUpdate")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<HnslH5School> batchParam) {
        if (batchParam.update(hnslSchoolService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @OperationLog
    @ApiOperation("批量删除学校表")
    @PostMapping("/batchRemove")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSchoolService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }
}
