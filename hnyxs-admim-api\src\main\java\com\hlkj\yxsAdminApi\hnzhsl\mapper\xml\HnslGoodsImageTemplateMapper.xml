<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslGoodsImageTemplateMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods_image_template a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsType != null">
                AND a.GOODS_TYPE = #{param.goodsType}
            </if>
            <if test="param.goodsChildType != null">
                AND a.GOODS_CHILD_TYPE = #{param.goodsChildType}
            </if>
            <if test="param.templateType != null">
                AND a.TEMPLATE_TYPE = #{param.templateType}
            </if>
            <if test="param.templateName != null">
                AND a.TEMPLATE_NAME LIKE CONCAT('%', #{param.templateName}, '%')
            </if>
            <if test="param.templateCode != null">
                AND a.TEMPLATE_CODE LIKE CONCAT('%', #{param.templateCode}, '%')
            </if>
            <if test="param.templateUrl != null">
                AND a.TEMPLATE_URL LIKE CONCAT('%', #{param.templateUrl}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsImageTemplate">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsImageTemplate">
        <include refid="selectSql"></include>
    </select>

</mapper>
