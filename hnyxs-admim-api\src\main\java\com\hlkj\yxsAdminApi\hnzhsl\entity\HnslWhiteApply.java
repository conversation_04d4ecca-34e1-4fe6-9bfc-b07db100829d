package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 白名单申请表
 * @Author: zwk
 * @Since: 2024/10/11
 * @return: null
 **/
@Data
@TableName("HNSL_WHITE_APPLY")
public class HnslWhiteApply implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//唯一标识
	private Long id;

	/** 批次号 */
	private String batchCode;

	/** 地市 */
	private String cityCode;

	/** 文件名 */
	private String fileName;

	/** 文件名称路径 */
	private String filePath;

	/** 文件类型 */
	@TableField(exist = false)
	private String fileType;

	/** 审批文件 */
	private String approveFile;

	/** 审批文件名称路径 */
	private String approveFilePath;

	/** 审批文件类型 */
	@TableField(exist = false)
	private String approveType;

	/** 文件事件类型 */
	private Integer activeEvent;

	/** 熟卡白名单业务号码数量 */
	private Integer whitePhoneNumber;

	/** 状态 (1 待提交，2 审批中，3 通过，4 驳回) */
	private Integer status;

	/** 提交审批人名称 */
	private String approveUser;

	/** 提交审批时间 */
	private Date approveDate;

	/** 备注 */
	private String remark;

	/** 创建时间 */
	private Date createTime;

	/** 审批人列表 */
	@TableField(exist = false)
	HnslUser approvalUserList;
	@TableField(exist = false)
	private String userName;
	@TableField(exist = false)
	private String userPhone;

}
