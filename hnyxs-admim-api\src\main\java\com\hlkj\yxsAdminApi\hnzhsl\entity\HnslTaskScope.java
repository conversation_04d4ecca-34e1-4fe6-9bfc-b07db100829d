package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务范围表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTaskScope对象", description = "任务范围表")
public class HnslTaskScope implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "范围编码（学校和楼栋)")
    @TableField("SCOPE_CODE")
    private String scopeCode;

    @ApiModelProperty(value = "任务编码")
    @TableField("TASK_CODE")
    private String taskCode;

    @ApiModelProperty(value = "范围类型(1:学校 2:房间")
    @TableField("SCOPE_TYPE")
    private Integer scopeType;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("SCOPE_STATUS")
    private Integer scopeStatus;

}
