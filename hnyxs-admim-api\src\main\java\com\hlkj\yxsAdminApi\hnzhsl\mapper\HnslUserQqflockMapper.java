package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserQqflock;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserQqflockParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 楼栋服务QQ群Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
public interface HnslUserQqflockMapper extends BaseMapper<HnslUserQqflock> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserQqflock>
     */
    List<HnslUserQqflock> selectPageRel(@Param("page") IPage<HnslUserQqflock> page,
                             @Param("param") HnslUserQqflockParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserQqflock> selectListRel(@Param("param") HnslUserQqflockParam param);

}
