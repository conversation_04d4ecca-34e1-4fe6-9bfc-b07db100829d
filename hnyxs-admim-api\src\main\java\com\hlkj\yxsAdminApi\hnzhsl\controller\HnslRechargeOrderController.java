package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.DateUtils;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslOrderParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslRechargeOrderService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRechargeOrder;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslRechargeOrderParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 充值订单表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "充值订单表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslRechargeOrder")
public class HnslRechargeOrderController extends BaseController {
    @Autowired
    private HnslRechargeOrderService hnslRechargeOrderService;

    @Autowired
    private ConfigProperties config;
    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:list')")
    @OperationLog
    @ApiOperation("分页查询充值订单表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslRechargeOrder>> page(@RequestBody HnslRechargeOrderParam param) {
        PageParam<HnslRechargeOrder, HnslRechargeOrderParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslRechargeOrderService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslRechargeOrderService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:list')")
    @OperationLog
    @ApiOperation("查询全部充值订单表")
    @PostMapping("/list")
    public ApiResult<List<HnslRechargeOrder>> list(@RequestBody HnslRechargeOrderParam param) {
        PageParam<HnslRechargeOrder, HnslRechargeOrderParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslRechargeOrderService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslRechargeOrderService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:list')")
    @OperationLog
    @ApiOperation("根据id查询充值订单表")
    @GetMapping("/{id}")
    public ApiResult<HnslRechargeOrder> get(@PathVariable("id") Integer id) {
        return success(hnslRechargeOrderService.getById(id));
        // 使用关联查询
        //return success(hnslRechargeOrderService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:save')")
    @OperationLog
    @ApiOperation("添加充值订单表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslRechargeOrder hnslRechargeOrder) {
        if (hnslRechargeOrderService.save(hnslRechargeOrder)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:update')")
    @OperationLog
    @ApiOperation("修改充值订单表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslRechargeOrder hnslRechargeOrder) {
        if (hnslRechargeOrderService.updateById(hnslRechargeOrder)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:remove')")
    @OperationLog
    @ApiOperation("删除充值订单表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslRechargeOrderService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:save')")
    @OperationLog
    @ApiOperation("批量添加充值订单表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslRechargeOrder> list) {
        if (hnslRechargeOrderService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:update')")
    @OperationLog
    @ApiOperation("批量修改充值订单表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslRechargeOrder> batchParam) {
        if (batchParam.update(hnslRechargeOrderService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRechargeOrder:remove')")
    @OperationLog
    @ApiOperation("批量删除充值订单表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslRechargeOrderService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslOrder:outputOrder')")
    @OperationLog
    @ApiOperation("导出充值记录订单")
    @PostMapping("/outResetOrder")
    public ApiResult<?> outResetOrder(@RequestBody HnslRechargeOrderParam hnslOrder, HttpServletRequest request,
                                      HttpServletResponse response) {
        logger.info("导出充值记录订单入参" + hnslOrder);

        ServletContext servletContext = request.getSession().getServletContext();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //查询列表数据
        try {
            List<HnslRechargeOrder> hnslOrderList = hnslRechargeOrderService.listRel(hnslOrder);

            HSSFWorkbook wb = new HSSFWorkbook();
            HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
            //设置列宽
            for (int i = 0; i <= 8; i++) {
                sheet.setColumnWidth(i, 20 * 250);
            }

            // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
            HSSFRow row = sheet.createRow((int) 0);
            // 4.创建单元格，设置值表头，设置表头居中
            HSSFCellStyle style = wb.createCellStyle();
            // 居中格式
            style.setAlignment(HorizontalAlignment.CENTER);
            // 设置表头
            HSSFCell cell = row.createCell(0);
            cell.setCellValue("订单编号");
            cell.setCellStyle(style);
            cell = row.createCell(1);
            cell.setCellValue("订单时间");
            cell.setCellStyle(style);
            cell = row.createCell(2);
            cell.setCellValue("订单状态");
            cell.setCellStyle(style);
            cell = row.createCell(3);
            cell.setCellValue("支付价格");
            cell.setCellStyle(style);
            cell = row.createCell(4);
            cell.setCellValue("所属学校");
            cell.setCellStyle(style);
            cell = row.createCell(5);
            cell.setCellValue("合伙人姓名");
            cell.setCellStyle(style);
            cell = row.createCell(6);
            cell.setCellValue("订购号码");
            cell.setCellStyle(style);
            cell = row.createCell(7);
            cell.setCellValue("合伙人等级");
            cell.setCellStyle(style);
            cell = row.createCell(8);
            cell.setCellValue("地市");
            cell.setCellStyle(style);
            // 循环将数据写入Excel
            if (null != hnslOrderList && hnslOrderList.size() != 0) {

                for (int i = 0; i < hnslOrderList.size(); i++) {
                    row = sheet.createRow((int) i + 1);
                    // 创建单元格，设置值
                    row.createCell(0).setCellValue(hnslOrderList.get(i).getOrderid());
                    row.createCell(1).setCellValue(simpleDateFormat.format(hnslOrderList.get(i).getCreatedDate()));
                    row.createCell(2).setCellValue("充值成功");
                    row.createCell(3).setCellValue(hnslOrderList.get(i).getMoney());
                    row.createCell(4).setCellValue(hnslOrderList.get(i).getSchoolName());
                    row.createCell(5).setCellValue(hnslOrderList.get(i).getUserName());
                    row.createCell(6).setCellValue(hnslOrderList.get(i).getPhone());
                    if("1".equals(hnslOrderList.get(i).getStatusSf())){
                        row.createCell(7).setCellValue("经理");
                    }else if("2".equals(hnslOrderList.get(i).getStatusSf())){
                        row.createCell(7).setCellValue("一级");
                    }else if("3".equals(hnslOrderList.get(i).getStatusSf())){
                        row.createCell(7).setCellValue("二级");
                    }else if("4".equals(hnslOrderList.get(i).getStatusSf())){
                        row.createCell(7).setCellValue("三级");
                    }else{
                        row.createCell(7).setCellValue("其他");
                    }
                    row.createCell(8).setCellValue(InterfaceUtil.city_code.get(hnslOrderList.get(i).getCityCode()));
                }
           //     String filepath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
                String filepath = config.getHnzhslFilePath()  + "file" + File.separator;
                logger.info("file文件" + filepath);
                String fileName="订单表";
                File file = new File(filepath + fileName + ".xls");
                OutputStream ouputStream;
                try {
                    ouputStream = new FileOutputStream(file);
                    try {
                        wb.write(ouputStream);
                        ouputStream.flush();
                        ouputStream.close();
                    } catch (IOException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                } catch (FileNotFoundException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

                if (file.exists()) {//判断文件是否存在
                    if (file.isFile()) {//判断是否是文件
                        Map<String, Object> map = new HashedMap();
                        map.put("code", "6");
                        map.put("fileName", fileName);
                        map.put("msg", "exportDaoUsers");
                        return success(map);
                    }
                }
            }
        } catch (Exception e) {
            return fail(e.getMessage());
        }
        return fail();
    }
}
