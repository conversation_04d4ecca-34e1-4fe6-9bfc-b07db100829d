package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslIntegralService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslIntegralParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 操作积分记录表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "操作积分记录表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslIntegral")
public class HnslIntegralController extends BaseController {
    @Autowired
    private HnslIntegralService hnslIntegralService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:list')")
    @OperationLog
    @ApiOperation("分页查询操作积分记录表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslIntegral>> page(@RequestBody HnslIntegralParam param) {
        PageParam<HnslIntegral, HnslIntegralParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslIntegralService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslIntegralService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:list')")
    @OperationLog
    @ApiOperation("查询全部操作积分记录表")
    @PostMapping("/list")
    public ApiResult<List<HnslIntegral>> list(@RequestBody HnslIntegralParam param) {
        PageParam<HnslIntegral, HnslIntegralParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslIntegralService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslIntegralService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:list')")
    @OperationLog
    @ApiOperation("根据id查询操作积分记录表")
    @GetMapping("/{id}")
    public ApiResult<JSONObject> get(@PathVariable("id") Integer id) {
        HnslIntegral integralServiceById = hnslIntegralService.getById(id);
        JSONObject jsonObject = hnslIntegralService.queryIntegralNum(Long.parseLong(integralServiceById.getUserId()));
        jsonObject.put("hnslIntegralPojo",integralServiceById);
        return success(jsonObject);
        // 使用关联查询
        //return success(hnslIntegralService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:save')")
    @OperationLog
    @ApiOperation("添加操作积分记录表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslIntegral hnslIntegral) {
        if (hnslIntegralService.save(hnslIntegral)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:update')")
    @OperationLog
    @ApiOperation("修改操作积分记录表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslIntegral hnslIntegral) {
        if (hnslIntegralService.updateById(hnslIntegral)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:remove')")
    @OperationLog
    @ApiOperation("删除操作积分记录表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslIntegralService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:save')")
    @OperationLog
    @ApiOperation("批量添加操作积分记录表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslIntegral> list) {
        if (hnslIntegralService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:update')")
    @OperationLog
    @ApiOperation("批量修改操作积分记录表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslIntegral> batchParam) {
        if (batchParam.update(hnslIntegralService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:remove')")
    @OperationLog
    @ApiOperation("批量删除操作积分记录表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslIntegralService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslIntegral:list')")
    @OperationLog
    @ApiOperation("根据用户工号订单记录")
    @PostMapping("/detail")
    public ApiResult<JSONObject> detail(@RequestBody JSONObject obj) {
        JSONObject jsonObject = hnslIntegralService.queryIntegralNum(Long.parseLong(obj.getString("userPhone")));
        return success(jsonObject);
        // 使用关联查询
        //return success(hnslIntegralService.getByIdRel(id));
    }
}
