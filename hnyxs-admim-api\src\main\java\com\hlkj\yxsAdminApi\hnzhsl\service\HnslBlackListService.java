package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBlackList;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface HnslBlackListService extends IService<HnslBlackList> {


    /**
     * 导出二维码黑名单
     * @param params
     * @return
     */
    List<Map<String, Object>> queryListTable(Map<String, Object> params);


    /**
     * 导入二维码黑名单
     * @param blackLists
     * @param request
     * @return
     */
    Map<String, String> saveBlackList(List<HnslBlackList> blackLists, HttpServletRequest request);
}
