package com.hlkj.yxsAdminApi.common.core.utils;
import org.apache.commons.io.FileUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * Created by Administrator on 2018/8/16.
 */
public class Des3Base64Utils {

    private static final byte[] DECODE_TABLE = new byte[] { 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 63, 52,
            53, 54, 55, 56, 57, 58, 59, 60, 61, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2,
            3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
            21, 22, 23, 24, 25, 0, 0, 0, 0, 0, 0, 26, 27, 28, 29, 30, 31, 32,
            33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49,
            50, 51, 0, 0, 0, 0, 0 };

    public static final int DEFAULT = 0;

    public static final int NO_PADDING = 1;

    public static final int NO_WRAP = 2;

    public static final int CRLF = 4;

    public static final int URL_SAFE = 8;

    public static final int NO_CLOSE = 16;

    private static final char[] legalChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
			.toCharArray();

    static abstract class Coder {
        public byte[] output;
        public int op;

        public abstract boolean process(byte[] input, int offset, int len, boolean finish);

        public abstract int maxOutputSize(int len);
    }

    //3des加密
    public static String Des3encode(byte[] byteData) {
        if (byteData == null) {
            return null;
        }

        int iSrcIdx; // index into source (byteData)
        int iDestIdx; // index into destination (byteDest)
        byte byteDest[] = new byte[((byteData.length + 2) / 3) * 4];

        for (iSrcIdx = 0, iDestIdx = 0; iSrcIdx < byteData.length - 2; iSrcIdx += 3) {
            byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx] >>> 2) & 077);
            byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx + 1] >>> 4) & 017 | (byteData[iSrcIdx] << 4) & 077);
            byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx + 2] >>> 6) & 003 | (byteData[iSrcIdx + 1] << 2) & 077);
            byteDest[iDestIdx++] = (byte) (byteData[iSrcIdx + 2] & 077);
        }

        if (iSrcIdx < byteData.length) {
            byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx] >>> 2) & 077);
            if (iSrcIdx < byteData.length - 1) {
                byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx + 1] >>> 4) & 017 | (byteData[iSrcIdx] << 4) & 077);
                byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx + 1] << 2) & 077);
            }

            else {
                byteDest[iDestIdx++] = (byte) ((byteData[iSrcIdx] << 4) & 077);
            }
        }

        for (iSrcIdx = 0; iSrcIdx < iDestIdx; iSrcIdx++) {
            if (byteDest[iSrcIdx] < 26) {
                byteDest[iSrcIdx] = (byte) (byteDest[iSrcIdx] + 'A');
            }

            else if (byteDest[iSrcIdx] < 52) {
                byteDest[iSrcIdx] = (byte) (byteDest[iSrcIdx] + 'a' - 26);
            }

            else if (byteDest[iSrcIdx] < 62) {
                byteDest[iSrcIdx] = (byte) (byteDest[iSrcIdx] + '0' - 52);
            }

            else if (byteDest[iSrcIdx] < 63) {
                byteDest[iSrcIdx] = (byte) '+';
            }

            else {
                byteDest[iSrcIdx] = (byte) '/';
            }
        }

        for (; iSrcIdx < byteDest.length; iSrcIdx++) {
            byteDest[iSrcIdx] = (byte) '=';
        }

        return new String(byteDest);
    }

    //3des解密
    public static byte[] Des3decode(String data) {

        byte[] base64Data = data.getBytes();
        byte[] result = null;
        int groupCount = base64Data.length / 4;

        int lastData = base64Data.length;
        while (base64Data[lastData - 1] == 0x3D) {
            if (--lastData == 0) {
                return new byte[0];
            }
        }

        result = new byte[lastData - groupCount];

        int temp = 0;
        int resultIndex = 0;
        int dataIndex = 0;
        for (; dataIndex + 4 < base64Data.length;) {
            temp = DECODE_TABLE[base64Data[dataIndex++]];
            temp = (temp << 6) + DECODE_TABLE[base64Data[dataIndex++]];
            temp = (temp << 6) + DECODE_TABLE[base64Data[dataIndex++]];
            temp = (temp << 6) + DECODE_TABLE[base64Data[dataIndex++]];

            result[resultIndex++] = (byte) ((temp >> 16) & 0xff);
            result[resultIndex++] = (byte) ((temp >> 8) & 0xff);
            result[resultIndex++] = (byte) (temp & 0xff);
        }

        temp = 0;
        int j = 0;
        for (; dataIndex < base64Data.length; dataIndex++, j++) {
            temp = (temp << 6) + DECODE_TABLE[base64Data[dataIndex]];
        }

        for (; j < 4; j++) {
            temp <<= 6;
        }

        result[resultIndex++] = (byte) ((temp >> 16) & 0xff);
        if (base64Data[dataIndex - 2] != '=')
            result[resultIndex++] = (byte) ((temp >> 8) & 0xff);
        if (base64Data[dataIndex - 1] != '=')
            result[resultIndex++] = (byte) (temp & 0xff);

        return result;
    }


    public static byte[] decode(String str, int flags) {
        return decode(str.getBytes(), flags);
    }

    public static byte[] decode(byte[] input, int flags) {
        return decode(input, 0, input.length, flags);
    }


    public static byte[] decode(byte[] input, int offset, int len, int flags) {
        Decoder decoder = new Decoder(flags, new byte[len*3/4]);

        if (!decoder.process(input, offset, len, true)) {
            throw new IllegalArgumentException("bad base-64");
        }

        if (decoder.op == decoder.output.length) {
            return decoder.output;
        }

        byte[] temp = new byte[decoder.op];
        System.arraycopy(decoder.output, 0, temp, 0, decoder.op);
        return temp;
    }

    static class Decoder extends Coder {
        private static final int DECODE[] = {
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63,
                52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -2, -1, -1,
                -1,  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14,
                15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1,
                -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
                41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        };

        private static final int DECODE_WEBSAFE[] = {
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1,
                52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -2, -1, -1,
                -1,  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14,
                15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, 63,
                -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
                41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        };

        private static final int SKIP = -1;
        private static final int EQUALS = -2;

        private int state;
        private int value;

        final private int[] alphabet;

        public Decoder(int flags, byte[] output) {
            this.output = output;

            alphabet = ((flags & URL_SAFE) == 0) ? DECODE : DECODE_WEBSAFE;
            state = 0;
            value = 0;
        }

        public int maxOutputSize(int len) {
            return len * 3/4 + 10;
        }

        public boolean process(byte[] input, int offset, int len, boolean finish) {
            if (this.state == 6) return false;

            int p = offset;
            len += offset;

            int state = this.state;
            int value = this.value;
            int op = 0;
            final byte[] output = this.output;
            final int[] alphabet = this.alphabet;

            while (p < len) {
                if (state == 0) {
                    while (p+4 <= len &&
                            (value = ((alphabet[input[p] & 0xff] << 18) |
                                    (alphabet[input[p+1] & 0xff] << 12) |
                                    (alphabet[input[p+2] & 0xff] << 6) |
                                    (alphabet[input[p+3] & 0xff]))) >= 0) {
                        output[op+2] = (byte) value;
                        output[op+1] = (byte) (value >> 8);
                        output[op] = (byte) (value >> 16);
                        op += 3;
                        p += 4;
                    }
                    if (p >= len) break;
                }

                int d = alphabet[input[p++] & 0xff];

                switch (state) {
                    case 0:
                        if (d >= 0) {
                            value = d;
                            ++state;
                        } else if (d != SKIP) {
                            this.state = 6;
                            return false;
                        }
                        break;

                    case 1:
                        if (d >= 0) {
                            value = (value << 6) | d;
                            ++state;
                        } else if (d != SKIP) {
                            this.state = 6;
                            return false;
                        }
                        break;

                    case 2:
                        if (d >= 0) {
                            value = (value << 6) | d;
                            ++state;
                        } else if (d == EQUALS) {
                            output[op++] = (byte) (value >> 4);
                            state = 4;
                        } else if (d != SKIP) {
                            this.state = 6;
                            return false;
                        }
                        break;

                    case 3:
                        if (d >= 0) {
                            value = (value << 6) | d;
                            output[op+2] = (byte) value;
                            output[op+1] = (byte) (value >> 8);
                            output[op] = (byte) (value >> 16);
                            op += 3;
                            state = 0;
                        } else if (d == EQUALS) {
                            output[op+1] = (byte) (value >> 2);
                            output[op] = (byte) (value >> 10);
                            op += 2;
                            state = 5;
                        } else if (d != SKIP) {
                            this.state = 6;
                            return false;
                        }
                        break;

                    case 4:
                        if (d == EQUALS) {
                            ++state;
                        } else if (d != SKIP) {
                            this.state = 6;
                            return false;
                        }
                        break;

                    case 5:
                        if (d != SKIP) {
                            this.state = 6;
                            return false;
                        }
                        break;
                }
            }

            if (!finish) {
                this.state = state;
                this.value = value;
                this.op = op;
                return true;
            }

            switch (state) {
                case 0:
                    break;
                case 1:
                    this.state = 6;
                    return false;
                case 2:
                    output[op++] = (byte) (value >> 4);
                    break;
                case 3:
                    output[op++] = (byte) (value >> 10);
                    output[op++] = (byte) (value >> 2);
                    break;
                case 4:
                    this.state = 6;
                    return false;
                case 5:
                    break;
            }

            this.state = state;
            this.op = op;
            return true;
        }
    }


    public static String encodeToString(byte[] input, int flags) {
        try {
            return new String(encode(input, flags), "US-ASCII");
        } catch (UnsupportedEncodingException e) {
            throw new AssertionError(e);
        }
    }


    public static String encodeToString(byte[] input, int offset, int len, int flags) {
        try {
            return new String(encode(input, offset, len, flags), "US-ASCII");
        } catch (UnsupportedEncodingException e) {
            throw new AssertionError(e);
        }
    }

    public static byte[] encode(byte[] input, int flags) {
        return encode(input, 0, input.length, flags);
    }

    public static byte[] encode(byte[] input, int offset, int len, int flags) {
        Encoder encoder = new Encoder(flags, null);

        int output_len = len / 3 * 4;

        if (encoder.do_padding) {
            if (len % 3 > 0) {
                output_len += 4;
            }
        } else {
            switch (len % 3) {
                case 0: break;
                case 1: output_len += 2; break;
                case 2: output_len += 3; break;
            }
        }

        if (encoder.do_newline && len > 0) {
            output_len += (((len-1) / (3 * Encoder.LINE_GROUPS)) + 1) *
                    (encoder.do_cr ? 2 : 1);
        }

        encoder.output = new byte[output_len];
        encoder.process(input, offset, len, true);

        assert encoder.op == output_len;

        return encoder.output;
    }

    static class Encoder extends Coder {
        public static final int LINE_GROUPS = 19;

        private static final byte ENCODE[] = {
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
                'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
                'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
                'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '/',
        };

        private static final byte ENCODE_WEBSAFE[] = {
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
                'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
                'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
                'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '-', '_',
        };

        final private byte[] tail;
        int tailLen;
        private int count;

        final public boolean do_padding;
        final public boolean do_newline;
        final public boolean do_cr;
        final private byte[] alphabet;

        public Encoder(int flags, byte[] output) {
            this.output = output;

            do_padding = (flags & NO_PADDING) == 0;
            do_newline = (flags & NO_WRAP) == 0;
            do_cr = (flags & CRLF) != 0;
            alphabet = ((flags & URL_SAFE) == 0) ? ENCODE : ENCODE_WEBSAFE;

            tail = new byte[2];
            tailLen = 0;

            count = do_newline ? LINE_GROUPS : -1;
        }

        public int maxOutputSize(int len) {
            return len * 8/5 + 10;
        }

        public boolean process(byte[] input, int offset, int len, boolean finish) {
            final byte[] alphabet = this.alphabet;
            final byte[] output = this.output;
            int op = 0;
            int count = this.count;

            int p = offset;
            len += offset;
            int v = -1;

            switch (tailLen) {
                case 0:
                    break;

                case 1:
                    if (p+2 <= len) {
                        v = ((tail[0] & 0xff) << 16) |
                                ((input[p++] & 0xff) << 8) |
                                (input[p++] & 0xff);
                        tailLen = 0;
                    };
                    break;

                case 2:
                    if (p+1 <= len) {
                        v = ((tail[0] & 0xff) << 16) |
                                ((tail[1] & 0xff) << 8) |
                                (input[p++] & 0xff);
                        tailLen = 0;
                    }
                    break;
            }

            if (v != -1) {
                output[op++] = alphabet[(v >> 18) & 0x3f];
                output[op++] = alphabet[(v >> 12) & 0x3f];
                output[op++] = alphabet[(v >> 6) & 0x3f];
                output[op++] = alphabet[v & 0x3f];
                if (--count == 0) {
                    if (do_cr) output[op++] = '\r';
                    output[op++] = '\n';
                    count = LINE_GROUPS;
                }
            }

            while (p+3 <= len) {
                v = ((input[p] & 0xff) << 16) |
                        ((input[p+1] & 0xff) << 8) |
                        (input[p+2] & 0xff);
                output[op] = alphabet[(v >> 18) & 0x3f];
                output[op+1] = alphabet[(v >> 12) & 0x3f];
                output[op+2] = alphabet[(v >> 6) & 0x3f];
                output[op+3] = alphabet[v & 0x3f];
                p += 3;
                op += 4;
                if (--count == 0) {
                    if (do_cr) output[op++] = '\r';
                    output[op++] = '\n';
                    count = LINE_GROUPS;
                }
            }

            if (finish) {
                if (p-tailLen == len-1) {
                    int t = 0;
                    v = ((tailLen > 0 ? tail[t++] : input[p++]) & 0xff) << 4;
                    tailLen -= t;
                    output[op++] = alphabet[(v >> 6) & 0x3f];
                    output[op++] = alphabet[v & 0x3f];
                    if (do_padding) {
                        output[op++] = '=';
                        output[op++] = '=';
                    }
                    if (do_newline) {
                        if (do_cr) output[op++] = '\r';
                        output[op++] = '\n';
                    }
                } else if (p-tailLen == len-2) {
                    int t = 0;
                    v = (((tailLen > 1 ? tail[t++] : input[p++]) & 0xff) << 10) |
                            (((tailLen > 0 ? tail[t++] : input[p++]) & 0xff) << 2);
                    tailLen -= t;
                    output[op++] = alphabet[(v >> 12) & 0x3f];
                    output[op++] = alphabet[(v >> 6) & 0x3f];
                    output[op++] = alphabet[v & 0x3f];
                    if (do_padding) {
                        output[op++] = '=';
                    }
                    if (do_newline) {
                        if (do_cr) output[op++] = '\r';
                        output[op++] = '\n';
                    }
                } else if (do_newline && op > 0 && count != LINE_GROUPS) {
                    if (do_cr) output[op++] = '\r';
                    output[op++] = '\n';
                }

                assert tailLen == 0;
                assert p == len;
            } else {
                if (p == len-1) {
                    tail[tailLen++] = input[p];
                } else if (p == len-2) {
                    tail[tailLen++] = input[p];
                    tail[tailLen++] = input[p+1];
                }
            }

            this.op = op;
            this.count = count;

            return true;
        }
    }

    private Des3Base64Utils() { }
    /**
     * base编码
     * @param filename
     * @return
     */
    public static String getBase64(String filename) {
        String base64 = null;
        try {
            base64 = Base64Constant.encodeToString(FileUtils.readFileToByteArray(new File(filename)), NO_WRAP);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return base64;
    }

    public static String getBase64(File file) {
        String base64 = null;
        try {
            base64 = Base64Constant.encodeToString(FileUtils.readFileToByteArray(file), NO_WRAP);

        } catch (IOException e) {
            e.printStackTrace();
        }
        return base64;
    }

    public static String getBase64(byte[] base) {
        String base64 = null;
        try {
            base64 = Base64Constant.encodeToString(base, NO_WRAP);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return base64;
    }

    /**
     * base64解码
     *
     * @param s
     * @return String 解码后的字节数组
     */
    public static byte[] getFromBASE64(String s) {
        if (s == null) {
            return null;
        }
        try {
            byte b[] = Base64Constant.decode(s, NO_WRAP);
            return b;
        } catch (Exception ie) {
            System.out.println("****** 照片解码错误************");
            ie.printStackTrace();
            return null;
        }
    }

    /**
     * base64解码
     *
     * @param s
     * @return String 解码后的字节数组
     */
    public static byte[] getFromBASE64(String s,
                                       int type) {
        if (s == null) {
            return null;
        }
        try {
            byte b[] = Base64Constant.decode(s, type);
            return b;
        } catch (Exception ie) {
            System.out.println("****** 照片解码错误************");
            ie.printStackTrace();
            return null;
        }
    }

    /**
     * 保存照片
     *
     * @param filename 照片文件名称
     * @param base64String 从接口获得照片的base64编码
     * @return
     */
    @SuppressWarnings("finally")
    public static boolean savePhoto(String filename,
                                    String base64String) {
        boolean f = false;
        FileOutputStream myFileStream = null;
        try {
            myFileStream = new FileOutputStream(filename);
            myFileStream.write(getFromBASE64(base64String));
            f = true;
            return f;
        } catch (IOException ie) {
            System.out.println("****** 保存照片错");
            ie.printStackTrace();
            return f;
        } catch (Exception ex) {
            System.out.println("****** 保存照片错");
            ex.printStackTrace();
            return f;
        } finally {
            try {
                myFileStream.close();
                return f;
            } catch (IOException e) {
                System.out.println("****** 保存照片错");
                e.printStackTrace();
                return f;
            }
        }
    }

    public static String encode(byte[] data) {
		int start = 0;
		int len = data.length;
		StringBuffer buf = new StringBuffer(data.length * 3 / 2);

		int end = len - 3;
		int i = start;
		int n = 0;

		while (i <= end) {
			int d = (data[i] & 0xFF) << 16 | (data[i + 1] & 0xFF) << 8 | data[i + 2] & 0xFF;

			buf.append(legalChars[d >> 18 & 0x3F]);
			buf.append(legalChars[d >> 12 & 0x3F]);
			buf.append(legalChars[d >> 6 & 0x3F]);
			buf.append(legalChars[d & 0x3F]);

			i += 3;

			if (n++ >= 14) {
				n = 0;
				buf.append(" ");
			}
		}

		if (i == start + len - 2) {
			int d = (data[i] & 0xFF) << 16 | (data[i + 1] & 0xFF) << 8;

			buf.append(legalChars[d >> 18 & 0x3F]);
			buf.append(legalChars[d >> 12 & 0x3F]);
			buf.append(legalChars[d >> 6 & 0x3F]);
			buf.append("=");
		} else if (i == start + len - 1) {
			int d = (data[i] & 0xFF) << 16;

			buf.append(legalChars[d >> 18 & 0x3F]);
			buf.append(legalChars[d >> 12 & 0x3F]);
			buf.append("==");
		}

		return buf.toString();
	}


}
