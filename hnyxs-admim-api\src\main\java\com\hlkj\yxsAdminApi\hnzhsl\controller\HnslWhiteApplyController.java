package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApply;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteApplyParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteApplyService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteApproveService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteListService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.xssf.usermodel.*;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Description: 白名单申请
 * @Author: zwk
 * @Since: 2025/1/22
 * @return: null
 **/
@RestController
@RequestMapping("/api/hnzhsl/hnslwhiteapply")
public class HnslWhiteApplyController extends BaseController {
    @Autowired
    private HnslWhiteApplyService hnslWhiteApplyService;

    @Autowired
    private HnslWhiteListService hnslWhiteListService;

    @Autowired
    private HnslWhiteApproveService hnslWhiteApproveService;

    @Autowired
    private HnslUserService hnslUserService;

    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    private ConfigProperties config;

    @PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapply:list')")
    @ApiOperation("分页查询熟卡白名单表")
    @PostMapping("/list")
    public ApiResult<PageResult<HnslWhiteApply>> page(@RequestBody HnslWhiteApplyParam param) {
        User user = getLoginUser();
        String hnslType = redisUtils.getString("hnslChannel" + user.getPhone());
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        String userName = redisUtils.getString("thisUserName" + user.getPhone());
        param.setHnslType(hnslType);
        param.setQueryCityCode(cityCode);
        param.setUserName(userName);
        param.setQueryType(statusSf);
        return success(hnslWhiteApplyService.pageRel(param));
    }

    /**
     * 申请审批
     */
    @PostMapping("/apply/{id}")
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public ApiResult<?> apply(@PathVariable("id") Long id) {
        User user = getLoginUser();
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
		String userPhone = redisUtils.getString("thisUserPhone" + user.getPhone());
		String userName = redisUtils.getString("thisUserName" + user.getPhone());
        if (!"6".equals(statusSf)) {
            return fail("对不起，您没有该权限操作");
        }
        HnslWhiteApply hnslWhiteApply = hnslWhiteApplyService.queryApplyObject(id, userPhone, userName);
        return success("data",hnslWhiteApply);
    }

    /**
     * 信息
     */
    @PostMapping("/info/{id}")
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapply:info')")
    public ApiResult<?> info(@PathVariable("id") Long id){
        HnslWhiteApply hnslWhiteApply = hnslWhiteApplyService.getById(id);

        return success("hnslWhiteApply", hnslWhiteApply);
    }


    /**
     * 导入上传熟卡白名单
     *
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapply:importUser')")
    @PostMapping("/decode/importWhiteApply")
    public ApiResult<?> importWhiteList(@RequestParam(value = "file", required = true) MultipartFile[] files, String base64Image, String base64FileImage,
                             String imageName, String imageFileName, String remark, HttpServletRequest request, HttpServletResponse response) throws IOException {

        MultipartFile file = files[0];

        User user = getLoginUser();
        String userName = redisUtils.getString("thisUserName" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());

        JSONObject result = new JSONObject();
        String image = "xls,xlsx";

        if (!file.isEmpty()) {
            String uploadPath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                logger.info("上传文件大小大于5M");
                return fail("resultCode", 1);
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                logger.info("上传文件格式不正确");
                return fail("resultCode", 2);
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                logger.info("文件上传格式不正确");
                return fail("resultCode", 2);
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    logger.info("文件所在目录创建失败");
                    return fail("resultCode", 3);
                }
            }

            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            File saveXlsFile = new File(uploadPath, imageFileName);
            try {
//                file.transferTo(saveFile);
                file.transferTo(saveXlsFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXls2(uploadPath, imageFileName, 1, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, imageFileName, 1, 0, 0);
                }

                logger.info("文件解析结果numberList：" + numberList);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
                String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));

                // 判断当前上传白名单管理员地市是否和白名单数据地市一致 地市管理员只能上传对应地市的熟卡白名单
                if (numberList != null && numberList.size() != 0) {
                    //将数据转化成user对象
                    List<HnslWhiteList> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslWhiteList listPojo = new HnslWhiteList();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //校园渠道-必填（1 高校/ 2 中小学）
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                if(Objects.equals("高校",StringUtil.trimString(userMap.get(name)))){
                                    listPojo.setSchoolChannel(1);
                                }else if(Objects.equals("中小学",StringUtil.trimString(userMap.get(name)))){
                                    listPojo.setSchoolChannel(2);
                                }else{
                                    listPojo.setSchoolChannel(0);
                                }
                            }
                            //地市编码-必填
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                listPojo.setCityCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //学校编码-必填
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                listPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //学校名称-必填
                            else if ("3".equalsIgnoreCase(name.trim())) {
                                listPojo.setSchoolName(StringUtil.trimString(userMap.get(name)));
                            }
                            //熟卡号码-必填
                            else if ("4".equalsIgnoreCase(name.trim())) {
                                listPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                            }
                            //使用者姓名-非必填
                            else if ("5".equalsIgnoreCase(name.trim())) {
                                listPojo.setCustomerName(StringUtil.trimString(userMap.get(name)));
                            }
                            //使用者-身份证-非必填
                            else if ("6".equalsIgnoreCase(name.trim())) {
                                String sfz=StringUtil.trimString(userMap.get(name));
                                if(StringUtil.isNotNull(sfz)){
                                    sfz=sfz.toUpperCase();
                                }
                                listPojo.setCustomerCard(sfz);
                            } else if ("7".equalsIgnoreCase(name.trim())) {
                                listPojo.setRemarks(StringUtil.trimString(userMap.get(name)));
                            }
                            else if ("8".equalsIgnoreCase(name.trim())) {
                                listPojo.setXlsName(StringUtil.trimString(userMap.get(name)));
                            }
                        }
                        // 默认失效时间为30天
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(new Date());
                        calendar.add(Calendar.DATE, 30);
                        Date failureDate = calendar.getTime();
                        listPojo.setFailureDate(failureDate);
                        listPojo.setBatchCode(batchCode);
                        listPojo.setCreatedDate(new Date());
                        listPojo.setStatus(1);
                        listPojo.setNumberStatus(1);
                        listPojo.setCreatedUser(userName);
                        hnslUserList.add(listPojo);
                    }
                    try {
                        Map<String, String> checkWhite = hnslWhiteApplyService.importCheckWhite(hnslUserList, cityCode, statusSf, request);
                        if (null != checkWhite & checkWhite.get("resultCode").equals("1")) {
                            if (checkWhite.get("isCheckOk").equals("6")) {
                                // 预校验通过后 上传xls OR xlsx文件
                                String xlsFileName = uploadXlsFile(saveXlsFile, imageFileName, batchCode);
                                // 上传审批文件图片
                                //String fileName = uploadImgFile(base64Image, imageName);
                                String fileName = uploadImage(base64Image, request);
                                // 存储到DB中的路径
                                String fileImgName = "uploads" + File.separator + "picture" + File.separator + "white" + File.separator + fileName;

                                // 保存上传记录
                                HnslWhiteApply hnslWhiteApplyEntity  = new HnslWhiteApply();
                                hnslWhiteApplyEntity.setBatchCode(batchCode);
                                hnslWhiteApplyEntity.setFileName(batchCode + imageFileName);
                                hnslWhiteApplyEntity.setFilePath(xlsFileName);
                                hnslWhiteApplyEntity.setFileType(fileSuffix);
                                hnslWhiteApplyEntity.setApproveFile(imageName);
                                hnslWhiteApplyEntity.setApproveFilePath(fileImgName);
                                hnslWhiteApplyEntity.setActiveEvent(1);
                                hnslWhiteApplyEntity.setWhitePhoneNumber(numberList.size());
                                hnslWhiteApplyEntity.setCityCode(cityCode);
                                //待提交
                                hnslWhiteApplyEntity.setApproveUser(userName);
                                hnslWhiteApplyEntity.setApproveDate(new Date());
                                hnslWhiteApplyEntity.setStatus(1);
                                hnslWhiteApplyEntity.setRemark(remark);
                                hnslWhiteApplyEntity.setCreateTime(new Date());
                                hnslWhiteApplyService.saveApply(hnslWhiteApplyEntity, request);
                                Map<String, String> resultMap = new HashMap();
                                resultMap.put("mes", "exportDaoUser");
                                resultMap.put("resultCode", "9");
                                resultMap.put("fileName", checkWhite.get("fileName"));
                                return success(resultMap);
                            } else {}
                            Map<String, String> resultMap = new HashMap();
                            resultMap.put("mes", "exportDaoUser");
                            resultMap.put("resultCode", "6");
                            resultMap.put("fileName", checkWhite.get("fileName"));
                            return success(resultMap);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        logger.error("插入信息失败:" + e);
                        return success("resultCode", 5);
                    }
                } else {
                    logger.info("文件内容为空，或者解析失败");
                    result.put("resultCode", "4");
                    return success("resultCode", 4);
                }
                Map<String, String> resultMap = new HashMap();
                resultMap.put("mes", "exportDaoUser");
                resultMap.put("resultCode", "0");
                return success(resultMap);
            } catch (Exception e) {
                logger.error("文件上传接口上传异常:" + e);
                return success("resultCode", 3);
            }
        } else {
            logger.info("上传文件为空");
            return success("resultCode", 3);
        }
    }


    /**
     * 上传xls，xlsx文件
     * @param saveFile
     * @param imageName
     * @return
     */
    public String uploadXlsFile(File saveFile, String imageName, String batchCode) {
        String imageType = "";
        if (imageName.endsWith("xls")) {
            imageType = "xls";
        }
        if (imageName.endsWith("xlsx")) {
            imageType = "xlsx";
        }
        int index = imageName.lastIndexOf(".");
        String imgName = imageName.substring(0, index);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String formattedDate = dateFormat.format(new Date());
        String fileName = imgName + formattedDate + "." + imageType;
        try {
            // 检查桶是否存在
            boolean isBucketExist = amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_WHITE_NAME);
            if (!isBucketExist) {
                try {
                    amazonS3.createBucket(ConstantUtil.BUCKET_WHITE_NAME);
                    logger.info("智慧扫楼项目-创建桶成功" + ConstantUtil.BUCKET_WHITE_NAME);
                } catch (AmazonS3Exception e) {
                    logger.error("智慧扫楼项目-创建桶失败 " + ConstantUtil.BUCKET_WHITE_NAME + ": " + e.getMessage());
                }
            }
            if (!amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_WHITE_NAME)) {
                amazonS3.createBucket(ConstantUtil.BUCKET_WHITE_NAME);
            }

            PutObjectResult result = amazonS3.putObject(ConstantUtil.BUCKET_WHITE_NAME, batchCode + imageName, saveFile);
            logger.info("上传结果" + result.toString());
        } catch (AmazonS3Exception e) {
            logger.error("Amazon S3 异常: " + e.getMessage(), e);
            throw new RuntimeException("上传失败: " + e.getMessage(), e);
        }
        return fileName;
    }

    /**
     * 上传审批文件图片
     * @param basedata
     * @param request
     * @return
     */
    public String uploadImage(String basedata, HttpServletRequest request) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String times = simpleDateFormat.format(new Date());
        String fileSuffix = "jpg";
        String uploadCode = times + (int) (Math.random() * (9999 - 1000) + 1000);
        String newname = uploadCode + "." + fileSuffix;
        BufferedOutputStream outputStream = null;
        FileOutputStream fileOutputStream = null;
        try {
            String uploadPath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("文件路径:" + uploadPath);
            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdir()) {
                    logger.info("文件所在目录创建失败");
                    throw new RuntimeException("文件所在目录创建失败");
                }
            }

            File saveFile = new File(uploadPath, newname);

            BASE64Decoder decoder = new BASE64Decoder();
            byte[] bytes = decoder.decodeBuffer(basedata);
            for (int i = 0; i < bytes.length; ++i) {
                if (bytes[i] < 0) {//调整异常数据
                    bytes[i] += 256;
                }
            }
            File ocrFile = new File(uploadPath, newname);
            fileOutputStream = new FileOutputStream(ocrFile);
            outputStream = new BufferedOutputStream(fileOutputStream);
            outputStream.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("文件上传接口上传异常:" + e);
            throw new RuntimeException("文件上传接口上传异常");
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.info("outputStream关闭异常" + e);
                }
            }
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    logger.info("fileOutputStream关闭异常" + e);
                }
            }
        }
        return newname;
    }

    /**
     * 文件下载
     * @return 成功响应
     */
    @GetMapping("/downloadImgFile")
    public void downloadTemplate(String batchCode, HttpServletResponse response, HttpServletRequest request) throws Exception {
        HnslWhiteApply hnslWhiteApplyEntity = hnslWhiteApplyService.queryBatchCode(batchCode);
        String key = hnslWhiteApplyEntity.getFileName();
        S3Object s3Object = amazonS3.getObject(ConstantUtil.BUCKET_WHITE_NAME, key);
        S3ObjectInputStream inputStream = null;
        try {
            inputStream = s3Object.getObjectContent();
            response.reset();
            //设置输出文件格式
            response.setContentType("application/vnd.ms-excel");
            String outFileNam = URLEncoder.encode(key, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + outFileNam);
            ServletOutputStream outputStream = response.getOutputStream();

            byte[] buff = new byte[1024];
            int length;
            while ((length = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, length);
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 下载白名单上传预校验反馈表
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/exportDaoUser")
    public void exportDaoUser(HttpServletResponse response, HttpServletRequest request, String name) throws Exception {

        //下载
        String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName = name + ".xls".toString(); // 文件的默认保存名

        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("白名单导入预校验表", "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inStream.close();
            file.delete();
        }
    }

    /**
     * 下载白名单导出文件
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/exportDaoUsers")
    public void exportDaoUsers(HttpServletResponse response, HttpServletRequest request, String name) throws Exception {
        String parameter = request.getParameter("fileName");
        //下载
        String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName = null;
        if (StringUtil.isNotNull(parameter)) {
            fileName = parameter + ".xls".toString(); // 文件的默认保存名
        } else {
            fileName = name + ".xls".toString(); // 文件的默认保存名
        }
        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inStream.close();
            file.delete();
        }
    }

    /**
     * 导出白名单报表
     *
     * @return
     */
    @RequestMapping("/outputWhitelTable")
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapply:output')")
    public ApiResult<?> outputWhitelTable(@RequestBody Map<String, Object> params, HttpServletRequest request,
                               HttpServletResponse response) {
        List<Map<String, Object>> hnslOrderList = hnslWhiteApplyService.queryListTable(params);

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        XSSFRow row = sheet.createRow((int) 0);
        XSSFCellStyle style = wb.createCellStyle();
        XSSFCell cell = row.createCell(0);
        // 设置字体
        XSSFFont font = wb.createFont();
        //设置列宽
        for (int i = 0; i <= 12; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 居中格式
//        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 设置表头
        cell.setCellValue("批次号");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("校园渠道");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("学校编码");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("学校名称");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("未激活");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("激活成功");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("激活失败");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("已过期");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("已使用");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("白名单导入量");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("导入时间");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != hnslOrderList && hnslOrderList.size() != 0) {

            for (int i = 0; i < hnslOrderList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(hnslOrderList.get(i).get("BATCH_CODE")));

                row.createCell(1).setCellValue(InterfaceUtil.city_code.get(String.valueOf(hnslOrderList.get(i).get("CITY_CODE"))));

                if ("1".equals(String.valueOf(hnslOrderList.get(i).get("SCHOOL_CHANNEL")))) {
                    row.createCell(2).setCellValue("高校");
                } else if ("2".equals(String.valueOf(hnslOrderList.get(i).get("SCHOOL_CHANNEL")))) {
                    row.createCell(2).setCellValue("中小学");
                } else {
                    row.createCell(2).setCellValue("未知");
                }
                row.createCell(3).setCellValue(String.valueOf(hnslOrderList.get(i).get("SCHOOL_CODE")));
                row.createCell(4).setCellValue(String.valueOf(hnslOrderList.get(i).get("SCHOOL_NAME")));
                row.createCell(5).setCellValue(String.valueOf(hnslOrderList.get(i).get("S1")));
                row.createCell(6).setCellValue(String.valueOf(hnslOrderList.get(i).get("S3")));
                row.createCell(7).setCellValue(String.valueOf(hnslOrderList.get(i).get("S4")));
                row.createCell(8).setCellValue(String.valueOf(hnslOrderList.get(i).get("S2")));
                row.createCell(9).setCellValue(String.valueOf(hnslOrderList.get(i).get("S5")));
                row.createCell(10).setCellValue(String.valueOf(hnslOrderList.get(i).get("ZS")));
                row.createCell(11).setCellValue(String.valueOf(hnslOrderList.get(i).get("CREATED_DATE")));
            }
//            try {
//                User user = getLoginUser();
//                WaterMarkUtil.insertWaterMarkTextToXlsxEntrance(wb, user.getUsername() + " " + user.getUsername());
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
            String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("file文件" + filepath);
            String fileName = "白名单报表";
            File file = new File(filepath + fileName + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) { //判断是否是文件
                    Map<String, Object> map = new HashedMap();
                    map.put("code", "6");
                    map.put("fileName", fileName);
                    map.put("msg", "exportDaoUsers");
                    return success(map);
                }
            }
        }
        return success();
    }

    /**
     * 图片下载
     * @return 成功响应
     */
    @GetMapping("/downloadImg")
    public void downloadImg(String batchCode, HttpServletResponse response, HttpServletRequest request) throws Exception {
        HnslWhiteApply hnslWhiteApplyEntity = hnslWhiteApplyService.queryBatchCode(batchCode);
        String key = hnslWhiteApplyEntity.getApproveFile();
//        return R.ok().put("url", ConstantUtil.SAVE_FILE_PATH + "/download/" + ConstantUtil.BUCKET_WHITE_NAME + "/" + key);
        S3Object s3Object = amazonS3.getObject(ConstantUtil.BUCKET_WHITE_NAME, key);
        S3ObjectInputStream inputStream = null;
        try {
            inputStream = s3Object.getObjectContent();
            response.reset();
            //设置输出文件格式
//            response.setContentType("application/vnd.ms-excel");
            response.setContentType("image/jpg");
            String outFileNam = URLEncoder.encode(key, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + outFileNam);
            ServletOutputStream outputStream = response.getOutputStream();

            byte[] buff = new byte[1024];
            int length;
            while ((length = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, length);
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
