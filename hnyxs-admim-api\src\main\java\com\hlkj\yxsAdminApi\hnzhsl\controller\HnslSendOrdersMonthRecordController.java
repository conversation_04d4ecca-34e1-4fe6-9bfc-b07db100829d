package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSendOrdersMonthRecordService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersMonthRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSendOrdersMonthRecordParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 派单每月统计表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "派单每月统计表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-send-orders-month-record")
public class HnslSendOrdersMonthRecordController extends BaseController {
    @Autowired
    private HnslSendOrdersMonthRecordService hnslSendOrdersMonthRecordService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:list')")
    @OperationLog
    @ApiOperation("分页查询派单每月统计表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSendOrdersMonthRecord>> page(@RequestBody HnslSendOrdersMonthRecordParam param) {
        PageParam<HnslSendOrdersMonthRecord, HnslSendOrdersMonthRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSendOrdersMonthRecordService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslSendOrdersMonthRecordService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:list')")
    @OperationLog
    @ApiOperation("查询全部派单每月统计表")
    @PostMapping("/list")
    public ApiResult<List<HnslSendOrdersMonthRecord>> list(@RequestBody HnslSendOrdersMonthRecordParam param) {
        PageParam<HnslSendOrdersMonthRecord, HnslSendOrdersMonthRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSendOrdersMonthRecordService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSendOrdersMonthRecordService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:list')")
    @OperationLog
    @ApiOperation("根据id查询派单每月统计表")
    @GetMapping("/{id}")
    public ApiResult<HnslSendOrdersMonthRecord> get(@PathVariable("id") Integer id) {
        return success(hnslSendOrdersMonthRecordService.getById(id));
        // 使用关联查询
        //return success(hnslSendOrdersMonthRecordService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:save')")
    @OperationLog
    @ApiOperation("添加派单每月统计表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSendOrdersMonthRecord hnslSendOrdersMonthRecord) {
        if (hnslSendOrdersMonthRecordService.save(hnslSendOrdersMonthRecord)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:update')")
    @OperationLog
    @ApiOperation("修改派单每月统计表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSendOrdersMonthRecord hnslSendOrdersMonthRecord) {
        if (hnslSendOrdersMonthRecordService.updateById(hnslSendOrdersMonthRecord)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:remove')")
    @OperationLog
    @ApiOperation("删除派单每月统计表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSendOrdersMonthRecordService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:save')")
    @OperationLog
    @ApiOperation("批量添加派单每月统计表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSendOrdersMonthRecord> list) {
        if (hnslSendOrdersMonthRecordService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:update')")
    @OperationLog
    @ApiOperation("批量修改派单每月统计表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSendOrdersMonthRecord> batchParam) {
        if (batchParam.update(hnslSendOrdersMonthRecordService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersMonthRecord:remove')")
    @OperationLog
    @ApiOperation("批量删除派单每月统计表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSendOrdersMonthRecordService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
