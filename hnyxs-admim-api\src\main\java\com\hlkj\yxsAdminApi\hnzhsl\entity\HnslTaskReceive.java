package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务接取表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTaskReceive对象", description = "任务接取表")
public class HnslTaskReceive implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "状态(1:任务进行中    2: 任务完成 3:任务时间过期即任务失败")
    @TableField("TASK_STATUS_REL")
    private Integer taskStatusRel;

    @ApiModelProperty(value = "合伙人手机号码")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "任务编码")
    @TableField("TASK_CODE")
    private String taskCode;

    @ApiModelProperty(value = "任务完成时间")
    @TableField("FINISH_TIME")
    private Date finishTime;

    @ApiModelProperty(value = "任务接收时间")
    @TableField("ACCEPT_TIME")
    private Date acceptTime;

    @ApiModelProperty(value = "领取奖励（0:未领取 1:已领取 2：审核中 3:审核通过 4:审核失败")
    @TableField("RECEIVE_AWARD")
    private Integer receiveAward;

    @ApiModelProperty(value = "任务发布人关联")
    @TableField("TASK_PUBLISH")
    private String taskPublish;

}
