package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫楼消息通知表
 *
 * <AUTHOR>
 * @since 2023-07-21 11:31:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslMessageNotification对象", description = "扫楼消息通知表")
public class HnslMessageNotification implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "当前状态（1：有效，0：无效）")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间（提交时间）")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "读取状态，查询详情即为读取（1：未读，0：已读）")
    @TableField("READ_STATUS")
    private Integer readStatus;

    @ApiModelProperty(value = "消息编码（HNSLMN+YYYYMMDDHHmmSS+随机6位）")
    @TableField("NOTIFICATION_CODE")
    private String notificationCode;

    @ApiModelProperty(value = "通知内容")
    @TableField("NOTIFICATION_CONTENT")
    private String notificationContent;

    @ApiModelProperty(value = "通知内容详情")
    @TableField("NOTIFICATION_CONTENT_DETAILS")
    private String notificationContentDetails;

    @ApiModelProperty(value = "关联的订单编码")
    @TableField("ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "客户姓名")
    @TableField("CUSTOMER_NAME")
    private String customerName;

    @ApiModelProperty(value = "客户订购号码")
    @TableField("CUSTOMER_PHONE")
    private String customerPhone;

    @ApiModelProperty(value = "通知用户")
    @TableField("NOTIFICATION_USER")
    private String notificationUser;

    @ApiModelProperty(value = "通知类型：1：任务通知，2：激活通知，3：一人一码下单通知")
    @TableField("NOTIFICATION_TYPE")
    private Integer notificationType;

}
