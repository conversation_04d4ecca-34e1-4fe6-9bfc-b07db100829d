<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.common.system.mapper.TenantMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnyxs_sys_tenant a
        <where>
            <if test="param.tenantId != null">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.tenantName != null">
                AND a.tenant_name LIKE CONCAT('%', #{param.tenantName}, '%')
            </if>
            <if test="param.comments != null">
                AND a.comments LIKE CONCAT('%', #{param.comments}, '%')
            </if>
            <if test="param.deleted != null">
                AND a.deleted = #{param.deleted}
            </if>
            <if test="param.deleted == null">
                AND a.deleted = 0
            </if>
            <if test="param.createTimeStart != null">
                AND a.create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null">
                AND a.create_time &lt;= #{param.createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.common.system.entity.Tenant">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.common.system.entity.Tenant">
        <include refid="selectSql"></include>
    </select>

    <select id="getLoginAllTenantList" resultType="com.hlkj.yxsAdminApi.common.system.entity.Tenant">
        select
            hst.*
        from
            hnyxs_sys_tenant hst
                inner join (
                select
                    distinct tenant_id
                from
                    hnyxs_sys_user
                where
                    deleted = 0
                  and username = #{userName})us on
                hst.tenant_id = us.tenant_id
    </select>
</mapper>
