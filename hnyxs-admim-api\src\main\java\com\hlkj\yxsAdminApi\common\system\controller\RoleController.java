package com.hlkj.yxsAdminApi.common.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.utils.CommonUtil;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.Role;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.param.RoleParam;
import com.hlkj.yxsAdminApi.common.system.service.PermissionChangeLogService;
import com.hlkj.yxsAdminApi.common.system.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:02
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/api/system/role")
public class RoleController extends BaseController {
    @Autowired
    private RoleService roleService;
    
    @Autowired
    private PermissionChangeLogService permissionChangeLogService;

    @PreAuthorize("hasAuthority('sys:role:list')")
    @OperationLog
    @ApiOperation("分页查询角色")
    @GetMapping("/page")
    public ApiResult<PageResult<Role>> page(RoleParam param) {
        PageParam<Role, RoleParam> page = new PageParam<>(param);
        return success(roleService.page(page, page.getWrapper()));
    }

    @PreAuthorize("hasAuthority('sys:role:list')")
    @OperationLog
    @ApiOperation("查询全部角色")
    @GetMapping("/list")
    public ApiResult<List<Role>> list(RoleParam param) {
        PageParam<Role, RoleParam> page = new PageParam<>(param);
        return success(roleService.list(page.getOrderWrapper()));
    }

    @PreAuthorize("hasAuthority('sys:role:list')")
    @OperationLog
    @ApiOperation("根据id查询角色")
    @GetMapping("/{id}")
    public ApiResult<Role> get(@PathVariable("id") Integer id) {
        return success(roleService.getById(id));
    }

    @PreAuthorize("hasAuthority('sys:role:save')")
    @OperationLog
    @ApiOperation("添加角色")
    @PostMapping("/save")
    public ApiResult<?> save(HttpServletRequest request, @RequestBody Role role) {
        if (roleService.count(new LambdaQueryWrapper<Role>().eq(Role::getRoleCode, role.getRoleCode())) > 0) {
            return fail("角色标识已存在");
        }
        if (roleService.count(new LambdaQueryWrapper<Role>().eq(Role::getRoleName, role.getRoleName())) > 0) {
            return fail("角色名称已存在");
        }
        if (roleService.save(role)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "新增";
            String operationModule = "角色管理";
            String targetId = String.valueOf(role.getRoleId());
            String targetName = role.getRoleName();
            String changeDetails = String.format("管理员{%s}新增角色{%s}", operator.getNickname(), role.getRoleName());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, null, role.toString(), null);
                    
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog
    @ApiOperation("修改角色")
    @PostMapping("/update")
    public ApiResult<?> update(HttpServletRequest request, @RequestBody Role role) {
        // 获取修改前的角色信息
        Role beforeRole = roleService.getById(role.getRoleId());
        if (beforeRole == null) {
            return fail("角色不存在");
        }
        
        if (role.getRoleCode() != null && roleService.count(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleCode, role.getRoleCode())
                .ne(Role::getRoleId, role.getRoleId())) > 0) {
            return fail("角色标识已存在");
        }
        if (role.getRoleName() != null && roleService.count(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleName, role.getRoleName())
                .ne(Role::getRoleId, role.getRoleId())) > 0) {
            return fail("角色名称已存在");
        }
        if (roleService.updateById(role)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "修改";
            String operationModule = "角色管理";
            String targetId = String.valueOf(role.getRoleId());
            String targetName = beforeRole.getRoleName();
            
            // 生成变更详情
            StringBuilder detailsBuilder = new StringBuilder();
            detailsBuilder.append(String.format("管理员{%s}修改角色{%s}：", operator.getNickname(), targetName));
            
            if (role.getRoleName() != null && !role.getRoleName().equals(beforeRole.getRoleName())) {
                detailsBuilder.append(permissionChangeLogService.formatRoleChangeDetails("roleName", beforeRole.getRoleName(), role.getRoleName())).append("；");
            }
            
            if (role.getRoleCode() != null && !role.getRoleCode().equals(beforeRole.getRoleCode())) {
                detailsBuilder.append(permissionChangeLogService.formatRoleChangeDetails("roleCode", beforeRole.getRoleCode(), role.getRoleCode())).append("；");
            }
            
            if (role.getComments() != null && !role.getComments().equals(beforeRole.getComments())) {
                detailsBuilder.append(permissionChangeLogService.formatRoleChangeDetails("comments", beforeRole.getComments(), role.getComments())).append("；");
            }
            
            String changeDetails = detailsBuilder.toString();
            if (changeDetails.endsWith("；")) {
                changeDetails = changeDetails.substring(0, changeDetails.length() - 1);
            }
            
            // 获取修改后的角色信息
            Role afterRole = roleService.getById(role.getRoleId());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeRole.toString(), afterRole.toString(), null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:role:remove')")
    @OperationLog
    @ApiOperation("删除角色")
    @PostMapping("/{id}")
    public ApiResult<?> remove(HttpServletRequest request, @PathVariable("id") Integer id) {
        // 获取删除前的角色信息
        Role beforeRole = roleService.getById(id);
        if (beforeRole == null) {
            return fail("角色不存在");
        }
        
        if (roleService.removeById(id)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "删除";
            String operationModule = "角色管理";
            String targetId = String.valueOf(id);
            String targetName = beforeRole.getRoleName();
            String changeDetails = String.format("管理员{%s}删除角色{%s}", operator.getNickname(), targetName);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeRole.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('sys:role:save')")
    @OperationLog
    @ApiOperation("批量添加角色")
    @PostMapping("/saveBatch")
    public ApiResult<List<String>> saveBatch(HttpServletRequest request, @RequestBody List<Role> list) {
        // 校验是否重复
        if (CommonUtil.checkRepeat(list, Role::getRoleName)) {
            return fail("角色名称存在重复", null);
        }
        if (CommonUtil.checkRepeat(list, Role::getRoleCode)) {
            return fail("角色标识存在重复", null);
        }
        // 校验是否存在
        List<Role> codeExists = roleService.list(new LambdaQueryWrapper<Role>().in(Role::getRoleCode,
                list.stream().map(Role::getRoleCode).collect(Collectors.toList())));
        if (codeExists.size() > 0) {
            return fail("角色标识已存在", codeExists.stream().map(Role::getRoleCode)
                    .collect(Collectors.toList())).setCode(2);
        }
        List<Role> nameExists = roleService.list(new LambdaQueryWrapper<Role>().in(Role::getRoleName,
                list.stream().map(Role::getRoleCode).collect(Collectors.toList())));
        if (nameExists.size() > 0) {
            return fail("角色标识已存在", nameExists.stream().map(Role::getRoleCode)
                    .collect(Collectors.toList())).setCode(3);
        }
        if (roleService.saveBatch(list)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量新增";
            String operationModule = "角色管理";
            String targetNames = list.stream()
                    .map(Role::getRoleName)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量新增角色{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    null, targetNames, changeDetails, null, list.toString(), null);
                    
            return success("添加成功", null);
        }
        return fail("添加失败", null);
    }

    @PreAuthorize("hasAuthority('sys:role:remove')")
    @OperationLog
    @ApiOperation("批量删除角色")
    @PostMapping("/avoid/removeBatch")
    public ApiResult<?> removeBatch(HttpServletRequest request, @RequestBody List<Integer> ids) {
        // 获取删除前的角色信息
        List<Role> beforeRoles = roleService.listByIds(ids);
        if (beforeRoles.isEmpty()) {
            return fail("角色不存在");
        }
        
        if (roleService.removeByIds(ids)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量删除";
            String operationModule = "角色管理";
            String targetNames = beforeRoles.stream()
                    .map(Role::getRoleName)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量删除角色{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    ids.toString(), targetNames, changeDetails, beforeRoles.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
