package com.hlkj.yxsAdminApi.common.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslExportApproval;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslExportApprovalEntity;
import com.hlkj.yxsAdminApi.hnzhsl.entity.hnslApproverSettingEntity;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslApproverSettingService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslExportApprovalService;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class QueryUserManagerUtil {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private HnslApproverSettingService hnslApproverSettingService;
    @Autowired
    private HnslExportApprovalService hnslExportApprovalService;
    @Autowired
    private RedisUtil redisUtils;

    public void superManagerList(HnslExportApprovalEntity entity, String userPhone, String fileName, long smsCode) {
        Date submitDate = entity.getSubmitDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String formattedDate = sdf.format(submitDate);
        JSONObject param = new JSONObject();
        param.put("phone_num", userPhone); //必填，号码
        param.put("source", "智慧扫楼");// 必填，电渠提供
        param.put("sms_id", "sms_00312"); // 必填，审核通过的模板ID
        param.put("srctermid", "");// 接入码，可选
        JSONObject params = new JSONObject();
        params.put("param1", entity.getSubmitUser());
        params.put("param2", formattedDate);
        params.put("param3", fileName);
        params.put("param4", entity.getExportNumber());
        params.put("param5", "DC#" + smsCode);
        param.put("params", params);
        try {
            String code = InterfaceUtil.dxtz3wnew(param);
            if ("0".equals(code)) {
                logger.info("导出短信发送成功");
            } else {
                throw new Exception("导出短信发送异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出短信发送异常");
        }
    }

    @SneakyThrows
    public void exportRecord(String fileName, String filePath, User user,
                             Integer activeEvent ,Integer resultListSize, Integer appSettingId) {
        String cityCode = (String) redisUtils.get("cityCode" + user.getPhone());
        // 生成自定义规则ID作为展示
        String dateStr = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        long sequenceNumber = redisUtils.incr("ruleIdSequence", 1);
        String sequenceStr = String.format("%01d", sequenceNumber);
        String ruleId = "HNFY" + cityCode + dateStr + sequenceStr;
        // 生成自定义短信子码
        long smsCodeIncr = redisUtils.incr("smsCodeIncr", 1);
        String smsCodeStr = "10001" + smsCodeIncr; // 固定前缀 + 自增数字
        Integer smsCode = Integer.parseInt(smsCodeStr);

        LambdaQueryWrapper<HnslExportApprovalEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslExportApprovalEntity::getSmsCode, smsCode);
        HnslExportApprovalEntity one = hnslExportApprovalService.getOne(queryWrapper);
        // 如果生成的smsCode已经存在，则重新生成一个新的smsCode
        while (null != one) {
            smsCodeIncr = redisUtils.incr("smsCodeIncr", 1);
            smsCodeStr = "10001" + smsCodeIncr;
            smsCode = Integer.parseInt(smsCodeStr);
            queryWrapper.clear();
            queryWrapper.eq(HnslExportApprovalEntity::getSmsCode, smsCode);
            one = hnslExportApprovalService.getOne(queryWrapper);
        }

        HnslExportApprovalEntity entity = new HnslExportApprovalEntity();
        entity.setRuleId(ruleId);
        entity.setFilePath(filePath);
        entity.setFileName(fileName);
        entity.setSubmitDate(new Date());
        entity.setSubmitUser(user.getNickname());
        entity.setSubmitUserId(Long.valueOf(user.getUserId()));
        entity.setStatus(1);
        entity.setExportNumber(resultListSize);
        entity.setCreateTime(new Date());
        entity.setActiveEvent(activeEvent);
        entity.setDataSourceStatus(1);
        entity.setDataSourceName(fileName);
        entity.setSmsCode(smsCode);
        hnslApproverSettingEntity service = hnslApproverSettingService.getById(appSettingId);
        if (service == null) {
            throw new Exception("审批人不存在异常");
        }
        entity.setApproveUser(service.getUserName());
        entity.setApproveUserPhone(service.getUserPhone());
        if (hnslExportApprovalService.save(entity)) {
            superManagerList(entity, service.getUserPhone(), fileName, smsCode);
        } else {
            throw new Exception("生成申请记录异常");
        }
    }
}
