package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRoom;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslRoomParam;

import java.util.List;

/**
 * 房间表Service
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
public interface HnslRoomService extends IService<HnslRoom> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslRoom>
     */
    PageResult<HnslRoom> pageRel(HnslRoomParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslRoom>
     */
    List<HnslRoom> listRel(HnslRoomParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslRoom
     */
    HnslRoom getByIdRel(Integer id);

}
