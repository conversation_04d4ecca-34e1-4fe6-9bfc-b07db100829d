package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprovalRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteApprovalRecordParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteApprovalRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 白名单审批记录
 * @date 2024-10-09
 */
@RestController
@RequestMapping("/api/hnzhsl/hnslwhiteapprovalrecord")
public class HnslWhiteApprovalRecordController extends BaseController {
	
	@Autowired
	private HnslWhiteApprovalRecordService hnslWhiteApprovalRecordService;

	@Autowired
	private RedisUtil redisUtils;

	/**
	 * 列表
	 */
	@RequestMapping("/list")
	@PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapprovalrecord:list')")
	public ApiResult<PageResult<HnslWhiteApprovalRecord>> list(@RequestBody HnslWhiteApprovalRecordParam param){
		//查询列表数据
		User user = getLoginUser();
		String hnslType = redisUtils.getString("hnslChannel" + user.getPhone());
		String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
		String cityCode = redisUtils.getString("cityCode" + user.getPhone());
		String userName = redisUtils.getString("thisUserName" + user.getPhone());
		param.setHnslType(hnslType);
		param.setQueryCityCode(cityCode);
		param.setUserName(userName);
		param.setQueryType(statusSf);
		return success(hnslWhiteApprovalRecordService.pageRel(param));
	}
}
