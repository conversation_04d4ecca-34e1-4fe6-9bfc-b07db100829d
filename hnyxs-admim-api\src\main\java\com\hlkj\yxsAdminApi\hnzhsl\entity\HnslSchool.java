package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学校表
 *
 * <AUTHOR>
 * @since 2023-05-04 14:46:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSchool对象", description = "学校表")
public class HnslSchool implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "学校名称")
    @TableField("SCHOOL_NAME")
    private String schoolName;

    @ApiModelProperty(value = "学校总人数")
    @TableField("SCHOOL_REGISTER")
    private Integer schoolRegister;

    @ApiModelProperty(value = "现有登记人数（信息已经采集的客户）")
    @TableField("SCHOOL_REGISTER_EXISTING")
    private Integer schoolRegisterExisting;

    @ApiModelProperty(value = "本网用户总人数（暂时不用）")
    @TableField("SCHOOL_NETWORK")
    private Integer schoolNetwork;

    @ApiModelProperty(value = "本网用户现有人数")
    @TableField("SCHOOL_NETWORK_EXISTING")
    private Integer schoolNetworkExisting;

    @ApiModelProperty(value = "楼栋数")
    @TableField("HOUSE_NUMBER")
    private Integer houseNumber;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "管理ID")
    @TableField("USER_ID")
    private Integer userId;

    @ApiModelProperty(value = "地市")
    @TableField("SCHOOL_CITY")
    private String schoolCity;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "学校图片")
    @TableField("SCHOOL_IMAGE")
    private String schoolImage;

    @ApiModelProperty(value = "类型 1:主学校  2:分校区")
    @TableField("SCHOOL_TYPE")
    private Integer schoolType;

    @ApiModelProperty(value = "分校区上级学校编码")
    @TableField("UP_SCHOOL")
    private String upSchool;

    @ApiModelProperty(value = "CRM编码")
    @TableField("CRM_CODE")
    private String crmCode;

    @ApiModelProperty(value = "校内流量编码")
    @TableField("SCHOOL_FLOW_CODE")
    private String schoolFlowCode;

    @ApiModelProperty(value = "学校多样功能 0：正常 1：未成年办理")
    @TableField("SCHOOL_VARIETY")
    private Integer schoolVariety;

    @ApiModelProperty(value = "是否开启学校熟卡二维码 0：关闭 1：开启")
    @TableField("SCHOOL_QRCODE")
    private Integer schoolQrcode;

    @ApiModelProperty(value = "学校对应营协六级ID")
    @TableField("SCHOOL_SIX_ID")
    private String schoolSixId;

    @ApiModelProperty(value = "学校类型 1:高校 2：中小学 3：公众")
    @TableField("SCHOOL_GRADE_TYPE")
    private Integer schoolGradeType;

    @ApiModelProperty(value = "客服二维码URL")
    @TableField("SCHOOL_SERVICE_URL")
    private String schoolServiceUrl;

    @ApiModelProperty(value = "控制学校展示模块 1：全部模块 2：部分模块")
    @TableField("SCHOOL_MODULE_TYPE")
    private Integer schoolModuleType;

    @ApiModelProperty(value = "图片上传类型 默认 1:只能拍照 2:拍照或上传相册")
    @TableField("SCHOOL_IMAGE_UPLOAD_TYPE")
    private Integer schoolImageUploadType;

    @ApiModelProperty(value = "预约生卡开关 1:开 2:关 默认关")
    @TableField("SCHOOL_RESERVATION_SWITCH")
    private Integer schoolReservationSwitch;

    @ApiModelProperty(value = "校园经理多个校园经理,分开")
    @TableField(exist = false)
    private String manager;

    @ApiModelProperty(value = "楼栋信息")
    @TableField(exist = false)
    private List<HnslBuilding> hnslBuilding;

}
