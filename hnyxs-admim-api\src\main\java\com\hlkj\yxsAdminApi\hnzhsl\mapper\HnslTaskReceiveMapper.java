package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskReceive;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskReceiveParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务接取表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslTaskReceiveMapper extends BaseMapper<HnslTaskReceive> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTaskReceive>
     */
    List<HnslTaskReceive> selectPageRel(@Param("page") IPage<HnslTaskReceive> page,
                             @Param("param") HnslTaskReceiveParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTaskReceive> selectListRel(@Param("param") HnslTaskReceiveParam param);

}
