package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**熟卡预约记录表
 * <AUTHOR> wuji<PERSON>
 * @date : 2024/9/23 20:07
 * @modyified By :
 */
@Data
@TableName("HNSL_WHITE_RECORD")
@Builder
public class HnslWhiteRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    //唯一标识
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    //熟卡号码
    private String userPhone;
    //修改人
    private String updatedUser;
    //创建人
    private String createdUser;
    //修改时间
    private Date updatedDate;
    //创建时间
    private Date createdDate;
    //是否可用状态（0:否 1:是
    private Long status;
    //所属地市编码
    private String cityCode;
    //状态(0：未激活 1：激活成功 2：激活失败)
    private int numberStatus;
    //姓名
    private String customerName;
    //身份证
    private String customerCard;
    /**
     * 联系号码
     */
    private String customerPhone;
    //操作类型 1：预约 2:释放
    private int operateType;
    //操作人 合伙人保存名字 用户默认保存用户
    private String operateUser;
    //激活时间
    private Date activateDate;
    /**
     * 合伙人ID
     */
    private int userId;
    /**
     * 熟卡预约类型
     */
    private String recordType;
    /**
     * 熟卡线上预约地址 (省市区)
     */
    private String address;
    /**
     * 熟卡线上预约详细地址
     */
    private String detailedAddress;

    /**
     * 快递单号
     */
    private String courierNumber;

}
