package com.hlkj.yxsAdminApi.common.core.utils;


import com.alibaba.fastjson.JSONObject;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.IOException;
import java.net.URL;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 工具�?
 * 工具类的功能函数�?��都是static
 *
 * <AUTHOR>
 */
public class StringUtil {

    public static Logger logger = LoggerFactory.getLogger(StringUtil.class);


    /**
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        if (str != null && !str.equals("") && !str.trim().equals("") && !str.equalsIgnoreCase("null"))
            return false;
        else
            return true;
    }


    public static String getString(String str) {
        if (str != null && !str.equals("") && !str.trim().equals("") && !str.equalsIgnoreCase("null"))
            return str;
        else
            return "";
    }

    public static String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    public static void main(String[] args) {
        String aString = "欧安";
        System.out.println(checkname(aString));
    }

    //	public static boolean isNumeric( String str )
//	{
//		return false;
//	}
    public static String trimString(String obj) {
        if (isEmpty(obj)) {
            return "";
        } else {
            return obj.trim();
        }
    }

    public static String getMapByKey(String key, Map<String, Object> map) {
        return map.get(key) != null ? map.get(key).toString() : "";
    }

    private static int BEGIN = 45217;
    private static int END = 63486;
    // 按照声母表示，这个表是在GB2312中的出现的第一个汉字，也就是说“啊”是代表首字母a的第一个汉字。
    // i, u, v都不做声母, 自定规则跟随前面的字母
    private static char[] chartable = {'啊', '芭', '擦', '搭', '蛾', '发', '噶', '哈',
            '哈', '击', '喀', '垃', '妈', '拿', '哦', '啪', '期', '然', '撒', '塌', '塌',
            '塌', '挖', '昔', '压', '匝',};
    // 二十六个字母区间对应二十七个端点
    // GB2312码汉字区间十进制表示
    private static int[] table = new int[27];
    // 对应首字母区间表
    private static char[] initialtable = {'a', 'b', 'c', 'd', 'e', 'f', 'g',
            'h', 'h', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
            't', 't', 'w', 'x', 'y', 'z',};

    // 初始化
    static {
        for (int i = 0; i < 26; i++) {
            table[i] = gbValue(chartable[i]);// 得到GB2312码的首字母区间端点表，十进制。
        }
        table[26] = END;// 区间表结尾
    }

    /**
     * 根据一个包含汉字的字符串返回一个汉字拼音首字母的字符串 最重要的一个方法，思路如下：一个个字符读入、判断、输出
     */
    public static String getFirstLetter(String sourceStr) {
        String result = "";
        String str = sourceStr.toLowerCase();
        int StrLength = str.length();
        int i;
        try {
            for (i = 0; i < StrLength; i++) {
                result += Char2Initial(str.charAt(i));
            }
        } catch (Exception e) {
            result = "";
        }
        return result;
    }

    /**
     * 输入字符,得到他的声母,英文字母返回对应的大写字母,其他非简体汉字返回 '0'
     */
    private static char Char2Initial(char ch) {
        // 对英文字母的处理：小写字母转换为大写，大写的直接返回
        if (ch >= 'a' && ch <= 'z') {
            return ch;
        }
        if (ch >= 'A' && ch <= 'Z') {
            return ch;
        }
        // 对非英文字母的处理：转化为首字母，然后判断是否在码表范围内，
        // 若不是，则直接返回。
        // 若是，则在码表内的进行判断。
        int gb = gbValue(ch);// 汉字转换首字母
        if ((gb < BEGIN) || (gb > END))// 在码表区间之前，直接返回
        {
            return ch;
        }
        int i;
        for (i = 0; i < 26; i++) {// 判断匹配码表区间，匹配到就break,判断区间形如“[,)”
            if ((gb >= table[i]) && (gb < table[i + 1])) {
                break;
            }
        }
        if (gb == END) {//补上GB2312区间最右端
            i = 25;
        }
        return initialtable[i]; // 在码表区间中，返回首字母
    }

    /**
     * 取出汉字的编码 cn 汉字
     */
    private static int gbValue(char ch) {// 将一个汉字（GB2312）转换为十进制表示。
        String str = new String();
        str += ch;
        try {
            byte[] bytes = str.getBytes("GB2312");
            if (bytes.length < 2) {
                return 0;
            }
            return (bytes[0] << 8 & 0xff00) + (bytes[1] & 0xff);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 判断字符串为空
     *
     * @param str
     * @return
     */
    public static Boolean isNull(String str) {
        if (str == null || str.trim().length() == 0 || str.equals("null")) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断字符串不为空
     *
     * @param str
     * @return
     */
    public static Boolean isNotNull(String str) {
        if (str != null && str.trim().length() > 0 && !str.equals("null")) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 常见特殊字符过滤
     *
     * @param str
     * @return
     */
    public static String filtration(String str) {
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    /**
     * map过滤特殊字符
     *
     * @param dataMap
     * @return
     */
    public static Map<String, String> MapFiltration(Map<String, String> dataMap) {
        for (Iterator iterator = dataMap.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry m = (Map.Entry) iterator.next();
            String mapValue = (String) m.getValue();
            if (StringUtil.isNotNull(mapValue)) {
                String result = StringUtil.filtration(mapValue);
                m.setValue(result);
            }
        }
        return dataMap;
    }

    /**
     * 判断字符串是否包含汉字
     *
     * @param countname
     * @return
     */
    public static boolean checkcountname(String countname) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(countname);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 判断整个字符串都由汉字组成
     *
     * @param name
     * @return
     */
    public static boolean checkname(String name) {
        int n = 0;
        for (int i = 0; i < name.length(); i++) {
            n = (int) name.charAt(i);
            if (!(19968 <= n && n < 40869)) {
                return false;
            }
        }
        return true;
    }

    public static String getUUid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 生成请求流水号或者订单号
     *
     * @return
     */
    public static String getExchangeId(String empcode) {
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String val = "";
        Random random = new Random();
        for (int i = 0; i < 10; i++) {
            val += String.valueOf(random.nextInt(10));
        }
        String exchangeId = empcode + format.format(new Date()) + val;
        return exchangeId;
    }

    public static String getStringValue(String key) {
        return isNotNull(key) ? key : "";
    }

    /**
     * 将URL图片转换为base64格式
     *
     * @param imageUrl：图片路径
     * @param sizeLimit：原图大小上限，当图片原图大小超过该值时先将图片大小 设置为该值以下再转换成base64格式,单位kb
     * @return
     */
    public static String convertImageToBase64(String imageUrl, Integer sizeLimit) throws IOException {
        // 默认上限为500k
        if (sizeLimit == null) {
            sizeLimit = 300;
        }
        sizeLimit = sizeLimit * 1024;
        String base64Image;
        DataInputStream dataInputStream = null;
        ByteArrayOutputStream outputStream = null;
        ByteArrayInputStream inputStream = null;
        try {
            // 从远程读取图片
            URL url = new URL(imageUrl);
            dataInputStream = new DataInputStream(url.openStream());
            outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[2048];
            int length;
            while ((length = dataInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            byte[] context = outputStream.toByteArray();

            // 将图片数据还原为图片
            inputStream = new ByteArrayInputStream(context);
            BufferedImage image = ImageIO.read(inputStream);
            int imageSize = context.length;
            int type = image.getType();
            int height = image.getHeight();
            int width = image.getWidth();

            BufferedImage tempImage;
            // 判断文件大小是否大于size,循环压缩，直到大小小于给定的值
            while (imageSize > sizeLimit) {
                // 将图片长宽压缩到原来的90%
                height = new Double(height * 0.9).intValue();
                width = new Double(width * 0.9).intValue();
                tempImage = new BufferedImage(width, height, type);
                // 绘制缩小后的图
                tempImage.getGraphics().drawImage(image, 0, 0, width, height, null);
                // 重新计算图片大小
                outputStream.reset();
                ImageIO.write(tempImage, "jpg", outputStream);
                imageSize = outputStream.toByteArray().length;
            }

            // 将图片转化为base64并返回
            byte[] data = outputStream.toByteArray();
            // 此处一定要使用org.apache.tomcat.util.codec.binary.Base64，防止再linux上出现换行等特殊符号
            // base64Image = Base64.encodeBase64String(data);
            base64Image = new BASE64Encoder().encode(data);
        } catch (Exception e) {
            // 抛出异常
            throw e;
        } finally {
            if (dataInputStream != null) {
                try {
                    dataInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return base64Image;
    }

    /**
     * 清除\r\n
     *
     * @param str
     * @return
     */
    public static String replaceBlank(String str) {
        String dest = "";
        if (str != null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    /**
     * 将逗号分隔的字符串转换为List
     *
     * @param str
     * @return
     */
    public static List<String> stringToList(String str) {
        if (str == null || str.trim().length() == 0) {
            return Collections.emptyList();
        }
        return Arrays.asList(str.split(",")).stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 将List转换为逗号分隔的字符串
     *
     * @param list
     * @return
     */
    public static String listToString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }
        return list.stream()
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining(","));
    }

    /**
     * 向JSONObject中添加键值对，当值为null时用空字符串替代
     *
     * @param jsonObject 要添加键值对的JSONObject对象
     * @param key 键名
     * @param value 值(可以为null)
     */
    public static void putJsonValue(JSONObject jsonObject, String key, Object value) {
        if (jsonObject != null && key != null) {
            if (value == null) {
                jsonObject.put(key, "");
            } else if (value instanceof String && StringUtils.isEmpty((String) value)) {
                jsonObject.put(key, "");
            } else {
                jsonObject.put(key, value);
            }
        }
    }

    /**
     * 批量向JSONObject中添加键值对，当值为null时用空字符串替代
     *
     * @param jsonObject 要添加键值对的JSONObject对象
     * @param map 包含要添加的键值对的Map
     */
    public static void putJsonValues(JSONObject jsonObject, Map<String, Object> map) {
        if (jsonObject != null && map != null) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                putJsonValue(jsonObject, entry.getKey(), entry.getValue());
            }
        }
    }
}
