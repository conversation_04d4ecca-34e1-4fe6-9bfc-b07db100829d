<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslSchoolUserMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_school_user a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.name != null">
                AND a.NAME LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.sex != null">
                AND a.SEX = #{param.sex}
            </if>
            <if test="param.identityCard != null">
                AND a.IDENTITY_CARD LIKE CONCAT('%', #{param.identityCard}, '%')
            </if>
            <if test="param.phone != null">
                AND a.PHONE LIKE CONCAT('%', #{param.phone}, '%')
            </if>
            <if test="param.network != null">
                AND a.NETWORK = #{param.network}
            </if>
            <if test="param.enroltime != null">
                AND a.ENROLTIME LIKE CONCAT('%', #{param.enroltime}, '%')
            </if>
            <if test="param.nativeplace != null">
                AND a.NATIVEPLACE LIKE CONCAT('%', #{param.nativeplace}, '%')
            </if>
            <if test="param.age != null">
                AND a.AGE = #{param.age}
            </if>
            <if test="param.phoneTwo != null">
                AND a.PHONE_TWO LIKE CONCAT('%', #{param.phoneTwo}, '%')
            </if>
            <if test="param.phoneBrand != null">
                AND a.PHONE_BRAND LIKE CONCAT('%', #{param.phoneBrand}, '%')
            </if>
            <if test="param.setmeal != null">
                AND a.SETMEAL LIKE CONCAT('%', #{param.setmeal}, '%')
            </if>
            <if test="param.voice != null">
                AND a.VOICE LIKE CONCAT('%', #{param.voice}, '%')
            </if>
            <if test="param.operator != null">
                AND a.OPERATOR LIKE CONCAT('%', #{param.operator}, '%')
            </if>
            <if test="param.networkVelocity != null">
                AND a.NETWORK_VELOCITY LIKE CONCAT('%', #{param.networkVelocity}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.roomId != null">
                AND a.ROOM_ID LIKE CONCAT('%', #{param.roomId}, '%')
            </if>
            <if test="param.buildingId != null">
                AND a.BUILDING_ID LIKE CONCAT('%', #{param.buildingId}, '%')
            </if>
            <if test="param.clientSfzSite != null">
                AND a.CLIENT_SFZ_SITE LIKE CONCAT('%', #{param.clientSfzSite}, '%')
            </if>
            <if test="param.clientSfzTime != null">
                AND a.CLIENT_SFZ_TIME LIKE CONCAT('%', #{param.clientSfzTime}, '%')
            </if>
            <if test="param.clientNumber != null">
                AND a.CLIENT_NUMBER LIKE CONCAT('%', #{param.clientNumber}, '%')
            </if>
            <if test="param.flow != null">
                AND a.FLOW LIKE CONCAT('%', #{param.flow}, '%')
            </if>
            <if test="param.balance != null">
                AND a.BALANCE = #{param.balance}
            </if>
            <if test="param.yipay != null">
                AND a.YIPAY = #{param.yipay}
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.clientRemark != null">
                AND a.CLIENT_REMARK LIKE CONCAT('%', #{param.clientRemark}, '%')
            </if>
            <if test="param.broadbandRemark != null">
                AND a.BROADBAND_REMARK LIKE CONCAT('%', #{param.broadbandRemark}, '%')
            </if>
            <if test="param.finishSchool != null">
                AND a.FINISH_SCHOOL = #{param.finishSchool}
            </if>
            <if test="param.schoolDepartment != null">
                AND a.SCHOOL_DEPARTMENT LIKE CONCAT('%', #{param.schoolDepartment}, '%')
            </if>
            <if test="param.schoolSpecialty != null">
                AND a.SCHOOL_SPECIALTY LIKE CONCAT('%', #{param.schoolSpecialty}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser">
        <include refid="selectSql"></include>
    </select>

</mapper>
