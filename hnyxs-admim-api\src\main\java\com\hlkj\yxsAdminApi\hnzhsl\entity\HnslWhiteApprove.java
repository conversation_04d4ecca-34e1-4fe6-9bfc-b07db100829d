package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 审批列表
 * @Author: zwk
 * @Since: 2024/10/12
 * @return: null
 **/
@Data
@TableName("HNSL_WHITE_APPROVE")
public class HnslWhiteApprove implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//唯一标识
	private Long id;

	/** 提交申请ID */
	private Long applyId;

	/** 批次号 */
	private String batchCode;

	/** 文件名 */
	private String fileName;

	/** 文件存储路径 */
	private String filePath;

	/** 地市 */
	private String cityCode;

	/** 审批文件 */
	private String approveFile;

	/** 审批文件存储路径 */
	private String approveFilePath;

	/** 审批文件类型 */
	@TableField(exist = false)
	private String approveType;

	/** 文件事件类型 */
	private Integer activeEvent;

	/** 熟卡白名单业务号码数量 */
	private Integer whitePhoneNumber;

	/** 状态 (1 待提交，2 审批中，3 通过，4 驳回) */
	private Integer status;

	/** 审批人名称 */
	private String approveUser;

	/** 审批人电话 */
	private String approvePhone;

	/** 提交审批时间 */
	private Date submitApprovalTime;

	/** 备注 */
	private String remark;

	/** 创建时间 */
	private Date createTime;

	/** 物理删除 */
	private Integer deleteRecord;

	/** 驳回原因 */
	private String rejectReason;
}
