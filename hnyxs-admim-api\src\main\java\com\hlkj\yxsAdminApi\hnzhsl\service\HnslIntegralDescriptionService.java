package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegralDescription;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslIntegralDescriptionParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 熟卡积分说明表Service
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslIntegralDescriptionService extends IService<HnslIntegralDescription> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslIntegralDescription>
     */
    PageResult<HnslIntegralDescription> pageRel(HnslIntegralDescriptionParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslIntegralDescription>
     */
    List<HnslIntegralDescription> listRel(HnslIntegralDescriptionParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslIntegralDescription
     */
    HnslIntegralDescription getByIdRel(Integer id);

    /**
     * 导入商品积分模板
     * @param userList
     * @param request
     * @return
     */
    public Map<String, String> saveUserArray(List<HnslIntegralDescription> userList, HttpServletRequest request);
}
