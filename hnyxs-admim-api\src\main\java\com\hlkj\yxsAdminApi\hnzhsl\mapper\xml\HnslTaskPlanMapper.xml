<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTaskPlanMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_task_plan a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.taskCode != null">
                AND a.TASK_CODE LIKE CONCAT('%', #{param.taskCode}, '%')
            </if>
            <if test="param.planStatus != null">
                AND a.PLAN_STATUS = #{param.planStatus}
            </if>
            <if test="param.clientNumber != null">
                AND a.CLIENT_NUMBER LIKE CONCAT('%', #{param.clientNumber}, '%')
            </if>
            <if test="param.planDate != null">
                AND a.PLAN_DATE LIKE CONCAT('%', #{param.planDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskPlan">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskPlan">
        <include refid="selectSql"></include>
    </select>

    <select id="queryTaskRestsByTaskCode" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskPlan">
        select b.*,a.RECEIVE_AWARD,
        (SELECT USER_NAME from HNSL_USER where USER_PHONE=b.USER_PHONE) as userName
        from HNSL_TASK_PLAN b RIGHT JOIN  HNSL_TASK_RECEIVE a ON b.USER_PHONE=a.USER_PHONE
        where a.TASK_STATUS_REL=2 AND b.TASK_CODE =#{task} AND a.TASK_CODE =#{task} and  b.PLAN_STATUS=1

    </select>

    <select id="queryTaskImageByTaskCode" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskPlan">
        select a.*,c.receive_award,d.USER_NAME as userName
        ,IF(@pre_name = a.user_phone,@cur_rank := @cur_rank+1, @cur_rank := 1) as rn
        ,@pre_name := a.user_phone
        from
        (select a.*,b.upload_image from HNSL_TASK_PLAN a left join HNSL_TASK_UPLOAD_PICTURES b on  a.client_number=b.upload_code
        where  a.plan_status=1 and b.upload_status=1  and b.upload_type=#{type}) a
        left join HNSL_TASK_RECEIVE c on  a.user_phone=c.user_phone
        left join (select * from HNSL_USER where status=1) d on  a.user_phone=d.user_phone,
        (select @cur_rank :=0,@pre_name :=null) r
            <where>
                <if test="task!=null and task!=''">
                    and a.task_code=#{task} and c.task_code=#{task}
                </if>
            </where>
    </select>

</mapper>
