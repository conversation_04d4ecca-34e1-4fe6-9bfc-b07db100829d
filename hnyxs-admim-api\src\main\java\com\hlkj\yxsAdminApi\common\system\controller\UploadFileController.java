package com.hlkj.yxsAdminApi.common.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.hlkj.yxsAdminApi.common.system.entity.R;
import com.hlkj.yxsAdminApi.common.system.service.UploadFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 文件上传控制层 截至2025-03-12以后都不允许使用【文件存储】的方式
 *
 * @ClassName uploadFileController
 * @<NAME_EMAIL>
 * @Date 2023/4/20 19:25
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class UploadFileController {
	@Resource
	UploadFileService uploadFileService;

	/**
	 * 文件上传 指定上传文件类型,对文件不做任何处理
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-04-19 10:59:32
	 */
	@RequestMapping("/avoid/ceph/uploadFile")
	public R uploadFile(@RequestParam("file") MultipartFile file, @RequestParam("maxSizeKb") Integer maxSizeKb,
			@RequestParam("bucketPhoto") String bucketPhoto) {
			try {
				R r = uploadFileService.uploadFile(file, maxSizeKb, bucketPhoto);
				// 如果上传成功 (code == 0)，处理返回的URL
				if (r != null && r.getInteger("code") != null && r.getInteger("code") == 0) {
					// JSONObject data = r.getJSONObject("data"); // R 本身就是 JSONObject，内部数据可能直接在 R 里，或者在 data 字段里。
                    // 尝试直接从 R 获取 url，如果 data 字段存在且是 JSONObject，再尝试从 data 获取
                    String originalUrl = r.getString("url");
                    if (originalUrl == null && r.getJSONObject("data") != null) {
                         JSONObject dataMap = r.getJSONObject("data");
                         originalUrl = dataMap.getString("url");
                    }

					if (originalUrl != null && originalUrl.startsWith("http://***************:31766")) {
						String newUrl = originalUrl.replace("http://***************:31766", "https://lst.hn.189.cn");
						// 因为 R 继承自 JSONObject，可以直接修改或添加 url 字段
                        // 如果 url 原本就在 R 的顶层，直接修改 
                        if (r.containsKey("url")) {
                            r.put("url", newUrl);
                        } 
                        // 如果 url 在 data 字段里，修改 data 字段里的 url
                        else if (r.getJSONObject("data") != null && r.getJSONObject("data").containsKey("url")) {
                             r.getJSONObject("data").put("url", newUrl);
                        } 
                        // 如果 url 不在顶层也不在 data 里（理论上不应该发生），则添加到顶层
                        else {
                            r.put("url", newUrl);
                        }
					}
				}
				return r; // 返回处理后的结果
			} catch (Exception e) {
				log.error("上传图片异常：", e.getMessage());
				return R.error(1, "上传图片异常：" + e.getMessage());
			}
	}
}