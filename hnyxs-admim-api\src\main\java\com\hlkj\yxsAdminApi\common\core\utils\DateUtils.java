package com.hlkj.yxsAdminApi.common.core.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 日期处理
 *
 * <AUTHOR>
 */
public class DateUtils {
	/** 时间格式(yyyy-MM-dd) */
	public final static String DATE_PATTERN = "yyyy-MM-dd";
	/** 时间格式(yyyy-MM-dd HH:mm:ss) */
	public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
	public static final long ONE_DAY_MILLS = 3600000 * 24;
	public static final int WEEK_DAYS = 7;
	public static final int dateLength = "yyyy-MM-dd HH:mm".length();
	public static final String DATE_YANGIDN = "yyyyMMddHHmmssSSS";
	/** The Constant STRING_DATE_FORMAT. */
	public static final String STRING_DATE_FORMAT = "yyyyMMddHHmmss";
	public static final String thisMonthDate = new SimpleDateFormat("yyyyMM").format(new Date());//当月的日期
	/** The Constant DEFAULT_DATE_FORMAT. */
	public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";


    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     * @param date  日期
     * @return  返回yyyy-MM-dd格式日期
     */
	public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     * @param date  日期
     * @param pattern  格式，如：DateUtils.DATE_TIME_PATTERN
     * @return  返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if(date != null){
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }


    /**
     * 日期转换
     * <AUTHOR>
     * @date 2019年7月29日
     * @version V1.0
     */
    public static Date formatStringToDate(String date, String format) { 
	    SimpleDateFormat sdf = new SimpleDateFormat(format); 
	    try { 
	      return sdf.parse(date); 
	    } catch (ParseException ex) { 
	      throw new RuntimeException(ex.toString()); 
	    } 
	  } 
    
    /**
	 * 获取到天还剩余的秒数
	 * @return
	 */
	public static Long curTimes() {
        Calendar curDate = Calendar.getInstance();
        Calendar nextDayDate = new GregorianCalendar(curDate.get(Calendar.YEAR), curDate.get(Calendar.MONTH), curDate.get(Calendar.DATE)+1, 0, 0, 0);
        Long times = (nextDayDate.getTimeInMillis() - curDate.getTimeInMillis())/1000;
		return times;
    }
    
    /**
     * 字符串转换成日期
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)){
            return null;
        }
        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     * @param week  周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return  返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));
        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     * @param date 日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     * @param date 日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     * @param date 日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     * @param date 日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     * @param date 日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     * @param date 日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }
    
    /** 
	   * 判断一个日期是否属于两个时段内 
	   * @param time 
	   * @param timeRange 
	   * @return 
	   */ 
	  public static boolean isTimeInRange(Date time, Date[] timeRange) { 
	    return (!time.before(timeRange[0]) && !time.after(timeRange[1])); 
	  } 
	 
	  /** 
	   * 从完整的时间截取精确到分的时间 
	   * 
	   * @param fullDateStr 
	   * @return 
	   */ 
	  public static String getDateToMinute(String fullDateStr) { 
	    return fullDateStr == null ? null 
	        : (fullDateStr.length() >= dateLength ? fullDateStr.substring( 
	            0, dateLength) : fullDateStr); 
	  } 
	 
	 
	 
	 
	 
	 
	  public static int getWeeksOfWeekYear(final int year) { 
	    Calendar cal = Calendar.getInstance(); 
	    cal.setFirstDayOfWeek(Calendar.MONDAY); 
	    cal.setMinimalDaysInFirstWeek(WEEK_DAYS); 
	    cal.set(Calendar.YEAR, year); 
	    return cal.getWeeksInWeekYear(); 
	  } 
	 
	  /** 
	   * 获取指定年份的第几周的第几天对应的日期yyyy-MM-dd(从周一开始) 
	   * 
	   * @param year 
	   * @param weekOfYear 
	   * @param dayOfWeek 
	   * @return yyyy-MM-dd 格式的日期 @ 
	   */ 
	  public static String getDateForDayOfWeek(int year, int weekOfYear, 
	      int dayOfWeek) { 
	    return getDateForDayOfWeek(year, weekOfYear, dayOfWeek, Calendar.MONDAY); 
	  } 
	 
	  /** 
	   * 获取指定年份的第几周的第几天对应的日期yyyy-MM-dd，指定周几算一周的第一天（firstDayOfWeek） 
	   * 
	   * @param year 
	   * @param weekOfYear 
	   * @param dayOfWeek 
	   * @param firstDayOfWeek 
	   *      指定周几算一周的第一天 
	   * @return yyyy-MM-dd 格式的日期 
	   */ 
	  public static String getDateForDayOfWeek(int year, int weekOfYear, 
	      int dayOfWeek, int firstDayOfWeek) { 
	    Calendar cal = Calendar.getInstance(); 
	    cal.setFirstDayOfWeek(firstDayOfWeek); 
	    cal.set(Calendar.DAY_OF_WEEK, dayOfWeek); 
	    cal.setMinimalDaysInFirstWeek(WEEK_DAYS); 
	    cal.set(Calendar.YEAR, year); 
	    cal.set(Calendar.WEEK_OF_YEAR, weekOfYear); 
	    return format(cal.getTime(), DATE_PATTERN); 
	  } 
	 
	  /** 
	   * 获取指定日期星期几 
	   * 
	   * @param datetime 
	   * @throws ParseException 
	   *       @ 
	   */ 
	  public static int getWeekOfDate(String datetime) throws ParseException { 
	    Calendar cal = Calendar.getInstance(); 
	    cal.setFirstDayOfWeek(Calendar.MONDAY); 
	    cal.setMinimalDaysInFirstWeek(WEEK_DAYS); 
	    Date date = formatStringToDate(datetime, DATE_PATTERN); 
	    cal.setTime(date); 
	    return cal.get(Calendar.DAY_OF_WEEK); 
	 
	  } 
	 
	  /** 
	   * 计算某年某周内的所有日期(从周一开始 为每周的第一天) 
	   * 
	   * @param yearNum 
	   * @param weekNum 
	   * @return @ 
	   */ 
	  public static List<String> getWeekDays(int yearNum, int weekNum) { 
	    return getWeekDays(yearNum, weekNum, Calendar.MONDAY); 
	  } 
	 
	  /** 
	   * 计算某年某周内的所有日期(七天) 
	   * 
	   * @param yearNum 
	   * @param weekNum 
	   * @return yyyy-MM-dd 格式的日期列表 
	   */ 
	  public static List<String> getWeekDays(int year, int weekOfYear, 
	      int firstDayOfWeek) { 
	    List<String> dates = new ArrayList<String>(); 
	    int dayOfWeek = firstDayOfWeek; 
	    for (int i = 0; i < WEEK_DAYS; i++) { 
	      dates.add(getDateForDayOfWeek(year, weekOfYear, dayOfWeek++, 
	          firstDayOfWeek)); 
	    } 
	    return dates; 
	  } 
	 
	 
	  /** 
	   * 计算个两日期的天数 
	   * 
	   * @param startDate 
	   *      开始日期字串 
	   * @param endDate 
	   *      结束日期字串 
	   * @return 
	   * @throws ParseException 
	   */ 
	  public static int getDaysBetween(String startDate, String endDate) 
	      throws ParseException { 
	    int dayGap = 0; 
	    if (startDate != null && startDate.length() > 0 && endDate != null 
	        && endDate.length() > 0) { 
	      Date end = formatStringToDate(endDate, DATE_PATTERN); 
	      Date start = formatStringToDate(startDate, DATE_PATTERN); 
	      dayGap = getDaysBetween(start, end); 
	    } 
	    return dayGap; 
	  } 
	 
	  private static int getDaysBetween(Date startDate, Date endDate) { 
	    return (int) ((endDate.getTime() - startDate.getTime()) / ONE_DAY_MILLS); 
	  } 
	 
	  /** 
	   * 计算两个日期之间的天数差 
	   * @param startDate 
	   * @param endDate 
	   * @return 
	   */ 
	  public static int getDaysGapOfDates(Date startDate, Date endDate) { 
	    int date = 0; 
	    if (startDate != null && endDate != null) { 
	      date = getDaysBetween(startDate, endDate); 
	    } 
	    return date; 
	  } 
	 
	  /** 
	   * 计算两个日期之间的年份差距 
	   * 
	   * @param firstDate 
	   * @param secondDate 
	   * @return 
	   */ 
	 
	  public static int getYearGapOfDates(Date firstDate, Date secondDate) { 
	    if (firstDate == null || secondDate == null) { 
	      return 0; 
	    } 
	    Calendar helpCalendar = Calendar.getInstance(); 
	    helpCalendar.setTime(firstDate); 
	    int firstYear = helpCalendar.get(Calendar.YEAR); 
	    helpCalendar.setTime(secondDate); 
	    int secondYear = helpCalendar.get(Calendar.YEAR); 
	    return secondYear - firstYear; 
	  } 
	 
	  /** 
	   * 计算两个日期之间的月份差距 
	   * 
	   * @param firstDate 
	   * @param secondDate 
	   * @return 
	   */ 
	  public static int getMonthGapOfDates(Date firstDate, Date secondDate) { 
	    if (firstDate == null || secondDate == null) { 
	      return 0; 
	    } 
	 
	    return (int) ((secondDate.getTime() - firstDate.getTime()) 
	        / ONE_DAY_MILLS / 30); 
	 
	  } 
	 
	  /** 
	   * 计算是否包含当前日期 
	   * 
	   * @param datys 
	   * @return 
	   */ 
	  public static boolean isContainCurrent(List<String> dates) { 
	    boolean flag = false; 
	    SimpleDateFormat fmt = new SimpleDateFormat(DATE_PATTERN); 
	    Date date = new Date(); 
	    String dateStr = fmt.format(date); 
	    for (int i = 0; i < dates.size(); i++) { 
	      if (dateStr.equals(dates.get(i))) { 
	        flag = true; 
	      } 
	    } 
	    return flag; 
	  } 
	 
	  /** 
	   * 从date开始计算time天后的日期 
	   * 
	   * @param date 
	   * @param time 
	   * @return 
	   * @throws ParseException 
	   */ 
	  public static String getCalculateDateToString(String startDate, int time) 
	      throws ParseException { 
	    String resultDate = null; 
	    if (startDate != null && startDate.length() > 0) { 
	      Date date = formatStringToDate(startDate, DATE_PATTERN); 
	      Calendar c = Calendar.getInstance(); 
	      c.setTime(date); 
	      c.set(Calendar.DAY_OF_YEAR, time); 
	      date = c.getTime(); 
	      resultDate = format(date, DATE_PATTERN); 
	    } 
	    return resultDate; 
	  } 
	 
	 
	 
	  /** 
	   * 根据时间点获取时间区间 
	   * 
	   * @param hours 
	   * @return 
	   */ 
	  public static List<String[]> getTimePointsByHour(int[] hours) { 
	    List<String[]> hourPoints = new ArrayList<String[]>(); 
	    String sbStart = ":00:00"; 
	    String sbEnd = ":59:59"; 
	    for (int i = 0; i < hours.length; i++) { 
	      String[] times = new String[2]; 
	      times[0] = hours[i] + sbStart; 
	      times[1] = (hours[(i + 1 + hours.length) % hours.length] - 1) 
	          + sbEnd; 
	      hourPoints.add(times); 
	    } 
	    return hourPoints; 
	  } 
	 
	  /** 
	   * 
	   * 根据指定的日期，增加或者减少天数 
	   * 
	   * @param date 
	   * @param amount 
	   * @return 
	   */ 
	  public static Date addDays(Date date, int amount) { 
	    return add(date, Calendar.DAY_OF_MONTH, amount); 
	  } 
	 
	  /** 
	   * 根据指定的日期，类型，增加或减少数量 
	   * 
	   * @param date 
	   * @param calendarField 
	   * @param amount 
	   * @return 
	   */ 
	  public static Date add(Date date, int calendarField, int amount) { 
	    if (date == null) { 
	      throw new IllegalArgumentException("The date must not be null"); 
	    } 
	    Calendar c = Calendar.getInstance(); 
	    c.setTime(date); 
	    c.add(calendarField, amount); 
	    return c.getTime(); 
	  } 
	 
	  /** 
	   * 获取当前日期的最大日期 时间2014-12-21 23:59:59 
	   * @return 
	   */ 
	  public static Date getCurDateWithMaxTime() { 
	    Calendar c = Calendar.getInstance(); 
	    c.set(Calendar.HOUR_OF_DAY, 23); 
	    c.set(Calendar.MINUTE, 59); 
	    c.set(Calendar.SECOND, 59); 
	    return c.getTime(); 
	  } 
	 
	  /** 
	   * 获取当前日期的最小日期时间 2014-12-21 00:00:00 
	   * @return 
	   */ 
	  public static Date getCurDateWithMinTime() { 
	    Calendar c = Calendar.getInstance(); 
	    c.set(Calendar.HOUR_OF_DAY, 0); 
	    c.set(Calendar.MINUTE, 0); 
	    c.set(Calendar.SECOND, 0); 
	    c.set(Calendar.MILLISECOND, 0); 
	    return c.getTime(); 
	  }

	/**
	 * Description: 根据时间输出符合时间格式的字符串<br>
	 *
	 * @param date
	 *            date
	 * @param format
	 *            format
	 * @return <br>
	 */
	public static String getDateString(Date date, String format) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
		String dateString = simpleDateFormat.format(date);
		return dateString;
	}
}
