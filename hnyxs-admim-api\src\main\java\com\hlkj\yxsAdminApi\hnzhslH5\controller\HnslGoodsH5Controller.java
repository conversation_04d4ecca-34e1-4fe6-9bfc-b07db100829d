package com.hlkj.yxsAdminApi.hnzhslH5.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.config.AwzS3Config;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.*;
import com.hlkj.yxsAdminApi.hnzhslH5.param.HnslGoodsH5Param;
import com.hlkj.yxsAdminApi.hnzhslH5.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 商品表控制器
 **/
@Api(tags = "商品表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoodsH5")
@Transactional(rollbackFor = {Exception.class})
public class HnslGoodsH5Controller extends BaseController {

    @Resource
    private HnslGoodsH5Service hnslGoodsService;
    @Resource
    private HnslGoodsBelongH5Service hnslGoodsBelongService;
    @Resource
    private HnslGoodsRelH5Service hnslGoodsRelService;
    @Resource
    private HnslGoodsLimitH5Service hnslGoodsLimitService;
    @Resource
    private HnslGoodsUpdateRecordH5Service hnslGoodsUpdateRecordH5Service;
    @Resource
    private RedisUtil redisUtils;
    @Autowired
    private AwzS3Config awzS3Config;
    @Autowired
    private ConfigProperties config;


    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsH5:list')")
    @OperationLog
    @ApiOperation("分页查询商品表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslH5Goods>> page(@RequestBody HnslGoodsH5Param param) {
        User loginUser = getLoginUser();
        String hnslType = redisUtils.getString("hnslH5Channel" + loginUser.getPhone());
        if (null != hnslType) {
            param.setHnslType(Integer.valueOf(hnslType));
        }
        return success(hnslGoodsService.pageRel(param));
    }

    @OperationLog
    @ApiOperation("查询全部商品表")
    @PostMapping("/list")
    public ApiResult<List<HnslH5Goods>> list(@RequestBody HnslGoodsH5Param param) {
        PageParam<HnslH5Goods, HnslGoodsH5Param> page = new PageParam<>(param);
        return success(hnslGoodsService.list(page.getOrderWrapper()));
    }

    /**
     * 信息
     */
    @OperationLog
    @ApiOperation("ID查询商品信息")
    @GetMapping("/info/{id}")
    public ApiResult<?> info(@PathVariable("id") String id) {
        logger.info("iD信息查询" + id);
        try {
            HnslH5Goods hnslGoods = hnslGoodsService.queryObject(id);
            if (!(StringUtil.isEmpty(hnslGoods.getMinPrice9()))
                    && !Objects.equals("5", hnslGoods.getGoodsType())) {
                hnslGoods.setSaflType(9);
            }
            return success("hnslGoods", hnslGoods);
        } catch (Exception e) {
            logger.error("查询商品数据异常" + e);
            return fail("查询商品数据异常");
        }
    }

    /**
     * 保存
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsH5:save')")
    @ApiOperation("保存")
    @PostMapping("/decode/save")
    public ApiResult<?> save(@RequestBody HnslH5Goods hnslGoods) {
        logger.info("商品添加入参" + hnslGoods.toString());
        User user = getLoginUser();
        String userName = redisUtils.getString("thisH5UserName" + user.getPhone());
        String userPhone = redisUtils.getString("thisH5UserPhone" + user.getPhone());

        if (StringUtil.isNull(hnslGoods.getGoodsNumber())) {
            return fail("商品编码不能为空");
        }
        if (StringUtil.isNull(hnslGoods.getGoodsName())) {
            return fail("商品名称不能为空");
        } else if (hnslGoods.getGoodsName().length() >= 25) {
            return fail("商品名称字符不能超过25位");
        }
        LambdaQueryWrapper<HnslH5Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslH5Goods::getGoodsNumber, hnslGoods.getGoodsNumber());
        int count = hnslGoodsService.count(queryWrapper);
        if (count > 0) {
            return fail("商品编码已存在不可重复新建");
        }
        if (StringUtil.isEmpty(hnslGoods.getGoodsMianNumber())) {
            return fail("主套餐销售品编码不能为空");
        } else {
            hnslGoods.setGoodsMianNumber(hnslGoods.getGoodsMianNumber().trim());
        }

        String regEx = ".*[-,，].*";
        if (Pattern.matches(regEx, hnslGoods.getGoodsNumber())) {
            return fail("商品编码不允许-,等特殊字符");
        }

        hnslGoods.setCreatedUser(userName);
        hnslGoods.setCreatedDate(new Date());
        BigDecimal prestore = hnslGoods.getPrestore() != null ? new BigDecimal(hnslGoods.getPrestore().toString()) : BigDecimal.ZERO;
        BigDecimal productionPrice = hnslGoods.getProductionPrice() != null ? new BigDecimal(hnslGoods.getProductionPrice().toString()) : BigDecimal.ZERO;
        BigDecimal goodsPrice = prestore.add(productionPrice);
        hnslGoods.setGoodsPrice(goodsPrice.doubleValue());
        hnslGoodsService.save(hnslGoods);
        logger.info("返回ID值" + hnslGoods.getId());
        logger.info((hnslGoods.getSchoolCode() != null) + "");
        if (hnslGoods.getSchoolCode() != null) {
            boolean status = hnslGoods.getSchoolCode().contains(",");
            if (status) {
                HnslH5GoodsBelong gBelong = new HnslH5GoodsBelong();
                gBelong.setCreatedUser(userName);
                gBelong.setGoodsNumber(hnslGoods.getGoodsNumber().trim());
                gBelong.setStatus(1);
                String[] schoolCodes = hnslGoods.getSchoolCode().split(",");
                logger.info("数组" + schoolCodes.length + " 数组值" + Arrays.toString(schoolCodes));
                for (int i = 0; i < schoolCodes.length; i++) {
                    gBelong.setSchoolCode(schoolCodes[i]);
                    hnslGoodsBelongService.save(gBelong);
                }

            }
        }
        HnslH5GoodsRel gRel = new HnslH5GoodsRel();
        gRel.setStatus(1);
        gRel.setGoodsNumber(hnslGoods.getGoodsNumber());
        if (hnslGoods.getMinPrice8() != null && !"".equals(hnslGoods.getMinPrice8())) {//X元预存
            gRel.setGoodsNumberRel(hnslGoods.getMinPrice8().trim());
            gRel.setRelType("7");
            gRel.setGoodsNumberRelMsg(hnslGoods.getText8());
            hnslGoodsRelService.save(gRel);
        }
        if (hnslGoods.getMinPrice1() != null && !"".equals(hnslGoods.getMinPrice1())) {
            gRel.setGoodsNumberRel(hnslGoods.getMinPrice1().trim());
            gRel.setRelType("1");
            gRel.setGoodsNumberRelMsg("");
            hnslGoodsRelService.saveChange(gRel, "MinPrice1");
        }
        if (hnslGoods.getMinPrice3() != null && !"".equals(hnslGoods.getMinPrice3())) {
            gRel.setGoodsNumberRel(hnslGoods.getMinPrice3().trim());
            gRel.setRelType("3");
            gRel.setGoodsNumberRelMsg("");
            hnslGoodsRelService.saveChange(gRel, "MinPrice3");
        }
        if (hnslGoods.getMinPrice10() != null && !"".equals(hnslGoods.getMinPrice10())) { //基本包
            gRel.setGoodsNumberRel(hnslGoods.getMinPrice10().trim());
            gRel.setRelType("9");
            gRel.setGoodsNumberRelMsg("");
            hnslGoodsRelService.saveChange(gRel, "MinPrice10");
        }
        //记录操作
        String updateName = "管理员:{" + userName + "}新增商品:" + hnslGoods.getGoodsName();
        HnslH5GoodsUpdateRecord hnslGoodsUpdateRecordH5 = new HnslH5GoodsUpdateRecord();
        hnslGoodsUpdateRecordH5.setStatus(1L);
        hnslGoodsUpdateRecordH5.setUpdatedDate(new Date());
        hnslGoodsUpdateRecordH5.setUserupdateDetails(updateName);
        hnslGoodsUpdateRecordH5.setUpdatedUser(userPhone);
        hnslGoodsUpdateRecordH5.setGoodsName(hnslGoods.getGoodsName());
        hnslGoodsUpdateRecordH5.setGoodsNumber(hnslGoods.getGoodsNumber());
        hnslGoodsUpdateRecordH5Service.save(hnslGoodsUpdateRecordH5);
        return success();
    }

    /**
     * 修改
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsH5:update')")
    @ApiOperation("修改")
    @PostMapping("/decode/update")
    public ApiResult<?> update(@RequestBody HnslH5Goods hnslGoods) {
        logger.info("修改商品入参" + hnslGoods);
        User user = getLoginUser();
        String userName = redisUtils.getString("thisH5UserName" + user.getPhone());
        String userPhone = redisUtils.getString("thisH5UserPhone" + user.getPhone());
        HnslH5Goods hnslGoodsEntity = hnslGoodsService.getById(hnslGoods.getId());
        //TODO 如果带宽及带宽销售编码都有值 保存套餐类型为号卡新装 方便前台展示套餐
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice9()) && hnslGoods.getSaflType() != 3) {
            hnslGoods.setSaflType(1);
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsMianNumber())) {
            hnslGoods.setGoodsMianNumber(hnslGoods.getGoodsMianNumber().trim());
        }
        if (!StringUtil.isEmpty(hnslGoods.getPrestore())) {
            if (hnslGoods.getGoodsImg() != null && !"".equals(hnslGoods.getGoodsImg())) {
                hnslGoods.setGoodsImg(hnslGoods.getGoodsImg());
            }
            if (hnslGoods.getGoodsDetailsImg() != null && !"".equals(hnslGoods.getGoodsDetailsImg())) {
                hnslGoods.setGoodsDetailsImg(hnslGoods.getGoodsDetailsImg());
            }
            if (hnslGoods.getGoodsServiceUrl() != null && !"".equals(hnslGoods.getGoodsServiceUrl())) {
                hnslGoods.setGoodsServiceUrl(hnslGoods.getGoodsServiceUrl());
            }
        }
        //添加修改时间 修改人
        hnslGoods.setUpdatedUser(userName);
        hnslGoods.setUpdatedDate(new Date());
        BigDecimal prestore = hnslGoods.getPrestore() != null ? new BigDecimal(hnslGoods.getPrestore().toString()) : BigDecimal.ZERO;
        BigDecimal productionPrice = hnslGoods.getProductionPrice() != null ? new BigDecimal(hnslGoods.getProductionPrice().toString()) : BigDecimal.ZERO;
        BigDecimal goodsPrice = prestore.add(productionPrice);
        hnslGoods.setGoodsPrice(goodsPrice.doubleValue());
        hnslGoodsService.updateById(hnslGoods);
        if (hnslGoods.getSchoolCode() != null) {
            boolean status = hnslGoods.getSchoolCode().contains(",");
            boolean statusCopy = hnslGoods.getSchoolCodes().contains(",");
            HnslH5GoodsBelong gBelong = new HnslH5GoodsBelong();
            gBelong.setGoodsNumber(hnslGoods.getGoodsNumber());
            if (status) {
                gBelong.setCreatedUser(userName);
                gBelong.setStatus(1);
                String[] schoolCodes = hnslGoods.getSchoolCode().split(",");
                if (statusCopy) {
                    if (!hnslGoods.getSchoolCode().equals(hnslGoods.getSchoolCodes())) {
                        String[] schoolCodeCopy = hnslGoods.getSchoolCodes().split(",");
                        List<String> schoolCodeList = new ArrayList<>();
                        //删除判定循环
                        for (int i = 0; i < schoolCodeCopy.length; i++) {
                            boolean judge = true;
                            for (int j = 0; j < schoolCodes.length; j++) {
                                if (schoolCodeCopy[i].equals(schoolCodes[j])) {
                                    //已存在无需改动
                                    judge = false;
                                    schoolCodeList.add(schoolCodes[j]);
                                }
                            }
                            if (judge) {
                                gBelong.setSchoolCode(schoolCodeCopy[i]);
//                                gBelong.setStatus(0);
                                hnslGoodsBelongService.updateBygoodsNumber(gBelong);
                            }
                        }
                        //添加判定循环
                        for (int i = 0; i < schoolCodes.length; i++) {
                            boolean judge = true;
                            for (String s : schoolCodeList) {
                                if (schoolCodes[i].equals(s)) {
                                    judge = false;
                                }
                            }
                            if (judge) {
                                gBelong.setSchoolCode(schoolCodes[i]);
                                hnslGoodsBelongService.save(gBelong);
                            }
                        }
                    }
                } else {
                    String code = hnslGoods.getSchoolCodes();
                    for (int j = 0; j < schoolCodes.length; j++) {
                        if (!schoolCodes[j].equals(code)) {
                            gBelong.setSchoolCode(schoolCodes[j]);
                            hnslGoodsBelongService.save(gBelong);
                        }
                    }
                }
            } else {
//                gBelong.setStatus(0);
                hnslGoodsBelongService.updateById(gBelong);
            }
        }

        LambdaQueryWrapper<HnslH5GoodsRel> relLambdaQueryWrapper = new LambdaQueryWrapper<>();
        relLambdaQueryWrapper.eq(HnslH5GoodsRel::getGoodsNumber, hnslGoods.getGoodsNumber())
                .eq(HnslH5GoodsRel::getStatus, 1);
        int count = hnslGoodsRelService.count(relLambdaQueryWrapper);
        if (count >= 1) {
            boolean remove = hnslGoodsRelService.remove(relLambdaQueryWrapper);
            if (!remove) {
                return fail("删除关联商品失败!");
            }
        }

        HnslH5GoodsRel gRel = new HnslH5GoodsRel();
        gRel.setStatus(1);
        gRel.setGoodsNumber(hnslGoods.getGoodsNumber());
        gRel.setGoodsNumberRelMsg("");
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice1())) {
            String[] minPrice = hnslGoods.getMinPrice1().trim().split(",");
            gRel.setRelType("1");
            for (int i = 0; i < minPrice.length; i++) {
                String[] numberRels = minPrice[i].split("-");
                gRel.setGoodsNumberRel(numberRels[0]);
                hnslGoodsRelService.save(gRel);
            }
        }

        if (!StringUtil.isEmpty(hnslGoods.getMinPrice3())) {
            String[] minPrice = hnslGoods.getMinPrice3().trim().split(",");
            gRel.setRelType("3");
            for (int i = 0; i < minPrice.length; i++) {
                String[] numberRels = minPrice[i].split("-");
                gRel.setGoodsNumberRel(numberRels[0]);
                hnslGoodsRelService.save(gRel);
            }
        }

        if (!StringUtil.isEmpty(hnslGoods.getMinPrice8())) {
            gRel.setRelType("7");
            gRel.setGoodsNumberRel(hnslGoods.getMinPrice8().trim());
            gRel.setGoodsNumberRelMsg(hnslGoods.getText8());
            hnslGoodsRelService.save(gRel);

        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice10())) {
            gRel.setRelType("9");
            gRel.setGoodsNumberRelMsg("");
            boolean MinPrice10 = hnslGoods.getMinPrice10().contains(",");
            if (MinPrice10) {
                String[] minPrice = hnslGoods.getMinPrice10().trim().split(",");
                for (int i = 0; i < minPrice.length; i++) {
                    gRel.setGoodsNumberRel(minPrice[i]);
                    hnslGoodsRelService.save(gRel);
                }
            } else {
                gRel.setGoodsNumberRel(hnslGoods.getMinPrice10().trim());
                hnslGoodsRelService.save(gRel);
            }
        }
        //记录操作
        extracted(hnslGoods, userName, hnslGoodsEntity, userPhone);
        return success();
    }

    private void extracted(HnslH5Goods hnslGoods, String userName, HnslH5Goods hnslGoodsEntity, String userPhone) {
        String updateName = "管理员:{" + userName + "}修改:";

        if (hnslGoods.getSaflType() != (hnslGoodsEntity.getSaflType())) {
            updateName += "原商品类型{" + hnslGoodsEntity.getSaflType() + "}修改为{" + hnslGoods.getSaflType() + "];";
        }

        if (!StringUtil.isEmpty(hnslGoods.getGoodsName())) {
            if (!hnslGoods.getGoodsName().equals(hnslGoodsEntity.getGoodsName())) {
                updateName += "原产品名称{" + hnslGoodsEntity.getGoodsName() + "}修改为{" + hnslGoods.getGoodsName() + "];";
            }
        }

        if (hnslGoods.getGoodsPackageType() != (hnslGoodsEntity.getGoodsPackageType())) {
            updateName += "原套餐类型{" + hnslGoodsEntity.getGoodsPackageType() + "}修改为{" + hnslGoods.getGoodsPackageType() + "];";
        }

        if (!StringUtil.isEmpty(hnslGoods.getCpsList())) {
            if (!hnslGoods.getCpsList().equals(hnslGoodsEntity.getCpsList())) {
                updateName += "原CPS{" + hnslGoodsEntity.getCpsList() + "}修改为{" + hnslGoods.getCpsList() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsNumber())) {
            if (!hnslGoods.getGoodsNumber().equals(hnslGoodsEntity.getGoodsNumber())) {
                updateName += "原产品编码{" + hnslGoodsEntity.getGoodsNumber() + "}修改为{" + hnslGoods.getGoodsNumber() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsMianNumber())) {
            if (!hnslGoods.getGoodsMianNumber().equals(hnslGoodsEntity.getGoodsMianNumber())) {
                updateName += "原主套餐编码{" + hnslGoodsEntity.getGoodsMianNumber() + "}修改为{" + hnslGoods.getGoodsMianNumber() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsType())) {
            if (!hnslGoods.getGoodsType().equals(hnslGoodsEntity.getGoodsType())) {
                updateName += "原加装类型{" + hnslGoodsEntity.getGoodsType() + "}修改为{" + hnslGoods.getGoodsType() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getPrestore())) {
            if (!hnslGoods.getPrestore().equals(hnslGoodsEntity.getPrestore())) {
                updateName += "原预存款{" + hnslGoodsEntity.getPrestore() + "}修改为{" + hnslGoods.getPrestore() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsType())) {
            if (!hnslGoods.getGoodsType().equals(hnslGoodsEntity.getGoodsType())) {
                updateName += "原加装类型{" + hnslGoodsEntity.getGoodsType() + "}修改为{" + hnslGoods.getGoodsType() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getSchoolCode())) {
            if (!hnslGoods.getSchoolCode().equals(hnslGoodsEntity.getSchoolCode())) {
                updateName += "原所属学校{" + hnslGoodsEntity.getSchoolCode() + "}修改为{" + hnslGoods.getSchoolCode() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice())) {
            if (!hnslGoods.getMinPrice().equals(hnslGoodsEntity.getMinPrice())) {
                updateName += "原保底消费{" + hnslGoodsEntity.getMinPrice() + "}修改为{" + hnslGoods.getMinPrice() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice9())) {
            if (!hnslGoods.getMinPrice9().equals(hnslGoodsEntity.getMinPrice9())) {
                updateName += "原带宽销售品编码{" + hnslGoodsEntity.getMinPrice9() + "}修改为{" + hnslGoods.getMinPrice9() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice1())) {
            if (!hnslGoods.getMinPrice1().equals(hnslGoodsEntity.getMinPrice1())) {
                updateName += "原可选包销售品编码{" + hnslGoodsEntity.getMinPrice1() + "}修改为{" + hnslGoods.getMinPrice1() + "];";
            }
        }
        // 这一段有个问题，hnslGoods.getMinPrice1().split(",") 如果只有一条就没有以,隔开，导致会报错
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice2())) {
            if (!hnslGoods.getMinPrice2().equals(hnslGoodsEntity.getMinPrice2())) {
                updateName += "原宽带可选包编码{" + hnslGoodsEntity.getMinPrice2() + "}修改为{" + hnslGoods.getMinPrice2() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice3())) {
            if (!hnslGoods.getMinPrice3().equals(hnslGoodsEntity.getMinPrice3())) {
                updateName += "原叠加包销售品编码{" + hnslGoodsEntity.getMinPrice3() + "}修改为{" + hnslGoods.getMinPrice3() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice8())) {
            if (!hnslGoods.getMinPrice8().equals(hnslGoodsEntity.getMinPrice8())) {
                updateName += "原X元预存编码{" + hnslGoodsEntity.getMinPrice8() + "}修改为{" + hnslGoods.getMinPrice8() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getText8())) {
            if (!hnslGoods.getText8().equals(hnslGoodsEntity.getText8())) {
                updateName += "原预存说明{" + hnslGoodsEntity.getText8() + "}修改为{" + hnslGoods.getText8() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getMinPrice10())) {
            if (!hnslGoods.getMinPrice10().equals(hnslGoodsEntity.getMinPrice10())) {
                updateName += "原基础包销售品编码{" + hnslGoodsEntity.getMinPrice10() + "}修改为{" + hnslGoods.getMinPrice10() + "];";
            }
        }

        if (Objects.equals(hnslGoods.getGoodsPrice(), hnslGoodsEntity.getGoodsPrice()) && null != hnslGoods.getGoodsPrice()) {
            updateName += "原预存包价格{" + hnslGoodsEntity.getGoodsPrice() + "}修改为{" + hnslGoods.getGoodsPrice() + "];";
        }

        if (!StringUtil.isEmpty(hnslGoods.getTransactionNum())) {
            if (!hnslGoods.getTransactionNum().equals(hnslGoodsEntity.getTransactionNum())) {
                updateName += "原可办理数量{" + hnslGoodsEntity.getTransactionNum() + "}修改为{" + hnslGoods.getTransactionNum() + "];";
            }
        }

        if (hnslGoods.getMinAge() != (hnslGoodsEntity.getMinAge())) {
            updateName += "原最小年龄限制{" + hnslGoodsEntity.getMinAge() + "}修改为{" + hnslGoods.getMinAge() + "];";
        }


        if (hnslGoods.getMaxAge() != (hnslGoodsEntity.getMaxAge())) {
            updateName += "原最大年龄限制{" + hnslGoodsEntity.getMaxAge() + "}修改为{" + hnslGoods.getMaxAge() + "];";
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsType())) {
            if (!hnslGoods.getGoodsType().equals(hnslGoodsEntity.getGoodsType())) {
                updateName += "原业务类型{" + hnslGoodsEntity.getGoodsType() + "}修改为{" + hnslGoods.getGoodsType() + "];";
            }
        }
        if (hnslGoods.getGoodsPosterImg() != null && hnslGoods.getGoodsPosterImg().length == 1 && !StringUtil.isEmpty(hnslGoods.getGoodsPosterImg()[0])) {
            if (!hnslGoods.getGoodsPosterImg()[0].startsWith("uploads")) {
                updateName += "修改分享海报1为{" + hnslGoods.getGoodsPosterImg()[0] + "}";
            }
        }
        if (hnslGoods.getGoodsPosterImg() != null && hnslGoods.getGoodsPosterImg().length == 2 && !StringUtil.isEmpty(hnslGoods.getGoodsPosterImg()[1])) {
            if (!hnslGoods.getGoodsPosterImg()[1].startsWith("uploads")) {
                updateName += "修改分享海报2为{" + hnslGoods.getGoodsPosterImg()[1] + "}";
            }
        }
        if (hnslGoods.getGoodsPosterImg() != null && hnslGoods.getGoodsPosterImg().length == 3 && !StringUtil.isEmpty(hnslGoods.getGoodsPosterImg()[2])) {
            if (!hnslGoods.getGoodsPosterImg()[2].startsWith("uploads")) {
                updateName += "修改分享海报3为{" + hnslGoods.getGoodsPosterImg()[2] + "}";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsImg())) {
            if (!hnslGoods.getGoodsImg().equals(hnslGoodsEntity.getGoodsImg())) {
                updateName += "原橱窗图{" + hnslGoodsEntity.getGoodsImg() + "}修改为{" + hnslGoods.getGoodsImg() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsDetailsImg())) {
            if (!hnslGoods.getGoodsDetailsImg().equals(hnslGoodsEntity.getGoodsDetailsImg())) {
                updateName += "原套餐详情图{" + hnslGoodsEntity.getGoodsDetailsImg() + "}修改为{" + hnslGoods.getGoodsDetailsImg() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getGoodsDetalt())) {
            if (!hnslGoods.getGoodsDetalt().equals(hnslGoodsEntity.getGoodsDetalt())) {
                updateName += "原商品描述{" + hnslGoodsEntity.getGoodsDetalt() + "}修改为{" + hnslGoods.getGoodsDetalt() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getRemark())) {
            if (!hnslGoods.getRemark().equals(hnslGoodsEntity.getRemark())) {
                updateName += "原备注{" + hnslGoodsEntity.getRemark() + "}修改为{" + hnslGoods.getRemark() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslGoods.getOrderNumber())) {
            if (!hnslGoods.getOrderNumber().equals(hnslGoodsEntity.getOrderNumber())) {
                updateName += "原备注{" + hnslGoodsEntity.getOrderNumber() + "}修改为{" + hnslGoods.getOrderNumber() + "];";
            }
        }
        HnslH5GoodsUpdateRecord hnslGoodsUpdateRecordH5 = new HnslH5GoodsUpdateRecord();
        hnslGoodsUpdateRecordH5.setStatus(1L);
        hnslGoodsUpdateRecordH5.setUpdatedDate(new Date());
        hnslGoodsUpdateRecordH5.setUserupdateDetails(updateName);
        hnslGoodsUpdateRecordH5.setUpdatedUser(userPhone);
        hnslGoodsUpdateRecordH5.setGoodsName(hnslGoods.getGoodsName());
        hnslGoodsUpdateRecordH5.setGoodsNumber(hnslGoods.getGoodsNumber());
        hnslGoodsUpdateRecordH5Service.save(hnslGoodsUpdateRecordH5);
    }

    // 逐个修改套餐关系 先删后加
    public void commonRel(HnslH5GoodsRel gRel, String text1, String textCopy, String id) {
        boolean status = text1.contains(",");
        boolean statusCopy = textCopy.contains(",");
        if (status) {
            String[] min1s = text1.split(",");

            if (statusCopy) {
                if (!text1.equals(textCopy)) {
                    String[] min1Copy = textCopy.split(",");
                    List<String> schoolCodeList = new ArrayList<>();
                    //删除判定循环
                    for (int i = 0; i < min1Copy.length; i++) {
                        boolean judge = true;
                        for (int j = 0; j < min1s.length; j++) {
                            if (min1Copy[i].equals(min1s[j])) {
                                //已存在无需改动
                                judge = false;
                                schoolCodeList.add(min1s[j]);
                            }
                        }
                        if (judge) {
                            gRel.setGoodsNumberRel(min1Copy[i]);
                            gRel.setStatus(0);
                            hnslGoodsRelService.updateByGoodsNumber(gRel);
                        }
                    }
                    //添加判定循环
                    for (int i = 0; i < min1s.length; i++) {
                        boolean judge = true;
                        for (String s : schoolCodeList) {
                            if (min1s[i].equals(s)) {
                                judge = false;
                            }
                        }
                        if (judge) {
                            gRel.setGoodsNumberRel(min1s[i]);
                            gRel.setStatus(1);
                            hnslGoodsRelService.save(gRel);
                        }
                    }
                }
            } else {
                String code = textCopy;
                for (int j = 0; j < min1s.length; j++) {
                    if (!min1s[j].equals(code)) {
                        gRel.setGoodsNumberRel(min1s[j]);
                        gRel.setStatus(1);
                        hnslGoodsRelService.save(gRel);
                    }
                }
            }
        } else {
            if (text1 != null && !"".equals(text1)) {
                String text2 = text1 + ",";
                if (!text2.equals(textCopy) || !text1.equals(textCopy)) {  // 双重判断待测试
                    if (textCopy != null && !"".equals(textCopy)) {
                        if (statusCopy) {
                            String[] min1Copy = textCopy.split(",");
                            List<String> schoolCodeList = new ArrayList<>();
                            //删除判定循环
                            boolean judgeAdd = false;
                            for (int i = 0; i < min1Copy.length; i++) {
                                boolean judge = true;
                                if (min1Copy[i].equals(text1)) {
                                    //已存在无需改动
                                    judge = false;
                                    judgeAdd = true;
                                }
                                if (judge) {
                                    gRel.setGoodsNumberRel(min1Copy[i]);
                                    gRel.setStatus(0);
                                    hnslGoodsRelService.updateByGoodsNumber(gRel);
                                }
                            }
                            if (judgeAdd) {
                                gRel.setGoodsNumberRel(text1);
                                gRel.setStatus(1);
                                hnslGoodsRelService.save(gRel);
                            }
                        }
                    } else {
                        gRel.setGoodsNumberRel(text1);
                        gRel.setStatus(1);
                        hnslGoodsRelService.save(gRel);
                    }
                }
            } else {
                gRel.setStatus(0);
                hnslGoodsRelService.updateByGoodsNumber(gRel);
            }
        }
    }

    public static boolean areArraysEqualIgnoringOrder(String[] array1, String[] array2) {
        // Convert arrays to sets
        Set<String> set1 = new HashSet<>(Arrays.asList(array1));
        Set<String> set2 = new HashSet<>(Arrays.asList(array2));

        // Compare the sets
        return set1.equals(set2);
    }

    /**
     * 删除
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsH5:delete')")
    @ApiOperation("修改")
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody String[] ids) {
        hnslGoodsService.deleteBatch(ids);
        return success();
    }

    /**
     * 学校下的楼栋信息
     */
    @ApiOperation("学校下的楼栋信息")
    @PostMapping("/schoolList")
    public ApiResult<?> schoolList() {
        User user = getLoginUser();
        String UserSf = redisUtils.getString("thisH5UserSf" + user.getPhone());
        logger.info("用户等级权限:" + UserSf);
        HnslH5User userPojo = new HnslH5User();
        String hnslType = redisUtils.getString("hnslH5Channel" + user.getPhone());
        userPojo.setHnslChannel(Integer.valueOf(hnslType));
        if ("1".equals(UserSf)) { //校园经理
            userPojo.setUserPhone(user.getUsername());
        }
        if ("6".equals(UserSf)) { //地市管理员
            String cityCode = redisUtils.getString("cityCodeH5" + user.getPhone());
            userPojo.setCityCode(cityCode);
        }
        List<HnslH5School> hnslGoodsList = hnslGoodsService.queryListSchoolBy(userPojo);

        return success("hnslGoodsList", hnslGoodsList);
    }

    /**
     * 上传商品图片
     */
    @PostMapping("/decode/uploadImage")
    public ApiResult<?> uploadImage(@RequestBody JSONObject reqData) {
        String base64Image = reqData.getString("base64Image");
        try {
            if (StringUtils.isBlank(base64Image)) {
                return fail("上传图片为空，请重新上传");
            }
            // 获取图片类型
            String imageType = FileUtil.getBase64ImageType(base64Image);
            // 随机生成图片名称
            String imageName = IdUtil.fastSimpleUUID();
            // 文件类型拦截
            String[] allowedFileTypes = {"jpg", "jpeg", "png"};
            if (!Arrays.asList(allowedFileTypes).contains(imageType)) {
                return fail("不支持的文件类型！");
            }

            // 获取桶名称按月份生成
            String bucketPhotoTop = DateUtils.thisMonthDate;
            String bucketName = ConstantUtil.BUCKET_NAME_KEY + "/" + imageName + "." + imageType;

            // 检查桶是否存在
            boolean isBucketExist = awzS3Config.getAmazon().doesBucketExistV2(bucketPhotoTop);
            if (!isBucketExist) {
                try {
                    awzS3Config.getAmazon().createBucket(bucketPhotoTop);
                    logger.info("扫楼H5项目-创建桶成功" + bucketPhotoTop);
                } catch (AmazonS3Exception e) {
                    logger.error("扫楼H5项目-创建桶失败 " + bucketPhotoTop + ": " + e.getMessage());
                    return fail("扫楼H5项目-创建桶失败：" + e.getMessage());
                }
            }

            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));

            // 水印文字信息
            JSONArray watermarkTextArray = new JSONArray();
            watermarkTextArray.add(imageName);

            // 压缩并添加水印
            BufferedImage image = FileUtil.compressAndAddWatermark(originalImage, imageType, 300, watermarkTextArray, imageBytes);
            // 保存到ceph
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(imageType);
            objectMetadata.setContentLength(new ByteArrayInputStream(FileUtil.getBytesFromBufferedImage(image, imageType)).available());
            PutObjectResult result = awzS3Config.getAmazon().putObject(bucketPhotoTop, bucketName,
                    new ByteArrayInputStream(FileUtil.getBytesFromBufferedImage(image, imageType)), objectMetadata);
            logger.info("扫楼H5保存图片到ceph返回结果：", result);
            // 上传成功后的返回结果
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("url", "/download/" + bucketPhotoTop + "/" + bucketName);
            resultMap.put("path", bucketPhotoTop + "/" + bucketName);
            resultMap.put("name", imageName + "." + imageType);
            return success(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("上传图片异常：" + e.getMessage());
            return fail("上传图片异常:" + e.getMessage());
        }
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsH5:importUser')")
    @OperationLog
    @ApiOperation("导入加装限制编码")
    @PostMapping("/decode/importAstrictCode")
    public ApiResult<?> importAstrictCode(MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        User loginUser = getLoginUser();
        String userName = loginUser.getUsername();

        JSONObject result = new JSONObject();
        String image = "xls,xlsx";

        if (!file.isEmpty()) {
            String uploadPath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 1, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 1, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                if (numberList != null) {
                    //将数据转化成user对象
                    List<HnslH5GoodsLimit> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslH5GoodsLimit listPojo = new HnslH5GoodsLimit();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //销售品编码
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                listPojo.setGoodsNumber(StringUtil.trimString(userMap.get(name)));
                            }
                            //限制类型 1:生卡数量限制
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                listPojo.setLimitType(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
                            }
                            //对应限制下单数量
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                listPojo.setLimitOrderCount(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
                            }
                            //学校编码
                            else if ("3".equalsIgnoreCase(name.trim())) {
                                listPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
                            }
                        }
                        listPojo.setCreatedDate(new Date());
                        listPojo.setStatus(1);
                        listPojo.setCreatedUser(userName);
                        hnslUserList.add(listPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslGoodsLimitService.saveUserArray(hnslUserList, request);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            HashMap<Object, Object> r = new HashMap<>();
                            r.put("mes", "exportDaoUser");
                            r.put("resultCode", "6");
                            r.put("fileName", saveUserArray.get("fileName"));
                            return success(r);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        logger.error("批量插入用户信息失败:" + e);
                        return success("resultCode", 5);
                    }
                } else {
                    logger.info("文件内容为空，或者解析失败");
                    result.put("resultCode", "4");
                    return success("resultCode", 4);
                }
                HashMap<Object, Object> r = new HashMap<>();
                r.put("mes", "exportDaoUser");
                r.put("resultCode", 0);
                r.put("fileName", "受理销售品限制导入表");
                return success(r);
            } catch (Exception e) {
                logger.error("文件上传接口上传异常:" + e);
                return success("resultCode", 3);
            }
        } else {
            logger.info("上传文件为空");
            return success("resultCode", 3);
        }
    }


    /**
     * 下载导入情况文件
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/exportDaoUser")
    public void exportDaoUser(HttpServletResponse response, HttpServletRequest request) throws Exception {

        String parameter = request.getParameter("fileName");
        String name = request.getParameter("name");
        //下载
        String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName = parameter + ".xls".toString(); // 文件的默认保存名
        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inStream.close();
            file.delete();
        }
    }

    /**
     * 复制商品
     *
     * @throws Exception
     */
    @OperationLog
    @ApiOperation("复制商品")
    @PostMapping("/copy/{id}")
    public ApiResult<?> copy(@PathVariable("id") String id) {
        HnslH5Goods h5Goods = hnslGoodsService.queryObject(id);
        String number = h5Goods.getGoodsNumber();
        String[] split = number.split("_");
        if (split.length == 1) {
            h5Goods.setGoodsNumber(split[0] + "_" + split.length);
        } else {
            h5Goods.setGoodsNumber(split[0] + "_" + Integer.valueOf(split[1]) + 1);
        }
        h5Goods.setId(null);
        return save(h5Goods);
    }

    /**
     * 修改商品信息
     *
     * @param hnslGoods
     * @return
     */
    @OperationLog
    @ApiOperation("修改商品信息")
    @PostMapping("/decode/updateInfo")
    public ApiResult<?> updateInfo(@RequestBody HnslH5Goods hnslGoods) {
        User loginUser = getLoginUser();
        hnslGoods.setUpdatedUser(loginUser.getUsername());
        hnslGoods.setUpdatedDate(new Date());
        UpdateWrapper<HnslH5Goods> hnslGoodsUpdateWrapper = new UpdateWrapper<>();
        hnslGoodsUpdateWrapper.set("status", hnslGoods.getStatus())
                .eq("id", hnslGoods.getId());
        if (hnslGoodsService.update(hnslGoodsUpdateWrapper)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除商品
     *
     * @param id
     * @return
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsH5:remove')")
    @OperationLog
    @ApiOperation("删除商品")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") String id) {
        HnslH5Goods h5Goods = hnslGoodsService.getById(id);
        if (!ObjectUtils.isEmpty(h5Goods)) {
            LambdaQueryWrapper<HnslH5GoodsRel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HnslH5GoodsRel::getGoodsNumber, h5Goods.getGoodsNumber());
           hnslGoodsRelService.remove(queryWrapper);

            LambdaQueryWrapper<HnslH5GoodsBelong> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(HnslH5GoodsBelong::getGoodsNumber, h5Goods.getGoodsNumber());
            boolean remove = hnslGoodsBelongService.remove(queryWrapper1);
            if (remove) {
                if (hnslGoodsService.removeById(id)) {
                    return success("删除成功");
                }
            }
        }
        return fail("删除失败");
    }
}
