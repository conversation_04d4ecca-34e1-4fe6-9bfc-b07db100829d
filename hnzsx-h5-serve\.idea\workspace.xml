<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2b38faaa-3e95-4885-a069-ffe2994c0132" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/common/controller/InterfaceController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/common/controller/InterfaceController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/hnzsxh5_homepage/controller/HomepageController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/hnzsxh5_homepage/controller/HomepageController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/hnzsxh5_homepage/service/HomepageService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/hnzsxh5_homepage/service/HomepageService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/hnzsxh5_homepage/service/impl/HomepageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/modules/hnzsxh5_homepage/service/impl/HomepageServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/util/InterfaceUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/hnzsxh5/util/InterfaceUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/homepage/HomepageMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/homepage/HomepageMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="srd19176679047/lyx" />
                    <option name="lastUsedInstant" value="1753839195" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1753839194" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30ZddHK9e5tte6IaR6HMyoiYEms" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.HnzsxH5ServeApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;srd19176679047/lyx&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/code/dianxinCode/智慧扫楼（旧版）&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="HnzsxH5ServeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="hnzsx-h5-serve" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hlkj.hnzsxh5.HnzsxH5ServeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2b38faaa-3e95-4885-a069-ffe2994c0132" name="更改" comment="" />
      <created>1753839115727</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753839115727</updated>
      <workItem from="1753839118600" duration="11627000" />
      <workItem from="1753922393573" duration="11576000" />
      <workItem from="1753949979609" duration="1662000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>