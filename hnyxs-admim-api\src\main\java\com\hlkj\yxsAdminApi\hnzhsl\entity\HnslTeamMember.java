package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 团队成员表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTeamMember对象", description = "团队成员表")
public class HnslTeamMember implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合伙人编码")
    @TableField("USER_CODE")
    private String userCode;

    @ApiModelProperty(value = "团队编码")
    @TableField("TEAM_CODE")
    private String teamCode;

    @ApiModelProperty(value = "团队身份(1:成员 2：一级团队长 3:二级团队长 4：普通团队长 5:核心团队长 )")
    @TableField("TEAM_IDENTITY")
    private Integer teamIdentity;

    @ApiModelProperty(value = "推荐人编码")
    @TableField("TEAM_REFERRER")
    private String teamReferrer;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "团队地位(1:成员 2:团队长 )")
    @TableField("TEAM_IDENTITY_LEVEL")
    private Integer teamIdentityLevel;

}
