package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApply;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprove;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteApproveParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteApplyService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteApproveService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 白名单审批列表
 * @date 2024-10-12
 */
@RestController
@RequestMapping("/api/hnzhsl/hnslwhiteapprove")
public class HnslWhiteApproveController extends BaseController {
    @Autowired
    private HnslWhiteApproveService hnslWhiteApproveService;

    @Autowired
    private HnslWhiteListService hnslWhiteListService;

    @Autowired
    private HnslWhiteApplyService hnslWhiteApplyService;

    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    AmazonS3 amazonS3;

    @Autowired
    private ConfigProperties config;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapprove:list')")
    public ApiResult<PageResult<HnslWhiteApprove>> list(@RequestBody HnslWhiteApproveParam param) {
        //查询列表数据
        User user = getLoginUser();
        String hnslType = redisUtils.getString("hnslChannel" + user.getPhone());
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        String userName = redisUtils.getString("thisUserName" + user.getPhone());
        param.setHnslType(hnslType);
        param.setQueryCityCode(cityCode);
        param.setUserName(userName);
        param.setQueryType(statusSf);
        return success(hnslWhiteApproveService.pageRel(param));
    }


    /**
     * 保存
     */
    @RequestMapping("/save")
    @PreAuthorize("hasAuthority('hnzhsl:hnslwhiteapprove:save')")
    public ApiResult<?> save(@RequestBody HnslWhiteApprove hnslWhiteApprove){
        hnslWhiteApproveService.save(hnslWhiteApprove);
        return success();
    }

    /**
     * 审批通过
     */
    @RequestMapping("/apply/{id}")
    public ApiResult<?> apply(@PathVariable("id") Long id, HttpServletResponse response, HttpServletRequest request) throws Exception{
        User user = getLoginUser();
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String userName = redisUtils.getString("thisUserName" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        String UserPhone = redisUtils.getString("thisUserPhone" + user.getPhone());
        if (!"5".equals(statusSf)) {
            return fail("对不起，您没有该权限操作");
        }
        HnslWhiteApprove whiteApprove = hnslWhiteApproveService.queryObject(UserPhone, userName, id);
        if (null == whiteApprove) {
            return fail("查询审批记录为空");
        }

        // 审批通过后将上传的白名单列表新增入库
        HnslWhiteApply hnslWhiteApplyEntity = hnslWhiteApplyService.getById(whiteApprove.getApplyId());
        String key = hnslWhiteApplyEntity.getFileName();

        S3Object s3Object = amazonS3.getObject(ConstantUtil.BUCKET_WHITE_NAME, key);
        S3ObjectInputStream inputStream = s3Object.getObjectContent();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));

        JSONObject result = new JSONObject();
        List<Map<String, String>> numberList = null;

        try {
            numberList = ObjectExcelRead.readWhiteExcelXls(inputStream, 1, 0, 0);

            if (numberList != null && numberList.size()!=0) {
                //将数据转化成user对象
                List<HnslWhiteList> hnslUserList = new ArrayList<>();
                for (int i = 0; i < numberList.size(); i++) {
                    HnslWhiteList listPojo = new HnslWhiteList();
                    Map<String, String> userMap = numberList.get(i);
                    Set<String> nameset = userMap.keySet();
                    Iterator<String> namelist = nameset.iterator();
                    while (namelist.hasNext()) {
                        String name = namelist.next();
                        if (StringUtil.isEmpty(name)) {
                            continue;
                        }
                        //校园渠道-必填（1 高校/ 2 中小学）
                        else if ("0".equalsIgnoreCase(name.trim())) {
                            if (Objects.equals("高校", StringUtil.trimString(userMap.get(name)))) {
                                listPojo.setSchoolChannel(1);
                            } else if (Objects.equals("中小学", StringUtil.trimString(userMap.get(name)))) {
                                listPojo.setSchoolChannel(2);
                            } else {
                                listPojo.setSchoolChannel(0);
                            }
                        }
                        //地市编码-必填
                        else if ("1".equalsIgnoreCase(name.trim())) {
                            listPojo.setCityCode(StringUtil.trimString(userMap.get(name)));
                        }
                        //学校编码-必填
                        else if ("2".equalsIgnoreCase(name.trim())) {
                            listPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
                        }
                        //学校名称-必填
                        else if ("3".equalsIgnoreCase(name.trim())) {
                            listPojo.setSchoolName(StringUtil.trimString(userMap.get(name)));
                        }
                        //熟卡号码-必填
                        else if ("4".equalsIgnoreCase(name.trim())) {
                            listPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                        }
                        //使用者姓名-非必填
                        else if ("5".equalsIgnoreCase(name.trim())) {
                            listPojo.setCustomerName(StringUtil.trimString(userMap.get(name)));
                        }
                        //使用者-身份证-非必填
                        else if ("6".equalsIgnoreCase(name.trim())) {
                            String sfz = StringUtil.trimString(userMap.get(name));
                            if (StringUtil.isNotNull(sfz)) {
                                sfz = sfz.toUpperCase();
                            }
                            listPojo.setCustomerCard(sfz);
                        } else if ("7".equalsIgnoreCase(name.trim())) {
                            listPojo.setRemarks(StringUtil.trimString(userMap.get(name)));
                        } else if ("8".equalsIgnoreCase(name.trim())) {
                            listPojo.setXlsName(StringUtil.trimString(userMap.get(name)));
                        }
                        // 默认失效时间为30天
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(new Date());
                        calendar.add(Calendar.DATE, 30);
                        Date failureDate = calendar.getTime();
                        listPojo.setFailureDate(failureDate);
                    }
                    listPojo.setBatchCode(batchCode);
                    listPojo.setCreatedDate(new Date());
                    listPojo.setStatus(1);
                    listPojo.setNumberStatus(1);
                    listPojo.setCreatedUser(userName);
                    hnslUserList.add(listPojo);
                }
                try {
                    logger.info("解析结果装入用户集合" + hnslUserList);
                    Map<String, String> saveUserArray = hnslWhiteListService.saveUserArray(hnslUserList, cityCode, statusSf, request);
                    if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                        Map<String, String> resultMap = new HashMap<>();
                        resultMap.put("mes", "exportDaoUser");
                        resultMap.put("resultCode", "6");
                        resultMap.put("fileName", saveUserArray.get("fileName"));
                        return success(resultMap);
                    }
                } catch (Exception e) {
                    // TODO Auto-generated catch block
                    logger.error("批量插入用户信息失败:" + e);
                    return success("resultCode", 5);
                }
            } else {
                logger.info("文件内容为空，或者解析失败");
                result.put("resultCode", "4");
                return success("resultCode", 4);
            }
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("mes", "exportDaoUser");
            resultMap.put("resultCode", "0");
            return success(resultMap);
        } catch (Exception e) {
            logger.error("文件接口异常:" + e);
            return success("resultCode", 3);
        }
    }


    /**
     * 驳回
     */
    @RequestMapping("/reject")
    public ApiResult<?> reject(@RequestBody HnslWhiteApprove hnslWhiteApprove){
        User user = getLoginUser();
        String statusSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String userName = redisUtils.getString("thisUserName" + user.getPhone());
        String UserPhone = redisUtils.getString("thisUserPhone" + user.getPhone());
        if (!"5".equals(statusSf)) {
            return fail("对不起，您没有该权限操作");
        }
        hnslWhiteApproveService.reject(UserPhone, userName, hnslWhiteApprove);
        return success();
    }

    /**
     * 下载白名单上传导入情况
     *
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/exportDaoUser")
    public void exportDaoUser(HttpServletResponse response, HttpServletRequest request, String name) throws Exception {
        //下载
        String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
        InterfaceUtil.createFile(filepath);
        String fileName = name + ".xls".toString(); // 文件的默认保存名

        File file = new File(filepath + fileName);
        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径

        // 设置输出的格式
        response.reset();

        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("白名单导入情况表", "UTF-8") + ".xls");
        // 循环取出流中的数据
        byte[] b = new byte[1024];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inStream.close();
            file.delete();
        }
    }
}
