<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslUserOrdersMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_user_orders a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userId != null">
                AND a.USER_ID = #{param.userId}
            </if>
            <if test="param.ordersTrueNumber != null">
                AND a.ORDERS_TRUE_NUMBER = #{param.ordersTrueNumber}
            </if>
            <if test="param.ordersFalseNumber != null">
                AND a.ORDERS_FALSE_NUMBER = #{param.ordersFalseNumber}
            </if>
            <if test="param.ordersNumber != null">
                AND a.ORDERS_NUMBER = #{param.ordersNumber}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.ordersRecycleNumber != null">
                AND a.ORDERS_RECYCLE_NUMBER = #{param.ordersRecycleNumber}
            </if>
            <if test="param.activityLogo != null">
                AND a.ACTIVITY_LOGO LIKE CONCAT('%', #{param.activityLogo}, '%')
            </if>
            <if test="param.schoolSixId != null">
                AND a.SCHOOL_SIX_ID LIKE CONCAT('%', #{param.schoolSixId}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrders">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrders">
        <include refid="selectSql"></include>
    </select>

</mapper>
