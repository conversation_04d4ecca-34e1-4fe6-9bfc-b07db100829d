package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSchool;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserSchoolParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户学校关系表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
public interface HnslUserSchoolMapper extends BaseMapper<HnslUserSchool> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserSchool>
     */
    List<HnslUserSchool> selectPageRel(@Param("page") IPage<HnslUserSchool> page,
                             @Param("param") HnslUserSchoolParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserSchool> selectListRel(@Param("param") HnslUserSchoolParam param);

}
