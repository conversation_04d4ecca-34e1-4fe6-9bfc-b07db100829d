package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskPlan;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskPlanParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务完成进度表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslTaskPlanMapper extends BaseMapper<HnslTaskPlan> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTaskPlan>
     */
    List<HnslTaskPlan> selectPageRel(@Param("page") IPage<HnslTaskPlan> page,
                             @Param("param") HnslTaskPlanParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTaskPlan> selectListRel(@Param("param") HnslTaskPlanParam param);

}
