package com.hlkj.yxsAdminApi.common.core.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig;
import com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig;
import com.hlkj.yxsAdminApi.common.core.config.SpringContextHolder;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.LoginRecord;
import com.hlkj.yxsAdminApi.common.system.entity.OperationRecord;
import com.hlkj.yxsAdminApi.common.system.entity.R;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Goods;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5Order;
import org.apache.catalina.security.SecurityUtil;
import org.apache.commons.httpclient.HttpConnectionManager;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import cn.hutool.core.util.StrUtil;


/**
 * <AUTHOR>
 * @Date 2020/6/8 9:52
 * @Version 1.0
 **/
@Component
public class InterfaceUtil extends BaseController {

	private static Logger logger = LogManager.getLogger(InterfaceUtil.class);

	private static RedisUtil redis;

	@Autowired
	public void setRedisService(RedisUtil redis) {
		InterfaceUtil.redis = redis;
	}

	// 能力平台地址
	public static final String url = "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/rest";// 内网http://**************:8081/api/rest

	public static String getNkUrl() {
		if (redis.hasKey("SJY_NLKF_URL")) {
			return redis.getString("SJY_NLKF_URL");
		}
		return url;
	}

	public static String getEopUrl() {
		if (redis.hasKey("HNZHSL-EOP-URL")) {
			return (String) redis.get("HNZHSL-EOP-URL");
		}
		return dcoos_url_eop;
	}

	public static String getNkEopUrl() {
		if (redis.hasKey("HNZHSL-NKEOP-URL")) {
			return (String) redis.get("HNZHSL-NKEOP-URL");
		}
		return "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/rest";
	}


	private static final int timeOut = 60000;
	private static final String charset = "UTF-8";
	private static final HttpConnectionManager httpConnectionManager;
	private static final String ACCOUNT_ID = "**********";// 业务主账号


	static {
		HttpConnectionManagerParams httpConnectionManagerParams = new HttpConnectionManagerParams();
		httpConnectionManagerParams.setConnectionTimeout(timeOut);
		httpConnectionManager = new SimpleHttpConnectionManager();
		httpConnectionManager.setParams(httpConnectionManagerParams);
	}

	public static final String payUrl = "http://134.175.22.23:1081/gateway.do";// 内网

	public static final String clientId = "HN-DQ-ZHSL-0024";

	/**
	 * 生产
	 */
	public static final String NEW_DCOOS_URL = "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/openapi";// 测试版用 9080 正式版用 8081
	public static final String dcoos_url_eop = "http://134.178.193.249:31008/api/openapi/";
	/**
	 * 测试
	 */
//    public static final String NEW_DCOOS_URL = "http://134.176.134.80:30048/api/openapi";// 测试版用 9080 正式版用 8081
    /*public static final String payUrl = "http://flow.hn.189.cn:1081/gateway.do";//外网
    private static final String url = "http://202.103.124.66:7098/api/rest";// 外网*/
	public static final String F5_INSIDE_URL = "http://134.178.251.102:31530/hnzhsl/smallFile/image2.do?fileName=";
	// 查询增值业务能力平台分配token以及bs3.0割接接口
	private static final String ACCESS_TOKEN = "MzMyN2YxNmExMjdjMjE0NTU2MWI0MDk5MDczZTE0ZjM=";
	public static final String user = "fengchuhui"; // 调用方账号（到时分配）
	public static final Map<String, String> cityMap = new HashMap<>(); // 城市对应的工号
	public static final Map<String, String> cityUserMap = new HashMap<>(); // 地市用的指定用户销售员编码
	public static final Map<String, String> cityMapChannel = new HashMap<>(); // 城市对应渠道
	public static final Map<String, String> area_id = new HashMap<>(); // 城市对应总公司编码
	public static final Map<String, String> organization = new HashMap<>(); // 城市对应输单组织信息
	public static final Map<String, String> organizationId = new HashMap<>(); // 城市对应输单组织id
	public static final LinkedHashMap<String, String> lan_codes = new LinkedHashMap();// 各地市集团编码
	public static final Map<String, String> crmOrderType = new HashMap<>();// 订单类别
	public static final Map<String, String> sourcesOfEquipment = new HashMap<>();// 设备来源
	public static final Map<String, String> bandwidth_Tolink = new HashMap<>();// 送达环节
	public static final Map<String, String> stagingCategory = new HashMap<>();// 花呗类别
	public static final Map<String, String> orderType = new HashMap<>();// 业务类型

	//时间格式对象(全局变量，重用对象提高程序性能)
	public static final SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


	public static final Map<String, String> staff_code = new HashMap<>(); // 城市对应的工号-staff_code
	public static final Map<String, String> staff_id = new HashMap<>(); // 城市对应的工号下的唯一标识
	public static final Map<String, String> city_code = new HashMap<>(); // 地市编码对应地市名称
	public static final LinkedHashMap<String, String> cityAdministrativeCode = new LinkedHashMap<>(); // 各地市行政编码

	static {
		cityMap.put("730", "HLFXB0730");
		cityMap.put("731", "HLFXB");
		cityMap.put("732", "HLFXB0732");
		cityMap.put("733", "HLFXB0733");
		cityMap.put("734", "HLFXB0734");
		cityMap.put("735", "HLFXB0735");
		cityMap.put("736", "HLFXB0736");
		cityMap.put("737", "HLFXB0737");
		cityMap.put("738", "HLFXB0738");
		cityMap.put("739", "HLFXB0739");
		cityMap.put("743", "HLFXB0743");
		cityMap.put("744", "HLFXB0744");
		cityMap.put("745", "HLFXB0745");
		cityMap.put("746", "HLFXB0746");

		cityUserMap.put("730", "4306022027628");
		cityUserMap.put("731", "4301022027766");
		cityUserMap.put("732", "4303022035386");
		cityUserMap.put("733", "4302032027782");
		cityUserMap.put("734", "4304072027800");
		cityUserMap.put("735", "4310032027820");
		cityUserMap.put("736", "4307022027840");
		cityUserMap.put("737", "4309032027879");
		cityUserMap.put("738", "4313022027870");
		cityUserMap.put("739", "4305032027953");
		cityUserMap.put("743", "4331012028035");
		cityUserMap.put("744", "4308022028039");
		cityUserMap.put("745", "4312022028130");
		cityUserMap.put("746", "4311032028164");

		organization.put("743", "{\"id\":\"110000426020\",\"value\":\"吉首市电信分公司@省实业公司(电子渠道)(吉首)\"}");// 吉首市电信分公司@省实业公司(电子渠道)(吉首)
		organization.put("746", "{\"id\":\"110000426057\",\"value\":\"永州市电信分公司@省实业公司(电子渠道)(永州)\"}");// 永州市电信分公司@省实业公司(电子渠道)(永州)
		organization.put("731", "{\"id\":\"110000426229\",\"value\":\"长沙市电信分公司@省实业公司(电子渠道)(长沙)\"}");// 长沙市电信分公司@省实业公司(电子渠道)(长沙)
		organization.put("735", "{\"id\":\"110000426078\",\"value\":\"郴州市电信分公司@省实业公司(电子渠道)(郴州)\"}");// 郴州市电信分公司@省实业公司(电子渠道)(郴州)
		organization.put("739", "{\"id\":\"110000426134\",\"value\":\"邵阳市电信分公司@省实业公司(电子渠道)(邵阳)\"}");// 邵阳市电信分公司@省实业公司(电子渠道)(邵阳)
		organization.put("736", "{\"id\":\"110000426162\",\"value\":\"常德市电信分公司@省实业公司(电子渠道)(常德)\"}");// 常德市电信分公司@省实业公司(电子渠道)(常德)
		organization.put("733", "{\"id\":\"20000807\",\"value\":\"株洲市电信分公司@省实业公司(电子渠道)(株洲)\"}");// 株洲市电信分公司@省实业公司(电子渠道)(株洲)
		organization.put("734", "{\"id\":\"20000808\",\"value\":\"衡阳市电信分公司@省实业公司(电子渠道)(衡阳)\"}");// 衡阳市电信分公司@省实业公司(电子渠道)(衡阳)
		organization.put("732", "{\"id\":\"20000806\",\"value\":\"湘潭市电信@省实业公司(电子渠道)(湘潭)\"}");// 湘潭市电信@省实业公司(电子渠道)(湘潭)
		organization.put("730", "{\"id\":\"20000805\",\"value\":\"岳阳市分公司@省实业公司(电子渠道)(岳阳)\"}");// 岳阳市分公司@省实业公司(电子渠道)(岳阳)
		organization.put("744", "{\"id\":\"20000815\",\"value\":\"张家界市电信公司@省实业公司(电子渠道)(张家界)\"}");// 张家界市电信公司@省实业公司(电子渠道)(张家界)
		organization.put("737", "{\"id\":\"20000811\",\"value\":\"益阳市电信分公司@省实业公司(电子渠道)(益阳)\"}");// 益阳市电信分公司@省实业公司(电子渠道)(益阳)
		organization.put("745", "{\"id\":\"20000816\",\"value\":\"怀化市电信分公司@省实业公司(电子渠道)(怀化)\"}");// 怀化市电信分公司@省实业公司(电子渠道)(怀化)
		organization.put("738", "{\"id\":\"20000812\",\"value\":\"娄底市电信分公司@省实业公司(电子渠道)(娄底)\"}");// 娄底市电信分公司@省实业公司(电子渠道)(娄底)

		organizationId.put("743", "110000426020");// 吉首市电信分公司@省实业公司(电子渠道)(吉首)
		organizationId.put("746", "110000426057");// 永州市电信分公司@省实业公司(电子渠道)(永州)
		organizationId.put("731", "110000426229");// 长沙市电信分公司@省实业公司(电子渠道)(长沙)
		organizationId.put("735", "110000426078");// 郴州市电信分公司@省实业公司(电子渠道)(郴州)
		organizationId.put("739", "110000426134");// 邵阳市电信分公司@省实业公司(电子渠道)(邵阳)
		organizationId.put("736", "110000426162");// 常德市电信分公司@省实业公司(电子渠道)(常德)
		organizationId.put("733", "20000807");// 株洲市电信分公司@省实业公司(电子渠道)(株洲)
		organizationId.put("734", "20000808");// 衡阳市电信分公司@省实业公司(电子渠道)(衡阳)
		organizationId.put("732", "20000806");// 湘潭市电信@省实业公司(电子渠道)(湘潭)
		organizationId.put("730", "20000805");// 岳阳市分公司@省实业公司(电子渠道)(岳阳)
		organizationId.put("744", "20000815");// 张家界市电信公司@省实业公司(电子渠道)(张家界)
		organizationId.put("737", "20000811");// 益阳市电信分公司@省实业公司(电子渠道)(益阳)
		organizationId.put("745", "20000816");// 怀化市电信分公司@省实业公司(电子渠道)(怀化)
		organizationId.put("738", "20000812");// 娄底市电信分公司@省实业公司(电子渠道)(娄底)
	}

	static {
		cityMapChannel.put("730", "4306022027629");
		cityMapChannel.put("731", "4301022027767");
		cityMapChannel.put("732", "4303022035387");
		cityMapChannel.put("733", "4302032027783");
		cityMapChannel.put("734", "4304072027801");
		cityMapChannel.put("735", "4310032027821");
		cityMapChannel.put("736", "4307022027841");
		cityMapChannel.put("737", "4309032027880");
		cityMapChannel.put("738", "4313022027872");
		cityMapChannel.put("739", "4305032027954");
		cityMapChannel.put("743", "4331012028036");
		cityMapChannel.put("744", "4308022028041");
		cityMapChannel.put("745", "4312022028150");
		cityMapChannel.put("746", "4311032028167");

		sourcesOfEquipment.put("1", "向电信购买");
		sourcesOfEquipment.put("2", "代管");
		sourcesOfEquipment.put("3", "自购");

		bandwidth_Tolink.put("1", "外线送达");
		bandwidth_Tolink.put("2", "营业送达");
		bandwidth_Tolink.put("3", "不需送达");

		stagingCategory.put("1", "花呗");
		stagingCategory.put("2", "异业花呗");
		stagingCategory.put("3", "橙分期");
		stagingCategory.put("4", "异业橙分期");

		orderType.put("2", "sjyxzrh");// 融合
		orderType.put("1", "sjyxzhk");// 号卡
		orderType.put("3", "sjyxzkditv");// 宽带ITV
		orderType.put("14", "sjyxzfq");// 分期
		orderType.put("15", "sjyxztcbg");// 套餐变更
		orderType.put("13", "sjyxzgh");// 固话
		orderType.put("5", "sjyecgh");// 过户
		orderType.put("6", "sjyecbk");// 补卡
		orderType.put("7", "sjyecyj");// 移机
		orderType.put("8", "sjyeccj");// 拆机
		orderType.put("9", "sjyeckdts");// 宽带提速
		orderType.put("10", "sjyecycj");// 预拆机
		orderType.put("11", "sjyecxjzl");// 新建资料
		orderType.put("12", "sjyecbgzl");// 变更资料
		orderType.put("212", "sjyecrh");// 二次类融合
		orderType.put("16", "sjyfqhy");// 分期合约
		orderType.put("17", "sjysmnc");// 岁末年初
	}

	static {
		area_id.put("730", "4306022027629");
		area_id.put("731", "4301022027767");
		area_id.put("732", "4303022035387");
		area_id.put("733", "4302032027783");
		area_id.put("734", "4304072027801");
		area_id.put("735", "4310032027821");
		area_id.put("736", "4307022027841");
		area_id.put("737", "4309032027880");
		area_id.put("738", "4313022027872");
		area_id.put("739", "4305032027954");
		area_id.put("743", "4331012028036");
		area_id.put("744", "4308022028041");
		area_id.put("745", "4312022028150");
		area_id.put("746", "4311032028167");

		lan_codes.put("730", "8430600");
		lan_codes.put("731", "8430100");
		lan_codes.put("732", "8430300");
		lan_codes.put("733", "8430200");
		lan_codes.put("734", "8430400");
		lan_codes.put("735", "8431000");
		lan_codes.put("736", "8430700");
		lan_codes.put("737", "8430900");
		lan_codes.put("738", "8431300");
		lan_codes.put("739", "8430500");
		lan_codes.put("743", "8433100");
		lan_codes.put("744", "8430800");
		lan_codes.put("745", "8431200");
		lan_codes.put("746", "8431100");

		crmOrderType.put("2", "400");// 4G新装
		crmOrderType.put("3", "310");// 宽带新装
		crmOrderType.put("4", "320");
		crmOrderType.put("13", "330");// 固话新装
		crmOrderType.put("14", "460");
	}

	static {
		staff_code.put("730", "HLFXB0730");
		staff_code.put("731", "HLFXB");
		staff_code.put("732", "HLFXB0732");
		staff_code.put("733", "HLFXB0733");
		staff_code.put("734", "HLFXB0734");
		staff_code.put("735", "HLFXB0735");
		staff_code.put("736", "HLFXB0736");
		staff_code.put("737", "HLFXB0737");
		staff_code.put("738", "HLFXB0738");
		staff_code.put("739", "HLFXB0739");
		staff_code.put("743", "HLFXB0743");
		staff_code.put("744", "HLFXB0744");
		staff_code.put("745", "HLFXB0745");
		staff_code.put("746", "HLFXB0746");

		cityAdministrativeCode.put("730", "43060000");
		cityAdministrativeCode.put("731", "43010000");
		cityAdministrativeCode.put("732", "43030000");
		cityAdministrativeCode.put("733", "43020000");
		cityAdministrativeCode.put("734", "43040000");
		cityAdministrativeCode.put("735", "43100000");
		cityAdministrativeCode.put("736", "43070000");
		cityAdministrativeCode.put("737", "43090000");
		cityAdministrativeCode.put("738", "43130000");
		cityAdministrativeCode.put("739", "43050000");
		cityAdministrativeCode.put("743", "43310000");
		cityAdministrativeCode.put("744", "43080000");
		cityAdministrativeCode.put("745", "43120000");
		cityAdministrativeCode.put("746", "43110000");
	}

	static {
		staff_id.put("730", "30004095863");
		staff_id.put("731", "30004094501");
		staff_id.put("732", "30004095866");
		staff_id.put("733", "30004095869");
		staff_id.put("734", "30004095889");
		staff_id.put("735", "30004095892");
		staff_id.put("736", "30004095895");
		staff_id.put("737", "30004095898");
		staff_id.put("738", "30004095902");
		staff_id.put("739", "30004095905");
		staff_id.put("743", "30004095908");
		staff_id.put("744", "30004095915");
		staff_id.put("745", "30004095918");
		staff_id.put("746", "30004095921");

		city_code.put("730", "岳阳");
		city_code.put("731", "长沙");
		city_code.put("732", "湘潭");
		city_code.put("733", "株洲");
		city_code.put("734", "衡阳");
		city_code.put("735", "郴州");
		city_code.put("736", "常德");
		city_code.put("737", "益阳");
		city_code.put("738", "娄底");
		city_code.put("739", "邵阳");
		city_code.put("743", "湘西");
		city_code.put("744", "张家界");
		city_code.put("745", "怀化");
		city_code.put("746", "永州");
	}


	/*****************************************
	 * 省集约预受理相关接口
	 *********************************************/
	/**
	 * BSS30-查询礼包清单按工号id ok
	 *
	 * @param stafffId
	 * @param lanId
	 * @return
	 */
	public ApiResult<?> qryOfferPackageList(String stafffId, String lanId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.offer.QryOfferPackageList");//bss.qry.offer.QryOfferPackageList
		body.put("version", "1.0");

		content.put("stafffId", stafffId);
		content.put("lanId", lanId);
		body.put("content", content);

		logger.info("查询礼包清单按工号id请求报文：" + body.toString());
		String rest = null;
		try {
			rest = RequestUtil.sendToPost(getNkUrl(), body.toJSONString());
			//     rest = RequestUtil.sendToPost("http://134.176.42.71:7103/api/rest", body.toJSONString());
			logger.info("查询礼包清单按工号id返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest).getJSONObject("result");
			if (!"0000".equals(restJson.getString("Code"))
					|| !restJson.getString("Message").equals("成功")) {
				return fail("接口调用失败");
			} else {
				Map<String, Object> map = new HashMap<>();
				map.put("code", "0");
				map.put("msg", "接口调用成功");
				map.put("data", restJson.getJSONObject("QueryResults").get("Result"));
				return success(restJson.getJSONObject("QueryResults").get("Result"));
			}
		} catch (IOException e) {
			logger.info("掌上销工具查询礼包清单按工号id接口异常：", e);
			return fail("接口调用异常");
		}
	}

	/**
	 * BSS30-查询销售品属性（礼包）ok
	 *
	 * @param stafffId
	 * @param lanId
	 * @param mainOfferId 礼包id
	 * @return Map<String, Object>
	 */
	public ApiResult<?> qryProdOfferAttr(String stafffId, String lanId, String mainOfferId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.offer.QryProdOfferAttr");
		body.put("version", "1.0");

		content.put("stafffId", stafffId);
		content.put("lanId", lanId);
		content.put("offer_id", mainOfferId);
		body.put("content", content);

		logger.info("查询销售品属性请求报文：" + body.toString());
		String rest = null;
		try {
			rest = RequestUtil.sendToPost(getNkUrl(), body.toJSONString());
			logger.info("查询销售品属性返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code")) || !restJson.getString("res_message").equals("Success")) {
				return fail("接口调用失败");
			} else {
				Map<String, String> map = new HashMap<>();
				JSONArray restJsonArray = restJson.getJSONObject("result").getJSONArray("Result");
				map.put("packageFlow", "");
				map.put("packageVoice", "");
				map.put("packagePrice", "");
				for (int i = 0; i < restJsonArray.size(); i++) {
					JSONObject restJsonObj = restJsonArray.getJSONObject(i);
					String if_default_value = restJsonObj.getString("if_default_value");
					String default_value = restJsonObj.getString("default_value");
					String cname = restJsonObj.getString("cname");
					if (if_default_value.equals("T")) {
						if (cname.contains("M") && cname.contains("流量")) {
							int a = Integer.valueOf(default_value);// M
							String gb = "";
							String mb = "";
							int c = (int) (a % 1024);
							if (c == 0) {
								gb = a / 1024 + "GB";
							} else {
								float aa = Float.valueOf(a) / 1024;
								if (aa < 1) {
									mb = (int) (aa * 1024) + "MB";
								} else {
									gb = a / 1024 + "GB";
									mb = a % 1024 + "MB";
								}
							}
							map.put("packageFlow", gb + mb);
						} else if (cname.contains("通话") && cname.contains("分钟") && cname.contains("时长")
								&& cname.contains("赠送")) {
							map.put("packageVoice", default_value + "分钟");
						} else if (cname.contains("月使用费")) {
							map.put("packagePrice", default_value);
						}
					}
				}
				return success(map);
			}
		} catch (IOException e) {
			logger.info("掌上销工具查询销售品属性接口异常：", e);
			return fail("接口调用失败");
		}
	}

	/**
	 * BSS30-查询礼包的销售品构成明细按礼包id ok
	 *
	 * @param stafffId
	 * @param lanId
	 * @param offerPackageId 礼包id
	 * @return
	 */
	public ApiResult<?> qryOfferPackageDetail(String stafffId, String lanId, String offerPackageId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.offer.QryOfferPackageDetail");
		body.put("version", "1.0");

		content.put("stafffId", stafffId);
		content.put("lanId", lanId);
		content.put("offerPackageId", offerPackageId);
		body.put("content", content);

		logger.info("查询礼包的销售品构成明细按礼包id请求报文：" + body.toString());
		String rest = null;
		try {
			rest = RequestUtil.sendToPost(getNkUrl(), body.toJSONString());
			//         rest = RequestUtil.sendToPost("http://134.176.42.71:7103/api/rest", body.toJSONString());
			logger.info("查询礼包的销售品构成明细按礼包id返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest).getJSONObject("result");
			if (!"0000".equals(restJson.getString("Code")) || !restJson.getString("Message").equals("成功")
					|| !((JSONObject) restJson.getJSONObject("QueryResults").get("Result")).containsKey("prodList")) {
				return fail("接口调用失败");
			} else {
				return success(restJson.getJSONObject("QueryResults").getJSONObject("Result"));
			}
		} catch (IOException e) {
			logger.info("掌上销工具查询礼包的销售品构成明细按礼包id接口异常：", e);
			return fail("接口调用失败");
		}
	}

	/**
	 * BSS查询掌上销销售品信息
	 *
	 * @param lanId
	 * @param mainOfferId 礼包id
	 * @return Map<String, Object>
	 */
	public ApiResult<?> QryPalmSalesOfferInfo(String lanId, String mainOfferId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();
		SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
		String exchangeId = "mmarkt" + sf.format(new Date().getTime()) + (int) ((Math.random() * 4 + 1) * 10000)
				+ (int) ((Math.random() * 4 + 1) * 10000);
		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.offer.QryPalmSalesOfferInfo");
		body.put("version", "1.0");
		body.put("staff_id", "100018798");
		body.put("staff_code", "hzbliuzhi");
		body.put("appKey", "android");
		body.put("deviceId", "f73c9854-bc7e-49db-8597-2e8ac3cb946c");
		//content.put("exchange_id", stafffId);
		content.put("lan_id", lanId);
		content.put("query_type", 1);
		content.put("query_value", mainOfferId);
		content.put("ExchangeId", exchangeId);
		content.put("page_index", "");
		content.put("page_size", "");
		body.put("content", content);

		logger.info("BSS查询掌上销销售品信息请求报文：" + body.toString());
		String rest = null;
		try {
			rest = RequestUtil.sendToPost(getNkUrl(), body.toJSONString());
			logger.info("BSS查询掌上销销售品信息返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code")) || !restJson.getString("res_message").equals("Success")) {
				return fail("接口调用失败");
			} else {
				JSONObject resultObj = restJson.getJSONObject("result");
				if (!"0000".equals(resultObj.getString("Code")) || !resultObj.getString("Message").equals("成功")) {
					return fail("接口调用失败");
				} else {
					JSONArray ProdOfferInfo = resultObj.getJSONObject("QueryResults").getJSONArray("ProdOfferInfo");
					return success(ProdOfferInfo);
				}
			}
		} catch (IOException e) {
			logger.info("BSS查询掌上销销售品信息接口异常：", e);
			return fail("接口调用失败");
		}
	}


	public static void main(String[] args) {
		InterfaceUtil interfaceUtil = new InterfaceUtil();
		interfaceUtil.QryPalmSalesOfferInfo("731", "9101825");
	}

	public static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYY-MM-dd HH:mm:ss");

	/**
	 * 学子公司获取公钥
	 *
	 * @return JSONObject
	 */
	public ApiResult<?> getPublicKey() {
		JSONObject body = new JSONObject();

		String rest = null;
		try {
			logger.info("学子公司获取公钥接口入参:" + body.toJSONString());
			rest = RequestUtil.doPost("https://zhzx.educlouds.cn/hhr_manager/api/intensubplatform-source/auth/login/getPublicKey", body.toJSONString());
			logger.info("学子公司获取公钥接口响应:" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			System.out.println(restJson.toJSONString());
			return success("查询成功", restJson);
		} catch (Exception e) {
			logger.info("学子公司获取公钥接口异常: ", e);
			return fail("学子公司获取公钥接口异常", "");
		}
	}

	public static String LeZhiUrl = "https://xzgs.educlouds.cn/api/intensubplatform-source/";

	/**
	 * 学子公司获取token信息     * [BSS30-BILL]查询客户名下所有帐户及余额信息(360)
	 *
	 * @return JSONObject
	 */
	public ApiResult<?> getStudentAccessToken() {
		JSONObject body = new JSONObject();
		String rest = null;
		try {
			rest = RequestUtil.doPost(LeZhiUrl + "auth/login/getPublicKey", body.toJSONString());
			logger.info("学子公司获取公钥接口响应:" + rest);
			JSONObject restJsons = JSONObject.parseObject(rest);
			String key = restJsons.getString("result");
			body.put("account", "hnxzhhr");
			body.put("paswd", RSAUtils.encryptByPublicKey("axy@2021".getBytes(), key));
			logger.info("学子公司获取token信息入参:" + body.toJSONString());
			rest = RequestUtil.doPost(LeZhiUrl + "auth/login/getAccessToken", body.toJSONString());
			logger.info("学子公司获取token信息响应:" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"200".equals(restJson.getString("code"))) {
				return success(restJson.getString("msg"), null);
			}
			return success("查询成功", restJson);
		} catch (Exception e) {
			logger.info("学子公司获取token信息接口异常: ", e);
			return fail("学子公司获取token信息异常", "");
		}
	}

	/**
	 * 城市编码转换成城市名称
	 *
	 * @param cityCode
	 * @cityName=
	 */
	public static String getCodeToName(String cityCode) {
		if ("730".equals(cityCode)) {
			return "岳阳";
		} else if ("731".equals(cityCode)) {
			return "长沙";
		} else if ("732".equals(cityCode)) {
			return "湘潭";
		} else if ("733".equals(cityCode)) {
			return "株洲";
		} else if ("734".equals(cityCode)) {
			return "衡阳";
		} else if ("735".equals(cityCode)) {
			return "郴州";
		} else if ("736".equals(cityCode)) {
			return "常德";
		} else if ("737".equals(cityCode)) {
			return "益阳";
		} else if ("738".equals(cityCode)) {
			return "娄底";
		} else if ("739".equals(cityCode)) {
			return "邵阳";
		} else if ("743".equals(cityCode)) {
			return "湘西";
		} else if ("744".equals(cityCode)) {
			return "张家界";
		} else if ("745".equals(cityCode)) {
			return "怀化";
		} else if ("746".equals(cityCode)) {
			return "永州";
		} else if ("700".equals(cityCode)) {
			return "全省";
		}
		return "其他";
	}

	/**
	 * 创建文件
	 *
	 * @param destFileName
	 * @return
	 */
	public static boolean createFile(String destFileName) {
		boolean flag = false;
		if (destFileName.length() > 0) {
			File file = new File(destFileName);
			if (!file.exists()) {
				flag = file.mkdirs();
			}
		}
		return flag;
	}

	//判断row是否为空
	public static boolean isRowEmpty(Row row) {
		if (null == row) {
			return true;
		}
		int firstCellNum = row.getFirstCellNum();   //第一个列位置
		int lastCellNum = row.getLastCellNum();     //最后一列位置
		int nullCellNum = 0;    //空列数量
		for (int c = firstCellNum; c < lastCellNum; c++) {
			Cell cell = row.getCell(c);
			if (null == cell || CellType.BLANK == cell.getCellType()) {
				nullCellNum++;
				continue;
			}
			cell.setCellType(CellType.STRING);
			String cellValue = cell.getStringCellValue().trim();
			if (StringUtils.isEmpty(cellValue)) {
				nullCellNum++;
			}
		}
		//所有列都为空
		if (nullCellNum == (lastCellNum - firstCellNum)) {
			return true;
		}
		return false;
	}

	/**
	 * 生成请求流水号
	 *
	 * @return
	 */
	public static String getExchangeId(String empcode) {
		DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		int code = (int) (Math.random() * 1000000000);
		while (code < 100000000) {
			code = code * 10;
			if (code >= 100000000)
				break;
		}
		String exchangeId = empcode + format.format(new Date()) + code + 0;
		return exchangeId;
	}

	/**
	 * CRM订单撤单
	 */
	public static Map<String, String> cancellationsCrmOrder(HnslOrder orderEntity) {
		Map<String, String> resultMap = new HashMap<>();

		logger.info("智慧扫楼订单撤单接口入参:condition=" + orderEntity.toString());

		JSONObject data = new JSONObject(); // data节点，在根节点下，根据各接口填入相应字段
		data.put("access_token", ACCESS_TOKEN);
		data.put("method", "bss.ord.order.Acceptance");
		data.put("staff_id", staff_id.get(orderEntity.getCitycode()));
		data.put("staff_code", staff_code.get(orderEntity.getCitycode()));
		data.put("appKey", "");
		data.put("version", "1.0");
		data.put("deviceId", "");
		JSONObject content = new JSONObject();
		JSONObject root = new JSONObject();
		JSONObject Header = new JSONObject();
		Header.put("ClientId", "HNWAPT");
		Header.put("ExchangeId", getExchangeId("HNWAPT"));
		Header.put("BizCode", "Order");
		Header.put("Password", "123456");
		JSONObject OrderRequest = new JSONObject();
		OrderRequest.put("lan_id", orderEntity.getCitycode());
		JSONObject CustOrder = new JSONObject();
		CustOrder.put("lan_id", orderEntity.getCitycode());
		CustOrder.put("ask_id", orderEntity.getCrmAskId());
		CustOrder.put("cust_order_id", orderEntity.getCrmOrderId());
		OrderRequest.put("CustOrder", CustOrder);
		OrderRequest.put("service_action", "CANCEL_ASK");
		JSONObject CompInstance = new JSONObject();
		CompInstance.put("pay_mode", "120400");
		OrderRequest.put("CompInstance", CompInstance);
		root.put("Header", Header);
		root.put("OrderRequest", OrderRequest);
		content.put("Root", root);

		data.put("content", content);


		logger.info("智慧扫楼crm订单撤单接口 请求报文：" + data.toJSONString());
		String result;
		try {
			result = RequestUtil.sendToPost(url, data.toJSONString());
		} catch (IOException e) {
			logger.info("智慧扫楼CRM订单创建异常" + e);
			resultMap.put("code", "1");
			return resultMap;
		}
		logger.info("智慧扫楼crm订单创建接口 返回报文：" + result);
		JSONObject jsonObject = JSONObject.parseObject(result);
		String resCode = jsonObject.getString("res_code");
		if ("00000".equals(resCode)) {
			// 接口调用成功获取成功
			String rspCode = jsonObject.getJSONObject("result").getString("Code");
			if ("0000".equals(rspCode)) {
				// 查询号码成功
				String custOrderId = jsonObject.getJSONObject("result").getJSONObject("CustOrder")
						.getString("cust_order_id");
				logger.info("智慧扫楼CRM订单撤单成功，用户订单编号custOrderId:" + orderEntity.getOrderId() + "——————" + custOrderId);
				resultMap.put("code", "0");
				resultMap.put("custOrderId", custOrderId);
				return resultMap;
			} else {
				String message = jsonObject.getJSONObject("result").getString("Message");
				logger.info("智慧扫楼CRM订单撤单失败，失败原因:" + orderEntity.getCustomerPhone() + "——————" + message);
				resultMap.put("code", "1");
				resultMap.put("message", message);
			}
		}
		resultMap.put("code", "1");
		return resultMap;
	}

	/**
	 * 异网短信发送
	 * <p>Title: sendMsg</p >
	 * <p>Description: </p >
	 *
	 * @param mobile
	 * @param message
	 * @return
	 * <AUTHOR>
	 * @date 2022年9月3日
	 * @version 1.0
	 */
	public static String sendMsg(String mobile, String message) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "push.sms.sendsms");
		body.put("version", "1.0");

		content.put("phone_num", mobile);
		content.put("source", "FXB");
		content.put("message", message);//短信告知内容（不超过325个汉字），每70个汉字会拆分成一条短信
		content.put("priority", "0");//0普通，1紧急
		content.put("type", "1");
		body.put("content", content);
		logger.info("掌上销工具异网短信发送请求报文：" + body.toString());
		String rest = null;
		try {
			rest = RequestUtil.sendToPost(url, body.toJSONString());
			logger.info("掌上销工具异网短信发送返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			if ("00000".equals(restJson.getString("res_code")) && "Success".equals(restJson.getJSONObject("res_message"))) {
				return restJson.getJSONObject("result").getString("RETFLAG");
			}
		} catch (Exception e) {
			logger.info("掌上销工具异网短信发送异常", e);
		}
		return "1";
	}

	/**
	 * 获取身份证的年龄
	 *
	 * @param birthDay
	 * @return
	 */
	public static int getAgeFromBirthTime(String birthDay) {
		String time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		String yearStr = time.split("-")[0];
		String monthStr = time.split("-")[1];
		String dayStr = time.split("-")[2];
		String yearBirthStr = birthDay.substring(0, 4);
		String monthBirthStr = birthDay.substring(4, 6);
		String dayBirthStr = birthDay.substring(6);
		int year = Integer.valueOf(yearStr);
		int yearBirth = Integer.valueOf(yearBirthStr);
		if (year - yearBirth <= 0) {
			return 0;
		}
		int age = year - yearBirth;
		int month = Integer.valueOf(monthStr);
		int monthBirth = Integer.valueOf(monthBirthStr);
		if (month - monthBirth > 0) {
			return age;
		}
		if (month - monthBirth < 0) {
			return --age;
		}
		int day = Integer.valueOf(dayStr);
		int dayBirth = Integer.valueOf(dayBirthStr);
		if (day - dayBirth >= 0) {
			return age;
		}
		return --age;
	}

	public static boolean isInteger(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	/**
	 * 根据审批类型获取审批模板API
	 *
	 * @param approveContentId
	 * @return ApiResult<?>
	 * @MethodName: queryApproveTemplate
	 * @<NAME_EMAIL>
	 * @date 2024-12-16 09:17:58
	 */
	public ApiResult<?> queryApproveTemplate(String approveContentId) {
		try {
			JSONObject orderJsonObject = new JSONObject();
			orderJsonObject.put("approve_content_id", approveContentId);
			StringBuffer result = RequestUtil.doServerHttpOpenapiNew(orderJsonObject.toJSONString(), NEW_DCOOS_URL + "/hncrm/queryApproveTemplate", "V1.0.00", "根据审批类型获取审批模板API");
			JSONObject jsonObject = JSONObject.parseObject(result.toString());
			if ("0000".equals(jsonObject.getString("code"))) {
				return success(jsonObject);
			} else {
				return fail(jsonObject.getString("message"));
			}
		} catch (Exception e) {
			logger.error("十卡查询异常：", e);
			return fail("十卡查询异常:" + e.getMessage());
		}
	}

	/**
	 * <AUTHOR>
	 * @date 2025/3/21
	 * 扫楼H5订单创建
	 */
	public static Map<String, String> createH5CrmOrder(Map<String, Object> condition, String customerPhone) {
		Map<String, String> resultMap = new HashMap<>();
		String lan_id = StringUtil.getMapByKey("lan_id", condition);
		String acc_num = StringUtil.getMapByKey("acc_num", condition);
		String col1 = StringUtil.getMapByKey("col1", condition);
		String offer_id = StringUtil.getMapByKey("offer_id", condition);
		String offer_id1 = StringUtil.getMapByKey("offer_id1", condition);
		String offer_id2 = StringUtil.getMapByKey("offer_id2", condition);
		String offer_id3 = StringUtil.getMapByKey("offer_id3", condition);
		String owner_cust_id = StringUtil.getMapByKey("owner_cust_id", condition);
		String use_cust_id = StringUtil.getMapByKey("use_cust_id", condition);
		String sale_code = StringUtil.getMapByKey("sale_code", condition);
		String x_payType = StringUtil.getMapByKey("x_payType", condition);
		String x_payTypePrice = StringUtil.getMapByKey("x_payTypePrice", condition);
		String prestoresales = StringUtil.getMapByKey("prestoresales", condition);
		String col4 = StringUtil.getMapByKey("col3", condition);
		String pre_store = StringUtil.getMapByKey("pre_store", condition);
		String phone_nbr_price = StringUtil.getMapByKey("phone_nbr_price", condition);
		String numberLevel = StringUtil.getMapByKey("numberLevel", condition);
		String col6 = StringUtil.getMapByKey("col6", condition);
		String offer_id5 = StringUtil.getMapByKey("offer_id5", condition);
		String handle_cust_id = StringUtil.getMapByKey("handle_cust_id", condition);

		String root = createStrH5(lan_id, offer_id, offer_id1, offer_id2, offer_id3, acc_num, col1, owner_cust_id,
				use_cust_id, sale_code, x_payType, x_payTypePrice, prestoresales, col4, phone_nbr_price, pre_store,
				numberLevel, col6, offer_id5, handle_cust_id); //订购报文拼接
		String result = "";
		try {
			result = RequestUtil.sendPost(getEopUrl(), root, "智慧扫楼H5crm订单创建接口");
		} catch (Exception e) {
			logger.error("智慧扫楼H5crm订单创建异常:", e);
			resultMap.put("code", "s1");
			return resultMap;
		}
		logger.info("智慧扫楼H5crm订单创建返回报文：{}", result);

		JSONObject jsonObject = JSONObject.parseObject(result);
		String resCode = jsonObject.getString("res_code");
		if ("00000".equals(resCode)) {
			// 接口调用成功获取成功
			String rspCode = jsonObject.getJSONObject("result").getString("Code");
			if ("0000".equals(rspCode)) {
				// 查询号码成功
				String custOrderId = jsonObject.getJSONObject("result").getJSONObject("CustOrder")
						.getString("cust_order_id");
				String askId = "zero";
				try {
					JSONArray ask = jsonObject.getJSONObject("result").getJSONObject("CustOrder")
							.getJSONArray("OrderItem");
					for (int i = 0; i < ask.size(); i++) {
						askId = ask.getJSONObject(i).getString("ask_id");
					}
					String needAmount = jsonObject.getJSONObject("result").getJSONObject("CustOrder")
							.getJSONObject("OrderFee").getString("need_amount");
					resultMap.put("needAmount", needAmount);
				} catch (Exception e) {
					logger.error("智慧扫楼H5crm订单创建获取ask_id异常:", e);
				}
				resultMap.put("code", "0");
				resultMap.put("custOrderId", custOrderId);
				resultMap.put("askId", askId);
				return resultMap;
			} else {
				String message = jsonObject.getJSONObject("result").getString("Message");
				logger.info("智慧扫楼H5crm订单创建失败，失败原因:{}——————{}", customerPhone, message);
				resultMap.put("code", rspCode);
				resultMap.put("message", message);
				return resultMap;
			}
		}
		resultMap.put("code", "s0");
		return resultMap;
	}

	/**
	 * CRM订单创建请求报文封装
	 *
	 * @param lan_id（本地网标识）
	 * @param offer_id（商品编码）
	 * @param offer_id1（可选包）
	 * @param offer_id2（优惠包）
	 * @param offer_id3（goodsRelDao）
	 * @param acc_num（客户选择的手机号码）
	 * @param col1（手机卡号ICCID）
	 * @param owner_cust_id（客户CUST_ID）
	 * @param use_cust_id（客户CUST_ID）
	 * @param sale_code（销售员编码）
	 * @param x_payType（关联商品编码）
	 * @param x_payTypePrice（X元预存金额）
	 * @param offer_id4                宽带商品编码
	 * @param school_crm               学校CRM编码
	 * @param phone_nbr_price          保底费用
	 * @param pre_store                预存费用
	 * @return
	 */
	public static String createStrH5(String lan_id, String offer_id, String offer_id1, String offer_id2, String offer_id3, String acc_num,
									 String col1, String owner_cust_id, String use_cust_id, String sale_code, String x_payType, String x_payTypePrice,
									 String prestoresales, String col4, String phone_nbr_price, String pre_store, String numberLevel, String col6,
									 String offer_id5, String handle_cust_id) {
		String root = "{\n" + "    \"access_token\": \"" + ACCESS_TOKEN + "\",\n"
				+ "    \"method\": \"bss.ord.order.Acceptance\",\n" + "    \"version\": \"1.0\",\n" + "    \"content\": {\n"
				+ "        \"Root\": {\n" + "            \"Header\": {\n" + "                \"ExchangeId\": \""
				+ StringUtil.getExchangeId("HNWAPT") + "\",\n" + "                \"BizCode\": \"Order\",\n"
				+ "                \"ClientId\": \"HNWAPT\",\n" + "                \"Password\": \"123456\"\n"
				+ "            },\n" + "            \"OrderRequest\": {\n"
				+ "                \"service_action\": \"cretateOrderToCalfee\",\n" + "                \"lan_id\": \""
				+ lan_id + "\",\n";
		if (!StringUtil.isEmpty(handle_cust_id)) {// 经办人cusdid 成年人
			root += "\"handle_cust_id\":\"" + handle_cust_id + "\",\n";
		}
		root += "                \"CompInstance\": [{" + "                        \"PromotionA\": {\n"
				+ "                            \"sale_code\": \"" + sale_code + "\",\n"
				+ "                            \"lan_id\": \"" + lan_id + "\",\n"
				+ "                            \"is_xtlj\":\"1\"" + "                        },\n"
				+ "                        \"action_type\": \"A\",\n" + "                        \"offer_id\": \""
				+ offer_id + "\",\n" + "                        \"pay_mode\": \"100000\",\n"
				+ "                        \"ProdOfferInst\": [\n";
		String str = "";
		if (!StringUtil.isEmpty(offer_id1)) {
			String[] offerId1 = offer_id1.split(",");
			for (int i = 0; i < offerId1.length; i++) {
				String[] id1 = offerId1[i].split("-");
				if ("1".equals(id1[1])) {
					str += "{\n" + "\"offer_id\": \"" + id1[0] + "\",\n" + "\"action_type\": \"A\",\n" + "\"col6\": \""
							+ col6 + "\"\n" + "},";
				} else {
					str += "{\n" + "\"offer_id\": \"" + id1[0] + "\",\n" + "\"action_type\": \"A\"\n" + "},";
				}
			}
		}
		if (!StringUtil.isEmpty(offer_id2)) {
			String[] offerId2 = offer_id2.split(",");
			for (int i = 0; i < offerId2.length; i++) {
				String[] id2 = offerId2[i].split("-");
				if ("1".equals(id2[1])) {
					str += "{\n" + "\"offer_id\": \"" + id2[0] + "\",\n" + "\"action_type\": \"A\",\n" + "\"col6\": \""
							+ col6 + "\"\n" + "},";
				} else {
					str += "{\n" + "\"offer_id\": \"" + id2[0] + "\",\n" + "\"action_type\": \"A\"\n" + "},";
				}
			}
		}

		if (str != null && str.trim().length() > 0) {// 去掉尾部的,
			int index = str.lastIndexOf("},");
			str = str.substring(0, index + 1);
		}
		root += str + "                        ],\n" + "                        \"AccessProdInst\": {\n"
				+ "							\"ProdInstAcct\": {\n" +
				"                                \"action_type\": \"A\",\n" +
				"                                \"account_id\": \"\"\n" +
				"                            },"
				+ "                            \"action_type\": \"A\",\n"
				+ "                            \"acc_num\": \"" + acc_num + "\",\n"
				+ "                            \"prod_inst_id\": \"-1\",\n" + "                            \"col1\": \""
				+ col1 + "\",\n" + "                            \"owner_cust_id\": \"" + owner_cust_id + "\",\n"
				+ "                            \"prod_inst_acct_attr\": \"is_new_acct_flag\",\n"
				+ "                            \"use_cust_id\": \"" + use_cust_id + "\",\n"
				+ "                            \"prod_id\": \"********\",\n"
				+ "                            \"base_offer_id\": \"********\",\n"
				+ "                            \"lan_id\": \"" + lan_id + "\",\n"

				+ "                            \"pre_store\": \"" + pre_store + "\",\n"
				+ "                            \"phone_nbr_price\": \"" + phone_nbr_price + "\",\n"
				+ "                            \"numberLevel\": \"" + numberLevel + "\",\n"

				+ "                            \"col2\": \"10\",\n"
				+ "                            \"address_desc\": \"0101\",\n"
				+ "                            \"bill_type\": \"0\",\n"
				+ "                            \"important_level\": \"3\",\n"
				+ "                            \"attrib_11\": \"255\",\n"
				+ "                            \"comp_role_id\": \"70000045\",\n"
				+ "                            \"cstop_flag\": \"0\",\n"
				+ "                            \"attrib_19\": \"50\",\n"
				+ "                            \"attrib_10\": \"T\",\n"
				+ "                            \"attrib_20\": \"G\",\n"
				+ "                            \"attrib_15\": \"3\",\n"
				+ "                            \"attrib_76\": \"F\",\n"
				+ "                            \"col15\": \"1\",\n" + "\"FuncProdInst\":[\n"
				+ "{\n" +
				"                  \"action_type\": \"A\",\n" +
				"                  \"prod_id\": \"80001307\"\n" +
				"                },\n" +
				"                {\n" +
				"                  \"action_type\": \"A\",\n" +
				"                  \"prod_id\": \"80014967\"\n" +
				"                },\n" +
				"                {\n" +
				"                  \"action_type\": \"A\",\n" +
				"                  \"prod_id\": \"80001009\"\n" +
				"                }," +
				"                {\n" +
				"                  \"action_type\": \"A\",\n" +
				"                  \"prod_id\": \"80434886\"\n" +
				"                },"
				+ "{\"action_type\":\"A\",\n" + "\"prod_id\":\"80001323\"\n" + "}\n";
		root += "],\n";
		if (!StringUtil.isEmpty(offer_id5)) {
			String[] offerId5 = offer_id5.split(",");
			root += "                            \"ProdOfferInst\": [{\n"
					+ "                                \"offer_id\": \"1124824\",\n"
					+ "                                \"action_type\": \"A\",\n"
					+ "                                \"col1\": \"20\"\n" + "                              }";
			for (int i = 0; i < offerId5.length; i++) {
				String[] id5 = offerId5[i].split("-");
				if ("1".equals(id5[1])) {
					root += ",{                               " + "\"offer_id\": \"" + id5[0] + "\",\n"
							+ "\"action_type\": \"A\",\n" + "\"col6\": \"" + col6 + "\"\n" + "}\n";
				} else {
					root += ",{\n                              " + "\"offer_id\": \"" + id5[0] + "\",\n"
							+ "\"action_type\": \"A\"\n" + "}\n";
				}
			}
			root += "									],\n";
		} else {
			root += "                            \"ProdOfferInst\": {\n"
					+ "                                \"offer_id\": \"1124824\",\n"
					+ "                                \"action_type\": \"A\",\n"
					+ "                                \"col1\": \"20\"\n" + "                              },\n";
		}
		root += "					\"PromotionA\": {\n" + "                                \"sale_code\": \""
				+ sale_code + "\",\n" + "\"is_xtlj\":\"1\"," + "                                \"offer_id\": \""
				+ x_payType + "\",\n" + "                                \"lan_id\": \"" + lan_id + "\"\n"
				+ "                            }" + "                        }\n" + "                    }\n";
		String s = "";
		if (!StringUtil.isEmpty(x_payType)) {
			s = ",{" + "\"offer_id\": \"" + x_payType + "\"," + "\"action_type\": \"A\"," + "\"col3\":\""
					+ x_payTypePrice + "\"," + "\"OfferProdInstRela\": {" + "\"prod_inst_id\": \"-1\","
					+ "\"comp_role_id\": \"70000045\"" + " }" + "}";
			root += s;
		}
		String offerId3Str = "";
		if (!StringUtil.isEmpty(offer_id3)) {
			String[] offerId3 = offer_id3.split(",");
			for (int i = 0; i < offerId3.length; i++) {
				String[] id3 = offerId3[i].split("-");
				if ("1".equals(id3[1])) {
					offerId3Str += ",{" + "\"offer_id\": \"" + id3[0] + "\"," + "\"action_type\": \"A\","
							+ "\"col6\": \"" + col6 + "\",\n" + " \"PromotionA\": {\n" + "    \"sale_code\": \""
							+ sale_code + "\"\n" + "  },\n" + "\"OfferProdInstRela\": {" + "\"prod_inst_id\": \"-1\","
							+ "\"comp_role_id\": \"70000045\"" + "}" + "}";
				} else {
					offerId3Str += ",{" + "\"offer_id\": \"" + id3[0] + "\"," + "\"action_type\": \"A\","
							+ " \"PromotionA\": {\n" + "    \"sale_code\": \"" + sale_code + "\"\n" + "  },\n"
							+ "\"OfferProdInstRela\": {" + "\"prod_inst_id\": \"-1\","
							+ "\"comp_role_id\": \"70000045\"" + "}" + "}";
				}
			}
		}
		root += offerId3Str;
		String c3 = "";
		if (!StringUtil.isEmpty(prestoresales)) {
			c3 = ",{" + "\"offer_id\": \"" + prestoresales + "\"," + "\"action_type\": \"A\"," + "\"col3\":\"" + col4
					+ "\"," + "\"OfferProdInstRela\": {" + "\"prod_inst_id\": \"-1\","
					+ "\"comp_role_id\": \"70000045\"" + " }" + "}";
		}
		root += c3 + "]\n" + "}\n" + "}\n" + "}\n" + "}";
		return root;
	}

	/**
	 * BSS上传客户图像信息
	 *
	 * @return
	 */
	public static String uploadCustOrderImage(Map<String, Object> condition, AmazonS3 amazonS3) {
		JSONObject map = new JSONObject();
		Map<String, Object> resultMap = new HashMap<>();
		map.put("access_token", ACCESS_TOKEN);
		map.put("method", "bss.ord.cust.uploadCustOrderImage");
		map.put("version", "1.0");
		JSONObject param = new JSONObject();
		param.put("photo", getImgBase64(amazonS3, StringUtil.getMapByKey("image_best", condition)));//人证比对免冠照
		param.put("cert_front", getImgBase64(amazonS3, StringUtil.getMapByKey("image_idcard", condition)));//身份证免冠照
		//工作证集合
		if (StringUtil.isNotNull(StringUtil.getMapByKey("workPermitList", condition))) {
			JSONArray jsonArray = JSONArray.parseArray(StringUtil.getMapByKey("workPermitList", condition));
			for (int i = 0; i < jsonArray.size(); i++) {
				if (i == 0) {
					param.put("other", getImgBase64(amazonS3, jsonArray.getString(i)));
				} else if (i == 1) {
					param.put("file_content", getImgBase64(amazonS3, jsonArray.getString(i)));
				} else if (i == 2) {
					param.put("cert_back", getImgBase64(amazonS3, jsonArray.getString(i)));//免冠照
				}
			}
		} else {
			param.put("file_content", "");
			param.put("other", "");
			param.put("photo", "");
		}
		param.put("lan_id", StringUtil.getMapByKey("lan_id", condition));
		param.put("cust_order_id", StringUtil.getMapByKey("cust_order_id", condition));
		map.put("content", param);
		try {
			String result = RequestUtil.doPost(getEopUrl(), map.toString());
			logger.info("BSS上传客户图像信息返回:{}", result);
			JSONObject parseObject = JSONObject.parseObject(result);
			String resCode = parseObject.getString("res_code");
			if (resCode == null || !"00000".equals(resCode)) {
				return null;
			}
			JSONObject jsonObject = JSONObject.parseObject(result).getJSONObject("result").getJSONObject("Result");
			String rspCode = jsonObject.getString("result_code");
			if ("0000".equals(rspCode)) {
				return "0";
			} else {
				return "1";
			}
		} catch (Exception e) {
			logger.error("BSS上传客户图像信息异常:", e);
			return "1";
		}
	}

	public static String getImgBase64(AmazonS3 amazonS3, String image_best) {
		String[] parts1 = image_best.split("/");
		if (parts1.length == 3) {
			return FileUtil.getImageBytess(amazonS3, parts1[0], parts1[1] + "/" + parts1[2]);
		} else {
			return FileUtil.getImageBytess(amazonS3, parts1[0], parts1[1]);
		}
	}


	/**
	 * <AUTHOR>
	 * @date 2025/3/25
	 * 扫楼H5订单正式单同步到BPS
	 */
	public static Map<String, String> autoOrdersSyncs(HnslH5Order orderPojo, HnslH5Goods goodsPojo) {
		Map<String, String> resultMap = new HashMap<>();
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String result = "";
		try {
			JSONObject body = new JSONObject();
			body.put("access_token", ACCESS_TOKEN);
			body.put("method", "syn.orderinfo.syncRobotOrder");
			body.put("version", "1.0");

			JSONObject obj = new JSONObject();
			obj.put("webOrder", orderPojo.getOrderId());
			obj.put("connectType", "602");
			obj.put("orderType", "9087");
			obj.put("createdDate", simpleDateFormat.format(new Date()));
			obj.put("citycode", "0" + orderPojo.getCitycode());
			obj.put("gatewayAccount", ConstantUtil.ACCOUNT_ID);//支付账号
			obj.put("goodsName", goodsPojo.getGoodsName());
			obj.put("goodsNumber", orderPojo.getGoodsNumber());
			obj.put("custId", StringUtil.getStringValue(orderPojo.getChildCustId()));
			obj.put("customerName", orderPojo.getCustomerName());
			obj.put("customerCardValidity", StringUtil.getStringValue(orderPojo.getCustomerCardValidity()));
			obj.put("customerCard", orderPojo.getCustomerCard());
			obj.put("customerAddress", StringUtil.getStringValue(orderPojo.getCustomerAddress()));
			obj.put("customerContactPhone", orderPojo.getCustomerContactPhone());
			obj.put("customerContactPhone2", orderPojo.getCustomerPhone());
			obj.put("orderPrice", orderPojo.getOrderPrice());
			obj.put("payChannel", StringUtil.getStringValue(orderPojo.getPayChannel()));
			obj.put("customerPhone", orderPojo.getCustomerPhone());
			obj.put("iccid", orderPojo.getIccid());
			obj.put("orderStatus", "A1001");
			obj.put("orderSubmitDate", simpleDateFormat.format(new Date()));
			obj.put("orderRemark", "");//备注
			obj.put("oldCrmOrderId", "");
			obj.put("payStatus", "0");
			obj.put("payTime", simpleDateFormat.format(new Date()));
			obj.put("otherOrderNo", orderPojo.getCrmOrderId());//CRM正式单号
			obj.put("cps1", StringUtil.getStringValue(goodsPojo.getCpsList()));
			obj.put("cps2", "");
			obj.put("besttoneOrderItemNo", orderPojo.getOrderId());//号百流水号（用于自动退款）
			obj.put("phongOfferName", "");//宽带套餐包 多个用+区分
			obj.put("phoneType", "1");

			body.put("content", obj.toJSONString());
			result = RequestUtil.doPost(getNkEopUrl(), body.toJSONString(), "正式单同步BPS系统");
			//result = RequestUtil.sendToPost("http://134.176.22.203:8080/iface/http/order/createOrder.do", obj.toJSONString());
			JSONObject restJson = JSONObject.parseObject(result);
			resultMap.put("code", restJson.getJSONObject("result").getString("code"));
		} catch (Exception e) {
			logger.error("正式单同步BPS系统异常", e);
			resultMap.put("code", "1");
		}
		return resultMap;
	}

	/**
	 * <AUTHOR>
	 * @date 2025/3/25
	 * 智慧扫楼H5订单确认
	 */
	public static JSONObject crmSureOrders(Map<String, Object> condition) {
		JSONObject resultJson = new JSONObject();
		String lan_id = StringUtil.getMapByKey("lan_id", condition);
		String pay_amount = StringUtil.getMapByKey("pay_amount", condition);
		String cust_order_id = StringUtil.getMapByKey("cust_order_id", condition);
		String sms_tel = StringUtil.getMapByKey("sms_tel", condition);
		String is_fan_channel = StringUtil.getMapByKey("is_fan_channel", condition);
		String pay_id = StringUtil.getMapByKey("pay_id", condition);
		String root = commitSureStr(lan_id, cust_order_id, sms_tel, pay_amount, is_fan_channel, pay_id);
		String result = "";
		try {
			result = RequestUtil.sendPost(getNkEopUrl(), root, "智慧扫楼H5CRM订单确认接口");
		} catch (Exception e) {
			logger.error("智慧扫楼H5CRM宽带订单创建异常:", e);
			resultJson.put("Code", "001");
			resultJson.put("Message", e.getMessage());
			return resultJson;
		}
		logger.info("智慧扫楼CRM订单确认接口返回报文：{}", result);
		JSONObject jsonObject = JSONObject.parseObject(result);
		String resCode = jsonObject.getString("res_code");

		if ("00000".equals(resCode)) {
			JSONObject result1 = jsonObject.getJSONObject("result");
			// 接口调用成功获取成功
			String rspCode = result1.getString("Code");
			if ("0000".equals(rspCode) || "0".equals(rspCode)) {
				resultJson.put("Code", "0");
				resultJson.put("Message", jsonObject.getString("Message"));
			} else {// {"code":"30005","msg":"EOP:获取应答超时!"}
				resultJson.put("Code", rspCode);
				resultJson.put("Message", result1.getString("Message"));
			}
		} else {
			resultJson.put("Code", resCode);
			resultJson.put("Message", jsonObject.getString("res_message"));
		}
		logger.info("智慧扫楼H5CRM订单确认接口返回报文保存redis:{}", resultJson);
		return resultJson;
	}


	public static String commitSureStr(String lan_id, String cust_order_id, String sms_tel, String pay_amount,
									   String is_fan_channel, String pay_id) {
		String root = "{\n" + "\"access_token\":\"" + ACCESS_TOKEN + "\",\n"
				+ "\"method\":\"bss.ord.order.Acceptance\",\n" + "\"version\":\"1.0\",\n" + "\"content\":{\n"
				+ "\t\"Root\":{\n" + "    \"Header\": {\n" + "        \"BizCode\": \"Order\", \n"
				+ "        \"Password\": \"123456\", \n" + "        \"ExchangeId\": \"" + StringUtil.getExchangeId("HNWAPT")
				+ "\", \n" + "        \"ClientId\": \"HNWAPT\"\n" + "    }, \n" + "    \"OrderRequest\": {\n"
				+ "        \"service_action\": \"COMMIT_FEE\", \n" + "        \"lan_id\": \"" + lan_id + "\", \n"
				+ "        \"CompInstance\": {\n" + "            \"pay_mode\": \"100000\"\n" + "        }, \n"
				+ "        \"CustOrder\": {\n" + "            \"cust_order_id\": \"" + cust_order_id + "\"";
		if (!StringUtil.isEmpty(pay_amount)) {
			root += ",\n             \"pay_amount\": \"" + pay_amount + "\"";
		}
		if (!StringUtil.isEmpty(lan_id)) {
			root += ", \n" + "            \"lan_id\": \"" + lan_id + "\", \n" + "            \"book_begin_time\": \""
					+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\"";
		}
		if (!StringUtil.isEmpty(sms_tel)) {
			root += ", \n" + "            \"is_open\": \"1\", \n" + "            \"contact_name\": \"\", \n"
					+ "            \"sms_tel\": \"" + sms_tel + "\"";
		}
		if (!StringUtil.isEmpty(is_fan_channel)) {
			root += ", \n" + "            \"is_fan_channel\": \"" + is_fan_channel + "\"";
		}
		root += "        }\n" + "    }\n" + "}\n" + "}\n" + "}";
		return root;
	}

	/**
	 * <AUTHOR>
	 * @date 2024/12/4
	 * 湖南CRM收费确认订单免填单生成与查询接口包年
	 */
	public static JSONObject hnCrmGetPdf(String busi_type, String cust_order_id) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("busi_type", busi_type);//1是查是否签字，2是生成免填单
		jsonObject.put("cust_order_id", cust_order_id);//87单号
		jsonObject.put("sys_source", "ZHXYJK"); //固定来源不变
		try {
			StringBuffer rest = RequestUtil.doServerHttpOpenapiNew(jsonObject.toJSONString(), NEW_DCOOS_URL + "/hnCrm/getPdf/getPdf", "湖南CRM收费确认订单免填单生成与查询接口");
			String result = rest.toString();
			logger.info("湖南CRM收费确认订单免填单生成与查询接口返回：{}", result);
			if (result.contains("http://**************:18082")) {
				result = result.replace("http://**************:18082", "https://wx.hn.189.cn");
			} else {
				result = result.replace("http://**************:7201", "https://wx.hn.189.cn");
			}
			return JSON.parseObject(result);
		} catch (Exception e) {
			logger.error("湖南CRM收费确认订单免填单生成与查询接口异常：", e);
			return null;
		}
	}

	/**
	 * 掌上销存根回执推送包年 DCOOS版
	 *
	 * @param map
	 * @return
	 */
	public static JSONObject zsxStubReceiptPush(Map<String, Object> map) {
		JSONObject body = new JSONObject();
		body.put("custOrderId", StringUtil.getMapByKey("custOrderId", map));//单号
		body.put("lanId", StringUtil.getMapByKey("lanId", map));//地市编码
		body.put("receipt_img_src", "9");
		String rest = null;
		try {
			logger.info("掌上销存根回执推送包年入参:{}", body.toJSONString());
			rest = RequestUtil.postHttpByUrlID(NEW_DCOOS_URL + "/orderPrint/orderPrint", body.toJSONString(), ConstantUtil.YXS_AppId, ConstantUtil.YXS_AppKey);
			logger.info("掌上销存根回执推送包年返回:{}", rest);
			return JSONObject.parseObject(rest);
		} catch (Exception e) {
			logger.error("掌上销存根回执推送包年异常", e);
			return null;
		}
	}

	/**
	 * <AUTHOR>
	 * @date 2025/3/25
	 * 扫楼H5订单正式单同步到BPS
	 */
	public static Map<String, String> autoOrderSyncs(HnslH5Order orderPojo, HnslH5Goods goodsPojo) {
		Map<String, String> resultMap = new HashMap<>();
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String result = "";
		try {
			JSONObject body = new JSONObject();
			body.put("access_token", ACCESS_TOKEN);
			body.put("method", "syn.orderinfo.syncRobotOrder");
			body.put("version", "1.0");

			JSONObject obj = new JSONObject();
			obj.put("webOrder", orderPojo.getOrderId());
			obj.put("connectType", "602");
			obj.put("orderType", "9087");
			obj.put("createdDate", simpleDateFormat.format(new Date()));
			obj.put("citycode", "0" + orderPojo.getCitycode());
			obj.put("gatewayAccount", ConstantUtil.ACCOUNT_ID);//支付账号
			obj.put("goodsName", goodsPojo.getGoodsName());
			obj.put("goodsNumber", orderPojo.getGoodsNumber());
			obj.put("custId", StringUtil.getStringValue(orderPojo.getChildCustId()));
			obj.put("customerName", orderPojo.getCustomerName());
			obj.put("customerCardValidity", StringUtil.getStringValue(orderPojo.getCustomerCardValidity()));
			obj.put("customerCard", orderPojo.getCustomerCard());
			obj.put("customerAddress", StringUtil.getStringValue(orderPojo.getCustomerAddress()));
			obj.put("customerContactPhone", orderPojo.getCustomerContactPhone());
			obj.put("customerContactPhone2", orderPojo.getCustomerPhone());
			obj.put("orderPrice", orderPojo.getOrderPrice());
			obj.put("payChannel", StringUtil.getStringValue(orderPojo.getPayChannel()));
			obj.put("customerPhone", orderPojo.getCustomerPhone());
			obj.put("iccid", orderPojo.getIccid());
			obj.put("orderStatus", "A1001");
			obj.put("orderSubmitDate", simpleDateFormat.format(new Date()));
			obj.put("orderRemark", "");//备注
			obj.put("oldCrmOrderId", "");
			obj.put("payStatus", "0");
			obj.put("payTime", simpleDateFormat.format(new Date()));
			obj.put("otherOrderNo", orderPojo.getCrmOrderId());//CRM正式单号
			obj.put("cps1", StringUtil.getStringValue(goodsPojo.getCpsList()));
			obj.put("cps2", "");
			obj.put("besttoneOrderItemNo", orderPojo.getOrderId());//号百流水号（用于自动退款）
			obj.put("phongOfferName", "");//宽带套餐包 多个用+区分
			obj.put("phoneType", "1");

			body.put("content", obj.toJSONString());
			result = RequestUtil.doPost(getEopUrl(), body.toJSONString(), "正式单同步BPS系统");
			//result = RequestUtil.sendToPost("http://134.176.22.203:8080/iface/http/order/createOrder.do", obj.toJSONString());
			JSONObject restJson = JSONObject.parseObject(result);
			resultMap.put("code", restJson.getJSONObject("result").getString("code"));
		} catch (Exception e) {
			logger.error("正式单同步BPS系统异常", e);
			resultMap.put("code", "1");
		}
		return resultMap;
	}

	/**
	 * 支付记录同步到BPS DCOOS版
	 *
	 * @param map
	 * @return
	 */
	public static JSONObject createPayRecord(Map<String, Object> map) {
		JSONObject body = new JSONObject();
		body.put("payOrderId", StringUtil.getMapByKey("payOrderId", map));//支付订单号
		body.put("payMoney", StringUtil.getMapByKey("payMoney", map));//支付金额(分)
		body.put("payWebOrder", StringUtil.getMapByKey("payOrderId", map));//业务订单号
		body.put("payMethod", "ZXZF");//支付方式(ZXZF:在线支付、XJZF：现金，WXZF:无需付款)
		body.put("payTime", StringUtil.getMapByKey("payTime", map));//支付时间
		body.put("paySource", StringUtil.getMapByKey("paySource", map));//支付渠道
		body.put("payPlatformId", StringUtil.getMapByKey("payPlatformId", map));//支付平台号
		body.put("payItemId", StringUtil.getMapByKey("payItemId", map));//销售品ID
		body.put("payAccPhone", StringUtil.getMapByKey("payAccPhone", map));//业务号码
		body.put("gatewayAccount", ACCOUNT_ID);//网关账户
		body.put("revenueType", "YS");//营收类型(YS：营收资金、FYS非营收资金)
		body.put("payChannel", StringUtil.getMapByKey("payMethod", map));//支付渠道(ZFB：支付宝、WX：微信、YZF:翼支付、DYQB:抖音钱包，XJ:现金，JDQB:京东钱包)
		body.put("payMerchantId", StringUtil.getMapByKey("payMerchantId", map));//支付商户号
		String rest = null;
		try {
			logger.info("支付记录同步到BPS入参" + body.toJSONString());
			//	System.out.println("支付记录同步到BPS入参"+body.toJSONString());
			rest = RequestUtil.postHttpByUrlID(getEopUrl() + "payRecord/createPayRecord.do",
					body.toJSONString(), ConstantUtil.xAppId, ConstantUtil.xAppKey);
			logger.info("支付记录同步到BPS返回:" + rest);
			//	System.out.println("支付记录同步到BPS返回:"+rest);
			return JSONObject.parseObject(rest);
		} catch (Exception e) {
			logger.error("查询熟卡 审核状态接口异常{}", e);
			return null;
		}
	}

	/**
	 * wm 2024-05-14 微信信用授权获取token接口
	 *
	 * @return
	 */
	public static R getToken() {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "qry.hbwx.getToken");
		body.put("version", "1.0");

		content.put("customerNo", ConstantUtil.customerNo);
		body.put("content", content);
		String rest = null;
		try {
			rest = RequestUtil.sendPost(url, body.toJSONString(), "微信信用授权获取token接口");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return R.error(1, "微信信用授权获取token接口失败");
			} else {
				JSONObject result = restJson.getJSONObject("result");
				if (result.get("returnCode").equals("10000")) {
					String decryptStr = Des3Util
							.desDecrypt((String) result.get("token"), ConstantUtil.keyHex, ConstantUtil.ivHex).trim();
					return R.ok("微信信用授权获取token接口成功").put("data", decryptStr);
				} else {
					return R.error(1, "微信信用授权获取token接口失败");
				}
			}
		} catch (IOException e) {
			logger.info("微信信用授权获取token接口异常：", e);
			return R.error(1, "微信信用授权获取token接口异常：" + e.getMessage());
		} catch (Exception e) {
			logger.info("微信信用授权token解密异常：", e);
			return R.error(1, "微信信用授权token解密异常:" + e.getMessage());
		}
	}

	/**
	 * 微信解冻
	 *
	 * @param besttoneOrderItemNo
	 * @return
	 */
	public static R cancelExamOnline(String besttoneOrderItemNo) {
		Map<String, Object> tokenRest = getToken();

		JSONObject paramJson = new JSONObject();
		paramJson.put("token", tokenRest.get("data"));
		paramJson.put("besttoneOrderItemNo", besttoneOrderItemNo);
		String paramDesStr = null;
		try {
			paramDesStr = Des3Util
					.desEncrypt(paramJson.toJSONString().getBytes("GBK"), ConstantUtil.keyHex, ConstantUtil.ivHex)
					.trim();
		} catch (Exception e1) {
			logger.info("取消微信免押加密异常1", e1);
		}

		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "syn.hbwx.cancelExamOnline");
		body.put("version", "1.0");

		content.put("customerNo", ConstantUtil.customerNo);
		content.put("paramJson", paramDesStr);
		body.put("content", content);

		String rest = null;

		logger.info("查询微信信用授权结果（线上渠道）接口请求报文：" + body.toString());
		try {
			rest = RequestUtil.sendToPost(url, body.toString());
			logger.info("查询微信信用授权结果（线上渠道）接口返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return R.error(1, "查询微信信用授权结果（线上渠道）接口失败");
			} else {
				JSONObject result = restJson.getJSONObject("result");
				if (result.get("returnCode").equals("10000")) {
					JSONObject json = new JSONObject();
					json.put("code", 0);
					json.put("message", "查询微信信用授权结果（线上渠道）接口成功");
					json.put("data", result);
					return R.ok(json);
				} else {
					return R.error(1, "查询微信信用授权结果（线上渠道）接口失败");
				}
			}
		} catch (IOException e) {
			logger.info("微信免押取消接口异常：", e);
			return R.error(1, "微信免押取消接口异常");
		}
	}

	/**
	 * 查询用户微信订单接口 接口功能描述:根据证件号，业务号码，查询用户微信竣工订单信息
	 *
	 * @param phone 冻结业务号码
	 * @param idnum 冻结证件号
	 * @return
	 * @throws Exception
	 * @throws UnsupportedEncodingException
	 */
	public static R queryCancelExamTosafe(JSONObject params) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();
		JSONObject paramJson = new JSONObject();
		try {
			body.put("access_token", ACCESS_TOKEN);
			body.put("method", "syn.hbsafe.cancelExamTosafe");
			body.put("version", "1.0");

			paramJson.put("token", System.currentTimeMillis());
			paramJson.put("phone", params.getString("phone"));
			paramJson.put("idnum", params.getString("customerCard"));
			paramJson.put("type", params.getString("type"));

			String paramDesStr = Des3Util
					.desEncrypt(paramJson.toJSONString().getBytes("GBK"), ConstantUtil.newKeyHex, ConstantUtil.ivHex).trim();

			content.put("customerNo", "HUNAN_ELEC");
			content.put("paramJson", paramDesStr);

			body.put("content", content);

			logger.info("查询用户微信订单接口入参:" + body.toJSONString());
			String result = RequestUtil.sendToPost(url, body.toJSONString());
			logger.info("查询用户微信订单接口响应:" + result);

			JSONObject restJson = JSONObject.parseObject(result);
			if (restJson.getJSONObject("result").get("returnCode").equals("10000")) {
				return R.ok("查询成功").put("data", JSONArray
						.parseArray(JSON.toJSONString(restJson.getJSONObject("result").getJSONArray("items"))));
			} else if (restJson.getJSONObject("result").get("returnCode").equals("20001")) {
				return R.error(1, restJson.getJSONObject("result").getString("message"));
			}
			return R.error(1, restJson.getJSONObject("result").getString("message"));
		} catch (Exception e) {
			logger.error("查询用户微信订单接口: ", e);
			return R.error(1, "查询异常" + e.getMessage());
		}
	}

	/**
	 * 查询可售模板列表 模板就是商品
	 *
	 * @param { "templateIdList": [24018,53008], "qryContent": "", "lanId":
	 *          "731","templateGroupId": "", "serviceOfferId": "1" }
	 * @return R
	 * @MethodName: qryTemplateList
	 * @<NAME_EMAIL>
	 * @date 2025-03-27 05:33:11
	 */
	public static R qryTemplateList(JSONObject body) {
		StringBuffer rest = null;
		try {
			rest = RequestUtil.doServerHttpOpenapiNew(JSON.toJSONString(body),
					NEW_DCOOS_URL + "/hncrm/qryTemplateList", "查询可售模板列表接口");
			if (rest == null) {
				return R.error(1, "可售模板列表为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONArray("resultObject"));
			}
		} catch (Exception e) {
			logger.error("查询可售模板列表接口异常：", e);
			return R.error(1, "查询可售模板列表异常：" + e.getMessage());
		}
	}

	/**
	 * 查询指定模板内容详情
	 *
	 * @param { "custId": "273100753572", "lanId": "731", "templateId": "19012",
	 *          "serviceOfferId": "1" }
	 * @return R
	 * @MethodName: qryTemplateInfo
	 * @<NAME_EMAIL>
	 * @date 2025-03-27 08:11:55
	 */
	public static R qryTemplateInfo(JSONObject body) {
		String rest = null;
		try {
			rest = RequestUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/qryTemplateInfo", null, "UTF-8",
					"查询指定模板内容详情接口", JSON.toJSONString(body));
			if (rest == null) {
				return R.error(1, "查询指定模板内容详情为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONObject("resultObject"));
			}
		} catch (Exception e) {
			logger.error("查询指定模板内容详情接口异常：", e);
			return R.error(1, "查询指定模板内容详情接口异常：" + e.getMessage());
		}
	}

	/**
	 * 对接湘安日志埋点
	 *
	 * @param sysLog
	 * @param request
	 * @param userAgent
	 * @param deviceType
	 * @param os
	 */
	public static void uebaLog(OperationRecord sysLog, HttpServletRequest request, String userAgent, String deviceType, String os) {
		User user = null;
		InetAddress localHost = null;
		try {
			localHost = InetAddress.getLocalHost();
			Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
			user = (User) authentication.getPrincipal();
		} catch (Exception e) {
			e.printStackTrace();
		}
		Map<String, Object> map = new HashMap<>();
		map.put("clientId", clientId);
		map.put("systemId", clientId);
		if (Objects.nonNull(user)) {
			map.put("uid", user.getUserId());
			map.put("mobilePhone", user.getPhone());
		}
		map.put("operType", sysLog.getLogType());
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String formattedTime = LocalDateTime.ofInstant(new Date().toInstant(), ZoneId.systemDefault()).format(formatter);
		map.put("eventTime", formattedTime);
		map.put("localTime", formattedTime);
		map.put("sendTime", formattedTime);
		map.put("deviceType", deviceType);
		map.put("osType", os);
		map.put("appVersion", "1.0");
		map.put("userAgent", userAgent);
		map.put("clientIp", IPUtils.getIpAddr(request));
		String ipAddr = localHost.getHostAddress();
		map.put("serverIp", ipAddr);
		map.put("menu", sysLog.getOperateMenu());
		map.put("queryCriteria", "");
		map.put("queryItems", sysLog.getParams());
		String jsonString = JSONObject.toJSONString(map);
		System.out.println("jsonString" + jsonString);
		String result = null;
		try {
			// 首次尝试调用接口
			result = RequestUtil.postHttpByUrlID(dcoos_url_eop + "sdc-web/tracing/log/usageData", jsonString, ConstantUtil.xAppId, ConstantUtil.xAppKey);
			logger.info("湘安日志埋点->{}" + result);
		} catch (Exception e) {
			logger.error("湘安日志埋点首次请求异常：", e);
			// 失败后等待10秒重试
			try {
				Thread.sleep(10000);
				logger.info("湘安日志埋点接口重试中...");
				result = RequestUtil.postHttpByUrlID(dcoos_url_eop + "sdc-web/tracing/log/usageData", jsonString, ConstantUtil.xAppId, ConstantUtil.xAppKey);
				logger.info("湘安日志埋点重试->{}" + result);
			} catch (Exception e2) {
				logger.error("湘安日志埋点重试请求异常：", e2);
			}
		}
	}


	/**
	 * 接入湘安平台登录日志
	 *
	 * @param logs
	 * @param request
	 */
	public static String logLoginDetails(LoginRecord logs, String uid, String name, HttpServletRequest request, String clientId, String systemName) {
		Map<String, Object> map = new HashMap<>();
		map.put("system_id", clientId);
		map.put("system_name", systemName);
		map.put("uid", uid);
		map.put("login_account", name);
		map.put("login_ip", "***************");
		map.put("login_method", "1");
		map.put("login_status", 1);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String formattedTime = LocalDateTime.ofInstant(new Date().toInstant(), ZoneId.systemDefault()).format(formatter);
		map.put("login_time", formattedTime);
		String jsonString = JSONObject.toJSONString(map);
		logger.info("湘安平台登录日志请求参数：{}", jsonString);
		String result = null;
		try {
			// 首次尝试调用接口
			result = RequestUtil.postHttpByUrlID(dcoos_url_eop + "loginlog/logLoginDetails//sas", jsonString, ConstantUtil.xAppId, ConstantUtil.xAppKey);
			logger.info("湘安平台登录日志->{}" + result);
		} catch (Exception e) {
			logger.error("湘安平台登录日志首次请求异常：", e);
			// 失败后等待10秒重试
			try {
				Thread.sleep(10000);
				logger.info("湘安平台登录日志接口重试中...");
				result = RequestUtil.postHttpByUrlID(dcoos_url_eop + "loginlog/logLoginDetails", jsonString, ConstantUtil.xAppId, ConstantUtil.xAppKey);
				logger.info("湘安平台登录日志重试->{}" + result);
			} catch (Exception e2) {
				logger.error("湘安平台登录日志重试请求异常：", e2);
			}
		}
		return result;
	}

	/**
	 * 熔断结果日志
	 *
	 * @param user
	 * @param menuName
	 */
	public static void breakerLog(User user, String menuName) {
		Map<String, Object> map = new HashMap<>();
		map.put("clientId", clientId);
		map.put("uid", user.getUserId());
		map.put("mobilePhone", user.getPhone());
		map.put("accountId", user.getUserId());
		map.put("account", user.getUsername());
		map.put("userName", user.getNickname());
		map.put("idCard", user.getIdCard());
		map.put("appName", "智慧扫楼管理平台");
		map.put("reason", "查询用户信息条数超过阈值，触发熔断");
		map.put("menu", menuName);
		String jsonString = JSONObject.toJSONString(map);
		logger.info("breakerLog->{}" + jsonString);
		String result = null;
		try {
			// 首次尝试调用接口
			result = RequestUtil.postHttpByUrlID(dcoos_url_eop + "dxFlow/breaker",
					jsonString, ConstantUtil.xAppId, ConstantUtil.xAppKey);
			logger.info("熔断结果日志->{}" + result);
		} catch (Exception e) {
			logger.error("熔断结果日志首次请求异常：", e);
			// 失败后等待10秒重试
			try {
				Thread.sleep(10000);
				logger.info("熔断结果日志接口重试中...");
				result = RequestUtil.postHttpByUrlID(dcoos_url_eop + "dxFlow/breaker",
						jsonString, ConstantUtil.xAppId, ConstantUtil.xAppKey);
				logger.info("熔断结果日志重试->{}" + result);
			} catch (Exception e2) {
				logger.error("熔断结果日志重试请求异常：", e2);
			}
		}
	}

	/**
	 * 获取用户信息 - 统一认证-智慧扫楼
	 */
	public static JSONObject slCall(String code) {
		JSONObject jsonObject = new JSONObject();
		SlAuthConfig authConfig = SpringContextHolder.getBean(SlAuthConfig.class);
		ConfigProperties configProperties = SpringContextHolder.getBean(ConfigProperties.class);
		logger.info("code:{}", code);
		logger.info("client_id:{}", authConfig.getClientId());
		logger.info("client_secret:{}", authConfig.getClientSecret());
		logger.info("redirect_uri:{}", authConfig.getRedirectUri());
		logger.info("authConfig:{}", authConfig.getMethodGetAccessToken());
		logger.info("X-APP-ID:{}", authConfig.getAppid());
		logger.info("X-APP-KEY:{}", authConfig.getAppkey());
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("grant_type", "authorization_code");
		paramMap.put("code", code);
		paramMap.put("client_id", authConfig.getClientId());
		paramMap.put("client_secret", authConfig.getClientSecret());
		paramMap.put("redirect_uri", authConfig.getRedirectUri());
		String result = null;
		try {
			result = HttpRequest.post(authConfig.getMethodGetAccessToken())
					.header("Content-Type", "application/x-www-form-urlencoded")
					.header("X-APP-ID", authConfig.getAppid())
					.header("X-APP-KEY", authConfig.getAppkey())
					.header("X-CTG-Province-ID", "HNX")
					.header("X-CTG-VERSION", "V1.0.00")
					.form(paramMap)
					.timeout(20000)
					.execute().body();
			logger.info("统一认证获取应用的授权token接口返回:{}", result);
			if (ObjectUtil.isNotEmpty(result)) {
				HashMap hashMap = JSON.parseObject(result, HashMap.class);
				if (0 == (int) hashMap.get("code")) {
					RSA rsa = new RSA(authConfig.getPrivateKey(), null);
					String decryptStr = rsa.decryptStr((String) hashMap.get("data"), KeyType.PrivateKey);
					HashMap map = JSON.parseObject(decryptStr, HashMap.class);
					//获取退出登陆token
					String accessToken = "Bearer " + map.get("access_token");
					// 解析 accessToken
					DecodedJWT decodeToken = JWT.decode((String) map.get("access_token"));
					// 获取用户信息
					jsonObject.put("account", decodeToken.getClaim("account").asString());
					jsonObject.put("certNumber", decodeToken.getClaim("certNumber").asString());
					jsonObject.put("ctHrCode", decodeToken.getClaim("ctHrCode").asString());
					jsonObject.put("mobilePhone", decodeToken.getClaim("mobilePhone").asString());
					jsonObject.put("userCode", decodeToken.getClaim("userCode").asString());
					jsonObject.put("username", decodeToken.getClaim("account").asString());
					jsonObject.put("tenant_id", 2); // 智慧扫楼租户ID
					jsonObject.put("accessToken", accessToken);
					logger.info("获取用户信息:" + decodeToken);
				} else {
					jsonObject.put("success", false);
					jsonObject.put("message", "解析用户信息异常，请联系管理员重试！");
					logger.error("智慧扫楼统一认证返回错误码：{}", hashMap.get("code"));
				}
			} else {
				jsonObject.put("success", false);
				jsonObject.put("message", "解析用户信息异常，请联系管理员重试！");
				logger.error("智慧扫楼统一认证返回结果为空");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("调用门户code换取token异常：" + e.getMessage());
			jsonObject.clear();
			jsonObject.put("success", false);
			jsonObject.put("message", "解析用户信息异常，请联系管理员重试！");
		}
		return jsonObject;
	}


	/**
	 * 获取用户信息 - 统一认证-省集约甩单
	 */
	public static JSONObject sjyCall(String code) {
		JSONObject jsonObject = new JSONObject();
		SjyAuthConfig authConfig = SpringContextHolder.getBean(SjyAuthConfig.class);
		ConfigProperties configProperties = SpringContextHolder.getBean(ConfigProperties.class);
		logger.info("code:{}", code);
		logger.info("client_id:{}", authConfig.getClientId());
		logger.info("client_secret:{}", authConfig.getClientSecret());
		logger.info("redirect_uri:{}", authConfig.getRedirectUri());
		logger.info("authConfig:{}", authConfig.getMethodGetAccessToken());
		logger.info("X-APP-ID:{}", authConfig.getAppid());
		logger.info("X-APP-KEY:{}", authConfig.getAppkey());
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("grant_type", "authorization_code");
		paramMap.put("code", code);
		paramMap.put("client_id", authConfig.getClientId());
		paramMap.put("client_secret", authConfig.getClientSecret());
		paramMap.put("redirect_uri", authConfig.getRedirectUri());
		String result = null;
		try {
			result = HttpRequest.post(authConfig.getMethodGetAccessToken())
					.header("Content-Type", "application/x-www-form-urlencoded")
					.header("X-APP-ID", authConfig.getAppid())
					.header("X-APP-KEY", authConfig.getAppkey())
					.header("X-CTG-Province-ID", "HNX")
					.header("X-CTG-VERSION", "V1.0.00")
					.form(paramMap)
					.timeout(20000)
					.execute().body();
			logger.info("统一认证获取应用的授权token接口返回:{}", result);
			if (ObjectUtil.isNotEmpty(result)) {
				HashMap hashMap = JSON.parseObject(result, HashMap.class);
				if (0 == (int) hashMap.get("code")) {
					RSA rsa = new RSA(authConfig.getPrivateKey(), null);
					String decryptStr = rsa.decryptStr((String) hashMap.get("data"), KeyType.PrivateKey);
					HashMap map = JSON.parseObject(decryptStr, HashMap.class);
					//获取退出登陆token
					String accessToken = "Bearer " + map.get("access_token");
					// 解析 accessToken
					DecodedJWT decodeToken = JWT.decode((String) map.get("access_token"));
					// 获取用户信息
					jsonObject.put("account", decodeToken.getClaim("account").asString());
					jsonObject.put("certNumber", decodeToken.getClaim("certNumber").asString());
					jsonObject.put("ctHrCode", decodeToken.getClaim("ctHrCode").asString());
					jsonObject.put("mobilePhone", decodeToken.getClaim("mobilePhone").asString());
					jsonObject.put("userCode", decodeToken.getClaim("userCode").asString());
					jsonObject.put("username", decodeToken.getClaim("account").asString());
					jsonObject.put("tenant_id", 3); // 省集约甩单租户ID
					jsonObject.put("accessToken", accessToken);
					logger.info("获取用户信息:" + decodeToken);
				} else {
					jsonObject.put("success", false);
					jsonObject.put("message", "解析用户信息异常，请联系管理员重试！");
					logger.error("省集约甩单统一认证返回错误码：{}", hashMap.get("code"));
				}
			} else {
				jsonObject.put("success", false);
				jsonObject.put("message", "解析用户信息异常，请联系管理员重试！");
				logger.error("省集约甩单统一认证返回结果为空");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("调用门户code换取token异常：" + e.getMessage());
			jsonObject.clear();
			jsonObject.put("success", false);
			jsonObject.put("message", "解析用户信息异常，请联系管理员重试！");
		}
		return jsonObject;
	}

	/**
	 * 统一认证注销登录
	 *
	 * @param accessToken 认证中心的token
	 * @param appId       应用ID
	 * @param appKey      应用密钥
	 * @return 返回注销结果，成功返回true，失败返回false
	 * <AUTHOR>
	 * @date 2025/7/10
	 */
	public static boolean logoutUnifiedAuth(String accessToken, String appId, String appKey) {
		logger.info("———————————————》三方接口调用统一认证注销登录接口");
		if (StrUtil.isBlank(accessToken) || StrUtil.isBlank(appId) || StrUtil.isBlank(appKey)) {
			logger.error("统一认证注销登录参数不完整: accessToken={}, appId={}, appKey={}", accessToken, appId, appKey);
			return false;
		}

		try {
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("accessToken", accessToken);
			paramMap.put("appId", appId);
			paramMap.put("appKey", appKey);
			String result = HttpRequest.post("http://134.176.134.80:30048/api/openapi/hnOauth/token/logout")
					.header("Content-Type", "application/x-www-form-urlencoded")
					.header("X-APP-ID", appId)
					.header("X-APP-KEY", appKey)
					.header("Authorization", accessToken)
					.header("X-CTG-Province-ID", "HNX")
					.header("X-CTG-VERSION", "V1.0.00")
					.form(paramMap)
					.timeout(20000)
					.execute().body();

			logger.info("统一认证注销登录接口返回：{}", result);

			if (StrUtil.isNotEmpty(result)) {
				JSONObject resultJson = JSONObject.parseObject(result);
				if (resultJson != null && resultJson.getIntValue("code") == 0) {
					logger.info("统一认证注销登录成功");
					return true;
				} else {
					logger.error("统一认证注销登录失败：{}", result);
				}
			}
		} catch (Exception e) {
			logger.error("统一认证注销登录异常", e);
		}

		return false;
	}


	/**
	 * 查询号码归属地
	 *
	 * @param phone
	 * @return
	 */
	public static String otherWSInterface(String phone) {
		String new_wsdl_url = getEopUrl() + "hnzynlzx.billingnuminfo/BILLINGNUMINFO ";

		try {
			String xml = "<Data>" + "<IntfCode>BILLINGNUMINFO</IntfCode>"
					+ "<Params><BILLING_NO>" + phone + "</BILLING_NO></Params>" + "</Data>";

			StringBuffer results = RequestUtil.doServerHttpOpenapi(xml, new_wsdl_url);
			Document document = DocumentHelper.parseText(results.toString());
			Element rootElement = (Element) document.getRootElement();
			Element aReturn = rootElement.element("Return");
			Element retFag = aReturn.element("RETFLAG");
			if (!retFag.getStringValue().equals("0")) {
				return null;
			}
			Element return_result = aReturn.element("RETURN_RESULT");
			Element cityCode = return_result.element("REGIONCODE");
			String cityValue = cityCode.getStringValue();
			return cityValue;
		} catch (Exception e) {
			logger.info("查询归属地接口异常：", e);
			return null;
		}
	}

	public static String getOtherWSInterfaceXML(String phone) {
		String xml = "<?xml version=\"1.0\" encoding=\"GB2312\"?>" +
				"<Data>" +
				"<IntfCode>BILLINGNUMINFO</IntfCode>" +
				"<Params><BILLING_NO>" + phone + "</BILLING_NO></Params>" +
				"</Data>";
		return xml;
	}

	/**
	 * <AUTHOR>
	 * @date 2025/4/23
	 * 新三网短信接口
	 */
	public static String dxtz3wnew(JSONObject param) {
		logger.info("新三网短信接口请求参数" + param.toJSONString());
		String result = null;
		try {
			result = RequestUtil.postHttpByUrlID(getEopUrl() + "hndtdxtzmsg/dxtz3wnew", param.toJSONString(), ConstantUtil.SL_AppId, ConstantUtil.SL_AppKey);
		} catch (Exception e) {
			logger.error("新三网短信接口请求异常：", e);
		}
		logger.info("新三网短信接口返回参数" + result);
		JSONObject parseObject = JSONObject.parseObject(result);
		return parseObject.getString("RETFLAG");
	}


}
