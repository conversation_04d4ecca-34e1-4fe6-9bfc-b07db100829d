package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslRoomService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRoom;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslRoomParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 房间表控制器
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
@Api(tags = "房间表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslRoom")
public class HnslRoomController extends BaseController {
    @Autowired
    private HnslRoomService hnslRoomService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:list')")
    @OperationLog
    @ApiOperation("分页查询房间表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslRoom>> page(@RequestBody HnslRoomParam param) {
        PageParam<HnslRoom, HnslRoomParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslRoomService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslRoomService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:list')")
    @OperationLog
    @ApiOperation("查询全部房间表")
    @GetMapping("/list")
    public ApiResult<List<HnslRoom>> list(@RequestBody HnslRoomParam param) {
        PageParam<HnslRoom, HnslRoomParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslRoomService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslRoomService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:list')")
    @OperationLog
    @ApiOperation("根据id查询房间表")
    @GetMapping("/{id}")
    public ApiResult<HnslRoom> get(@PathVariable("id") Integer id) {
        return success(hnslRoomService.getById(id));
        // 使用关联查询
        //return success(hnslRoomService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:save')")
    @OperationLog
    @ApiOperation("添加房间表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslRoom hnslRoom) {
        if (hnslRoomService.save(hnslRoom)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:update')")
    @OperationLog
    @ApiOperation("修改房间表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslRoom hnslRoom) {
        if (hnslRoomService.updateById(hnslRoom)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:remove')")
    @OperationLog
    @ApiOperation("删除房间表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslRoomService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:save')")
    @OperationLog
    @ApiOperation("批量添加房间表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslRoom> list) {
        if (hnslRoomService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:update')")
    @OperationLog
    @ApiOperation("批量修改房间表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslRoom> batchParam) {
        if (batchParam.update(hnslRoomService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslRoom:remove')")
    @OperationLog
    @ApiOperation("批量删除房间表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslRoomService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 修改房间下的年级
     */
    @RequestMapping("/updateGrade")
    public ApiResult<?> updateGrade(@RequestBody Map<String, Object> params) {
        try{
            Number num = Float.parseFloat(String.valueOf(params.get("id")));
            int oamount = num.intValue();
            long id = Long.valueOf(oamount);
            logger.info("获取楼栋信息入参" + id);
            HnslRoom hnslRoom = hnslRoomService.getById(id);
//            HnslSchoolUser hnslSchoolUserEntity = new HnslSchoolUser();
//            hnslSchoolUserEntity.setUpdatedDate(new Date());
//            hnslSchoolUserEntity.setRoomId(hnslRoom.getRoomNumber());
//            hnslSchoolUserEntity.setEnroltime(String.valueOf(params.get("dates"))+"年");
//            hnslSchoolUserService.updateGrade(hnslSchoolUserEntity);
        }catch (Exception e){
            logger.error("修改年级异常报错:{}",e);
            return success("系统修改异常，请联系管理员！");
        }
        return success("修改成功");
    }

}
