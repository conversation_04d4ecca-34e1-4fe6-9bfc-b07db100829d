package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrdersData;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserOrdersDataParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
public interface HnslUserOrdersDataMapper extends BaseMapper<HnslUserOrdersData> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserOrdersData>
     */
    List<HnslUserOrdersData> selectPageRel(@Param("page") IPage<HnslUserOrdersData> page,
                             @Param("param") HnslUserOrdersDataParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserOrdersData> selectListRel(@Param("param") HnslUserOrdersDataParam param);

}
