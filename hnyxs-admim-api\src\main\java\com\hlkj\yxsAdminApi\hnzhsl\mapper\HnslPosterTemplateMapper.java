package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPosterTemplate;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslPosterTemplateParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 海报模板表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslPosterTemplateMapper extends BaseMapper<HnslPosterTemplate> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslPosterTemplate>
     */
    List<HnslPosterTemplate> selectPageRel(@Param("page") IPage<HnslPosterTemplate> page,
                             @Param("param") HnslPosterTemplateParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslPosterTemplate> selectListRel(@Param("param") HnslPosterTemplateParam param);

}
