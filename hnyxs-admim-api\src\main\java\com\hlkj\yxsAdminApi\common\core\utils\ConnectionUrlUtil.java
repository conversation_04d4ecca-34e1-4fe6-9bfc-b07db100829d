package com.hlkj.yxsAdminApi.common.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;

/**
 * http请求工具类
 * @ClassName:  ConnectionUrlUtil   
 * @author:MCC
 * @date:   2019年2月12日 下午4:05:50   
 * @version V2.0
 */
public class ConnectionUrlUtil {
	protected static Logger log = LoggerFactory.getLogger(ConnectionUrlUtil.class);
	
	
	
	/**
	 * post提交
	 * @param url 接收的url
	 * @param parameter 接收的参数
	 * @return 返回结果
	 */
	public static String sendPostOut(String url, String parameter,int timeOut) {
		log.info("sendPostOut请求地址:" + url + ",请求参数:" + parameter + ",时间:" + timeOut);
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("POST");
			byte contentbyte[] = parameter.toString().getBytes();
			//设置请求类型
//			conn.setRequestProperty("Content-Type", "application/json");
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			//设置表单长度
			conn.setRequestProperty("Content-Length", (new StringBuilder()).append(contentbyte.length).toString());
			//设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");//zh-CN代表中国  默认为美式英语
			//连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(timeOut);
			//从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(60000);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;  
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在    2 
			// http正文内，因此需要设为true, 默认情况下是false;  
			conn.setDoOutput(true);
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream(),"utf-8"));
			bWriter.write(parameter.toString());
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,   
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端。
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			//此方法是用Reader读取BufferedReader reader = new BufferedReader(new InputStreamReader( connection.getInputStream()));

			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
			log.info("sendPostOut请求结果:" + result);
		} catch (Exception ex) {
			log.error("sendPostOut请求异常", ex);
		}
		return result;
	}
	
	
	/**
	 * 设置响应时间
	 */
	public static String sendGetOut(String url, String param,int timeOut) {
		log.info("sendGetOut请求地址:" + url + ",请求参数:" + param + ",请求时间:" + timeOut);
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setConnectTimeout(timeOut);
            // 建立实际的连接
            connection.connect();
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            result = new String(result.getBytes(),"UTF8");
            log.info("sendGetOut请求结果:" + result);
        } catch (Exception e) {
            log.error("sendGetOut请求异常", e);
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                log.error("sendGetOut请求关闭异常", e2);
            }
        }
        return result;
    }
	
	/**
	 * post提交
	 * @param url 接收的url
	 * @param parameter 接收的参数
	 * @return 返回结果
	 */
	public static String sendJsonPost(String url, String parameter) {
		log.info("sendJsonPost请求地址:" + url + ",请求参数:" + parameter);
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("POST");
			byte contentbyte[] = parameter.toString().getBytes();
			//设置请求类型
			conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
//			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			//设置表单长度
			conn.setRequestProperty("Content-Length", (new StringBuilder()).append(contentbyte.length).toString());
			//设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");//zh-CN代表中国  默认为美式英语
			//连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(60000);
			//从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(60000);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;  
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在    2 
			// http正文内，因此需要设为true, 默认情况下是false;  
			conn.setDoOutput(true);
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream()));
			bWriter.write(parameter.toString());
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,   
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端。
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			//此方法是用Reader读取BufferedReader reader = new BufferedReader(new InputStreamReader( connection.getInputStream()));

			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
			log.info("sendJsonPost请求结果:" + result);
		} catch (Exception ex) {
			log.error("sendJsonPost请求异常", ex);
		}
		return result;
	}
	
	/**
	 * post提交
	 * @param url 接收的url
	 * @param parameter 接收的参数second超时时间
	 * @return 返回结果
	 */
	public static String sendJsonSecondPost(String url, String parameter,int second) {
		log.info("sendJsonSecondPost请求地址:" + url + ",请求参数:" + parameter + ",请求时间:" + second);
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("POST");
			byte contentbyte[] = parameter.toString().getBytes();
			//设置请求类型
			conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
//			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			//设置表单长度
			conn.setRequestProperty("Content-Length", (new StringBuilder()).append(contentbyte.length).toString());
			//设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");//zh-CN代表中国  默认为美式英语
			//连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(second);
			//从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(second);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;  
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在    2 
			// http正文内，因此需要设为true, 默认情况下是false;  
			conn.setDoOutput(true);
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream()));
			bWriter.write(parameter.toString());
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,   
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端。
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			//此方法是用Reader读取BufferedReader reader = new BufferedReader(new InputStreamReader( connection.getInputStream()));

			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
			log.info("sendJsonSecondPost请求结果:" + result);
		} catch (Exception ex) {
			log.error("sendJsonSecondPost请求异常", ex);
		}
		return result;
	}
	
	
	
	/**
	 * post提交
	 * @param url 接收的url
	 * @param parameter 接收的参数
	 * @return 返回结果
	 */
	public static String sendPost(String url, String parameter) {
		log.info("sendPost请求地址:" + url + ",请求参数:" + parameter);
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("POST");
			byte contentbyte[] = parameter.toString().getBytes();
			//设置请求类型
//			conn.setRequestProperty("Content-Type", "application/json");
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			//设置表单长度
			conn.setRequestProperty("Content-Length", (new StringBuilder()).append(contentbyte.length).toString());
			//设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");//zh-CN代表中国  默认为美式英语
			//连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(60000);
			//从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(60000);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;  
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在    2 
			// http正文内，因此需要设为true, 默认情况下是false;  
			conn.setDoOutput(true);
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream(),"utf-8"));
			bWriter.write(parameter.toString());
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,   
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端。
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			//此方法是用Reader读取BufferedReader reader = new BufferedReader(new InputStreamReader( connection.getInputStream()));

			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
			log.info("sendPost请求结果:" + result);
		} catch (Exception ex) {
			log.error("sendPost请求异常", ex);
		}
		return result;
	}
	
	/**
	 * get的请求
	 */
	public static String sendGet(String url, String param) {
		log.info("sendGet请求地址:" + url + ",请求参数:" + param);
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            result = new String(result.getBytes(),"UTF8");
            log.info("sendGet请求结果:" + result);
        } catch (Exception e) {
            log.error("sendGet请求异常", e);
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                log.error("sendGet请求关闭异常", e2);
            }
        }
        return result;
    }


	
}
