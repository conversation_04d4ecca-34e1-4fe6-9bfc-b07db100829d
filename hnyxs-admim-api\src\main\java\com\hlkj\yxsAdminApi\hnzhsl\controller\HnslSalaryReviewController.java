package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSalaryReviewService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryReview;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSalaryReviewParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 薪资审核表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "薪资审核表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslSalaryReview")
public class HnslSalaryReviewController extends BaseController {
    @Autowired
    private HnslSalaryReviewService hnslSalaryReviewService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:list')")
    @OperationLog
    @ApiOperation("分页查询薪资审核表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSalaryReview>> page(@RequestBody HnslSalaryReviewParam param) {
        PageParam<HnslSalaryReview, HnslSalaryReviewParam> page = new PageParam<>(param);
        QueryWrapper<HnslSalaryReview> hnslStudentApprovalEntityQueryWrapper = page.getWrapper();
        return success(hnslSalaryReviewService.page(page, hnslStudentApprovalEntityQueryWrapper));
        // 使用关联查询
        //return success(hnslSalaryReviewService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:list')")
    @OperationLog
    @ApiOperation("查询全部薪资审核表")
    @PostMapping("/list")
    public ApiResult<List<HnslSalaryReview>> list(@RequestBody HnslSalaryReviewParam param) {
        PageParam<HnslSalaryReview, HnslSalaryReviewParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSalaryReviewService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSalaryReviewService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:list')")
    @OperationLog
    @ApiOperation("根据id查询薪资审核表")
    @GetMapping("/{id}")
    public ApiResult<HnslSalaryReview> get(@PathVariable("id") Integer id) {
        return success(hnslSalaryReviewService.getById(id));
        // 使用关联查询
        //return success(hnslSalaryReviewService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:save')")
    @OperationLog
    @ApiOperation("添加薪资审核表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSalaryReview hnslSalaryReview) {
        User loginUser = getLoginUser();
        hnslSalaryReview.setCreatedDate(new Date());
        hnslSalaryReview.setCreatedUser(loginUser.getUsername());
        hnslSalaryReview.setAuditName(loginUser.getUsername());
        hnslSalaryReview.setStatus(1);
        if (hnslSalaryReviewService.save(hnslSalaryReview)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:update')")
    @OperationLog
    @ApiOperation("修改薪资审核表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSalaryReview hnslSalaryReview) {
        User loginUser = getLoginUser();
        hnslSalaryReview.setUpdatedUser(loginUser.getUsername());
        hnslSalaryReview.setUpdatedDate(new Date());
        if (hnslSalaryReviewService.updateById(hnslSalaryReview)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:remove')")
    @OperationLog
    @ApiOperation("删除薪资审核表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSalaryReviewService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:save')")
    @OperationLog
    @ApiOperation("批量添加薪资审核表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSalaryReview> list) {
        if (hnslSalaryReviewService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:update')")
    @OperationLog
    @ApiOperation("批量修改薪资审核表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSalaryReview> batchParam) {
        if (batchParam.update(hnslSalaryReviewService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryReview:remove')")
    @OperationLog
    @ApiOperation("批量删除薪资审核表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSalaryReviewService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslsalaryreview:list')")
    @OperationLog
    @ApiOperation("查询学子清单表分页信息")
    @PostMapping("/userReviewList")
    public ApiResult<PageResult<Map<String,Object>>> userReviewList(@RequestBody Map<String, Object> params){
        logger.info("入参{}",params.toString());
        //查询列表数据
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
        String time=null;
        HashMap<String, Object> hashMap = new HashMap<>();
        if(StringUtil.isEmpty(String.valueOf(params.get("beginTime")))){
            time=sdf.format(new Date());
            params.put("queryTime",sdf.format(new Date()));
            hashMap.put("queryTime",sdf.format(new Date()));
        }else{
            time=String.valueOf(params.get("beginTime"));
            params.put("queryTime",params.get("beginTime"));
            hashMap.put("queryTime",params.get("beginTime"));
        }
        try{
            Date parse = sdf.parse(time);
            Calendar instance = Calendar.getInstance();
            instance.setTime(parse);
            instance.add(Calendar.MONTH,1);
            params.put("startTime",time+"-01");
            params.put("endTime",sdf.format(instance.getTime())+"-01");
            hashMap.put("startTime",time+"-01");
            hashMap.put("endTime",sdf.format(instance.getTime())+"-01");
        }catch (Exception e){
            logger.error("查询学子清单时间转化异常{}",e.getMessage());
        }
        String value=String.valueOf(params.get("userValue"));
        if(!StringUtil.isEmpty(value)){
            value=value.trim();
            if(value.length()==11){
                params.put("userPhone",value);
            }else{
                params.put("userName",value);
            }
        }
        if(!StringUtil.isEmpty(String.valueOf(params.get("agentPoint")))){
            if("-1".equals(params.get("agentPoint"))){
                params.remove("agentPoint");
            }
        }

//        String UserPhone = redisUtils.get("thisUserPhone" + user.getMobile());
//        if ("6".equals(UserSf)) {//地市管理员
//            params.put("cityCode", cityCode);
//        }else if ("1".equals(UserSf)) {//校园经理
//            HnslUserSchoolEntity hnslUserSchoolEntity = new HnslUserSchoolEntity();
//            hnslUserSchoolEntity.setUserPhone(UserPhone);
//            List<HnslUserSchoolEntity> hnslUserSchoolEntities = hnslUserSchoolService.queryObjectBy(hnslUserSchoolEntity);
//            List<String> collect = hnslUserSchoolEntities.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());
//            params.put("schoolList", collect);
//        }

        PageResult<Map<String, Object>> hnslSalaryReviewList = hnslSalaryReviewService.queryReviewPageRel(params);
        hnslSalaryReviewList.getList().stream().forEach(sr ->{
            String team_identity = String.valueOf(sr.get("TEAM_IDENTITY"));
            if("5".equals(team_identity)){//核心团队长计算 积分积分
                hashMap.put("phone",sr.get("USER_PHONE"));
                int count = hnslSalaryReviewService.calculateManagementFee(hashMap);
                sr.put("teamglf",count);
            }
        });


        return success(hnslSalaryReviewList);
    }
}
