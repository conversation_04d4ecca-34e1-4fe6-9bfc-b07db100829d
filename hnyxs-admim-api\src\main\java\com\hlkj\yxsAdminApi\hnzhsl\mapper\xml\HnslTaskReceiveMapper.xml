<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTaskReceiveMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_task_receive a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.taskStatusRel != null">
                AND a.TASK_STATUS_REL = #{param.taskStatusRel}
            </if>
            <if test="param.userPhone != null">
                AND a.USER_PHONE LIKE CONCAT('%', #{param.userPhone}, '%')
            </if>
            <if test="param.taskCode != null">
                AND a.TASK_CODE LIKE CONCAT('%', #{param.taskCode}, '%')
            </if>
            <if test="param.finishTime != null">
                AND a.FINISH_TIME LIKE CONCAT('%', #{param.finishTime}, '%')
            </if>
            <if test="param.acceptTime != null">
                AND a.ACCEPT_TIME LIKE CONCAT('%', #{param.acceptTime}, '%')
            </if>
            <if test="param.receiveAward != null">
                AND a.RECEIVE_AWARD = #{param.receiveAward}
            </if>
            <if test="param.taskPublish != null">
                AND a.TASK_PUBLISH LIKE CONCAT('%', #{param.taskPublish}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskReceive">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskReceive">
        <include refid="selectSql"></include>
    </select>

</mapper>
