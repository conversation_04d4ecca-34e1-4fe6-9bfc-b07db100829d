package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSalaryMonthlyRecordService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryMonthlyRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSalaryMonthlyRecordParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 扫楼薪资每月记录表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "扫楼薪资每月记录表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslSalaryMonthlyRecord")
public class HnslSalaryMonthlyRecordController extends BaseController {
    @Autowired
    private HnslSalaryMonthlyRecordService hnslSalaryMonthlyRecordService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼薪资每月记录表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSalaryMonthlyRecord>> page(@RequestBody HnslSalaryMonthlyRecordParam param) {
        PageParam<HnslSalaryMonthlyRecord, HnslSalaryMonthlyRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSalaryMonthlyRecordService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslSalaryMonthlyRecordService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼薪资每月记录表")
    @PostMapping("/list")
    public ApiResult<List<HnslSalaryMonthlyRecord>> list(@RequestBody HnslSalaryMonthlyRecordParam param) {
        PageParam<HnslSalaryMonthlyRecord, HnslSalaryMonthlyRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSalaryMonthlyRecordService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSalaryMonthlyRecordService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼薪资每月记录表")
    @GetMapping("/{id}")
    public ApiResult<HnslSalaryMonthlyRecord> get(@PathVariable("id") Integer id) {
        return success(hnslSalaryMonthlyRecordService.getById(id));
        // 使用关联查询
        //return success(hnslSalaryMonthlyRecordService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:save')")
    @OperationLog
    @ApiOperation("添加扫楼薪资每月记录表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSalaryMonthlyRecord hnslSalaryMonthlyRecord) {
        if (hnslSalaryMonthlyRecordService.save(hnslSalaryMonthlyRecord)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:update')")
    @OperationLog
    @ApiOperation("修改扫楼薪资每月记录表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSalaryMonthlyRecord hnslSalaryMonthlyRecord) {
        if (hnslSalaryMonthlyRecordService.updateById(hnslSalaryMonthlyRecord)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:remove')")
    @OperationLog
    @ApiOperation("删除扫楼薪资每月记录表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSalaryMonthlyRecordService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:save')")
    @OperationLog
    @ApiOperation("批量添加扫楼薪资每月记录表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSalaryMonthlyRecord> list) {
        if (hnslSalaryMonthlyRecordService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:update')")
    @OperationLog
    @ApiOperation("批量修改扫楼薪资每月记录表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSalaryMonthlyRecord> batchParam) {
        if (batchParam.update(hnslSalaryMonthlyRecordService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSalaryMonthlyRecord:remove')")
    @OperationLog
    @ApiOperation("批量删除扫楼薪资每月记录表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSalaryMonthlyRecordService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
