package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 熟卡二维码表
 * <AUTHOR>
 * @Since: 2024/10/21
 * @return: null
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslMatureCardQrcode对象", description = "熟卡二维码")
public class HnslMatureCardQrcode implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "学校名称")
    @TableField("school_name")
    private String schoolName;

    @ApiModelProperty(value = "学校编码")
    @TableField("school_code")
    private String schoolCode;

    @ApiModelProperty(value = "地市")
    @TableField("city_code")
    private String cityCode;

    @ApiModelProperty(value = "线下预约二维码（1. 开启 2.关闭）")
    @TableField("offline_qrcode")
    private Integer offlineQrcode;

    @ApiModelProperty(value = "省外使用线下预约二维码(1. 允许，2 不允许)")
    @TableField("offline_qrcode_out")
    private Integer offlineQrcodeOut;

    @ApiModelProperty(value = "线下预约跨地市使用（1. 允许 2.不允许）")
    @TableField("offline_cross_city_use")
    private Integer offlineCrossCityUse;

    @ApiModelProperty(value = "线上预约二维码（1. 开启 2.关闭）")
    @TableField("online_qrcode")
    private Integer onlineQrcode;

    @ApiModelProperty(value = "省外使用线上预约二维码(1.允许 2.不允许)")
    @TableField("online_qrcode_out")
    private Integer onlineQrcodeOut;

    @ApiModelProperty(value = "线上预约跨地市使用（1. 允许 2.不允许）")
    @TableField("online_cross_city_use")
    private Integer onlineCrossCityUse;

    @ApiModelProperty(value = "线上激活二维码（1. 开启 2.关闭）")
    @TableField("online_active_qrcode")
    private Integer onlineActiveQrcode;

    @ApiModelProperty(value = "省外使用线上激活二维码（1. 允许 2.不允许）")
    @TableField("online_active_qrcode_out")
    private Integer onlineActiveQrcodeOut;

    @ApiModelProperty(value = "线上激活跨地市使用（1. 允许 2.不允许）")
    @TableField("online_active_cross_city_use")
    private Integer onlineActiveCrossCityUse;

    @ApiModelProperty(value = "自助激活二维码（1. 开启 2.关闭）")
    @TableField("self_active_qrcode")
    private Integer selfActiveQrcode;

    @ApiModelProperty(value = "省外使用自助激活二维码（1. 允许 2.不允许）")
    @TableField("self_active_qrcode_out")
    private Integer selfActiveQrcodeOut;

    @ApiModelProperty(value = "自助激活跨地市使用（1. 允许 2.不允许）")
    @TableField("self_active_cross_city_use")
    private Integer selfActiveCrossCityUse;

    @ApiModelProperty(value = "线下面对面预约（1. 允许 2.不允许）")
    @TableField("offline_face_reservation")
    private Integer offlineFaceReservation;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_user")
    private String updateUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;

}
