package com.hlkj.yxsAdminApi.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.Tenant;
import com.hlkj.yxsAdminApi.common.system.param.TenantParam;

import java.util.List;

/**
 * 租户Service
 *
 * <AUTHOR>
 * @since 2023-02-16 12:27:16
 */
public interface TenantService extends IService<Tenant> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<Tenant>
     */
    PageResult<Tenant> pageRel(TenantParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<Tenant>
     */
    List<Tenant> listRel(TenantParam param);

    /**
     * 根据id查询
     *
     * @param tenantId 租户id
     * @return Tenant
     */
    Tenant getByIdRel(Integer tenantId);

    /**
     * 根据账户名查询所有的租户信息
     * @param userName
     * @return
     */
    List<Tenant> getLoginAllTenantList(String userName);
}
