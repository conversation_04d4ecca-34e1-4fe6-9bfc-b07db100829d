package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 私人号池
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUserCardpool对象", description = "私人号池")
public class HnslUserCardpool implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "号码池编码")
    @TableField("CARD_POOL_NUMBER")
    private String cardPoolNumber;

    @ApiModelProperty(value = "号码池名称")
    @TableField("CARD_POOL_NAME")
    private String cardPoolName;

    @ApiModelProperty(value = "号码池状态（1：有效 0无效）")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "关联揽机工号")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "经理名称")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "所在城市")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "所属学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "所属学校名称")
    @TableField(exist = false)
    private String schoolName;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "学校信息集合")
    @TableField(exist = false)
    private List<HnslSchool> schoolList;

    @ApiModelProperty(value = "渠道ID")
    @TableField("CHANNEL_ID")
    private String channelId;

    @ApiModelProperty(value = "MD5专用key")
    @TableField(value = "MD5_KEY")
    private String md5Key;

    @ApiModelProperty("号池类型 1:省内3.0 2:集团号池")
    @TableField( "CARD_POOL_TYPE")
    private int cardPoolType;
}
