package com.hlkj.yxsAdminApi.common.core.execl;

import java.awt.*;

/*
 * 	水印配置类
 * 	LFH 2023-07-05 
 */
public class Watermark {

	/**
     * 	水印内容
     */
    private String content = "";

    /**
     * 	画笔颜色. eg:#C5CBCF
     */
    private String color = "#C5CBCF";

    /**
     * 	字体颜色
     */
    private Font font = new Font("microsoft-yahei", Font.PLAIN, 20);

    /**
     * 	水印宽、高
     */
    private int width = 300;
    private int height = 100;

    /**
     * 	倾斜度
     */
    private double shear1 = 0.1;
    private double shear2 = -0.26;

    /**
     * 	字体的y轴位置
     */
    private int yAxis = 50;

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public Font getFont() {
		return font;
	}

	public void setFont(Font font) {
		this.font = font;
	}

	public int getWidth() {
		return width;
	}

	public void setWidth(int width) {
		this.width = width;
	}

	public int getHeight() {
		return height;
	}

	public void setHeight(int height) {
		this.height = height;
	}

	public double getShear1() {
		return shear1;
	}

	public void setShear1(double shear1) {
		this.shear1 = shear1;
	}

	public double getShear2() {
		return shear2;
	}

	public void setShear2(double shear2) {
		this.shear2 = shear2;
	}

	public int getyAxis() {
		return yAxis;
	}

	public void setyAxis(int yAxis) {
		this.yAxis = yAxis;
	}
    
    
}
