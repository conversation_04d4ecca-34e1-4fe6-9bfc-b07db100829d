package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绩效表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslPerformance对象", description = "绩效表")
public class HnslPerformance implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "操作时间 ")
    @TableField("CREATIONTIME")
    private Date creationtime;

    @ApiModelProperty(value = "绩效等级(A  B   C ")
    @TableField("LEVELS")
    private String levels;

    @ApiModelProperty(value = "父ID")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "绩效时间（几月份的绩效）")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty(value = "查询账期YYYY-MM")
    @TableField(exist = false)
    private String dates;
}
