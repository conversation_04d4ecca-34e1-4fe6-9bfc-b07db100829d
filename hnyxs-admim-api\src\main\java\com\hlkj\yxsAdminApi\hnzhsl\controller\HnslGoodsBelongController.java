package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsBelongService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsBelong;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsBelongParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 商品学校关系表控制器
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
@Api(tags = "商品学校关系表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoodsBelong")
public class HnslGoodsBelongController extends BaseController {
    @Autowired
    private HnslGoodsBelongService hnslGoodsBelongService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:list')")
    @OperationLog
    @ApiOperation("分页查询商品学校关系表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsBelong>> page(@RequestBody HnslGoodsBelongParam param) {
        PageParam<HnslGoodsBelong, HnslGoodsBelongParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsBelongService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsBelongService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:list')")
    @OperationLog
    @ApiOperation("查询全部商品学校关系表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoodsBelong>> list(@RequestBody HnslGoodsBelongParam param) {
        PageParam<HnslGoodsBelong, HnslGoodsBelongParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsBelongService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsBelongService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:list')")
    @OperationLog
    @ApiOperation("根据id查询商品学校关系表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsBelong> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsBelongService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsBelongService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:save')")
    @OperationLog
    @ApiOperation("添加商品学校关系表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslGoodsBelong hnslGoodsBelong) {
        if (hnslGoodsBelongService.save(hnslGoodsBelong)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:update')")
    @OperationLog
    @ApiOperation("修改商品学校关系表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoodsBelong hnslGoodsBelong) {
        if (hnslGoodsBelongService.updateById(hnslGoodsBelong)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:update')")
    @OperationLog
    @ApiOperation("修改商品绑定指定测试学校")
    @PostMapping("/decode/updateTestSchool")
    public ApiResult<?> updateTestSchool(@RequestBody HnslGoodsBelong hnslGoodsBelong) {
        List<String> codeList = Arrays.asList("730001", "731001", "732001", "733001"
                , "734001", "735001", "736001", "737001", "738001", "739001"
                , "743001", "744001", "745001", "746001");
        try {
            User loginUser = getLoginUser();
            //修改原来学校状态
            UpdateWrapper<HnslGoodsBelong> hnslGoodsBelongUpdateWrapper = new UpdateWrapper<>();
            hnslGoodsBelongUpdateWrapper.set("STATUS",0).eq("GOODS_NUMBER",hnslGoodsBelong.getGoodsNumber());
            boolean update = hnslGoodsBelongService.update(hnslGoodsBelongUpdateWrapper);
            ArrayList<HnslGoodsBelong> hnslGoodsBelongs = new ArrayList<>();
            for(String good : codeList){
                HnslGoodsBelong hnslGoodsBelong1 = new HnslGoodsBelong();
                hnslGoodsBelong1.setStatus(1);
                hnslGoodsBelong1.setGoodsNumber(hnslGoodsBelong1.getGoodsNumber());
                hnslGoodsBelong1.setSchoolCode(good);
                hnslGoodsBelong1.setCreatedDate(new Date());
                hnslGoodsBelong1.setCreatedUser(loginUser.getNickname());
                hnslGoodsBelongs.add(hnslGoodsBelong1);
            }
            if(hnslGoodsBelongService.saveBatch(hnslGoodsBelongs)){
                return success("修改成功");
            }else{
                return fail("修改失败");
            }
        }catch (Exception e) {
            return fail("修改失败"+e.getMessage());
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:remove')")
    @OperationLog
    @ApiOperation("删除商品学校关系表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsBelongService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:save')")
    @OperationLog
    @ApiOperation("批量添加商品学校关系表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsBelong> list) {
        if (hnslGoodsBelongService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:update')")
    @OperationLog
    @ApiOperation("批量修改商品学校关系表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoodsBelong> batchParam) {
        if (batchParam.update(hnslGoodsBelongService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsBelong:remove')")
    @OperationLog
    @ApiOperation("批量删除商品学校关系表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsBelongService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
