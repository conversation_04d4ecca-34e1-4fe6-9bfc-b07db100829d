<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="379c16b6-a6f9-484a-b0d3-a24fb3750922" name="Changes" comment="统一认证本地登录界面拦截优化">
      <change beforePath="$PROJECT_DIR$/src/views/hnzsxH5/configRelation/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/hnzsxH5/configRelation/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/vue.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="origin/srd17373673312/zq" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="srd19176679047/lyx" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="srd17373673312/zq" />
                    <option name="lastUsedInstant" value="1753930896" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="srd19176679047/lyx" />
                    <option name="lastUsedInstant" value="1752216585" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1745306892" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/vue.config.js" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2vRdRbttRYSPHbes6EQIL8mGtJ3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;srd17373673312/zq&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-app" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-app\src\views\hnzsxH5" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-app\src\views\hnzsxH5\template\components" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-app\src\api\hnzsxH5" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="379c16b6-a6f9-484a-b0d3-a24fb3750922" name="Changes" comment="" />
      <created>1744111813564</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744111813564</updated>
      <workItem from="1744111814683" duration="1550000" />
      <workItem from="1744163392116" duration="1801000" />
      <workItem from="1745292555364" duration="4733000" />
      <workItem from="1745393264504" duration="6102000" />
      <workItem from="1745457492119" duration="3520000" />
      <workItem from="1745496385215" duration="325000" />
      <workItem from="1745497076725" duration="526000" />
      <workItem from="1745565345964" duration="2320000" />
      <workItem from="1745576460791" duration="600000" />
      <workItem from="1745662518315" duration="4621000" />
      <workItem from="1745731905427" duration="12000" />
      <workItem from="1745732704711" duration="704000" />
      <workItem from="1745742025705" duration="890000" />
      <workItem from="1745759587913" duration="4640000" />
      <workItem from="1745769977421" duration="1962000" />
      <workItem from="1745776979051" duration="1493000" />
      <workItem from="1745830008892" duration="1508000" />
      <workItem from="1745933662197" duration="1989000" />
      <workItem from="1745993549227" duration="609000" />
      <workItem from="1746491639609" duration="241000" />
      <workItem from="1746523401791" duration="958000" />
      <workItem from="1746588806017" duration="651000" />
      <workItem from="1746603817701" duration="780000" />
      <workItem from="1746605721275" duration="854000" />
      <workItem from="1746664983790" duration="59000" />
      <workItem from="1746695039731" duration="2331000" />
      <workItem from="1746751614448" duration="63000" />
      <workItem from="1746751713925" duration="684000" />
      <workItem from="1746775251830" duration="2000" />
      <workItem from="1747057232651" duration="72000" />
      <workItem from="1747057320004" duration="873000" />
      <workItem from="1747061638410" duration="54000" />
      <workItem from="1747099581090" duration="1183000" />
      <workItem from="1747104296691" duration="101000" />
      <workItem from="1747127777034" duration="161000" />
      <workItem from="1747218970260" duration="2990000" />
      <workItem from="1747271708031" duration="87000" />
      <workItem from="1747304201510" duration="1271000" />
      <workItem from="1747322687789" duration="1069000" />
      <workItem from="1747337698462" duration="4325000" />
      <workItem from="1747356822378" duration="2458000" />
      <workItem from="1747578544299" duration="1545000" />
      <workItem from="1747637569121" duration="1102000" />
      <workItem from="1747702300758" duration="268000" />
      <workItem from="1747702607280" duration="277000" />
      <workItem from="1747703210265" duration="598000" />
      <workItem from="1747990450685" duration="1189000" />
      <workItem from="1748138688703" duration="2784000" />
      <workItem from="1748219939140" duration="685000" />
      <workItem from="1748242543643" duration="2648000" />
      <workItem from="1748350828896" duration="253000" />
      <workItem from="1748479794950" duration="3104000" />
      <workItem from="1748574592510" duration="4116000" />
      <workItem from="1748911157513" duration="1194000" />
      <workItem from="1748938963745" duration="757000" />
      <workItem from="1749018579757" duration="5700000" />
      <workItem from="1749454061653" duration="4142000" />
      <workItem from="1749518900985" duration="594000" />
      <workItem from="1750726658331" duration="2287000" />
      <workItem from="1750815652661" duration="1608000" />
      <workItem from="1750862770690" duration="27000" />
      <workItem from="1750986208273" duration="2889000" />
      <workItem from="1751018727831" duration="597000" />
      <workItem from="1751100048758" duration="2201000" />
      <workItem from="1751596547439" duration="164000" />
      <workItem from="1751854380167" duration="7599000" />
      <workItem from="1751942500477" duration="1627000" />
      <workItem from="1751967279429" duration="597000" />
      <workItem from="1752023757763" duration="4298000" />
      <workItem from="1752130166955" duration="347000" />
      <workItem from="1752195376258" duration="8134000" />
      <workItem from="1752453896786" duration="1744000" />
      <workItem from="1752540341581" duration="592000" />
      <workItem from="1752626804671" duration="595000" />
      <workItem from="1752713343346" duration="856000" />
      <workItem from="1752820535480" duration="141000" />
      <workItem from="1753326771246" duration="3755000" />
      <workItem from="1753410989138" duration="1595000" />
      <workItem from="1753759413719" duration="2343000" />
      <workItem from="1753836178518" duration="8000" />
      <workItem from="1753922404716" duration="505000" />
      <workItem from="1753930850462" duration="3900000" />
      <workItem from="1753949730459" duration="813000" />
    </task>
    <task id="LOCAL-00001" summary="掌上销后台管理">
      <option name="closed" value="true" />
      <created>1745565785910</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1745565785910</updated>
    </task>
    <task id="LOCAL-00002" summary="掌上销后台管理4.28优化">
      <option name="closed" value="true" />
      <created>1745786369753</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1745786369753</updated>
    </task>
    <task id="LOCAL-00003" summary="掌上销后台管理4.28优化">
      <option name="closed" value="true" />
      <created>1745786390343</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1745786390343</updated>
    </task>
    <task id="LOCAL-00004" summary="掌上销h5模板列表同步刷新优化，编辑回显数据优化">
      <option name="closed" value="true" />
      <created>1745830089207</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1745830089207</updated>
    </task>
    <task id="LOCAL-00005" summary="订单列表优化，商品筛选调整，订单信息导出">
      <option name="closed" value="true" />
      <created>1745943025191</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1745943025192</updated>
    </task>
    <task id="LOCAL-00006" summary="模块、分类、标签及属性管理/关联配置实现">
      <option name="closed" value="true" />
      <created>1746523749195</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1746523749195</updated>
    </task>
    <task id="LOCAL-00007" summary="商品列表增加模板id展示">
      <option name="closed" value="true" />
      <created>1746588843753</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1746588843753</updated>
    </task>
    <task id="LOCAL-00008" summary="关联标签接口无数据bug修复">
      <option name="closed" value="true" />
      <created>1746605752417</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746605752417</updated>
    </task>
    <task id="LOCAL-00009" summary="5.8功能优化">
      <option name="closed" value="true" />
      <created>1746751781024</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746751781024</updated>
    </task>
    <task id="LOCAL-00010" summary="商品新增白名单功能">
      <option name="closed" value="true" />
      <created>1747102016446</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1747102016446</updated>
    </task>
    <task id="LOCAL-00011" summary="工号用户列表分页优化">
      <option name="closed" value="true" />
      <created>1747127880795</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1747127880795</updated>
    </task>
    <task id="LOCAL-00012" summary="订单流程日志跟踪">
      <option name="closed" value="true" />
      <created>1747225538743</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1747225538743</updated>
    </task>
    <task id="LOCAL-00013" summary="新增礼包状态、跳转路由及链接等">
      <option name="closed" value="true" />
      <created>1747343374942</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1747343374942</updated>
    </task>
    <task id="LOCAL-00014" summary="新增商品类型关联">
      <option name="closed" value="true" />
      <created>1747345765327</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1747345765327</updated>
    </task>
    <task id="LOCAL-00015" summary="bug修复">
      <option name="closed" value="true" />
      <created>1747375737929</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1747375737929</updated>
    </task>
    <task id="LOCAL-00016" summary="白名单工号列表优化">
      <option name="closed" value="true" />
      <created>1747578634485</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747578634485</updated>
    </task>
    <task id="LOCAL-00017" summary="规则配置第一阶段">
      <option name="closed" value="true" />
      <created>1748147899800</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1748147899800</updated>
    </task>
    <task id="LOCAL-00018" summary="商品规则配置以及bug修复">
      <option name="closed" value="true" />
      <created>1748242604523</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1748242604523</updated>
    </task>
    <task id="LOCAL-00019" summary="工号重复bug修复">
      <option name="closed" value="true" />
      <created>1748249468657</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1748249468657</updated>
    </task>
    <task id="LOCAL-00020" summary="更改路径配置、商品组件bug修复">
      <option name="closed" value="true" />
      <created>1748509270621</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1748509270621</updated>
    </task>
    <task id="LOCAL-00021" summary="商品信息导出">
      <option name="closed" value="true" />
      <created>1749018647053</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1749018647053</updated>
    </task>
    <task id="LOCAL-00022" summary="配置文件修改">
      <option name="closed" value="true" />
      <created>1749020248820</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1749020248821</updated>
    </task>
    <task id="LOCAL-00023" summary="主套餐配置">
      <option name="closed" value="true" />
      <created>1749460000349</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1749460000349</updated>
    </task>
    <task id="LOCAL-00024" summary="主套餐配置、url配置">
      <option name="closed" value="true" />
      <created>1750815932544</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1750815932544</updated>
    </task>
    <task id="LOCAL-00025" summary="增加后台订单状态变更处理">
      <option name="closed" value="true" />
      <created>1750986434875</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1750986434875</updated>
    </task>
    <task id="LOCAL-00026" summary="路由配置">
      <option name="closed" value="true" />
      <created>1750986539540</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1750986539540</updated>
    </task>
    <task id="LOCAL-00027" summary="反馈意见管理">
      <option name="closed" value="true" />
      <created>1752216264954</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1752216264954</updated>
    </task>
    <task id="LOCAL-00028" summary="智慧扫楼导出审批">
      <option name="closed" value="true" />
      <created>1752713569841</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1752713569841</updated>
    </task>
    <task id="LOCAL-00029" summary="下载中心审批按钮状态控制">
      <option name="closed" value="true" />
      <created>1753351227371</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753351227371</updated>
    </task>
    <task id="LOCAL-00030" summary="统一认证本地登录界面拦截优化">
      <option name="closed" value="true" />
      <created>1753411084653</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753411084653</updated>
    </task>
    <option name="localTasksCounter" value="31" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="e0c27d02-09c8-447a-9601-20ef1c2a1c4f" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="srd19176679047/lyx" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/srd17373673312/zq" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="srd19176679047/lyx" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="e0c27d02-09c8-447a-9601-20ef1c2a1c4f">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app/src/views/system/user" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="关联标签接口无数据bug修复" />
    <MESSAGE value="5.8功能优化" />
    <MESSAGE value="商品新增白名单功能" />
    <MESSAGE value="Merge branch 'master' into srd19176679047/lyx&#10;&#10;# Conflicts:&#10;#&#9;.env.preview&#10;#&#9;dist/index.html&#10;#&#9;dist/js/1032.90648014.js&#10;#&#9;dist/js/1032.90648014.js.gz&#10;#&#9;dist/js/109.3b9f40b5.js&#10;#&#9;dist/js/200.4d43c0f5.js&#10;#&#9;dist/js/200.4d43c0f5.js.gz&#10;#&#9;dist/js/2224.465798a9.js&#10;#&#9;dist/js/2962.ae6fa2fe.js&#10;#&#9;dist/js/2971.281c063e.js&#10;#&#9;dist/js/4572.669aaf33.js&#10;#&#9;dist/js/4572.669aaf33.js.gz&#10;#&#9;dist/js/4762.e54eaad0.js&#10;#&#9;dist/js/4762.e54eaad0.js.gz&#10;#&#9;dist/js/491.18e639b7.js&#10;#&#9;dist/js/491.18e639b7.js.gz&#10;#&#9;dist/js/5085.967ad7ac.js&#10;#&#9;dist/js/5947.6477cece.js&#10;#&#9;dist/js/5947.6477cece.js.gz&#10;#&#9;dist/js/6579.ee057db4.js&#10;#&#9;dist/js/6579.ee057db4.js.gz&#10;#&#9;dist/js/7024.9ec15534.js&#10;#&#9;dist/js/7131.90d03991.js&#10;#&#9;dist/js/7560.a9a6ea06.js&#10;#&#9;dist/js/7601.9d5de34b.js&#10;#&#9;dist/js/7601.9d5de34b.js.gz&#10;#&#9;dist/js/8028.fce7de37.js&#10;#&#9;dist/js/829.602922e6.js&#10;#&#9;dist/js/chunk-vendors.d40e1249.js&#10;#&#9;dist/js/chunk-vendors.d40e1249.js.gz" />
    <MESSAGE value="商编辑工号和ui先" />
    <MESSAGE value="工号用户列表分页优化" />
    <MESSAGE value="订单流程日志跟踪" />
    <MESSAGE value="新增礼包状态、跳转路由及链接等" />
    <MESSAGE value="新增商品类型关联" />
    <MESSAGE value="bug修复" />
    <MESSAGE value="白名单工号列表优化" />
    <MESSAGE value="规则配置第一阶段" />
    <MESSAGE value="商品规则配置以及bug修复" />
    <MESSAGE value="工号重复bug修复" />
    <MESSAGE value="更改路径配置、商品组件bug修复" />
    <MESSAGE value="商品信息导出" />
    <MESSAGE value="配置文件修改" />
    <MESSAGE value="主套餐配置" />
    <MESSAGE value="主套餐配置、url配置" />
    <MESSAGE value="增加后台订单状态变更处理" />
    <MESSAGE value="路由配置" />
    <MESSAGE value="反馈意见管理" />
    <MESSAGE value="智慧扫楼导出审批" />
    <MESSAGE value="下载中心审批按钮状态控制" />
    <MESSAGE value="统一认证本地登录界面拦截优化" />
    <option name="LAST_COMMIT_MESSAGE" value="统一认证本地登录界面拦截优化" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>