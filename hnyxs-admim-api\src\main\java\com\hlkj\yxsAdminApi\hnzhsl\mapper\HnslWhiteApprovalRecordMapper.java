package com.hlkj.yxsAdminApi.hnzhsl.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprovalRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteApprovalRecordParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HnslWhiteApprovalRecordMapper extends BaseMapper<HnslWhiteApprovalRecord> {

    List<HnslWhiteApprovalRecord> selectPageRel(@Param("page") IPage<HnslWhiteApprovalRecord> page,
                                         @Param("param") HnslWhiteApprovalRecordParam param);

//    public List<Map<String, Object>> queryListTable(Map<String,Object> map);

//    HnslWhiteApprovalRecord queryRecord(Long id);
}
