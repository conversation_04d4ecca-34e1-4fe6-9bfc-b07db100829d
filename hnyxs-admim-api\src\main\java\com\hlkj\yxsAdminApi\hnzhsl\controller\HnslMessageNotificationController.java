package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslMessageNotificationService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMessageNotification;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslMessageNotificationParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 扫楼消息通知表控制器
 *
 * <AUTHOR>
 * @since 2023-07-21 11:31:45
 */
@Api(tags = "扫楼消息通知表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-message-notification")
public class HnslMessageNotificationController extends BaseController {
    @Autowired
    private HnslMessageNotificationService hnslMessageNotificationService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼消息通知表")
    @GetMapping("/page")
    public ApiResult<PageResult<HnslMessageNotification>> page(HnslMessageNotificationParam param) {
        PageParam<HnslMessageNotification, HnslMessageNotificationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslMessageNotificationService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslMessageNotificationService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼消息通知表")
    @GetMapping()
    public ApiResult<List<HnslMessageNotification>> list(HnslMessageNotificationParam param) {
        PageParam<HnslMessageNotification, HnslMessageNotificationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslMessageNotificationService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslMessageNotificationService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼消息通知表")
    @GetMapping("/{id}")
    public ApiResult<HnslMessageNotification> get(@PathVariable("id") Integer id) {
        return success(hnslMessageNotificationService.getById(id));
        // 使用关联查询
        //return success(hnslMessageNotificationService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:save')")
    @OperationLog
    @ApiOperation("添加扫楼消息通知表")
    @PostMapping()
    public ApiResult<?> save(@RequestBody HnslMessageNotification hnslMessageNotification) {
        if (hnslMessageNotificationService.save(hnslMessageNotification)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:update')")
    @OperationLog
    @ApiOperation("修改扫楼消息通知表")
    @PutMapping()
    public ApiResult<?> update(@RequestBody HnslMessageNotification hnslMessageNotification) {
        if (hnslMessageNotificationService.updateById(hnslMessageNotification)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:remove')")
    @OperationLog
    @ApiOperation("删除扫楼消息通知表")
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslMessageNotificationService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:save')")
    @OperationLog
    @ApiOperation("批量添加扫楼消息通知表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslMessageNotification> list) {
        if (hnslMessageNotificationService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:update')")
    @OperationLog
    @ApiOperation("批量修改扫楼消息通知表")
    @PutMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<HnslMessageNotification> batchParam) {
        if (batchParam.update(hnslMessageNotificationService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslMessageNotification:remove')")
    @OperationLog
    @ApiOperation("批量删除扫楼消息通知表")
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslMessageNotificationService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
