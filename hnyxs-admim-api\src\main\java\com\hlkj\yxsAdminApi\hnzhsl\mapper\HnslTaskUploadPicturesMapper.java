package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskUploadPictures;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskUploadPicturesParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分享图片表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslTaskUploadPicturesMapper extends BaseMapper<HnslTaskUploadPictures> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTaskUploadPictures>
     */
    List<HnslTaskUploadPictures> selectPageRel(@Param("page") IPage<HnslTaskUploadPictures> page,
                             @Param("param") HnslTaskUploadPicturesParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTaskUploadPictures> selectListRel(@Param("param") HnslTaskUploadPicturesParam param);

}
