package com.hlkj.yxsAdminApi.common.core.utils;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static com.hlkj.yxsAdminApi.common.core.utils.ConnectionUrlUtil.log;


/**
 * 文件工具类
 */
public class FileUtil {
    private static final Logger logger = LogManager.getLogger(FileUtil.class);

    private final Image img;
    private final int width;
    private final int height;
    private final String newPath;

    /**
     * 构造函数
     */
    public FileUtil(File f) throws IOException {
        img = ImageIO.read(f); // 构造Image对象
        width = img.getWidth(null); // 得到源图宽
        height = img.getHeight(null); // 得到源图长
        newPath = f.getAbsolutePath();
    }

    /**
     * 按照宽度还是高度进行压缩
     *
     * @param w int 最大宽度
     * @param h int 最大高度
     */
    /*public void resizeFix(int w, int h) throws IOException {
        if (width / height > w / h) {
            resizeByWidth(w);
        } else {
            resizeByHeight(h);
        }
    }*/

    /**
     * 以宽度为基准，等比例放缩图片
     *
     * @param w int 新宽度
     */
   /* public void resizeByWidth(int w) throws IOException {
        int h = (int) (height * w / width);
        resize(w, h);
    }*/

    /**
     * 以高度为基准，等比例缩放图片
     *
     * @param h int 新高度
     */
   /* public void resizeByHeight(int h) throws IOException {
        int w = (int) (width * h / height);
        resize(w, h);
    }*/

    /**
     * 强制压缩/放大图片到固定的大小
     *
     */
  /*  public void resize(int w, int h) throws IOException {
        // SCALE_SMOOTH 的缩略算法 生成缩略图片的平滑度的 优先级比速度高 生成的图片质量比较好 但速度慢
        BufferedImage image = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
        image.getGraphics().drawImage(img, 0, 0, w, h, null); // 绘制缩小后的图
        File destFile = new File(newPath);
        FileOutputStream out = new FileOutputStream(destFile); // 输出到文件流
        // 可以正常实现bmp、png、gif转jpg
        JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
        encoder.encode(image); // JPEG编码
        out.close();
    }*/

    public static File downLoadFileFormUtil(String url, String dir) {
        File f;
        try {
            URL httpUrl = new URL(url);
            String fileName = getFileNameFromUrl(url);
            logger.info("获取到fileName:" + fileName);
            f = new File(dir + fileName + ".zip");
            FileUtils.copyURLToFile(httpUrl, f);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return f;
    }

    /**
     * 从url获取文件名称
     *
     * @param url 路径
     * @return 文件名
     */
    public static String getFileNameFromUrl(String url) {
        String name = System.currentTimeMillis() + ".X";
        int index = url.lastIndexOf("/");
        if (index > 0) {
            name = url.substring(index + 1);
            if (name.trim().length() > 0) {
                return name;
            }
        }
        return name;
    }


    public static List<String> unzip(File file, String unzipPath) {
        List<String> base64List = new CopyOnWriteArrayList<>();
        try {
            ZipFile zf = new ZipFile(file);
            Enumeration<? extends ZipEntry> zipEntrys = zf.entries();
            while (zipEntrys.hasMoreElements()) {
                ZipEntry zipEntry = zipEntrys.nextElement();
                InputStream is = zf.getInputStream(zipEntry);
                FileOutputStream fos = new FileOutputStream(unzipPath + zipEntry.getName());
                byte[] bytes = new byte[1024];
                int read = 0;
                while ((read = is.read(bytes)) != -1) {
                    fos.write(bytes, 0, read);
                }
                fos.close();
                is.close();
                String unzipPhotoPath = unzipPath + zipEntry.getName();
                File photoFile = new File(unzipPhotoPath);
                String file2Base64 = fileToBase64(photoFile);
                if (StringUtil.isNotNull(file2Base64)) {
                    base64List.add(file2Base64);
                    photoFile.delete();
                }

            }
            zf.close();
            file.delete();
        } catch (Exception e) {
            logger.info("FileUtil unzip error:" + e.getMessage());
            e.printStackTrace();
            return null;
        }

        return base64List;
    }


    public static String fileToBase64(File file) {
        if (file == null) {
            return null;
        }
        String base64 = null;
        FileInputStream fin = null;
        try {
            fin = new FileInputStream(file);
            byte[] buff = new byte[fin.available()];
            fin.read(buff);
            base64 = Base64.encode(buff);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fin != null) {
                try {
                    fin.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return base64;
    }

    /**
     * 文件File类型转byte[]
     *
     * @param file 文件对象
     * @return
     */
    public static byte[] fileToByte(File file) {
        byte[] fileBytes = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            fileBytes = new byte[(int) file.length()];
            fis.read(fileBytes);
            fis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileBytes;
    }


    /**
     * 其他视频视频格式转化为MP4格式
     * @param file
     * @return 文件路径
     */
//    public static String convertToMp4(File file) {
//        FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(file);
//        String fileName = null;
//        Frame captured_frame = null;
//        FFmpegFrameRecorder recorder = null;
//        try {
//            frameGrabber.start();
//            fileName = file.getAbsolutePath().replace(".webm",".mp4");
//
//            recorder = new FFmpegFrameRecorder(fileName, frameGrabber.getImageWidth(), frameGrabber.getImageHeight(), frameGrabber.getAudioChannels());
//            recorder.setVideoCodec(frameGrabber.getVideoCodec());
//            recorder.setFormat(".mp4");
//            recorder.setFrameRate(frameGrabber.getFrameRate());
//            recorder.setSampleRate(frameGrabber.getSampleRate());
//
//            recorder.setAudioChannels(frameGrabber.getAudioChannels());
//            recorder.setFrameRate(frameGrabber.getFrameRate());
//            recorder.start();
//            while ((captured_frame = frameGrabber.grabFrame()) != null) {
//                try {
//                    recorder.record(captured_frame);
//                } catch (Exception e) {
//                    System.out.println(e);
//                }
//            }
//            recorder.stop();
//            recorder.release();
//            frameGrabber.stop();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return fileName;
//    }

    /**
     * 截取视频图片两张 不同帧数
     * @param fileVideo
     * @return
     */
//    public static JSONObject videoInterceptImage(File fileVideo) {
//        String SAVE_FILE_PATH=ConstantUtil.SAVE_FILE_PATH;
//        JSONObject jsonObject = new JSONObject();
//        try {
//            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
//            SimpleDateFormat sfFile = new SimpleDateFormat("yyyy-MM-dd");
//            String path= "uploads" + File.separator + "picture" //servletContext.getRealPath("/") +
//                    + File.separator+"video"+ File.separator+sfFile.format(new Date())+ File.separator;
//            String uploadPath =SAVE_FILE_PATH+path;
//            File dirFile = new File(uploadPath);
//            if (!dirFile.isDirectory()) {
//                if (!dirFile.mkdir()) {
//                    logger.info("活体图片文件所在目录创建失败");
//                }
//            }
//            //截取图片保存
//            long fileSizeJT =  1024;// 1KB
//            String newname1 = sf_.format(new Date()) + (int) (Math.random() * (99999 - 10000) + 10000);
//            String imgeFilePath1=uploadPath+newname1+".jpg";
//            logger.info("截图图片1路径："+imgeFilePath1);
//            File file2 =writeBytesToFile(fileVideo,imgeFilePath1,0,2);
//            if(file2.length() < fileSizeJT) {
//                file2 = writeBytesToFile(fileVideo, imgeFilePath1, 0,2);
//            }
//            String rarPath =SAVE_FILE_PATH+path
//                    +sf_.format(new Date()) + (int) (Math.random() * (99999 - 10000) + 10000)+".jpg";
//            String imagePath1 = zipImageFile(file2, rarPath, 1000, 700, 0.8f); // 1500, 1200
//            jsonObject.put("imagePath1", imagePath1);
//
//            String newname2 = sf_.format(new Date()) + (int) (Math.random() * (99999 - 10000) + 10000);
//            String imgeFilePath2=uploadPath+newname2+".jpg";
//            logger.info("截图图片2路径："+imgeFilePath2);
//            File file3 = writeBytesToFile(fileVideo,imgeFilePath2,10,3);
//            if(file3.length() < fileSizeJT) {
//                file3 = writeBytesToFile(fileVideo,imgeFilePath2,10,3);
//            }
//            String rarPath2 =SAVE_FILE_PATH+path
//                    +sf_.format(new Date()) + (int) (Math.random() * (99999 - 10000) + 10000)+".jpg";
//            String imagePath2 = zipImageFile(file3, rarPath2, 1000, 700, 0.8f); // 1500, 1200
//
//            jsonObject.put("imagePath2", imagePath2);
//            jsonObject.put("resultCode", "0");
//        }catch (Exception e) {
//            logger.error("获取小程序视频接口异常："+e);
//            jsonObject.put("resultCode", "1");
//        }
//        return jsonObject;
//    }

//    public static File writeBytesToFile(File video, String picPath,int frameNumber,int takeNumber) {
//        try {
//            FFmpegFrameGrabber ff =new FFmpegFrameGrabber(video);
//            ff.start();
//            //视频旋转角度，可能是null
//            String rotate_old=ff.getVideoMetadata("rotate");
//            int lenght = ff.getLengthInFrames();
//            System.out.println(lenght);
//            int i = 0;
//            int index=0;
//            if(takeNumber==2){//获取中间帧数
//                index=lenght/takeNumber;
//            }else if(takeNumber==3){//获取倒数10帧
//                index=lenght-frameNumber;
//            }else{
//                index=frameNumber;
//            }
//            Frame f = null;
//            while (i < lenght) {
//                // frameNumber:5 过滤前5帧，避免出现全黑的图片，依自己情况而定
//                f = ff.grabFrame();
//
//                //grabFrame();//解析问题 目前只发现一例 待测试
//                //.grabImage();
//                if ((i > index) && (f != null)
//                        && (f.image != null)) {
//                    break;
//                }
//                i++;
//            }
//            int owidth = f.imageWidth;
//            int oheight = f.imageHeight;
//            // 对截取的帧进行等比例缩放
//            int width = 480;//生成图片宽度为480px
//            int height = (int) (((double) width / owidth) * oheight);
//            // 截取的帧图片
//            Java2DFrameConverter converter = new Java2DFrameConverter();
//            BufferedImage srcImage = converter.getBufferedImage(f);
//            BufferedImage bi = new BufferedImage(width, height, BufferedImage.TYPE_3BYTE_BGR);
//            bi.getGraphics().drawImage(srcImage.getScaledInstance(width, height, Image.SCALE_SMOOTH),
//                    0, 0, null);
//            File file =null;
//            if(rotate_old!=null && !rotate_old.isEmpty()){
//                //log.info("进入旋转");
//                int rotate=Integer.parseInt(rotate_old);
//                file =  rotatePhonePhoto(bi,rotate,picPath);
//            }else {
//                //log.info("未进入旋转");
//                file = new File(picPath);
//                ImageIO.write(bi, "jpg", file);
//            }
//            ff.stop();
//            return file;
//        }catch (Exception e) {
//            logger.error("小程序视频转图片异常："+e);
//            e.printStackTrace();
//        }catch (Throwable e) {
//            logger.error("小程序视频转图片Throwable异常："+e);
//            //e.printStackTrace();
//        }
//        return null;
//    }

    /**
     * 旋转照片
     * @param src
     * @param angel
     * @return
     */
    public static File rotatePhonePhoto(BufferedImage src, int angel,String fileName) {
        try {
            int src_width = src.getWidth(null);
            int src_height = src.getHeight(null);

            int swidth = src_width;
            int sheight = src_height;

            if (angel == 90 || angel == 270) {
                swidth = src_height;
                sheight = src_width;
            }
            Rectangle rect_des = new Rectangle(new Dimension(swidth, sheight));
            BufferedImage res = new BufferedImage(rect_des.width, rect_des.height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2 = res.createGraphics();
            g2.translate((rect_des.width - src_width) / 2, (rect_des.height - src_height) / 2);
            g2.rotate(Math.toRadians(angel), src_width / 2, src_height / 2);
            g2.drawImage(src, null, null);
            File file = new File(fileName);
            ImageIO.write(res,"jpg",file);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据设置的宽高等比例压缩图片文件<br>
     * 先保存原文件，再压缩、上传
     *
     * @param oldFile 要进行压缩的文件
     * @param srcImgPath 修改后的图片路径
     * @param width 宽度 //设置宽度时（高度传入0，等比例缩放）
     * @param height 高度 //设置高度时（宽度传入0，等比例缩放）
     * @param quality 质量
     * @return 返回压缩后的文件的全路径
     */
    public static String zipImageFile(File oldFile, String srcImgPath, int width, int height, float quality) {
        if (oldFile == null) {
            return null;
        }
        try {
            /** 对服务器上的临时文件进行处理 */
            Image srcFile = ImageIO.read(oldFile);
            int w = srcFile.getWidth(null);
            int h = srcFile.getHeight(null);
            double bili;
            if (width > 0) {
                bili = width / (double) w;
                height = (int) (h * bili);
            } else {
                if (height > 0) {
                    bili = height / (double) h;
                    width = (int) (w * bili);
                }
            }

            logger.info("HNSL上传图片路径：" + srcImgPath);
            String subfix = "jpg";
            subfix = srcImgPath.substring(srcImgPath.lastIndexOf(".") + 1, srcImgPath.length());

            BufferedImage buffImg = null;
            if ("png".equals(subfix)) {
                buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            } else {
                buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String timeStr = simpleDateFormat.format(System.currentTimeMillis());
            Graphics2D graphics = buffImg.createGraphics();
            graphics.setBackground(new Color(255, 255, 255));
            graphics.setColor(new Color(255, 255, 255));
            graphics.fillRect(0, 0, width, height);
            graphics.drawImage(srcFile.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);

            // graphics.setPaint(new Color(0, 0, 0)); // 画第一行 姓名
            graphics.setFont(new Font("SansSerif", Font.PLAIN, 15));
            // 水印
            graphics.drawString("仅用于电信业务办理（扫楼小程序）" + timeStr, 25, height - 15);

            ImageIO.write(buffImg, subfix, new File(srcImgPath));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        int begin = srcImgPath.indexOf("uploads");// TODO 线上改为/uploads
        int last = srcImgPath.length();
        logger.info("HNSL打标后的图片上传返回地址：" + srcImgPath.substring(begin, last));
        return srcImgPath.substring(begin, last);
    }

    /**
     * 获取图片的类型
     *
     * @param base64Image
     * @return
     */
    public static String getBase64ImageType(String base64Image) {
        String[] parts = base64Image.split(",");
        if (parts.length > 1) {
            String header = parts[0];
            return header.substring(header.indexOf('/') + 1, header.indexOf(';'));
        } else {
            String imageData = parts[0];
            byte[] imageBytes = java.util.Base64.getDecoder().decode(imageData);

            // 根据图像头字节确定图像类型
            if (imageBytes.length >= 3 && imageBytes[0] == (byte) 0xFF && imageBytes[1] == (byte) 0xD8
                    && imageBytes[2] == (byte) 0xFF) {
                return "jpeg";
            } else if (imageBytes.length >= 4 && imageBytes[0] == (byte) 0x89 && imageBytes[1] == (byte) 0x50
                    && imageBytes[2] == (byte) 0x4E && imageBytes[3] == (byte) 0x47) {
                return "png";
            } else if (imageBytes.length >= 2 && imageBytes[0] == (byte) 0x47 && imageBytes[1] == (byte) 0x49) {
                return "gif";
            } else {
                return "unknown";
            }
        }
    }

    /**
     * 压缩图片并添加水印
     *
     * @param originalImage 原始图片
     * @param imageType     图片类型
     * @param maxSizeKb     最大压缩大小，单位KB
     * @param watermarkTextArray 水印文本数组
     * @return 压缩并添加水印后的图片
     * @throws IOException 如果处理过程中发生错误
     */
    @SneakyThrows
    public static BufferedImage compressAndAddWatermark(BufferedImage originalImage, String imageType, int maxSizeKb, JSONArray watermarkTextArray, byte[] imageBytes) {
        double compressionQuality = 2.0;
        int imageSizeKb = imageBytes.length / 1024;

        if (imageSizeKb > maxSizeKb) {
            compressionQuality = (double) maxSizeKb / imageSizeKb;
        }

        int newWidth = (int) (originalImage.getWidth() * compressionQuality);
        int newHeight = (int) (originalImage.getHeight() * compressionQuality);

        BufferedImage compressedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        compressedImage.getGraphics().drawImage(originalImage.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH), 0, 0, null);

        addWatermark(compressedImage, watermarkTextArray);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(compressedImage, imageType, outputStream);
        return compressedImage;

    }

    /**
     * 在图片上添加水印
     *
     * @param image         图片
     * @param watermarkTextArray 水印文本数组
     */
    public static void addWatermark(BufferedImage image, JSONArray watermarkTextArray) {
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,  RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.9f));
        g2d.setColor(Color.lightGray);

        int width = image.getWidth(null);/*获取文件的宽*/
        int size = width / 300 * 10;
        if (size == 0) {
            size = 10;
        }
        Font font = new Font("宋体", Font.BOLD, size);//水印字体，大小

        int fontSize = Math.min(image.getWidth(), image.getHeight()) / 22;
        g2d.setFont(font);

        int bottomY = image.getHeight() - fontSize;
        int startY = bottomY - (watermarkTextArray.size() * fontSize) + 15;

        for (int i = 0; i < watermarkTextArray.size(); i++) {
            String watermarkText = watermarkTextArray.getString(i);
            Rectangle2D rect = g2d.getFontMetrics().getStringBounds(watermarkText, g2d);
            int centerX = (image.getWidth() - (int) rect.getWidth()) / 2;
            g2d.drawString(watermarkText, centerX, startY);
            startY += fontSize;
        }
        g2d.dispose();
    }

    // 保存图片
    public static void saveImage(BufferedImage image, String imagePath, String imageType) throws IOException {
        File output = new File(imagePath);
        ImageIO.write(image, imageType, output);
    }

    // 添加水印
    public static void addWatermark(String imagePath, JSONArray watermarkTextArray, String imageType)
            throws IOException {
        BufferedImage image = ImageIO.read(new File(imagePath));
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setColor(Color.RED);
        int fontSize = Math.min(image.getWidth(), image.getHeight()) / 22;
        g2d.setFont(new Font("SansSerif", Font.BOLD, fontSize));
        FontMetrics fontMetrics = g2d.getFontMetrics();

        int bottomY = image.getHeight() - fontSize;
        int startY = bottomY - (watermarkTextArray.size() * fontSize) + 15;

        for (int i = 0; i < watermarkTextArray.size(); i++) {
            String watermarkText = watermarkTextArray.getString(i);
            Rectangle2D rect = fontMetrics.getStringBounds(watermarkText, g2d);
            int centerX = (image.getWidth() - (int) rect.getWidth()) / 2;
            g2d.drawString(new String(watermarkText.getBytes("UTF-8"), "UTF-8"), centerX, startY);
            startY += fontSize;
        }

        g2d.dispose();
        ImageIO.write(image, imageType, new File(imagePath));
    }


    public static byte[] getBytesFromBufferedImage(BufferedImage image,String imageType) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, imageType, baos); // 或者其他格式，如 "PNG"
        baos.flush();
        byte[] imageBytes = baos.toByteArray();
        baos.close();
        return imageBytes;
    }

    /**
     * 返回图片base64
     *
     * @param bucketPhoto
     * @param filename
     * @return
     */
    public static String getImageBytess(AmazonS3 amazonS3, String bucketPhoto, String filename) {
        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketPhoto, filename);
            S3Object s3Object = amazonS3.getObject(getObjectRequest);
            S3ObjectInputStream objectInputStream = s3Object.getObjectContent();
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = objectInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, length);
                }
                return java.util.Base64.getEncoder().encodeToString(outputStream.toByteArray());
            }
        } catch (Exception e) {
            log.error("getImageBytess>>>异常", e);
        }
        return null;
    }

}
