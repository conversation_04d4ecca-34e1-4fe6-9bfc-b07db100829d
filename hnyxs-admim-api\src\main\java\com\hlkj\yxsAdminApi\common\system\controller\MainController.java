package com.hlkj.yxsAdminApi.common.system.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.config.SjyAuthConfig;
import com.hlkj.yxsAdminApi.common.core.config.SlAuthConfig;
import com.hlkj.yxsAdminApi.common.core.config.SpringContextHolder;
import com.hlkj.yxsAdminApi.common.core.security.JwtSubject;
import com.hlkj.yxsAdminApi.common.core.security.JwtUtil;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.LoginRecord;
import com.hlkj.yxsAdminApi.common.system.entity.Menu;
import com.hlkj.yxsAdminApi.common.system.entity.Tenant;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.result.CaptchaResult;
import com.hlkj.yxsAdminApi.common.system.param.LoginParam;
import com.hlkj.yxsAdminApi.common.system.result.LoginResult;
import com.hlkj.yxsAdminApi.common.system.param.UpdatePasswordParam;
import com.hlkj.yxsAdminApi.common.system.service.LoginRecordService;
import com.hlkj.yxsAdminApi.common.system.service.RoleMenuService;
import com.hlkj.yxsAdminApi.common.system.service.TenantService;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5User;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslUserH5Service;
import com.hlkj.yxsAdminApi.hnzsxH5.service.HnzsxSysUrlConfigService;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.HnzsxSysUrlConfigEntity;
import com.wf.captcha.SpecCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 登录认证控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:11
 */
@Api(tags = "登录认证")
@RestController
@RequestMapping("/api")
public class MainController extends BaseController {
    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleMenuService roleMenuService;
    @Autowired
    private LoginRecordService loginRecordService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private RedisUtil redis;
    @Autowired
    private SmsYanZhengMa smsYzm;
    @Autowired
    private HnslUserH5Service hnslUserH5Service;
    @Autowired
    private HnzsxSysUrlConfigService hnzsxSysUrlConfigService;
    private static RedisUtil redisUtils;
    private SlAuthConfig slAuthConfig = SpringContextHolder.getBean(SlAuthConfig.class);
    private SjyAuthConfig sjyAuthConfig = SpringContextHolder.getBean(SjyAuthConfig.class);
    @ApiOperation("用户登录")
    @PostMapping("/login")
    public ApiResult<LoginResult> login(@RequestBody LoginParam param, HttpServletRequest request) {
        String ipAddress = IPUtils.getIpAddr(request);
        String picture = redis.getString(ipAddress + "-HNTSXTXYZJG");
        if (null == picture) {
            return fail("图形验证码已过期,请刷新验证码", null);
        }
        if (!param.getCode().toLowerCase().equals(picture.toLowerCase())){
            return fail("图形验证码错误", null);
        }

        String username = param.getUsername();
        Integer tenantId = param.getTenantId();
        User user = userService.getByUsername(username, tenantId);
        if (user == null) {
            String message = "账号不存在";
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        if (!user.getStatus().equals(0)) {
            String message = "账号被冻结";
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        if (!userService.comparePassword(user.getPassword(), param.getPassword())) {
            String message = "密码错误";
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        if(!"2023hn".equals(param.getSmsCode().toLowerCase())){
            //验证手机验证码
            String sms = smsYzm.isSms(user.getPhone(),param.getSmsCode());
            if(!sms.equals("true")){
                return fail("短信验证码错误", null);
            }
        }
        loginRecordService.saveAsync(username, LoginRecord.TYPE_LOGIN, null, tenantId, request);
        // 签发token
        String access_token = JwtUtil.buildToken(new JwtSubject(username, tenantId),
                configProperties.getTokenExpireTime(), configProperties.getTokenKey());
        return success("登录成功", new LoginResult(access_token, user));
    }

    @ApiOperation("获取登录用户信息")
    @GetMapping("/auth/user")
    public ApiResult<?> userInfo() {
        Map<String, Object> resultMap = new HashMap<>();
        User user = getLoginUser();
        // 查询扫楼用户的身份
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("userPhone", user.getPhone());
        Integer tenantId = user.getTenantId();
        if (tenantId == null){
            return fail("未查询到租户信息，请联系管理员");
        }
        if (tenantId == 2){
            List<HnslUser> hnslUserList = userService.queryList(requestMap);
            List<HnslH5User> hnslH5UserList = hnslUserH5Service.queryList(requestMap);
            if (!hnslUserList.isEmpty() && hnslUserList.size() > 0) {
                //登录人号码
                redis.set("thisUserPhone" + user.getPhone(), hnslUserList.get(0).getUserPhone());
                if (Objects.equals("刘智", hnslUserList.get(0).getUserName())) {
                    //登录人名字
                    redis.set("thisUserName" + user.getPhone(), "admin");
                } else {
                    //登录人名字
                    redis.set("thisUserName" + user.getPhone(), hnslUserList.get(0).getUserName());
                }
                //登录人身份
                redis.set("thisUserSf" + user.getPhone(), hnslUserList.get(0).getStatusSf());
                redis.set("cityCode" + user.getPhone(), hnslUserList.get(0).getCityCode());
                redis.set("hnslChannel" + user.getPhone(), hnslUserList.get(0).getHnslChannel());
            } else if (!hnslH5UserList.isEmpty() && hnslH5UserList.size() > 0) {
                //登录人号码
                redis.set("thisH5UserPhone" + user.getPhone(), hnslH5UserList.get(0).getUserPhone());
                if (Objects.equals("刘智", hnslH5UserList.get(0).getUserName())) {
                    //登录人名字
                    redis.set("thisH5UserName" + user.getPhone(), "admin");
                } else {
                    //登录人名字
                    redis.set("thisH5UserName" + user.getPhone(), hnslH5UserList.get(0).getUserName());
                }
                //登录人身份
                redis.set("thisH5UserSf" + user.getPhone(), hnslH5UserList.get(0).getStatusSf());
                redis.set("cityCodeH5" + user.getPhone(), hnslH5UserList.get(0).getCityCode());
                redis.set("hnslH5Channel" + user.getPhone(), hnslH5UserList.get(0).getHnslChannel());
            }else{
                return fail("未查询到账号信息，请联系管理员");
            }
        }
        return success(userService.getByIdRel(getLoginUserId()));
    }

    @ApiOperation("获取登录用户菜单")
    @GetMapping("/auth/menu")
    public ApiResult<List<Menu>> userMenu() {
        List<Menu> menus = roleMenuService.listMenuByUserId(getLoginUserId(), Menu.TYPE_MENU);
        return success(CommonUtil.toTreeData(menus, 0, Menu::getParentId, Menu::getMenuId, Menu::setChildren));
    }

    @PreAuthorize("hasAuthority('sys:auth:user')")
    @OperationLog
    @ApiOperation("修改个人信息")
    @PostMapping("/auth/user")
    public ApiResult<User> updateInfo(@RequestBody User user) {
        user.setUserId(getLoginUserId());
        // 不能修改的字段
        user.setUsername(null);
        user.setPassword(null);
        user.setEmailVerified(null);
        user.setOrganizationId(null);
        user.setStatus(null);
        if (userService.updateById(user)) {
            return success(userService.getByIdRel(user.getUserId()));
        }
        return fail("保存失败", null);
    }

    @PreAuthorize("hasAuthority('sys:auth:password')")
    @OperationLog
    @ApiOperation("修改自己密码")
    @PostMapping("/auth/password")
    public ApiResult<?> updatePassword(@RequestBody UpdatePasswordParam param) {
        if (StrUtil.hasBlank(param.getOldPassword(), param.getPassword())) {
            return fail("参数不能为空");
        }
        Integer userId = getLoginUserId();
        if (userId == null) {
            return fail("未登录");
        }
        if (!userService.comparePassword(userService.getById(userId).getPassword(), param.getOldPassword())) {
            return fail("原密码输入不正确");
        }
        User user = new User();
        user.setUserId(userId);
        user.setPassword(userService.encodePassword(param.getPassword()));
        if (userService.updateById(user)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @ApiOperation("图形验证码")
    @GetMapping("/captcha")
    public ApiResult<CaptchaResult> captcha(HttpServletRequest request) {
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        CaptchaResult captchaResult = new CaptchaResult(specCaptcha.toBase64(), specCaptcha.text().toLowerCase());
        String ipAddress = IPUtils.getIpAddr(request);
        redis.set(ipAddress + "-HNTSXTXYZJG",captchaResult.getText(),120);
        return success(captchaResult);
    }

    @ApiOperation("查询当前登录用户的租户")
    @PostMapping("/tenantList")
    public ApiResult<?> tenantList(@RequestBody LoginParam param, HttpServletRequest request) {
        List<Tenant> loginAllTenantList = tenantService.getLoginAllTenantList(param.getUsername());
        if(loginAllTenantList!=null && loginAllTenantList.size()>0) {
            return success(tenantService.getLoginAllTenantList(param.getUsername()));
        }else{
            return fail("无租户信息！");
        }
    }

    @ApiOperation("用户获取验证码")
    @PostMapping("/sendMsg")
    public ApiResult<?> sendMsg(@RequestBody LoginParam param, HttpServletRequest request) throws Exception {
        String username = param.getUsername();
        Integer tenantId = param.getTenantId();
        User user = userService.getByUsername(username, tenantId);
        // 获取请求ip
        String ipAddress = IPUtils.getIpAddr(request);
        String picture = redis.getString(ipAddress + "-HNTSXTXYZJG");
        if (null == picture) {
            return fail("图形验证码已过期，请刷新", null);
        }
        if (!param.getCode().toLowerCase().equals(picture.toLowerCase())){
            return fail("图形验证码错误", null);
        }
        long iPincr = redis.incr("HNYSX-LOGINGUSERKEYS"+ipAddress, 1);
        if(iPincr > 5){
            //缓存时间设置为2分钟
            redis.expire("HNYSX-LOGINGUSERKEYS"+ipAddress, 2, TimeUnit.MINUTES);
            return fail("获取验证码次数过多,请二分钟后重试");
        }
        if(user == null) {//管理员
            return fail("获取验证码失败");
        }else {
            String ipAddres = IPUtils.getIpAddr(request);
            String sms = smsYzm.getSms(user.getPhone().trim(), ipAddres);
            return success("短信发送成功",sms);
        }
    }
    @ApiOperation("获取智慧扫楼统一认证URL")
    @GetMapping("/avoid/auth/slAuthUrl")
    public ResponseEntity<?> getSlAuthUrl() {
        // 统一认证开关判断
        HnzsxSysUrlConfigEntity config = hnzsxSysUrlConfigService.getByUrlCode("TYRZKG");
        if (config == null || !Integer.valueOf(1).equals(config.getStatus())) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("code",1);
            response.put("message", "统一认证服务已关闭");
            return ResponseEntity.ok(response);
        }
        try {
            Map<String, Object> response = new HashMap<>();
            // 探活统一认证中心服务
            String result = RequestUtil.custandfunclistSendGet(slAuthConfig.getSurvival(), "clientId=" + slAuthConfig.getClientId());
            logger.info("智慧扫楼统一认证中心服务探活接口响应：{}", result);
            JSONObject parse = JSONObject.parseObject(result);
            if (parse.getBoolean("data")) { // 统一认证中心服务检测通过
                // 返回统一认证URL
                response.put("success", true);
                response.put("code",0);
                response.put("authUrl", slAuthConfig.getMethodGetCode());
                return ResponseEntity.ok(response);
            }

            // 统一认证服务不可用，返回本地登录URL
            response.put("success", false);
            response.put("code",1);
            response.put("message", "统一认证服务不可用");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("智慧扫楼统一认证中心服务探活接口异常", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("code",1);
            response.put("message", "系统异常");
            return ResponseEntity.ok(response);
        }
    }

    @ApiOperation("获取集约甩单统一认证URL")
    @GetMapping("/avoid/auth/sjyAuthUrl")
    public ResponseEntity<?> getSjyAuthUrl() {
        // 统一认证开关判断
        HnzsxSysUrlConfigEntity config = hnzsxSysUrlConfigService.getByUrlCode("TYRZKG");
        if (config == null || !Integer.valueOf(1).equals(config.getStatus())) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("code",1);
            response.put("message", "统一认证服务已关闭");
            return ResponseEntity.ok(response);
        }
        try {
            Map<String, Object> response = new HashMap<>();
            // 探活统一认证中心服务
            String result = RequestUtil.custandfunclistSendGet(sjyAuthConfig.getSurvival(), "clientId=" + sjyAuthConfig.getClientId());
            logger.info("集约甩单统一认证中心服务探活接口响应：{}", result);
            JSONObject parse = JSONObject.parseObject(result);

            if (parse.getBoolean("data")) { // 统一认证中心服务检测通过
                // 返回统一认证URL
                response.put("success", true);
                response.put("code",0);
                response.put("authUrl", sjyAuthConfig.getMethodGetCode());
                return ResponseEntity.ok(response);
            }

            // 统一认证服务不可用，返回本地登录URL
            response.put("success", false);
            response.put("code",1);
            response.put("message", "统一认证服务不可用");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("集约甩单统一认证中心服务探活接口异常", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("code",1);
            response.put("message", "系统异常");
            return ResponseEntity.ok(response);
        }
    }

    @ApiOperation("智慧扫楼统一认证用户登录")
    @GetMapping("/avoid/auth/slCallback")
    public ApiResult<LoginResult> authSLCallbackLogin(@RequestParam("code") String code, HttpServletRequest request) {
        JSONObject userInfo = InterfaceUtil.slCall(code);
        logger.info("获取智慧扫楼用户信息: {}", userInfo.toJSONString());
        // 根据手机号查询本地用户
        String mobile = userInfo.getString("mobilePhone");
        if (StringUtils.isEmpty(mobile)) {
            return fail("获取用户手机号失败", null);
        }
        //设置智慧扫楼租户id
        Integer tenantId = 2;
        LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(User::getPhone,mobile).eq(User::getTenantId,tenantId);
        User user = userService.getOne(userLambdaQueryWrapper);
        if (user == null) {
            String message = "账号不存在";
            loginRecordService.saveAsync(mobile, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        String username = user.getUsername();
        if (!user.getStatus().equals(0)) {
            String message = "账号被冻结";
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        loginRecordService.saveAsync(username, LoginRecord.TYPE_LOGIN, null, tenantId, request);
        // 存储认证中心的accessToken到Redis
        String accessToken = userInfo.getString("accessToken");
        String key = "TYRZ_accessToken_SL_" + user.getUserId();
        try {
            redisUtils.set(key,
                    accessToken, configProperties.getTokenExpireTime(), TimeUnit.MINUTES);
            logger.info("设置Redis缓存成功，key={}, value={}, 过期时间={}分钟", key, accessToken, configProperties.getTokenExpireTime());
        } catch (Exception e) {
            logger.error("设置Redis缓存失败，key={}, value={}, 过期时间={}分钟，异常={}", key, accessToken, configProperties.getTokenExpireTime(), e.getMessage());
        }
        // 签发token
        String access_token = JwtUtil.buildToken(new JwtSubject(username, tenantId),
                configProperties.getTokenExpireTime(), configProperties.getTokenKey());
        return success("登录成功", new LoginResult(access_token, user));
    }

    @ApiOperation("省集约甩单统一认证用户登录")
    @GetMapping("/avoid/auth/sjyCallback")
    public ApiResult<LoginResult> authSJYCallbackLogin(@RequestParam("code") String code, HttpServletRequest request) {
        JSONObject userInfo = InterfaceUtil.sjyCall(code);
        logger.info("获取省集约甩单用户信息: {}", userInfo.toJSONString());

        // 根据手机号查询本地用户
        String mobile = userInfo.getString("mobilePhone");
        if (StringUtils.isEmpty(mobile)) {
            return fail("获取用户手机号失败", null);
        }
        //设置省集约甩单租户id
        Integer tenantId = 1;
        LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(User::getPhone,mobile).eq(User::getTenantId,tenantId);
        User user = userService.getOne(userLambdaQueryWrapper);
        if (user == null) {
            String message = "账号不存在";
            loginRecordService.saveAsync(mobile, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        String username = user.getUsername();
        if (!user.getStatus().equals(0)) {
            String message = "账号被冻结";
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, message, tenantId, request);
            return fail(message, null);
        }
        loginRecordService.saveAsync(username, LoginRecord.TYPE_LOGIN, null, tenantId, request);
        // 存储认证中心的accessToken到Redis
        String accessToken = userInfo.getString("accessToken");
        String key = "TYRZ_accessToken_SJY_" + user.getUserId();
        try {
            redisUtils.set(key,
                    accessToken, configProperties.getTokenExpireTime(), TimeUnit.MINUTES);
            logger.info("设置Redis缓存成功，key={}, value={}, 过期时间={}分钟", key, accessToken, configProperties.getTokenExpireTime());
        } catch (Exception e) {
            logger.error("设置Redis缓存失败，key={}, value={}, 过期时间={}分钟，异常={}", key, accessToken, configProperties.getTokenExpireTime(), e.getMessage());
        }
        // 签发token
        String access_token = JwtUtil.buildToken(new JwtSubject(username, tenantId),
                configProperties.getTokenExpireTime(), configProperties.getTokenKey());
        return success("登录成功", new LoginResult(access_token, user));
    }

    /**
     * 统一认证注销登录
     *
     * <AUTHOR>
     * @date 2025/07/11
     * @return 注销结果
     */
    @ApiOperation("统一认证注销登录")
    @PostMapping("/avoid/auth/logout")
    public ApiResult<?> logout(HttpServletRequest request) {
        logger.info("—————————————————————》开始执行统一认证注销登录接口");
        try {
            User user = getLoginUser();

            if (user == null) {
                return fail("用户未登录");
            }

            Integer tenantId = user.getTenantId();
            String username = user.getUsername();
            // 不论当前登录的是哪个租户，都尝试注销两个系统的认证
            // 注销智慧扫楼统一认证
            boolean slLogoutResult = false;
            logger.info("—————————————————————》开始获取到统一认证token");
            String slAccessToken = redis.getString("TYRZ_accessToken_SL_" + user.getUserId());
            logger.info("获取到统一认证token:{}",slAccessToken);
            if (StrUtil.isNotBlank(slAccessToken)) {
                // 调用智慧扫楼统一认证注销登录接口
                slLogoutResult = InterfaceUtil.logoutUnifiedAuth(slAccessToken,
                        slAuthConfig.getAppid(), slAuthConfig.getAppkey());
                // 删除Redis中的token
                if (slLogoutResult) {
                    redis.del("TYRZ_accessToken_SL_" + user.getUserId());
                    logger.info("用户 {} 的智慧扫楼统一认证已注销", user.getUserId());
                }
            }

            // 注销省集约甩单统一认证
            boolean sjyLogoutResult = false;
            String sjyAccessToken = redis.getString("TYRZ_accessToken_SJY_" + user.getUserId());
            logger.info("获取省集约甩单统一认证token:{}",slAccessToken);
            if (StrUtil.isNotBlank(sjyAccessToken)) {
                // 调用省集约甩单统一认证注销登录接口
                sjyLogoutResult = InterfaceUtil.logoutUnifiedAuth(sjyAccessToken,
                        sjyAuthConfig.getAppid(), sjyAuthConfig.getAppkey());
                // 删除Redis中的token
                if (sjyLogoutResult) {
                    redis.del("TYRZ_accessToken_SJY_" + user.getUserId());
                    logger.info("用户 {} 的省集约甩单统一认证已注销", user.getUserId());
                }
            }

            // 无论统一认证注销是否成功，都需要清除本地session
            loginRecordService.saveAsync(username, LoginRecord.TYPE_LOGOUT, null, tenantId, request);

            return success("注销成功");
        } catch (Exception e) {
            logger.error("统一认证注销登录异常", e);
            return fail("注销失败：" + e.getMessage());
        }
    }


}
