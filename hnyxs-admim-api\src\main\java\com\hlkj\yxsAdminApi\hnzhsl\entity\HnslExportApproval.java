package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导出审批实体类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@TableName("hnsl_export_approval")
@ApiModel(value = "导出审批", description = "导出审批实体类")
public class HnslExportApproval implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("提交人ID")
    private Long submitUserId;

    @ApiModelProperty("提交人")
    private String submitUser;

    @ApiModelProperty("提交时间")
    private Date submitDate;

    @ApiModelProperty("审批人手机号")
    private String approveUserPhone;

    @ApiModelProperty("审批人")
    private String approveUser;

    @ApiModelProperty("审批时间")
    private Date approveDate;

    @ApiModelProperty("状态：0待审批，1已通过，2已拒绝")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("数据来源状态：0无，1数据源，2报表")
    private Integer dataSourceStatus;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件路径")
    private String filePath;

    @ApiModelProperty("筛选条件")
    private String filterConditions;

    @ApiModelProperty("业务类型：1积分明细，2人员导出，3团队导出")
    @TableField(exist = false)
    private Integer type;

    @ApiModelProperty("数据量")
    @TableField(exist = false)
    private Integer dataCount;

    @ApiModelProperty("规则ID")
    private String ruleId;

    @ApiModelProperty("导出数量")
    private Integer exportNumber;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("活动事件")
    private Integer activeEvent;

    @ApiModelProperty("数据源名称")
    private String dataSourceName;

    @ApiModelProperty("短信验证码")
    private Integer smsCode;

    @TableField(exist = false)
    @ApiModelProperty("是否显示下载按钮：0否，1是")
    private Integer showDownload;

    @TableField(exist = false)
    @ApiModelProperty("是否显示审批按钮：0否，1是")
    private Integer showApproval;
} 