package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsCpsService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsCps;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsCpsParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 商品关联CPS表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "商品关联CPS表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoodsCps")
public class HnslGoodsCpsController extends BaseController {
    @Autowired
    private HnslGoodsCpsService hnslGoodsCpsService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:list')")
    @OperationLog
    @ApiOperation("分页查询商品关联CPS表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsCps>> page(@RequestBody HnslGoodsCpsParam param) {
        PageParam<HnslGoodsCps, HnslGoodsCpsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsCpsService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsCpsService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:list')")
    @OperationLog
    @ApiOperation("查询全部商品关联CPS表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoodsCps>> list(@RequestBody HnslGoodsCpsParam param) {
        PageParam<HnslGoodsCps, HnslGoodsCpsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsCpsService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsCpsService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:list')")
    @OperationLog
    @ApiOperation("根据id查询商品关联CPS表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsCps> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsCpsService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsCpsService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:save')")
    @OperationLog
    @ApiOperation("添加商品关联CPS表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslGoodsCps hnslGoodsCps) {
        User loginUser = getLoginUser();
        hnslGoodsCps.setCreatedDate(new Date());
        hnslGoodsCps.setCreatedUser(loginUser.getUsername());
        if (hnslGoodsCpsService.save(hnslGoodsCps)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:update')")
    @OperationLog
    @ApiOperation("修改商品关联CPS表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoodsCps hnslGoodsCps) {
        User loginUser = getLoginUser();
        hnslGoodsCps.setUpdatedDate(new Date());
        hnslGoodsCps.setUpdatedUser(loginUser.getUsername());
        if (hnslGoodsCpsService.updateById(hnslGoodsCps)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:remove')")
    @OperationLog
    @ApiOperation("删除商品关联CPS表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsCpsService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:save')")
    @OperationLog
    @ApiOperation("批量添加商品关联CPS表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsCps> list) {
        if (hnslGoodsCpsService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:update')")
    @OperationLog
    @ApiOperation("批量修改商品关联CPS表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoodsCps> batchParam) {
        if (batchParam.update(hnslGoodsCpsService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsCps:remove')")
    @OperationLog
    @ApiOperation("批量删除商品关联CPS表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsCpsService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
