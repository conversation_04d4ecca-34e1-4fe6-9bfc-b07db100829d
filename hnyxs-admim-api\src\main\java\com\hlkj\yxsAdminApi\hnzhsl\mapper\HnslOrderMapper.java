package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslOrderParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 扫楼工具订单表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslOrderMapper extends BaseMapper<HnslOrder> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslOrder>
     */
    List<HnslOrder> selectPageRel(@Param("page") IPage<HnslOrder> page,
                             @Param("param") HnslOrderParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslOrder> selectListRel(@Param("param") HnslOrderParam param);

    /**
     * 查询订单详情信息
     *
     * @param param 查询参数
     * @return List<HnslOrder>
     */
    HnslOrder queryObject(Wrapper<HnslOrderParam> param);
}
