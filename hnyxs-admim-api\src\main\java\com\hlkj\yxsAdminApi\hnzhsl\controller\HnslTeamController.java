package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTeamService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeam;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合伙人团队表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "合伙人团队表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-team")
public class HnslTeamController extends BaseController {
    @Autowired
    private HnslTeamService hnslTeamService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:list')")
    @OperationLog
    @ApiOperation("分页查询合伙人团队表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTeam>> page(@RequestBody HnslTeamParam param) {
        PageParam<HnslTeam, HnslTeamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTeamService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:list')")
    @OperationLog
    @ApiOperation("查询全部合伙人团队表")
    @PostMapping("/list")
    public ApiResult<List<HnslTeam>> list(@RequestBody HnslTeamParam param) {
        PageParam<HnslTeam, HnslTeamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTeamService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:list')")
    @OperationLog
    @ApiOperation("根据id查询合伙人团队表")
    @GetMapping("/{id}")
    public ApiResult<HnslTeam> get(@PathVariable("id") Integer id) {
        return success(hnslTeamService.getById(id));
        // 使用关联查询
        //return success(hnslTeamService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:save')")
    @OperationLog
    @ApiOperation("添加合伙人团队表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTeam hnslTeam) {
        if (hnslTeamService.save(hnslTeam)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:update')")
    @OperationLog
    @ApiOperation("修改合伙人团队表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTeam hnslTeam) {
        if (hnslTeamService.updateById(hnslTeam)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:remove')")
    @OperationLog
    @ApiOperation("删除合伙人团队表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTeamService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:save')")
    @OperationLog
    @ApiOperation("批量添加合伙人团队表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTeam> list) {
        if (hnslTeamService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:update')")
    @OperationLog
    @ApiOperation("批量修改合伙人团队表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTeam> batchParam) {
        if (batchParam.update(hnslTeamService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeam:remove')")
    @OperationLog
    @ApiOperation("批量删除合伙人团队表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTeamService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
