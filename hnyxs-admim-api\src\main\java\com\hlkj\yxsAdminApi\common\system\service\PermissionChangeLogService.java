package com.hlkj.yxsAdminApi.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.system.entity.PermissionChangeLog;
import com.hlkj.yxsAdminApi.common.system.entity.User;

import javax.servlet.http.HttpServletRequest;

/**
 * 权限变更日志Service
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface PermissionChangeLogService extends IService<PermissionChangeLog> {

    /**
     * 记录权限变更日志
     *
     * @param request HttpServletRequest
     * @param operatorUser 操作人
     * @param operationType 操作类型
     * @param operationModule 操作模块
     * @param targetId 目标对象ID
     * @param targetName 目标对象名称
     * @param changeDetails 变更详情
     * @param beforeChange 变更前内容
     * @param afterChange 变更后内容
     * @param approvalOrderNo 审核工单号
     * @return 是否成功
     */
    boolean recordLog(HttpServletRequest request, User operatorUser, String operationType, 
                   String operationModule, String targetId, String targetName, 
                   String changeDetails, String beforeChange, String afterChange, 
                   String approvalOrderNo);

    /**
     * 格式化用户相关变更详情
     *
     * @param field 字段名称
     * @param before 变更前
     * @param after 变更后
     * @return 格式化后的变更详情
     */
    String formatUserChangeDetails(String field, Object before, Object after);
    
    /**
     * 格式化角色相关变更详情
     *
     * @param field 字段名称
     * @param before 变更前
     * @param after 变更后
     * @return 格式化后的变更详情
     */
    String formatRoleChangeDetails(String field, Object before, Object after);
    
    /**
     * 格式化菜单相关变更详情
     *
     * @param field 字段名称
     * @param before 变更前
     * @param after 变更后
     * @return 格式化后的变更详情
     */
    String formatMenuChangeDetails(String field, Object before, Object after);
    
    /**
     * 格式化组织相关变更详情
     *
     * @param field 字段名称
     * @param before 变更前
     * @param after 变更后
     * @return 格式化后的变更详情
     */
    String formatOrgChangeDetails(String field, Object before, Object after);
} 