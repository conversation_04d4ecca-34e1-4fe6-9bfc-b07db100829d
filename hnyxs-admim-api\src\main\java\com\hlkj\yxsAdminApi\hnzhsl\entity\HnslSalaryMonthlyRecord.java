package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫楼薪资每月记录表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSalaryMonthlyRecord对象", description = "扫楼薪资每月记录表")
public class HnslSalaryMonthlyRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "审核记录月（2020-1）")
    @TableField("AUDIT_DATE")
    private String auditDate;

    @ApiModelProperty(value = "地市编码")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty(value = "学校名称")
    @TableField("SCHOOL_NAME")
    private String schoolName;

    @ApiModelProperty(value = "学子姓名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "角色 *核心团队长/普通团队长/直销员")
    @TableField("TEAM_LEVEL")
    private String teamLevel;

    @ApiModelProperty(value = "身份证号码")
    @TableField("USER_CARD")
    private String userCard;

    @ApiModelProperty(value = "年龄")
    @TableField("USER_AGE")
    private String userAge;

    @ApiModelProperty(value = "服务时长(单位天)")
    @TableField("USER_SERVICE_TIME")
    private String userServiceTime;

    @ApiModelProperty(value = "团队名称")
    @TableField("TEAM_NAME")
    private String teamName;

    @ApiModelProperty(value = "KPI打分")
    @TableField("USER_KPI")
    private String userKpi;

    @ApiModelProperty(value = "注册时间")
    @TableField("USER_DATE")
    private String userDate;

    @ApiModelProperty(value = "新装数量")
    @TableField("BUSINESS_NUMBER1")
    private Integer businessNumber1;

    @ApiModelProperty(value = "新装积分")
    @TableField("BUSINESS_INTEGRATION1")
    private Integer businessIntegration1;

    @ApiModelProperty(value = "维系数量")
    @TableField("BUSINESS_NUMBER2")
    private Integer businessNumber2;

    @ApiModelProperty(value = "维系积分")
    @TableField("BUSINESS_INTEGRATION2")
    private Integer businessIntegration2;

    @ApiModelProperty(value = "个人总积分")
    @TableField("USER_INTEGRATION")
    private Integer userIntegration;

    @ApiModelProperty(value = "团队总积分")
    @TableField("TEAM_INTEGRATION")
    private Integer teamIntegration;

    @ApiModelProperty(value = "手机号码(工号)")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "个人服务费 普通直销个人服务费=个人积分*0.7		                     核心团队长个人服务费=个人积分*0.8		                     核心团队长个人服务费=个人积分*0.92")
    @TableField("MONEY1")
    private Integer money1;

    @ApiModelProperty(value = "团队管理费 仅团队长有。		                      普通团队长=团队内所有团员的个人服务费*0.1		                      核心团队长=（下辖团队+所属成员）的个人服务费*0.12")
    @TableField("MONEY2")
    private Integer money2;

    @ApiModelProperty(value = "应发 (个人服务费+团队管理费)*KPI系数(A100%,B80%,C60%,D40%)。		              上限：普通直销=1000		                         普通团队长=2000		                         核心团队长=3000")
    @TableField("MONEY3")
    private Integer money3;

    @ApiModelProperty(value = "个税 （应发-800）*0.2	个税 （应发-800）*0.2		")
    @TableField("MONEY4")
    private Integer money4;

    @ApiModelProperty(value = "实发 应发-个税")
    @TableField("MONEY5")
    private Integer money5;

    @ApiModelProperty(value = "银行名称")
    @TableField("USER_BANK_NAME")
    private String userBankName;

    @ApiModelProperty(value = "银行账号")
    @TableField("USER_BANK_NUMBER")
    private String userBankNumber;

    @ApiModelProperty(value = "纸质签章报告")
    @TableField("REPORT_PICTURE")
    private String reportPicture;

    @ApiModelProperty(value = "状态( 0:失效 1:审判中 2:通过 3:驳回)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "审批人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "审批时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "省级审批人")
    @TableField("PROVINCIAL_USER")
    private String provincialUser;

    @ApiModelProperty(value = "省级审批时间")
    @TableField("PROVINCIAL_DATE")
    private Date provincialDate;

    @ApiModelProperty(value = "其他积分")
    @TableField("BUSINESS_INTEGRATION3")
    private Integer businessIntegration3;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "团队编码")
    @TableField("TEAM_CODE")
    private String teamCode;

    @ApiModelProperty(value = "团队人数")
    @TableField("TEAM_COUNT")
    private String teamCount;

}
