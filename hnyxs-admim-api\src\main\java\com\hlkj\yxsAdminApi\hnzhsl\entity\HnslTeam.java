package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合伙人团队表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTeam对象", description = "合伙人团队表")
public class HnslTeam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "团队名称")
    @TableField("TEAM_NAME")
    private String teamName;

    @ApiModelProperty(value = "团队编码")
    @TableField("TEAM_CODE")
    private String teamCode;

    @ApiModelProperty(value = "团队等级(1:一级团队 2：二级团队 3:三级团队)")
    @TableField("TEAM_LEVEL")
    private Integer teamLevel;

    @ApiModelProperty(value = "是否 代办点 （默认 0:不是 1:是）")
    @TableField("AGENT_POINT")
    private Integer agentPoint;

    @ApiModelProperty(value = "团队类型(1:普通团队 2:核心团队 3:非普通非核心)")
    @TableField("TEAM_TYPE")
    private Integer teamType;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

}
