package com.hlkj.yxsAdminApi.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 系统配置属性
 *
 * <AUTHOR>
 * @since 2021-08-30 17:58:16
 */
@Data
@ConfigurationProperties(prefix = "config")
public class ConfigProperties {
    /**
     * 文件上传磁盘位置
     */
    private Integer uploadLocation = 0;

    /**
     * 文件上传是否使用uuid命名
     */
    private Boolean uploadUuidName = true;

    /**
     * 文件上传生成缩略图的大小(kb)
     */
    private Integer thumbnailSize = 60;

    /**
     * OpenOffice的安装目录
     */
    private String openOfficeHome;

    /**
     * swagger扫描包
     */
    private String swaggerBasePackage;

    /**
     * swagger文档标题
     */
    private String swaggerTitle;

    /**
     * swagger文档描述
     */
    private String swaggerDescription;

    /**
     * swagger文档版本号
     */
    private String swaggerVersion;

    /**
     * swagger地址
     */
    private String swaggerHost;

    /**
     * token过期时间, 单位秒
     * 60 * 60 * 24L 默认一天 调整为 30分钟
     */
    private Long tokenExpireTime = 60 * 60L;

    /**
     * token快要过期自动刷新时间, 单位分钟
     */
    private int tokenRefreshTime = 30;

    /**
     * 生成token的密钥Key的base64字符
     */
    private String tokenKey;

    /**
     * 限制发送key
     */
    private String one_minute_limit;

    /**
     * 限制发送时间(秒)
     */
    private Integer one_minute_limit_time;

    /**
     * 手机号码限制发送key
     */
    private String moblie_one_day_limit;

    /**
     * 最终的短信验证码编码
     */
    private String zuizhong_duanxin_nums;

    /**
     * 是否发送接着上面的短信信息（true:是，false：否）
     */
    private Boolean isduanxin;

    /**
     * 验证码的长度
     */
    private Integer duanxin_length;

    /**
     * 验证码保存时长(秒)
     */
    private Integer duanxin_time;

    /**
     * 短信验证码验证错误
     */
    private String duanxin_error_nums;

    /**
     * 短信验证码输入错误次数
     */
    private Integer duanxin_error_xianzhi_nums;

    /**
     * 区域限制次数
     */
    private Integer duanxin_xiangzhi_nums;

    /**
     * 一天限制次数
     */
    private Integer duanxin_xiangzhi_data_nums;

    /**
     * 短信发送地址
     */
    private String sms_pars_url;

    /**
     * ip限制发送key
     */
    private String ipArress_one_day_limit;

    /**
     * 放行的手机号码
     */
    private String userPhones;
    /**
     * 是否测试
     * @return
     */
    private String acctiond;

    /**
     * 掌上销图片上传地址
     */
    private String hnzsxFileImgPath;

    /**
     * 智慧扫楼文件上传地址
     */
    private String hnzhslFilePath;

    /**
     * 图片访问地址
     */
    private String hnzsxImgPath;
    /**
     * 省集约上传商品图片桶名称
     */
    private String hnzsx_admin_goods;

}
