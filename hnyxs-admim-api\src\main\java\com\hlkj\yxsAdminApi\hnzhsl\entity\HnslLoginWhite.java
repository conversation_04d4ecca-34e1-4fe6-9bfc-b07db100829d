package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子围栏管理
 * <AUTHOR>
 * @Since: 2024/11/18
 * @return: null
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslLoginWhite对象", description = "电子围栏管理")
public class HnslLoginWhite implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合伙人姓名")
    private String hhrName;

    @ApiModelProperty(value = "合伙人联系电话")
    private String hhrMobile;

    @ApiModelProperty(value = "所属地市编码")
    private String cityCode;

    @ApiModelProperty(value = "地市外登录")
    private Integer cityCodeOutLogin;

    @ApiModelProperty(value = "允许时效")
    private Date allowDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    private String xlsName;
}
