package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 白名单申请表
 * @Author: zwk
 * @Since: 2024/10/11
 * @return: null
 **/
@Data
@TableName("hnsl_white_approve_record")
public class HnslWhiteApprovalRecord implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//唯一标识
	private Long id;

	/** 审批ID */
	private Long approveId;

	/** 申请ID */
	private Long applyId;

	/** 批次号 */
	private String batchCode;

	/** 文件名 */
	private String fileName;

	/** 文件存储路径 */
	private String filePath;

	/** 分公司政策文件 */
	private String branchPolicyFiles;

	/** 分公司政策文件路径 */
	private String branchPolicyFilePath;

	/** 地市 */
	private String cityCode;

	/** 数据数量 */
	private Integer whitePhoneNumber;

	/** 状态(审批中，审批通过，审批驳回) */
	private Integer status;

	/** 提交人 */
	private String presenter;

	/** 审批人名称 */
	private String approveUser;

	/** 事件类型(1.导入白名单) 可扩展 */
	private Integer activeEvent;

	/** 提交人联系电话 */
	private String presenterMobile;

	/** 提交审批时间 */
	private Date submitApprovalTime;

	/** 备注 */
	private String remark;

	/** 驳回原因 */
	private String rejectReason;

	/** 创建时间 */
	private Date createTime;
}
