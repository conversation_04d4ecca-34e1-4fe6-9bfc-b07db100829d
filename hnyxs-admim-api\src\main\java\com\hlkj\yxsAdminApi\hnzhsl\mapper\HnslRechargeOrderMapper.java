package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRechargeOrder;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslRechargeOrderParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 充值订单表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslRechargeOrderMapper extends BaseMapper<HnslRechargeOrder> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslRechargeOrder>
     */
    List<HnslRechargeOrder> selectPageRel(@Param("page") IPage<HnslRechargeOrder> page,
                             @Param("param") HnslRechargeOrderParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslRechargeOrder> selectListRel(@Param("param") HnslRechargeOrderParam param);

}
