package com.hlkj.yxsAdminApi.hnzhsl.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 智慧扫楼白名单生成报表
 * @Author: zwk
 * @Since: 2024/11/06
 * @return: null
 **/
@Data
public class HnslWhiteReport implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 主键ID */
	private String id;

	/** 学校编码 */
	private String schoolCode;

	/** 熟卡号码数量 */
	private Integer totalPhones;

	/** 有效期内熟卡数量 */
	private Integer unActiveCount;

	/** 主键已激活熟卡数量ID */
	private Integer activeCount;

	/** 7天内到期的熟卡数量 */
	private Integer expiringCount;

	/** 学校名称 */
	private String schoolName;

	/** 地市 */
	private String cityCode;

	/** 创建时间 */
	private Date createTime;

	/** 客户群类型 */
	private Integer schoolChannel;

	/** 账期 */
	private Date accountPeriod;
}
