package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryReview;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSalaryReviewParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 薪资审核表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslSalaryReviewMapper extends BaseMapper<HnslSalaryReview> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSalaryReview>
     */
    List<HnslSalaryReview> selectPageRel(@Param("page") IPage<HnslSalaryReview> page,
                             @Param("param") HnslSalaryReviewParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSalaryReview> selectListRel(@Param("param") HnslSalaryReviewParam param);

    /**
     * 查询学子清单分页数据
     * @param map
     * @return
     */
    List<Map<String,Object>> queryReviewPageRel(@Param("page") IPage<Map<String,Object>> page,
                                                @Param("param") Map<String,Object> map);

    /**
     * 查询学子清单全部数据
     * @param map
     * @return
     */
    List<Map<String,Object>> queryReviewListRel(@Param("param") Map<String,Object> map);

    /**
     * 计算核心合伙人下所有团队和子团队管理费
     * @param map
     * @return
     */
    int calculateManagementFee(Map<String,Object> map);
}
