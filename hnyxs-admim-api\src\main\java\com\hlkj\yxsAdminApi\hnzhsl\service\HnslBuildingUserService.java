package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBuildingUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslBuildingUserParam;

import java.util.List;

/**
 * 楼栋管理人表Service
 *
 * <AUTHOR>
 * @since 2023-06-19 10:07:51
 */
public interface HnslBuildingUserService extends IService<HnslBuildingUser> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslBuildingUser>
     */
    PageResult<HnslBuildingUser> pageRel(HnslBuildingUserParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslBuildingUser>
     */
    List<HnslBuildingUser> listRel(HnslBuildingUserParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslBuildingUser
     */
    HnslBuildingUser getByIdRel(Integer id);

}
