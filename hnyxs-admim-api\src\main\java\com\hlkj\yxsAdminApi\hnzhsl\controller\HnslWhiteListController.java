package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslWhiteListService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteListParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.utils.AwsS3Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 熟卡白名单表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "熟卡白名单表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslWhiteList")
public class HnslWhiteListController extends BaseController {
    @Autowired
    private HnslWhiteListService hnslWhiteListService;
    @Autowired
    private QueryUserManagerUtil queryUserManagerUtil;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ConfigProperties config;
    @Autowired
    private AwsS3Utils awsS3Utils;

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:list')")
    @OperationLog
    @ApiOperation("分页查询熟卡白名单表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslWhiteList>> page(@RequestBody HnslWhiteListParam param) {
        PageParam<HnslWhiteList, HnslWhiteListParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslWhiteListService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslWhiteListService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:list')")
    @OperationLog
    @ApiOperation("查询全部熟卡白名单表")
    @PostMapping("/list")
    public ApiResult<List<HnslWhiteList>> list(@RequestBody HnslWhiteListParam param) {
        PageParam<HnslWhiteList, HnslWhiteListParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslWhiteListService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslWhiteListService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:list')")
    @OperationLog
    @ApiOperation("根据id查询熟卡白名单表")
    @GetMapping("/{id}")
    public ApiResult<HnslWhiteList> get(@PathVariable("id") Integer id) {
        return success(hnslWhiteListService.getById(id));
        // 使用关联查询
        //return success(hnslWhiteListService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:save')")
    @OperationLog
    @ApiOperation("添加熟卡白名单表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslWhiteList hnslWhiteList) {
        if (hnslWhiteListService.save(hnslWhiteList)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:update')")
    @OperationLog
    @ApiOperation("修改熟卡白名单表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslWhiteList hnslWhiteList) {
        if (hnslWhiteListService.updateById(hnslWhiteList)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:remove')")
    @OperationLog
    @ApiOperation("删除熟卡白名单表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslWhiteListService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:save')")
    @OperationLog
    @ApiOperation("批量添加熟卡白名单表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslWhiteList> list) {
        if (hnslWhiteListService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:update')")
    @OperationLog
    @ApiOperation("批量修改熟卡白名单表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslWhiteList> batchParam) {
        if (batchParam.update(hnslWhiteListService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:remove')")
    @OperationLog
    @ApiOperation("批量删除熟卡白名单表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslWhiteListService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:output')")
    @OperationLog
    @ApiOperation("导出白名单报表")
    @PostMapping("/outputWhitelTable")
    public ApiResult<?> outputWhitelTable(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        ServletContext servletContext = request.getSession().getServletContext();

        List<Map<String,Object>> whiteList =hnslWhiteListService.queryListTable(params);

        // 生成一条记录
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf1.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "白名单报表" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath + filePath);
        Integer activeEvent = 2;
        Integer appSettingId = (Integer) params.get("appSettingId");
        Integer list = whiteList.size();
        User user = getLoginUser();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, list, appSettingId);

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        for (int i = 0; i <= 12; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("批次号");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("校园渠道");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("学校编码");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("学校名称");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("未激活");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("激活成功");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("激活失败");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("已过期");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("已使用");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("白名单导入量");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("导入时间");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != whiteList && whiteList.size() != 0) {

            for (int i = 0; i < whiteList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(whiteList.get(i).get("BATCH_CODE")));

                row.createCell(1).setCellValue(InterfaceUtil.getCodeToName(String.valueOf(whiteList.get(i).get("CITY_CODE"))));

                if("1".equals(String.valueOf(whiteList.get(i).get("SCHOOL_CHANNEL")))){
                    row.createCell(2).setCellValue("高校");
                }else if("2".equals(String.valueOf(whiteList.get(i).get("SCHOOL_CHANNEL")))){
                    row.createCell(2).setCellValue("中小学");
                } else {
                    row.createCell(2).setCellValue("未知");
                }
                row.createCell(3).setCellValue(String.valueOf(whiteList.get(i).get("SCHOOL_CODE")));
                row.createCell(4).setCellValue(String.valueOf(whiteList.get(i).get("SCHOOL_NAME")));
                row.createCell(5).setCellValue(String.valueOf(whiteList.get(i).get("S1")));
                row.createCell(6).setCellValue(String.valueOf(whiteList.get(i).get("S3")));
                row.createCell(7).setCellValue(String.valueOf(whiteList.get(i).get("S4")));
                row.createCell(8).setCellValue(String.valueOf(whiteList.get(i).get("S2")));
                row.createCell(9).setCellValue(String.valueOf(whiteList.get(i).get("S5")));
                row.createCell(10).setCellValue(String.valueOf(whiteList.get(i).get("ZS")));
                row.createCell(11).setCellValue(String.valueOf(whiteList.get(i).get("CREATED_DATE")));
            }
            // 修改为存放到webapps/downloads目录下
            String webappsPath = System.getProperty("user.dir") + File.separator + "src" + File.separator + "main" + File.separator + "webapps" + File.separator + "downloads" + File.separator;
            // 确保目录存在
            File dir = new File(webappsPath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + webappsPath);
                } else {
                    logger.error("无法创建目录: " + webappsPath);
                    return fail("无法创建导出目录");
                }
            }
            
            logger.info("白名单报表file文件路径:" + webappsPath);
            
            // 上传到S3文件服务器
            String s3FilePath = fileName + "_" + System.currentTimeMillis() + ".xls";
            awsS3Utils.putFile(s3FilePath, wb);
            logger.info("文件已上传到S3文件服务器，路径：" + s3FilePath);
            
            // 本地文件保存
            File file = new File(webappsPath + fileName + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    return fail("写入文件失败: " + e.getMessage());
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
                return fail("创建文件失败: " + e.getMessage());
            }

            if (file.exists()) {//判断文件是否存在 download
                if (file.isFile()) { //判断是否是文件
                    Map<String, Object> map = new HashedMap<>();
                    map.put("code", "6");
                    map.put("fileName", fileName);
                    map.put("msg", "exportDaoUsers");
                    return success("导出成功",map);
                }
            }
        }
        return success("导出成功");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslWhiteList:importUser')")
    @OperationLog
    @ApiOperation("导入熟卡白名单")
    @PostMapping("/decode/importWhiteList")
    public ApiResult<?> importWhiteList(MultipartFile file, HttpServletRequest request) {
        JSONObject result = new JSONObject();
        User user = getLoginUser();
        String userName = redisUtil.getString("thisUserName" + user.getPhone());
        String cityCode = redisUtil.getString("cityCode" + user.getPhone());
        String statusSf = redisUtil.getString("thisUserSf" + user.getPhone());
        String image = "xls,xlsx";
        User loginUser = getLoginUser();
        if (!file.isEmpty()) {
            String uploadPath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确", 2);
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                logger.info("文件上传格式不正确");
                return fail("resultCode", 2);
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    logger.info("文件所在目录创建失败");
                    return fail("resultCode", 3);
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 1, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 1, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
                String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
                if (numberList != null && numberList.size() != 0) {
                    //将数据转化成user对象
                    List<HnslWhiteList> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslWhiteList listPojo = new HnslWhiteList();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //校园渠道-必填（1 高校/ 2 中小学）
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                if (Objects.equals("高校", StringUtil.trimString(userMap.get(name)))) {
                                    listPojo.setSchoolChannel(1);
                                } else if (Objects.equals("中小学", StringUtil.trimString(userMap.get(name)))) {
                                    listPojo.setSchoolChannel(2);
                                } else {
                                    listPojo.setSchoolChannel(0);
                                }
                            }
                            //地市编码-必填
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                listPojo.setCityCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //学校编码-必填
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                listPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //学校名称-必填
                            else if ("3".equalsIgnoreCase(name.trim())) {
                                listPojo.setSchoolName(StringUtil.trimString(userMap.get(name)));
                            }
                            //熟卡号码-必填
                            else if ("4".equalsIgnoreCase(name.trim())) {
                                listPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                            }
                            //使用者姓名-非必填
                            else if ("5".equalsIgnoreCase(name.trim())) {
                                listPojo.setCustomerName(StringUtil.trimString(userMap.get(name)));
                            }
                            //使用者-身份证-非必填
                            else if ("6".equalsIgnoreCase(name.trim())) {
                                String sfz = StringUtil.trimString(userMap.get(name));
                                if (StringUtil.isNotNull(sfz)) {
                                    sfz = sfz.toUpperCase();
                                }
                                listPojo.setCustomerCard(sfz);
                            } else if ("7".equalsIgnoreCase(name.trim())) {
                                listPojo.setRemarks(StringUtil.trimString(userMap.get(name)));
                            } else if ("8".equalsIgnoreCase(name.trim())) {
                                listPojo.setXlsName(StringUtil.trimString(userMap.get(name)));
                            }
                            // 默认失效时间为30天
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            calendar.add(Calendar.DATE, 30);
                            Date failureDate = calendar.getTime();
                            listPojo.setFailureDate(failureDate);
                        }
                        listPojo.setBatchCode(batchCode);
                        listPojo.setCreatedDate(new Date());
                        listPojo.setStatus(1);
                        listPojo.setNumberStatus(1);
                        listPojo.setCreatedUser(loginUser.getUsername());
                        hnslUserList.add(listPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslWhiteListService.saveUserArray(hnslUserList, cityCode, statusSf, request);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            HashMap<Object, Object> r = new HashMap<>();
                            r.put("mes", "exportDaoUser");
                            r.put("resultCode", "0");
                            r.put("fileName", saveUserArray.get("fileName"));
                            return success("导入成功", r);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        logger.error("批量插入用户信息失败:" + e);
                        return fail("resultCode", 5);
                    }
                } else {
                    logger.info("文件内容为空，或者解析失败");
                    result.put("resultCode", "4");
                    return fail("resultCode", 4);
                }
                HashMap<Object, Object> r = new HashMap<>();
                r.put("mes", "exportDaoUser");
                r.put("resultCode", 0);
                return success("导入成功", r);
            } catch (Exception e) {
                logger.error("文件上传接口上传异常:" + e);
                return fail("文件上传接口上传异常" + e);
            }
        } else {
            logger.info("上传文件为空");
            return fail("上传文件为空");
        }
    }

}
