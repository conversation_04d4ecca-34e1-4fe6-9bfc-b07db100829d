package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户数据开关表
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUserSwitch对象", description = "用户数据开关表")
public class HnslUserSwitch implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户手机号码")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "是否已签名反欺诈承诺书 （默认 0:未签名 1:已签名） 是否驳回 （默认 1:否 2：是）")
    @TableField("COMMITMENT_STATUS")
    private Integer commitmentStatus;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "反欺诈图片路径")
    @TableField("COMMITMENT_IMAGE")
    private String commitmentImage;

    @ApiModelProperty(value = "合伙人姓名")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty(value = "地市")
    @TableField(exist = false)
    private String cityCode;

    @ApiModelProperty(value = "学校名称")
    @TableField(exist = false)
    private String schoolName;
}
