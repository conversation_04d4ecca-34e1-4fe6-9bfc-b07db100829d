<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hlkj.yxsAdminApi.common.system.mapper.UserMapper">

    <!-- 性别字典查询sql -->
    <sql id="selectSexDictSql">
        SELECT ta.*
        FROM hnyxs_sys_dictionary_data ta
                 LEFT JOIN hnyxs_sys_dictionary tb
                           ON ta.dict_id = tb.dict_id
                               AND tb.deleted = 0
        WHERE ta.deleted = 0
          AND tb.dict_code = 'sex'
    </sql>

    <!-- 用户角色查询sql -->
    <sql id="selectUserRoleSql">
        SELECT a.user_id,
               GROUP_CONCAT(b.role_name) role_name
        FROM hnyxs_sys_user_role a
                 LEFT JOIN hnyxs_sys_role b ON a.role_id = b.role_id
        GROUP BY a.user_id
    </sql>

<!--     关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,
        b.organization_name,
        c.dict_data_name sex_name
        FROM hnyxs_sys_user a
        LEFT JOIN hnyxs_sys_organization b ON a.organization_id = b.organization_id
        LEFT JOIN (
        <include refid="selectSexDictSql"/>
        ) c ON a.sex = c.dict_data_code
        LEFT JOIN(
        <include refid="selectUserRoleSql"/>
        ) d ON a.user_id = d.user_id
        <where>
            <if test="param.userId != null">
                AND a.user_id = #{param.userId}
            </if>
            <if test="param.username != null and param.username != ''">
                AND a.username LIKE CONCAT('%', #{param.username}, '%')
            </if>
            <if test="param.nickname != null and param.nickname != ''">
                AND a.nickname LIKE CONCAT('%', #{param.nickname}, '%')
            </if>
            <if test="param.sex != null">
                AND a.sex = #{param.sex}
            </if>
            <if test="param.phone != null">
                AND a.phone LIKE CONCAT('%', #{param.phone}, '%')
            </if>
            <if test="param.email != null">
                AND a.email LIKE CONCAT('%', #{param.email}, '%')
            </if>
            <if test="param.emailVerified != null">
                AND a.email_verified = #{param.emailVerified}
            </if>
            <if test="param.realName != null">
                AND a.real_name LIKE CONCAT('%', #{param.realName}, '%')
            </if>
            <if test="param.idCard != null">
                AND a.id_card LIKE CONCAT('%', #{param.idCard}, '%')
            </if>
            <if test="param.birthday != null">
                AND a.birthday LIKE CONCAT('%', #{param.birthday}, '%')
            </if>
            <if test="param.organizationId != null">
                AND a.organization_id = #{param.organizationId}
            </if>
            <if test="param.status != null">
                AND a.`status` = #{param.status}
            </if>
            <if test="param.createTimeStart != null">
                AND a.create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null">
                AND a.create_time &lt;= #{param.createTimeEnd}
            </if>
            <if test="param.deleted != null">
                AND a.deleted = #{param.deleted}
            </if>
            <if test="param.deleted == null">
                AND a.deleted = 0
            </if>
            <if test="param.roleId != null">
                AND a.user_id IN (SELECT user_id FROM hnyxs_sys_user_role WHERE role_id=#{param.roleId})
            </if>
            <if test="param.organizationName != null">
                AND b.organization_name LIKE CONCAT('%', #{param.organizationName}, '%')
            </if>
            <if test="param.sexName != null">
                AND c.dict_data_name = #{param.sexName}
            </if>
            <if test="param.keywords != null">
                AND (
                a.username LIKE CONCAT('%', #{param.keywords}, '%')
                OR a.nickname LIKE CONCAT('%', #{param.keywords}, '%')
                OR b.organization_name LIKE CONCAT('%', #{param.keywords}, '%')
                OR c.dict_data_name LIKE CONCAT('%', #{param.keywords}, '%')
                OR d.role_name LIKE CONCAT('%', #{param.keywords}, '%')
                )
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.common.system.entity.User">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.common.system.entity.User">
        <include refid="selectSql"></include>
    </select>

    <!-- 根据账号查询 -->
    <select id="selectByUsername" resultType="com.hlkj.yxsAdminApi.common.system.entity.User">
        SELECT a.* ,
        b.organization_name,
        c.dict_data_name sex_name
        FROM hnyxs_sys_user a
        LEFT JOIN hnyxs_sys_organization b ON a.organization_id = b.organization_id
        LEFT JOIN (
        <include refid="selectSexDictSql"/>
        ) c ON a.sex = c.dict_data_code
        <where>
            AND a.deleted = 0
            AND a.username = #{username}
            <if test="tenantId != null">
                AND a.tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="queryList" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser">
        SELECT u.*,ss.SCHOOL_NAME FROM hnsl_user u
        <choose>
            <when test="schoolList !=null and schoolList.size() >0">
                right join
            </when>
            <otherwise>
                left join
            </otherwise>
        </choose>
        (select us.USER_PHONE,group_concat(s.SCHOOL_NAME) as SCHOOL_NAME from hnsl_user_school us
        left join hnsl_school s on us.SCHOOL_CODE=s.SCHOOL_CODE where us.`STATUS`=1
        <if test="schoolList !=null and schoolList.size() >0">
            AND us.school_code in
            <foreach collection="schoolList" item="schoolCode" open="(" separator="," close=")">
                #{schoolCode}
            </foreach>
        </if>
        GROUP BY us.USER_PHONE) ss
        on u.USER_PHONE=ss.USER_PHONE
        <where>
            <if test="userName !=null and userName !=''">
                and u.USER_NAME like concat(concat('%',#{userName}),'%')
            </if>
            <if test="userManager!=null and userManager!=''">
                and (u.USER_MANGER is null or u.USER_MANGER=#{userManager})
            </if>
            <if test="condition !=null and condition !=''">
                and (u.USER_NAME like concat(concat('%',#{condition}),'%') or
                u.USER_SFZ like concat(concat('%',#{condition}),'%') or
                u.USER_PHONE like concat(concat('%',#{condition}),'%'))
            </if>
            <if test="statusSf !=null and statusSf!=''">
                and u.STATUS_SF = #{statusSf}
            </if>
            <if test="level==1">
                and u.STATUS_SF not in (1,5,6)
            </if>
            <if test="level==5">
                and u.STATUS_SF not in (5)
            </if>
            <if test="level==6">
                and u.STATUS_SF not in (5,6)
            </if>
            <if test="status !=null and status !=''">
                and u.STATUS = #{status}
            </if>
            <if test="cityCode !=null and cityCode !=''">
                and u.CITY_CODE =#{cityCode}
            </if>
            <if test="numbers !=null and numbers!=''">
                and u.NUMBERS =#{numbers}
            </if>
            <if test="userSfz !=null and userSfz!=''">
                and u.USER_SFZ =#{userSfz}
            </if>
            <if test="userPhone !=null and userPhone!=''">
                and u.USER_PHONE =#{userPhone}
            </if>
            <if test="statusSuperior !=null and statusSuperior!=''">
                and u.status_superior =#{statusSuperior}
            </if>
            <if test="hnslChannel !=null and hnslChannel!=''  and hnslChannel!=1 ">
                and u.HNSL_CHANNEL =#{hnslChannel}
            </if>
            <if test="beginTime != null and beginTime.trim() != ''">
                and u.CREATED_DATE between
                str_to_date(#{beginTime},'%Y-%m-%d %T')and
                str_to_date(#{endTime},'%Y-%m-%d %T')
            </if>
            <if test="schoolName !=null and schoolName!=''">
                and ss.school_name like concat(concat('%',#{schoolName}),'%')
            </if>
        </where>
        <choose>
            <when test="sidx != null and sidx.trim() != ''">
                order by ${sidx} ${order}
            </when>
            <otherwise>
                order by u.ID desc
            </otherwise>
        </choose>
        <choose>
            <when test="page != null and limit != null">
                LIMIT #{page},#{limit}
            </when>
            <otherwise>

            </otherwise>
        </choose>
    </select>
    <select id="getPortalPersonnelInfo" resultType="java.util.Map">
        select * from portal_personnel_info p
        where cert_num = #{userSfz}
    </select>
</mapper>
