package com.hlkj.yxsAdminApi.hnzhslH5.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.*;
import com.hlkj.yxsAdminApi.hnzhslH5.service.*;
import com.hlkj.yxsAdminApi.hnzhslH5.param.HnslUserH5Param;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户管理表控制器
 *
 * @Author: zwk
 * @Since: 2025/3/15
 */
@Api(tags = "用户管理表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslUserH5")
public class HnslUserH5Controller extends BaseController {
    @Autowired
    private HnslUserH5Service hnslUserService;
    @Autowired
    private HnslUserSchoolH5Service hnslUserSchoolService;
    @Autowired
    private HnslSchoolH5Service hnslSchoolService;
    @Autowired
    private HnslUserUpdateRecordService hnslUserUpdateRecordService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserService userService;
    @Autowired
    private ConfigProperties config;
    public static final String ADMIN = "13348615280";// admin绑定账户

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:list')")
    @OperationLog
    @ApiOperation("分页查询用户管理表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslH5User>> page(@RequestBody HnslUserH5Param param) {
        User user = getLoginUser();
        if (redisUtil.limitRateCount(Constants.limitUser, user)) {
            // 熔断账号冻结
            user.setStatus(1);
            userService.updateById(user);
            // 推送熔断日志用户信息
            InterfaceUtil.breakerLog(user, "用户信息查询");
            return new ApiResult<>(Constants.RESULT_ERROR_CODE, "当前查询次数已被限制");
        }
        String userPhone = redisUtil.getString("thisH5UserPhone" + user.getPhone());
        String userName = redisUtil.getString("thisH5UserName" + user.getPhone());
        String userSf = redisUtil.getString("thisH5UserSf" + user.getPhone());
        String cityCode = redisUtil.getString("cityCodeH5" + user.getPhone());
        String hnslType = redisUtil.getString("hnslH5Channel" + user.getPhone());
        param.setHnslChannel(hnslType);
        if ("1".equals(userSf)) {
            HnslH5UserSchool hnslH5UserSchool = new HnslH5UserSchool();
            hnslH5UserSchool.setUserPhone(userPhone);
            List<HnslH5UserSchool> hnslH5UserSchools = hnslUserSchoolService.queryObjectBy(hnslH5UserSchool);
            List<String> collect = hnslH5UserSchools.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());
            param.setSchoolList(collect);
            param.setLevel("1");
        } else if ("5".equals(userSf)) {//省级管理员
            if (!"admin".equals(userName)) { // admin 账号
                param.setLevel("5");
            }
        } else if ("6".equals(userSf)) {//地市管理员
            param.setLevel("6");
            param.setCityCode(cityCode);
        }
        return success(hnslUserService.pageRel(param, userName, user.getPhone()));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:list')")
    @OperationLog
    @ApiOperation("查询全部用户管理表")
    @PostMapping("/list")
    public ApiResult<List<HnslH5User>> list(@RequestBody HnslUserH5Param param) {
        PageParam<HnslH5User, HnslUserH5Param> page = new PageParam<>(param);
        return success(hnslUserService.list(page.getOrderWrapper()));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:list')")
    @OperationLog
    @ApiOperation("根据id查询用户管理表")
    @GetMapping("/getById/{id}")
    public ApiResult<HnslH5User> getById(@PathVariable("id") Integer id) {
        HnslH5User hnslUser = hnslUserService.getById(id);
        return success(hnslUser);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:list')")
    @OperationLog
    @ApiOperation("通过手机号查询学校")
    @PostMapping("/querySchool")
    public ApiResult<?> querySchool(@RequestBody HnslUserH5Param param) {
        HnslH5User h5User = hnslUserService.getById(param.getId());
        HnslH5UserSchool hnslH5UserSchool = new HnslH5UserSchool();
        hnslH5UserSchool.setUserPhone(h5User.getUserPhone());
        List<HnslH5UserSchool> hnslH5UserSchools = hnslUserSchoolService.queryObjectBy(hnslH5UserSchool);
        List<String> collect = hnslH5UserSchools.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());

        LambdaQueryWrapper<HnslH5School> queryWrapper = new LambdaQueryWrapper<>();
        if (param.getSchoolName() != null && !param.getSchoolName().isEmpty()) {
            queryWrapper.like(HnslH5School::getSchoolName, param.getSchoolName());
        }
        queryWrapper.in(HnslH5School::getSchoolCode, collect);
        List<HnslH5School> list = hnslSchoolService.list(queryWrapper);
        return success(list);
    }

    /**
     * 信息
     */
    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:list')")
    @OperationLog
    @ApiOperation("信息")
    @GetMapping("/info/{id}")
    public ApiResult<?> info(@PathVariable("id") Integer id) {
        logger.info("获取用户信息入参" + id);
        try {
            User user = getLoginUser();
            String UserPhone = redisUtil.getString("thisH5UserPhone" + user.getPhone());
            String userName = redisUtil.getString("thisH5UserName" + user.getPhone());
            boolean adminbool = true;
            if (ADMIN.equals(UserPhone)) {
                adminbool = false;
            }
            HnslH5User hnslUser = hnslUserService.getById(id);
            /**
             * 获取渠道信息
             */
            List<HnslH5School> schoolstr = hnslSchoolService.querySchooleByUserPhoneOrSchoolCode(hnslUser.getUserPhone(), null);
            String schoolName = "";
            String schoolCode = "";
            for (HnslH5School hnslSchoolEntity : schoolstr) {
                if (schoolName == "") {
                    schoolName = schoolName + hnslSchoolEntity.getSchoolName();
                    schoolCode = hnslSchoolEntity.getSchoolCode();
                } else {
                    schoolName = schoolName + "," + hnslSchoolEntity.getSchoolName();
                    schoolCode = schoolCode + "," + hnslSchoolEntity.getSchoolCode();
                }
            }
            hnslUser.setSchoolName(schoolName);
            hnslUser.setSchoolCode(schoolCode);
            if (!"admin".equals(userName)) {
                hnslUser.setUserSfz(DesensitizationUtil.idCardDesensitization(hnslUser.getUserSfz()));
                hnslUser.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhone()));
                hnslUser.setUserName(DesensitizationUtil.desensitizeName((hnslUser.getUserName())));
                if (null != hnslUser.getStatusSuperior()) {
                    hnslUser.setStatusSuperior(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getStatusSuperior()));
                }
            }
            return success(hnslUser);
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            return fail("获得用户失败！");
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:save')")
    @OperationLog
    @ApiOperation("添加用户管理表")
    @PostMapping("/decode/save")
    public ApiResult<?> save(@RequestBody HnslH5User hnslUser) {
        User loginUser = getLoginUser();
        String loginUserPhone = redisUtil.getString("thisH5UserPhone" + loginUser.getPhone());
        String userPhone = hnslUser.getUserPhone();
        if ("5".equals(hnslUser.getStatusSf()) && !ADMIN.equals(loginUserPhone)) {
            return fail("无权修改省级管理员角色");
        }
        if (StringUtil.isEmpty(userPhone) || userPhone.length() != 11) {
            return fail("手机号不能为空且必须11位");
        }
        if (StringUtil.isEmpty(hnslUser.getUserSfz()) || hnslUser.getUserSfz().length() != 18) {
            return fail("身份证不能为空且必须为18位");
        }

        LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslH5User::getUserPhone, hnslUser.getUserPhone());
        int count = hnslUserService.count(queryWrapper);
        if (count > 0) {
            return fail("该用户已存在,无法新建");
        }

        LambdaQueryWrapper<HnslH5User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(HnslH5User::getUserSfz, hnslUser.getUserSfz());
        int resultSFZCount = hnslUserService.count(userLambdaQueryWrapper);
        if (resultSFZCount > 0) {
            return fail("该身份证号已录入系统，无法再新建");
        }

        //上级合伙人校验
        HnslH5User hnslH5User = null;
        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {
            if (hnslUser.getStatusSuperior().length() != 11) {
                return fail("上级合伙人手机号必须位11位");
            }

            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HnslH5User::getUserPhone, hnslUser.getStatusSuperior());
            List<HnslH5User> userSj = hnslUserService.list(queryWrapper);

            if (userSj != null && userSj.size() > 0) {
                hnslH5User = userSj.get(0);

                if (hnslH5User.getStatusSf() == 1) {
                    return fail("渠道经理不能成为上级合伙人");
                } else if (hnslH5User.getStatusSf() == 2 || hnslH5User.getStatusSf() == 3) {
                    // 上级身份 （1.渠道经理 2.一级合伙人 3.二级合伙人 4.三级合伙人 5省级管理员 6 地市管理员")
                    Integer sjLevel = hnslH5User.getStatusSf();
                    // 当前用户身份
                    Integer curLevel = hnslUser.getStatusSf();

                    if (sjLevel < curLevel) {
                        return fail("上级合伙人等级不能小于当前合伙人");
                    } else {
                        // 判断上级合伙人与下级合伙人渠道是否一致
                        LambdaQueryWrapper<HnslH5UserSchool> userSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        if (StringUtil.isNotNull(hnslUser.getSchoolCode())) {
                            String[] schoolCode = hnslUser.getSchoolCode().split(",");
                            userSchoolLambdaQueryWrapper.eq(HnslH5UserSchool::getUserPhone, hnslH5User.getUserPhone())
                                    .in(HnslH5UserSchool::getSchoolCode, schoolCode);
                            int count1 = hnslUserSchoolService.count(userSchoolLambdaQueryWrapper);
                            if (count1 == 0) {
                                return fail("上级与下级渠道不一致");
                            }
                        }
                    }
                } else if (hnslH5User.getStatusSf() == 5 || hnslH5User.getStatus() == 6) {
                    return fail("省级或地市管理员不能成为上级合伙人");
                }
            } else {
                return fail("未查询到到该上级用户信息");
            }
        }

        if ("700".equals(hnslUser.getCityCode())) {
            hnslUser.setSelCityCode("731");
        }
        SimpleDateFormat sdfCode = new SimpleDateFormat("yyyyMMddHHmmss");
        String userCode = "SLID" + sdfCode.format(new Date()) + String.valueOf((int) ((Math.random() * 6 + 1) * 100000));
        hnslUser.setUserCode(userCode);
        hnslUser.setCreatedDate(new Date());
        String[] schoolCode = hnslUser.getSchoolCode().split(",");
        hnslUser.setSchoolCode(schoolCode[0]);
        boolean save = hnslUserService.save(hnslUser);
        if (!save) {
            return fail("添加失败");
        }

        // 用户渠道关联数据
        HnslH5UserSchool hnslUserSchool = new HnslH5UserSchool();
        hnslUserSchool.setUserPhone(hnslUser.getUserPhone());
        for (int i = 0; i < schoolCode.length; i++) {
            hnslUserSchool.setSchoolCode(schoolCode[i]);
            logger.info("用户渠道关联对象" + hnslUserSchool);
            hnslUserSchoolService.save(hnslUserSchool);
        }
        return success("添加成功");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:update')")
    @OperationLog
    @ApiOperation("修改用户管理表")
    @PostMapping("/decode/update")
    public ApiResult<?> update(@RequestBody HnslH5User hnslUser) {
        logger.info("修改用户入参:" + hnslUser.toString());

        User user = getLoginUser();
        HnslH5User hnslUserInit = hnslUserService.getById(hnslUser.getId());
        String userName = redisUtil.getString("thisH5UserName" + user.getPhone());
        String userPhone = redisUtil.getString("thisH5UserPhone" + user.getPhone());
        String userSf = redisUtil.getString("thisH5UserSf" + user.getPhone());
        String hnslType = redisUtil.getString("hnslH5Channel" + user.getPhone());

        if ("5".equals(hnslUser.getStatusSf()) && !ADMIN.equals(userPhone)) {
            return fail("无权修改省级管理员角色");
        }

        // 身份证校验
        if (StringUtil.isEmpty(hnslUser.getUserSfz()) || hnslUser.getUserSfz().length() != 18) {
            return fail("身份证不能为空且必须为18位");
        } else if (hnslUser.getUserSfz().contains("********")) {
            hnslUser.setUserSfz(null);
        } else {
            if (!hnslUser.getUserSfz().equals(hnslUserInit.getUserSfz()) && hnslUser.getHnslChannel() == 3) {
                LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(HnslH5User::getUserSfz, hnslUser.getUserSfz())
                        .eq(HnslH5User::getHnslChannel, 3);
                int count = hnslUserService.count(queryWrapper);
                if (count > 0) {
                    return fail("【" + DesensitizationUtil.idCardDesensitization(hnslUser.getUserSfz()) + "】用户已存在系统，不允许修改");
                }
            }
        }

        // 用户手机号校验
        if (StringUtil.isEmpty(hnslUser.getUserPhone()) || hnslUser.getUserPhone().length() != 11) {
            return fail("手机号不能为空且必须为11位");
        } else if (hnslUser.getUserPhone().contains("****")) {
            hnslUser.setUserPhone(null);
        } else if (!hnslUser.getUserPhone().equals(hnslUserInit.getUserPhone())) {
            // 判别是否已经存在用户
            LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HnslH5User::getUserPhone, hnslUser.getUserPhone());
            int resultCount = hnslUserService.count(queryWrapper);
            if (resultCount > 0) {
                return fail("该用户已存在,修改失败");
            }
        } else {
            if (!hnslUser.getUserPhone().equals(hnslUser.getUserPhoneTwo())) {
                LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(HnslH5User::getUserPhone, hnslUser.getUserPhone());
                List<HnslH5User> queryList = hnslUserService.list(queryWrapper);
                if (null != queryList && queryList.size() > 0) {
                    return fail("修改失败,合伙人账户已存在");
                }
                Map<String, Object> map = new HashMap<>();
                // 用户已登陆 调用存储过程全修改
                map.put("newPhone", hnslUser.getUserPhone());
                map.put("userPhone", hnslUser.getUserPhoneTwo());
                map.put("v_user_name", userName);
                hnslUserService.updateBYPhone(map);
            }
        }

        // 上级手机号校验
        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {
            if (hnslUser.getStatusSuperior().length() != 11) {
                return fail("上级手机号必须为11位");
            } else if (hnslUser.getStatusSuperior().contains("****")) {
                hnslUser.setStatusSuperior(null);
            } else if (hnslUserInit.getUserPhone().equals(hnslUser.getStatusSuperior())) {
                return fail("上级手机号不能与下级手机号一致");
            } else {
                HnslH5User hnslH5User = null;
                // 查询用户上级信息
                LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(HnslH5User::getUserPhone, hnslUser.getStatusSuperior());
                List<HnslH5User> userSj = hnslUserService.list(queryWrapper);

                if (!userSj.isEmpty() && userSj.size() > 0) {
                    hnslH5User = userSj.get(0);

                    if (hnslH5User.getStatusSf() == 1) {
                        return fail("渠道经理不能成为上级合伙人");
                    } else if (hnslH5User.getStatusSf() == 2 || hnslH5User.getStatusSf() == 3) {
                        // 上级身份 （1.渠道经理 2.一级合伙人 3.二级合伙人 4.三级合伙人 5省级管理员 6 地市管理员")
                        Integer sjLevel = hnslH5User.getStatusSf();
                        // 当前用户身份
                        Integer curLevel = hnslUser.getStatusSf();

                        if (sjLevel < curLevel) {
                            return fail("上级合伙人等级不能小于当前合伙人");
                        } else {
                            // 判断上级合伙人与下级合伙人渠道是否一致
                            LambdaQueryWrapper<HnslH5UserSchool> userSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            if (StringUtil.isNotNull(hnslUser.getSchoolCode())) {
                                String[] schoolCode = hnslUser.getSchoolCode().split(",");
                                userSchoolLambdaQueryWrapper.eq(HnslH5UserSchool::getUserPhone, hnslH5User.getUserPhone())
                                        .in(HnslH5UserSchool::getSchoolCode, schoolCode);
                                int count = hnslUserSchoolService.count(userSchoolLambdaQueryWrapper);
                                if (count == 0) {
                                    return fail("上级与下级渠道不一致");
                                }
                            } else {
                                Map<String, Object> towMap = new HashMap<>();
                                towMap.put("phone1", hnslH5User.getUserPhone());
                                towMap.put("phone2", hnslUserInit.getUserPhone());
                                int twoIndex = hnslUserService.querySchoolByUserPhoneTwo(towMap);
                                if (twoIndex == 0) {
                                    return fail("上级与下级渠道不一致");
                                }
                            }
                        }
                    } else if (hnslH5User.getStatusSf() == 5 || hnslH5User.getStatus() == 6) {
                        return fail("省级或地市管理员不能成为上级合伙人");
                    }
                } else {
                    return fail("未查询到上级用户信息");
                }
            }
        } else {
            hnslUser.setStatusSuperior("");
        }

        // 用户姓名校验
        if (hnslUser.getUserName().contains("*") || hnslUser.getUserName().contains("**") || hnslUser.getUserName().contains("***")) {
            hnslUser.setUserName(null);
        }

        //判断合伙人等级是否修改
        if (!hnslUser.getStatusSf().equals(hnslUserInit.getStatusSf())) {
            //删除 大于等于 合伙人当前等级的合伙人上级记录
            if (Objects.equals("3", hnslUser.getStatusSf())
                    || Objects.equals("4", hnslUser.getStatusSf())
                    || Objects.equals("2", hnslUser.getStatusSf())) {
                HashMap<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("phone", hnslUser.getUserPhone());
                if (Objects.equals("3", hnslUser.getStatusSf())) {
                    objectHashMap.put("array", new Integer[]{1, 2, 3});
                } else if (Objects.equals("4", hnslUser.getStatusSf())) {
                    objectHashMap.put("array", new Integer[]{1, 2, 3, 4});
                } else if (Objects.equals("2", hnslUser.getStatusSf())) {
                    objectHashMap.put("array", new Integer[]{1, 2});
                }
                hnslUserService.updateBySuperior(objectHashMap);
            }
        }
        // 修改用户信息
        if ("700".equals(hnslUser.getCityCode())) {
            hnslUser.setSelCityCode("731");
        }
        hnslUser.setUpdatedUser(userName);
        hnslUser.setUpdatedDate(new Date());
        hnslUserService.updateByUser(hnslUser);

        HnslH5UserUpdateRecord userUpdateRecord = new HnslH5UserUpdateRecord();
        String updateName = "管理员:{" + userName + "}修改:";
        // 修改关联渠道 SchoolCode不为空则修改 否则修改失败
        if (!StringUtil.isEmpty(hnslUser.getSchoolCode())) {
            if (null != hnslUser.getSchoolNameUpdate()) {
                if (!hnslUser.getSchoolNameUpdate().equals(hnslUser.getSchoolName())) {
                    updateName += "原所属渠道{" + hnslUser.getSchoolNameUpdate() + "}修改为{" + hnslUser.getSchoolName() + "];";
                    // 删除关联渠道
                    LambdaQueryWrapper<HnslH5UserSchool> memberQueryWrapper = new LambdaQueryWrapper<>();
                    memberQueryWrapper.eq(HnslH5UserSchool::getUserPhone, hnslUserInit.getUserPhone());
                    hnslUserSchoolService.remove(memberQueryWrapper);
                    // 新增关联渠道
                    HnslH5UserSchool userSchool = new HnslH5UserSchool();
                    userSchool.setUserPhone(hnslUserInit.getUserPhone());
                    String[] schoolCode = hnslUser.getSchoolCode().split(",");
                    for (int i = 0; i < schoolCode.length; i++) {
                        userSchool.setSchoolCode(schoolCode[i]);
                        hnslUserSchoolService.save(userSchool);
                    }
                }
            }
        } else {
            return fail("修改失败,所属渠道为空");
        }
        // 修改日志记录
        extracted(hnslUser, hnslUserInit, userPhone, userUpdateRecord, updateName);
        return success("修改成功");
    }

    private void extracted(HnslH5User hnslUser, HnslH5User hnslUserInit, String userPhone, HnslH5UserUpdateRecord userUpdateRecord, String updateName) {
        if (!StringUtil.isEmpty(hnslUser.getUserName())) {
            if (!hnslUser.getUserName().equals(hnslUserInit.getUserName())) {
                updateName += "原姓名{" + hnslUserInit.getUserName() + "}修改为{" + hnslUser.getUserName() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getUserSite())) {
            if (!hnslUser.getUserSite().equals(hnslUserInit.getUserSite())) {
                updateName += "原地址{" + hnslUserInit.getUserSite() + "}修改为{" + hnslUser.getUserSite() + "];";
            }
        }
        if (null != hnslUser.getStatusSf()) {
            if (!hnslUser.getStatusSf().equals(hnslUserInit.getStatusSf())) {
                updateName += "原身份级别{" + hnslUserInit.getStatusSf() + "}修改为{" + hnslUser.getStatusSf() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getUserSfz())) {
            if (!hnslUser.getUserSfz().equals(hnslUserInit.getUserSfz())) {
                updateName += "原身份证{" + hnslUserInit.getUserSfz() + "}修改为{" + hnslUser.getUserSfz() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getUserPhone())) {
            if (!hnslUser.getUserPhone().equals(hnslUserInit.getUserPhone())) {
                updateName += "原手机号码{" + hnslUserInit.getUserPhone() + "}修改为{" + hnslUser.getUserPhone() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getCityCode())) {
            if (!hnslUser.getCityCode().equals(hnslUserInit.getCityCode())) {
                updateName += "原地市{" + hnslUserInit.getCityCode() + "}修改为{" + hnslUser.getCityCode() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {
            if (!hnslUser.getStatusSuperior().equals(hnslUserInit.getStatusSuperior())) {
                updateName += "原上级号码{" + hnslUserInit.getStatusSuperior() + "}修改为{" + hnslUser.getStatusSuperior() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {
            if (!hnslUser.getStatusSuperior().equals(hnslUserInit.getStatusSuperior())) {
                updateName += "原上级号码{" + hnslUserInit.getStatusSuperior() + "}修改为{" + hnslUser.getStatusSuperior() + "];";
            }
        }
        if (!("0").equals(hnslUser.getHnslChannel())) {
            if (hnslUser.getHnslChannel() != hnslUserInit.getHnslChannel()) {
                updateName += "原扫楼渠道{" + hnslUserInit.getHnslChannel() + "}修改为{" + hnslUser.getHnslChannel() + "];";
            }
        }
        if (!("0").equals(hnslUser.getChannelType())) {
            if (!hnslUser.getChannelType().equals(hnslUserInit.getChannelType())) {
                updateName += "原来源渠道{" + hnslUserInit.getChannelType() + "}修改为{" + hnslUser.getChannelType() + "];";
            }
        }
        if (!StringUtil.isEmpty(hnslUser.getSalesCode())) {
            if (!hnslUser.getSalesCode().equals(hnslUserInit.getSalesCode())) {
                updateName += "原销售员编码{" + hnslUserInit.getSalesCode() + "}修改为{" + hnslUser.getSalesCode() + "];";
            }
        }
        userUpdateRecord.setStatus(1L);
        userUpdateRecord.setUpdatedDate(new Date());
        userUpdateRecord.setUserupdateDetails(updateName);
        userUpdateRecord.setUserupdatePhone(hnslUser.getUserPhone());
        userUpdateRecord.setUpdatedUser(userPhone);
        hnslUserUpdateRecordService.save(userUpdateRecord);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:remove')")
    @OperationLog
    @ApiOperation("删除用户管理表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:save')")
    @OperationLog
    @ApiOperation("批量添加用户管理表")
    @PostMapping("/decode/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslH5User> list) {
        if (hnslUserService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @OperationLog
    @ApiOperation("批量修改用户管理表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslH5User> batchParam) {
        if (batchParam.update(hnslUserService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @OperationLog
    @ApiOperation("批量删除用户管理表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        for (Integer id : ids) {
            HnslH5User hnslUserEntity = hnslUserService.getById(id);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("userPhone", hnslUserEntity.getUserPhone());
            hnslUserService.deleteBYPhone(hashMap);
        }
        if (hnslUserService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:importUser')")
    @OperationLog
    @ApiOperation("导入用户")
    @PostMapping("/decode/importUser")
    public ApiResult<?> importUser(MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        String image = "xls,xlsx";
        User loginUser = getLoginUser();
        String hnslType = redisUtil.getString("hnslH5Channel" + loginUser.getPhone());
        if (!file.isEmpty()) {
            String uploadPath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXls(uploadPath, newname, 6, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 6, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);

                if (numberList != null) {
                    //将数据转化成user对象
                    List<HnslH5User> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslH5User userPojo = new HnslH5User();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            //姓名
                            if ("0".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户姓名不能为空");
                                }
                                userPojo.setUserName(StringUtil.trimString(userMap.get(name)));
                            }
                            //联系电话
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户联系电话不能为空");
                                }
                                userPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                            }
                            //地址
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                userPojo.setUserSite(StringUtil.trimString(userMap.get(name)));
                            }
                            //销售员编码
                            else if ("3".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户销售员编码不能为空");
                                }
                                userPojo.setSalesCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //身份
                            else if ("4".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户身份不能为空");
                                }
                                userPojo.setStatusSf(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
                            }
                            //地市ID
                            else if ("5".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户地市ID不能为空");
                                }
                                userPojo.setCityCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //上级ID
                            else if ("6".equalsIgnoreCase(name.trim())) {
                                userPojo.setStatusSuperior(StringUtil.trimString(userMap.get(name)));
                            }
                            //渠道名称
                            else if ("7".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户渠道名称不能为空");
                                }
                                userPojo.setSchoolName(StringUtil.trimString(userMap.get(name)));
                            }
                            //渠道编码
                            else if ("8".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户渠道编码不能为空");
                                }
                                userPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
                            }//合伙人身份证
                            else if ("9".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户身份证不能为空");
                                }
                                userPojo.setUserSfz(StringUtil.trimString(userMap.get(name)));
                            } else //来源渠道
                                if ("10".equalsIgnoreCase(name.trim())) {
                                    String channelType = StringUtil.trimString(userMap.get(name));
                                    if (StringUtil.isEmpty(channelType)) {
                                        return fail("导入渠道类型不能为空");
                                    }
                                    userPojo.setChannelType(Integer.parseInt(channelType));
                                }
                        }
                        if (userPojo.getUserPhone().equals(userPojo.getStatusSuperior())) {
                            return fail("上级账号不能相同，无上级可不填");
                        }
                        if (StringUtil.isEmpty(userPojo.getUserSfz())) {
                            return fail("导入用户身份证不能为空");
                        }
                        if (StringUtil.isEmpty(String.valueOf(userPojo.getChannelType())) || userPojo.getChannelType() == 0) {
                            return fail("导入渠道类型不能为空");
                        }
                        hnslUserList.add(userPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslUserService.saveUserArray(hnslUserList, request, hnslType);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            HashMap<String, String> r = new HashMap<>();
                            r.put("mes", "exportDaoUsers");
                            r.put("resultCode", "6");
                            r.put("fileName", saveUserArray.get("fileName"));
                            return success(r);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        logger.error("批量插入用户信息失败:" + e);
                        return fail("批量插入用户信息失败:" + e);
                    }
                } else {
                    return fail("文件内容为空，或者解析失败");
                }
                return fail("文件内容为空，或者解析失败");
            } catch (Exception e) {
                return fail("文件上传接口上传异常:" + e);
            }
        } else {
            return fail("上传文件为空");
        }
    }

    @OperationLog
    @ApiOperation("禁用或允许用户")
    @PostMapping("/decode/disableUser")
    public ApiResult<?> outputUserJiFei(@RequestBody HnslH5User hnslUser) {
        hnslUserService.updateById(hnslUser);
        return success("修改成功");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5User:exportUser')")
    @OperationLog
    @ApiOperation("用户详情导出")
    @PostMapping("/exportUser")
    public ApiResult<?> exportUserIntegrate(@RequestBody JSONObject conditionStr, HttpServletRequest request, HttpServletResponse response) {
        logger.info("导出查询积分条件: " + conditionStr.toString());
        Map<String, Object> conditionMap = (Map<String, Object>) JSON.parse(conditionStr.toString());

        User user = getLoginUser();
        String userPhone = redisUtil.getString("thisH5UserPhone" + user.getPhone());
        String userSf = redisUtil.getString("thisH5UserSf" + user.getPhone());
        String cityCode = redisUtil.getString("cityCodeH5" + user.getPhone());
        String hnslType = redisUtil.getString("hnslH5Channel" + user.getPhone());
        String userName = redisUtil.getString("thisH5UserName" + user.getPhone());
        conditionMap.put("loginUserSf", userSf); // 查询语句判断各个身份导出数据权限
        conditionMap.put("hnslChannel", hnslType);
        // 如果是地市管理员 则能够导出所属地市的全部人员
        if ("6".equals(userSf)) {

            if (!StringUtil.isEmpty(cityCode)) {
                conditionMap.put("cityCode", cityCode);
            }
        }
        // 如果是渠道经理 则能够导出其管理下的校园所有人员
        if ("1".equals(userSf)) {
            // 根据手机号查询管理校园编码
            List<String> list = hnslUserService.querySchoolCodeByPhone(userPhone);
            if (null != list) {
                conditionMap.put("schoolCode", list);
            }
//             conditionMap.put("hnslChannel", hnslType);
        }
        try {
            //根据条件查询出数据
            conditionMap.put("page", null);
            List<Map<String, String>> resultList = hnslUserService.queryExportUserList(conditionMap);
            if (resultList == null || resultList.size() == 0) {
                logger.info("exportUser --> resultList is null");
                return success("exportUser --> resultList is null");
            }

            HSSFWorkbook wb = new HSSFWorkbook();
            HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));

            //设置20列及列宽
            for (int i = 0; i < 22; i++) {
                if (i == 2 || i == 5 || i == 7 || i == 9 || i == 10 || i == 14) {
                    sheet.setColumnWidth(i, 20 * 300);
                    continue;
                }
                sheet.setColumnWidth(i, 20 * 140);
            }

            // 在sheet表中添加第一行表头
            HSSFRow row = sheet.createRow((int) 0);
            // 合并第一行单元格
            CellRangeAddress region = new CellRangeAddress(0, 0, 0, 21);
            sheet.addMergedRegion(region);
            // 创建单元格，设置值表头，设置表头居中
            HSSFCellStyle style = wb.createCellStyle();

            // 设置字体
            HSSFFont font = wb.createFont();
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 11);

            style.setFont(font);

            HSSFCell cell = row.createCell(0);
            cell.setCellValue("智慧扫楼人员信息详表");
            cell.setCellStyle(style);

            HSSFCellStyle style1 = wb.createCellStyle();
            //设置字体,黄色背景 及居中显示
            HSSFFont font1 = wb.createFont();
            font1.setFontName("宋体");
            font1.setFontHeightInPoints((short) 9);
            style1.setFont(font1);
            Calendar c = Calendar.getInstance();


            // 设置第三行数据字段 添加至excel
            Map<String, String> columnMap = new HashMap<>();
            columnMap.put("0", "序号");
            columnMap.put("1", "地市");
            columnMap.put("2", "渠道名称");
            columnMap.put("3", "渠道编号");
            columnMap.put("4", "用户姓名");
            columnMap.put("5", "手机号码");
            columnMap.put("6", "身份证号");
            columnMap.put("7", "来源渠道");
            columnMap.put("8", "归属渠道类型");
            columnMap.put("9", "注册时间");
            columnMap.put("10", "地址");
            columnMap.put("11", "人员状态");
            columnMap.put("12", "上级合伙人姓名");
            columnMap.put("13", "上级合伙人手机号码");
            columnMap.put("14", "上上级合伙人姓名");
            columnMap.put("15", "上上级合伙人手机号码");

            HSSFRow row2 = sheet.createRow(2);

            HSSFCellStyle style2 = wb.createCellStyle();

            // 居中格式
            for (int i = 0; i < 22; i++) {
                HSSFCell cell2 = row2.createCell(i);
                cell2.setCellValue(columnMap.get(i + ""));
                cell2.setCellStyle(style2);
            }


            // 将数据写入excel
            if (null != resultList && resultList.size() != 0) {
                // 遍历结果集
                for (int i = 0; i < resultList.size(); i++) {
                    Map<String, String> resultMap = resultList.get(i);

                    // 填写数据从第三行开始
                    row = sheet.createRow((int) i + 3);
                    // 序列
                    row.createCell(0).setCellValue(String.valueOf(i + 1));
                    // 地市
                    if (!StringUtil.isEmpty(resultMap.get("CITY_CODE"))) {
                        row.createCell(1).setCellValue(InterfaceUtil.city_code.get(resultMap.get("CITY_CODE")));
                    }
                    // 渠道名称
                    if (!StringUtil.isEmpty(resultMap.get("SCHOOL_NAME"))) {
                        row.createCell(2).setCellValue(StringUtil.filtration(resultMap.get("SCHOOL_NAME")));
                    }
                    // 渠道编码 school_code
                    if (!StringUtil.isEmpty(resultMap.get("SCHOOL_CODE"))) {
                        row.createCell(3).setCellValue(StringUtil.filtration(resultMap.get("SCHOOL_CODE")));
                    }
                    // 用户姓名
                    if (!StringUtil.isEmpty(resultMap.get("USER_NAME"))) {
                        if ("admin".equals(userName)) {
                            row.createCell(4).setCellValue(StringUtil.filtration(resultMap.get("USER_NAME")));
                        } else {
                            row.createCell(4).setCellValue(DesensitizationUtil.desensitizeName(StringUtil.filtration(resultMap.get("USER_NAME"))));
                        }
                    }
                    // 手机号码
                    if (!StringUtil.isEmpty(resultMap.get("USER_PHONE"))) {
                        if ("admin".equals(userName)) {
                            row.createCell(5).setCellValue(StringUtil.filtration(resultMap.get("USER_PHONE")));
                        } else {
                            row.createCell(5).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.filtration(resultMap.get("USER_PHONE"))));
                        }
                    }
                    // 身份证号
                    if (!StringUtil.isEmpty(resultMap.get("USER_SFZ"))) {
                        if ("admin".equals(userName)) {
                            row.createCell(6).setCellValue(StringUtil.filtration(resultMap.get("USER_SFZ")));
                        } else {
                            row.createCell(6).setCellValue(DesensitizationUtil.idCardDesensitization(StringUtil.filtration(resultMap.get("USER_SFZ"))));
                        }
                    }
                    // 来源渠道
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("CHANNEL_TYPE")))) {
                        if ("1".equals(String.valueOf(resultMap.get("CHANNEL_TYPE")))) {
                            row.createCell(7).setCellValue("门店代理商");
                        } else if ("2".equals(String.valueOf(resultMap.get("CHANNEL_TYPE")))) {
                            row.createCell(7).setCellValue("泛渠道（合作直销）");
                        }
                    }
                    // 归属渠道类型
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("HNSL_CHANNEL")))) {
                        if ("1".equals(String.valueOf(resultMap.get("HNSL_CHANNEL")))) {
                            row.createCell(8).setCellValue("全渠道");
                        } else if ("2".equals(String.valueOf(resultMap.get("HNSL_CHANNEL")))) {
                            row.createCell(8).setCellValue("分公司运营渠道");
                        } else if ("3".equals(String.valueOf(resultMap.get("HNSL_CHANNEL")))) {
                            row.createCell(8).setCellValue("电渠互联网卡渠道");
                        } else if ("4".equals(String.valueOf(resultMap.get("HNSL_CHANNEL")))) {
                            row.createCell(8).setCellValue("其他");
                        }
                    }
                    // 注册时间
                    if (null != resultMap.get("CREATED_DATE")) {
                        Object createdDate = resultMap.get("CREATED_DATE");
                        String formattedDate;
                        if (createdDate instanceof String) {
                            formattedDate = ((String) createdDate).replace("T", " ");
                        } else {
                            formattedDate = createdDate.toString().replace("T", " ");
                        }
                        row.createCell(9).setCellValue(formattedDate);
                    }
                    // 地址
                    if (!StringUtil.isEmpty(resultMap.get("USER_SITE"))) {
                        row.createCell(10).setCellValue(StringUtil.filtration(resultMap.get("USER_SITE")));
                    }
                    // STATUS
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("STATUS")))) {
                        if ("0".equals(String.valueOf(resultMap.get("STATUS")))) {
                            row.createCell(11).setCellValue("无效");
                        } else if ("1".equals(String.valueOf(resultMap.get("STATUS")))) {
                            row.createCell(11).setCellValue("有效");
                        }
                    }
                    // 上级姓名
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("NAME_SUPERIOR"))))) {
                        if ("admin".equals(userName)) {
                            row.createCell(12).setCellValue(StringUtil.filtration(resultMap.get("NAME_SUPERIOR")));
                        } else {
                            row.createCell(12).setCellValue(DesensitizationUtil.desensitizeName(StringUtil.filtration(resultMap.get("NAME_SUPERIOR"))));
                        }
                    }
                    // 上级号码
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("STATUS_SUPERIOR"))))) {
                        if ("admin".equals(userName)) {
                            row.createCell(13).setCellValue(StringUtil.filtration(resultMap.get("STATUS_SUPERIOR")));
                        } else {
                            row.createCell(13).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.filtration(resultMap.get("STATUS_SUPERIOR"))));
                        }
                    }
                    // 上上级姓名
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("MANAGER_NAME"))))) {
                        if ("admin".equals(userName)) {
                            row.createCell(14).setCellValue(StringUtil.filtration(resultMap.get("MANAGER_NAME")));
                        } else {
                            row.createCell(14).setCellValue(DesensitizationUtil.desensitizeName(StringUtil.filtration(resultMap.get("MANAGER_NAME"))));
                        }
                    }
                    // 上上级号码
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("MANAGER_SUPERIOR"))))) {
                        if ("admin".equals(userName)) {
                            row.createCell(15).setCellValue(StringUtil.filtration(resultMap.get("MANAGER_SUPERIOR")));
                        } else {
                            row.createCell(15).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.filtration(resultMap.get("MANAGER_SUPERIOR"))));
                        }
                    }
                }
            }
            String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            logger.info("智慧扫楼导出人员信息详表file文件路径:" + filepath);
            String fileName = "智慧扫楼人员信息详表";
            File file = new File(filepath + "智慧扫楼人员信息详表" + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) {//判断是否是文件
                    Map<String, Object> map = new HashedMap<>();
                    map.put("code", "6");
                    map.put("msg", "downloadExportFile");
                    map.put("fileName", fileName);
                    return success(map);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return fail("hnsluser exportUser exception : " + e.getMessage());
        }
        return fail("导出失败");
    }

    /**
     * 查询所有省级管理员
     *
     * @return
     */
    @RequestMapping("/queryStatusSf")
    public ApiResult<?> queryUserList(@RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<HnslH5User> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslH5User::getStatusSf, 5)
                .eq(HnslH5User::getHnslChannel, 2)
                .eq(HnslH5User::getStatus, 1);
        Page<HnslH5User> resultPage = hnslUserService.page(page, queryWrapper);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("list", resultPage.getRecords());
        resultMap.put("total", resultPage.getTotal());
        return success(resultMap);
    }

    /**
     * 获取当前登录用户的渠道权限
     *
     * @return
     */
    @OperationLog
    @ApiOperation("获取当前登录用户的渠道权限")
    @PostMapping("/queryChannel")
    public ApiResult<?> queryChannel(@RequestBody HnslH5User hnslH5User) {
        LambdaQueryWrapper<HnslH5User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslH5User::getUserPhone, hnslH5User.getUserPhone());
        HnslH5User one = hnslUserService.getOne(queryWrapper);
        return success(one);
    }
}
