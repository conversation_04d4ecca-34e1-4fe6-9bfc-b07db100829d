package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslOrder;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslOrderParam;

import java.util.List;
import java.util.Map;

/**
 * 扫楼工具订单表Service
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslOrderService extends IService<HnslOrder> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslOrder>
     */
    PageResult<HnslOrder> pageRel(HnslOrderParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslOrder>
     */
    List<HnslOrder> listRel(HnslOrderParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslOrder
     */
    HnslOrder getByIdRel(Integer id);

    /**
     * 查询订单详情信息
     *
     * @param param 查询参数
     * @return List<HnslOrder>
     */
    HnslOrder queryObject(Wrapper<HnslOrderParam> param);

}
