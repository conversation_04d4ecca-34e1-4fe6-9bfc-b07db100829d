<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslUserOrdersDataMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_user_orders_data a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.userId != null">
                AND a.USER_ID = #{param.userId}
            </if>
            <if test="param.activityLogo != null">
                AND a.ACTIVITY_LOGO LIKE CONCAT('%', #{param.activityLogo}, '%')
            </if>
            <if test="param.touchId != null">
                AND a.TOUCH_ID LIKE CONCAT('%', #{param.touchId}, '%')
            </if>
            <if test="param.orderId != null">
                AND a.ORDER_ID LIKE CONCAT('%', #{param.orderId}, '%')
            </if>
            <if test="param.sendOrdersStatus != null">
                AND a.SEND_ORDERS_STATUS = #{param.sendOrdersStatus}
            </if>
            <if test="param.orderReceiptDate != null">
                AND a.ORDER_RECEIPT_DATE LIKE CONCAT('%', #{param.orderReceiptDate}, '%')
            </if>
            <if test="param.orderReceiptResult != null">
                AND a.ORDER_RECEIPT_RESULT LIKE CONCAT('%', #{param.orderReceiptResult}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.schoolSixId != null">
                AND a.SCHOOL_SIX_ID LIKE CONCAT('%', #{param.schoolSixId}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrdersData">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrdersData">
        <include refid="selectSql"></include>
    </select>

</mapper>
