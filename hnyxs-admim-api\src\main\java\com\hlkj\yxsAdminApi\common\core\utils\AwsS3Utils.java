package com.hlkj.yxsAdminApi.common.core.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.util.IOUtils;
import com.amazonaws.util.StringUtils;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 工具类：S3服务器中文件上传、删除操作（用来存储jmeter脚本中参数化文件）
 */
@Component
public class AwsS3Utils extends BaseController {
    @Autowired
    private AmazonS3 amazonS3;
    private static Logger logger = LogManager.getLogger(AwsS3Utils.class);

    private static String accessKeyID = "M5LGR55DFMU035DJO2JF";
    private static String secretKey = "yx6pQO8Kgn8upOaIGKDzYs4MmxZes9rbyPDOGuk8";

    private static final String endpoint1 = "http://**************:8080";

    private static final String endpoint2 = "http://**************:8080";

    private static final String endpoint3 = "http://**************:8080";

    /**
     * default bucket
     */
    private static final String defaultBucket = "hnyxsAdmin";

    /**
     * 向 AWS 客户端明确提供凭证
     */
    private static final BasicAWSCredentials AWS_CREDENTIALS = new BasicAWSCredentials(accessKeyID, secretKey);

    private static int state = 0;
    public static int delState = 0;

    /**
     * s3 客户端 目前无法创建文件夹下的文件 只能通过桶实现多目录日期隔开
     */
    private static final AmazonS3 S1 = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(AWS_CREDENTIALS))
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint1, Regions.US_EAST_1.name()))
            .withPathStyleAccessEnabled(true)
            .build();

    private static final AmazonS3 S2 = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(AWS_CREDENTIALS))
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint2, Regions.US_EAST_1.name()))
            .withPathStyleAccessEnabled(true)
            .build();

    private static final AmazonS3 S3 = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(AWS_CREDENTIALS))
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint3, Regions.US_EAST_1.name()))
            .withPathStyleAccessEnabled(true)
            .build();

    public static AmazonS3 getRandomBean() {
        if (delState > 0) {
            logger.info("存储文件指定>>>>>>>>>{}", delState);
            switch (delState) {
                case 1:
                    return S1;
                case 2:
                    return S2;
                case 3:
                    return S3;
                default:
                    return S1;
            }
        } else {
            state = 0 == state || 3 == state ? 1 : ++state;
            switch (state) {
                case 1:
                    return S1;
                case 2:
                    return S2;
                case 3:
                    return S3;
                default:
                    return S1;
            }
        }
    }


    /**
     * 存放文件至s3
     *
     * @param file 桶名称 AmazonS3BucketConstant
     * @param key  key名称
     * @param file 文件
     * @return PutObjectResult
     */
    public ApiResult<?> putFile(String key, String name, MultipartFile file) {
        try {
            JSONObject jsonObject = new JSONObject();
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(file.getContentType());
            objectMetadata.setContentLength(file.getSize());
            //判断桶是否存在并创建
            if (!getRandomBean().doesBucketExistV2(key)) {
                getRandomBean().createBucket(key);
            }
            //上传文件
            PutObjectResult result = getRandomBean().putObject(new PutObjectRequest(key, name, file.getInputStream(), objectMetadata));
            jsonObject.put("result", result);
            return success(jsonObject);
        } catch (IOException e) {
            logger.error("{}{}上传文件失败：{}", key, name, e.getMessage());
            return fail(e.getMessage());
        }
    }
    /**
     * 存放文件至s3
     * @param filepath
     * @return
     */
    public void putFile(String filepath, XSSFWorkbook wb) {
        ByteArrayOutputStream baos;
        PutObjectRequest putRequest;
        try {
            baos = new ByteArrayOutputStream();
            wb.write(baos);
            byte[] data = baos.toByteArray();
            // 检查桶是否存在
            boolean isBucketExist = amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_KEY);
            if (!isBucketExist) {
                try {
                    amazonS3.createBucket(ConstantUtil.BUCKET_KEY);
                    logger.info("智慧扫楼项目-创建桶成功" + ConstantUtil.BUCKET_KEY);
                } catch (AmazonS3Exception e) {
                    logger.error("智慧扫楼项目-创建桶失败 " + ConstantUtil.BUCKET_KEY + ": " + e.getMessage());
                }
            }
            if (!amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_KEY)) {
                amazonS3.createBucket(ConstantUtil.BUCKET_KEY);
            }

            PutObjectResult result = amazonS3.putObject(ConstantUtil.BUCKET_KEY, filepath,
                    new ByteArrayInputStream(data), null);
            logger.info("上传结果" + result.toString());
        } catch (AmazonS3Exception e) {
            logger.error("Amazon S3 异常: " + e.getMessage(), e);
            throw new RuntimeException("上传失败: " + e.getMessage(), e);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error(" 异常: " + e.getMessage(), e);
        }
    }

    /**
     * 存放文件至s3 (支持HSSFWorkbook)
     * @param filepath 文件路径
     * @param wb HSSFWorkbook对象
     */
    public void putFile(String filepath, HSSFWorkbook wb) {
        ByteArrayOutputStream baos;
        PutObjectRequest putRequest;
        try {
            baos = new ByteArrayOutputStream();
            wb.write(baos);
            byte[] data = baos.toByteArray();
            // 检查桶是否存在
            boolean isBucketExist = amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_KEY);
            if (!isBucketExist) {
                try {
                    amazonS3.createBucket(ConstantUtil.BUCKET_KEY);
                    logger.info("智慧扫楼项目-创建桶成功" + ConstantUtil.BUCKET_KEY);
                } catch (AmazonS3Exception e) {
                    logger.error("智慧扫楼项目-创建桶失败 " + ConstantUtil.BUCKET_KEY + ": " + e.getMessage());
                }
            }
            if (!amazonS3.doesBucketExistV2(ConstantUtil.BUCKET_KEY)) {
                amazonS3.createBucket(ConstantUtil.BUCKET_KEY);
            }

            PutObjectResult result = amazonS3.putObject(ConstantUtil.BUCKET_KEY, filepath,
                    new ByteArrayInputStream(data), null);
            logger.info("上传结果" + result.toString());
        } catch (AmazonS3Exception e) {
            logger.error("Amazon S3 异常: " + e.getMessage(), e);
            throw new RuntimeException("上传失败: " + e.getMessage(), e);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error(" 异常: " + e.getMessage(), e);
        }
    }

    public ApiResult<?> putFile(String key, String name, File file) {
        try {
            JSONObject jsonObject = new JSONObject();
            //判断桶是否存在并创建
            if (!getRandomBean().doesBucketExistV2(key)) {
                getRandomBean().createBucket(key);
            }
            //上传文件
            PutObjectResult result = getRandomBean().putObject(new PutObjectRequest(key, name, file));
            jsonObject.put("result", result);
            return success(jsonObject);
        } catch (Exception e) {
            logger.error("{}{}上传文件失败：{}", key, name, e.getMessage());
            return fail(e.getMessage());
        }
    }

    /**
     * 读取文件 直接返回流
     *
     * @param key  桶名称 AmazonS3BucketConstant
     * @param name key名称
     * @return S3Object: s3Object.getBucketName(); s3Object.getKey(); InputStream inputStream = s3Object.getObjectContent()...
     * 务必关闭 S3Object:     s3Object.close();
     */
    public static void getFile(String key, String name, HttpServletResponse response) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(key, name);
        S3Object s3Object = getRandomBean().getObject(getObjectRequest);
        try {
            S3ObjectInputStream objectInputStream = s3Object.getObjectContent();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM.toString());
            response.setHeader("Content-disposition", "attachment;filename=" + URLUtil.encode(name));
            response.setHeader("Content-Length", String.valueOf(s3Object.getObjectMetadata().getContentLength()));
            response.setContentType(s3Object.getObjectMetadata().getContentType());
            ServletUtil.write(response, objectInputStream);
            s3Object.close();
        } catch (Exception e) {
            logger.error("获取文件流失败：", e);
        }
    }

    /**
     * 读取文件 直接返回base64
     *
     * @param key  桶名称 AmazonS3BucketConstant
     * @param name key名称
     * @return S3Object: s3Object.getBucketName(); s3Object.getKey(); InputStream inputStream = s3Object.getObjectContent()...
     * 务必关闭 S3Object:     s3Object.close();
     */
    public String getFileBase64(String key, String name) throws IOException {
        GetObjectRequest getObjectRequest = new GetObjectRequest(key, name);
        S3Object s3Object = S3.getObject(getObjectRequest);
        BufferedImage imgBuf = ImageIO.read(s3Object.getObjectContent());
        String base64 = "";
        if (imgBuf == null) {
            base64 = null;
        } else {
            BASE64Encoder encoder = new BASE64Encoder();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(imgBuf, "JPG", out);
            s3Object.close();
            return "data:image/jpeg:base64," + encoder.encode(out.toByteArray()).replaceAll("\n", "");
        }
        return base64;
    }

    /**
     * 读取文件 直接返回File
     *
     * @param keyName 桶名称 AmazonS3BucketConstant / key名称
     * @return S3Object: s3Object.getBucketName(); s3Object.getKey(); ...
     * 务必关闭 S3Object:     s3Object.close();
     */
    public File getFile(String keyName) throws IOException {
        String[] split = keyName.split("/");
        if (split.length < 2) {
            return null;
        }
        GetObjectRequest getObjectRequest = new GetObjectRequest(split[0], split[1]);
        S3Object s3Object = S3.getObject(getObjectRequest);
        InputStream inputStream = s3Object.getObjectContent();
        //创建临时文件
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String PREFIX = "HNSLLS" + simpleDateFormat.format(new Date()) + (int) (Math.random() * (99999 - 10000) + 10000);//前缀字符串定义文件名；必须至少三个字符
        String SUFFIX = ".tmp";//后缀字符串定义文件的扩展名；如果为null，则将使用后缀"
        File tempFile = File.createTempFile(PREFIX, SUFFIX);
        tempFile.deleteOnExit();
        try (FileOutputStream out = new FileOutputStream(tempFile)) {
            IOUtils.copy(inputStream, out);
        }
        s3Object.close();
        return tempFile;
    }

    /**
     * 获取图片
     * @param bucketPhoto
     * @param filename
     * @return
     */
    public byte[] getImageBytess(String bucketPhoto, String filename) {
        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketPhoto, filename);
            S3Object s3Object = S3.getObject(getObjectRequest);
            S3ObjectInputStream objectInputStream = s3Object.getObjectContent();
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = objectInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, length);
                }
                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            logger.error("getImageBytess>>>异常", e);
        }
        return null;
    }

    /**
     * 根据文件名称进行删除
     *
     * @param bucketName
     * @param key
     * @return
     */
    public void delFile(String bucketName, String key) {
        if (S3.doesBucketExistV2(bucketName) == false) {
            logger.info(bucketName + "不存在");
            return;
        }

        //根据桶名及文件夹名获取该桶该文件夹操作对象
        ListObjectsRequest lor = new ListObjectsRequest().withBucketName(defaultBucket).withPrefix("perfFile/");
        ObjectListing objectListing = S3.listObjects(lor);

        String str_key = null;

        //根据操作对象列出所有文件对象,单个对象使用objectSummary.getKey()即可获取此文件完整路径，配合桶名可以用于操作
        for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
            str_key = objectSummary.getKey();
            //文件名是否匹配
            if (str_key.matches(key)) {
                //根据桶名及key信息进行删除
                S3.deleteObject(bucketName, key);
                break;
            }
        }
    }

    public List<Bucket> getList() {
        return S3.listBuckets();
    }

    public ListObjectsV2Result list(String name) {
        ListObjectsV2Request request = new ListObjectsV2Request()
                .withBucketName(name);
        ListObjectsV2Result result = S3.listObjectsV2(request);
        return result;
    }

    public String create() {
        Bucket bucket = S3.createBucket(defaultBucket);
        ObjectListing objects = S3.listObjects(bucket.getName());
        do {
            for (S3ObjectSummary objectSummary : objects.getObjectSummaries()) {
                System.out.println(objectSummary.getKey() + "\t" + objectSummary.getSize() + "\t" + StringUtils.fromDate(objectSummary.getLastModified()));
            }
            objects = S3.listNextBatchOfObjects(objects);
        } while (objects.isTruncated());
        return "0";
    }

    public ApiResult<?> upload(MultipartFile file) throws IOException {
        try {
            JSONObject jsonObject = new JSONObject();
            String filename = DateUtil.format(new Date(), "yyyy_MM_dd") + "_" + IdUtil.randomUUID();
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(file.getContentType());
            objectMetadata.setContentLength(file.getSize());
            PutObjectResult result = S3.putObject(defaultBucket, filename, file.getInputStream(), objectMetadata);
            jsonObject.put("key", filename);
            jsonObject.put("result", result);
            return success(jsonObject);
        } catch (IOException e) {
            return fail(e.getMessage());
        }
    }

    public void download(String filename, HttpServletResponse response) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(defaultBucket, filename);
        S3Object s3Object = S3.getObject(getObjectRequest);
        S3ObjectInputStream objectInputStream = s3Object.getObjectContent();
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM.toString());
        response.setHeader("Content-disposition", "attachment;filename=" + URLUtil.encode(filename));
        ServletUtil.write(response, objectInputStream);
    }
}