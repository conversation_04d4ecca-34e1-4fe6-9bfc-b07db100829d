package com.hlkj.yxsAdminApi.common.system.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.utils.CommonUtil;
import com.hlkj.yxsAdminApi.common.core.utils.DesensitizationUtil;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.web.*;
import com.hlkj.yxsAdminApi.common.system.entity.DictionaryData;
import com.hlkj.yxsAdminApi.common.system.entity.Organization;
import com.hlkj.yxsAdminApi.common.system.entity.Role;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.param.UserImportParam;
import com.hlkj.yxsAdminApi.common.system.param.UserParam;
import com.hlkj.yxsAdminApi.common.system.service.DictionaryDataService;
import com.hlkj.yxsAdminApi.common.system.service.OrganizationService;
import com.hlkj.yxsAdminApi.common.system.service.PermissionChangeLogService;
import com.hlkj.yxsAdminApi.common.system.service.RoleService;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:41
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/system/user")
public class UserController extends BaseController {
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private DictionaryDataService dictionaryDataService;
    @Autowired
    private PermissionChangeLogService permissionChangeLogService;
    @Resource
    private RedisUtil redisUtil;

    @OperationLog
    @ApiOperation("服务器启动成功提示")
    @GetMapping("/login")
    public ApiResult<String> login() {
        ApiResult<String> stringApiResult = new ApiResult<>();
        stringApiResult.setCode(0);
        stringApiResult.setMessage("hello wrod");
        redisUtil.set("cs123","hello pjy");
        return stringApiResult;
    }

    @PreAuthorize("hasAuthority('sys:user:list')")
    @OperationLog
    @ApiOperation("分页查询用户")
    @PostMapping("/page")
    public ApiResult<PageResult<User>> page(@RequestBody UserParam param) {
        User user = getLoginUser();
        if (redisUtil.limitRateCount(Constants.limitUser, user)) {
            // 熔断账号冻结
            user.setStatus(1);
            userService.updateById(user);
            // 推送熔断日志用户信息
            InterfaceUtil.breakerLog(user, "用户信息查询");
            return new ApiResult<>(Constants.RESULT_ERROR_CODE, "当前查询次数已被限制");
        }
        // 获取查询结果
        PageResult<User> pageResult = userService.pageRel(param);

        // 对手机号和昵称进行脱敏处理
        if (pageResult != null && pageResult.getList() != null && !pageResult.getList().isEmpty()) {
            for (User u : pageResult.getList()) {
                // 手机号脱敏
                if (u.getPhone() != null && !u.getPhone().isEmpty()) {
                    u.setPhone(DesensitizationUtil.mobilePhoneDesensitization(u.getPhone()));
                }
                // 昵称脱敏
                if (u.getNickname() != null && !u.getNickname().isEmpty()) {
                    u.setNickname(DesensitizationUtil.desensitizedName(u.getNickname()));
                }
            }
        }

        return success(pageResult);
    }

    @PreAuthorize("hasAuthority('sys:user:list')")
    @OperationLog
    @ApiOperation("查询全部用户")
    @GetMapping("/list")
    public ApiResult<List<User>> list(UserParam param) {
        return success(userService.listRel(param));
    }

    @PreAuthorize("hasAuthority('sys:user:list')")
    @OperationLog
    @ApiOperation("根据id查询用户")
    @GetMapping("/{id}")
    public ApiResult<User> get(@PathVariable("id") Integer id) {
        User user = userService.getByIdRel(id);
        if (user != null) {
            // 手机号脱敏
            if (user.getPhone() != null && !user.getPhone().isEmpty()) {
                user.setPhone(DesensitizationUtil.mobilePhoneDesensitization(user.getPhone()));
            }
            // 昵称脱敏
            if (user.getNickname() != null && !user.getNickname().isEmpty()) {
                user.setNickname(DesensitizationUtil.desensitizedName(user.getNickname()));
            }
        }
        return success(user);
    }

    @PreAuthorize("hasAuthority('sys:user:save')")
    @OperationLog
    @ApiOperation("添加用户")
    @PostMapping("/add")
    public ApiResult<?> add(HttpServletRequest request, @RequestBody User user) {
        user.setStatus(0);
        user.setPassword(userService.encodePassword(user.getPassword()));
        if (userService.saveUser(user)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "新增";
            String operationModule = "用户管理";
            String targetId = user.getUserId().toString();
            String targetName = user.getNickname();
            String changeDetails = String.format("管理员{%s}新增用户{%s}", operator.getNickname(), user.getNickname());
            String beforeChange = null;
            String afterChange = user.toString();
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeChange, afterChange, null);
            
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('sys:user:update')")
    @OperationLog
    @ApiOperation("修改用户")
    @PostMapping("/update")
    public ApiResult<?> update(HttpServletRequest request, @RequestBody User user) {
        // 获取修改前的用户信息
        User beforeUser = userService.getByIdRel(user.getUserId());
        if (beforeUser == null) {
            return fail("用户不存在");
        }
        
        user.setStatus(null);
        user.setUsername(null);
        user.setPassword(null);
        if (userService.updateUser(user)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "修改";
            String operationModule = "用户管理";
            String targetId = user.getUserId().toString();
            String targetName = beforeUser.getNickname();
            
            // 生成变更详情
            StringBuilder detailsBuilder = new StringBuilder();
            detailsBuilder.append(String.format("管理员{%s}修改用户{%s}：", operator.getNickname(), targetName));
            
            if (user.getNickname() != null && !user.getNickname().equals(beforeUser.getNickname())) {
                detailsBuilder.append(permissionChangeLogService.formatUserChangeDetails("nickname", beforeUser.getNickname(), user.getNickname())).append("；");
            }
            
            if (user.getPhone() != null && !user.getPhone().equals(beforeUser.getPhone())) {
                detailsBuilder.append(permissionChangeLogService.formatUserChangeDetails("phone", beforeUser.getPhone(), user.getPhone())).append("；");
            }
            
            if (user.getEmail() != null && !user.getEmail().equals(beforeUser.getEmail())) {
                detailsBuilder.append(permissionChangeLogService.formatUserChangeDetails("email", beforeUser.getEmail(), user.getEmail())).append("；");
            }
            
            if (user.getOrganizationId() != null && !user.getOrganizationId().equals(beforeUser.getOrganizationId())) {
                String beforeOrgName = beforeUser.getOrganizationName();
                Organization afterOrg = organizationService.getById(user.getOrganizationId());
                String afterOrgName = afterOrg != null ? afterOrg.getOrganizationName() : "无";
                detailsBuilder.append(permissionChangeLogService.formatUserChangeDetails("organizationId", beforeOrgName, afterOrgName)).append("；");
            }
            
            String changeDetails = detailsBuilder.toString();
            if (changeDetails.endsWith("；")) {
                changeDetails = changeDetails.substring(0, changeDetails.length() - 1);
            }
            
            // 获取修改后的用户信息
            User afterUser = userService.getByIdRel(user.getUserId());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeUser.toString(), afterUser.toString(), null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:user:remove')")
    @OperationLog
    @ApiOperation("删除用户")
    @PostMapping("/{id}")
    public ApiResult<?> remove(HttpServletRequest request, @PathVariable("id") Integer id) {
        User beforeUser = userService.getByIdRel(id);
        if (beforeUser == null) {
            return fail("用户不存在");
        }
        
        if (userService.removeById(id)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "删除";
            String operationModule = "用户管理";
            String targetId = id.toString();
            String targetName = beforeUser.getNickname();
            String changeDetails = String.format("管理员{%s}删除用户{%s}", operator.getNickname(), targetName);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeUser.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('sys:user:update')")
    @OperationLog
    @ApiOperation("批量修改用户")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(HttpServletRequest request, @RequestBody BatchParam<User> batchParam) {
        if (batchParam.update(userService, User::getUserId)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量修改";
            String operationModule = "用户管理";
            String changeDetails = String.format("管理员{%s}批量修改用户信息", operator.getNickname());
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    null, null, changeDetails, batchParam.getIds().toString(), null, null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:user:remove')")
    @OperationLog
    @ApiOperation("批量删除用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "id数组", required = true, dataType = "string")
    })
    @PostMapping("/deleteBatch")
    public ApiResult<?> deleteBatch(HttpServletRequest request, @RequestBody List<Integer> ids) {
        List<User> beforeUsers = userService.listByIds(ids);
        if (beforeUsers.isEmpty()) {
            return fail("用户不存在");
        }
        
        if (userService.removeByIds(ids)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量删除";
            String operationModule = "用户管理";
            String targetNames = beforeUsers.stream()
                    .map(User::getNickname)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量删除用户{%s}", operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    ids.toString(), targetNames, changeDetails, beforeUsers.toString(), null, null);
                    
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('sys:user:update')")
    @OperationLog
    @ApiOperation("修改用户状态")
    @PostMapping("/status")
    public ApiResult<?> updateStatus(HttpServletRequest request, @RequestBody User user) {
        if (user.getUserId() == null || user.getStatus() == null || !Arrays.asList(0, 1).contains(user.getStatus())) {
            return fail("参数不正确");
        }
        
        User beforeUser = userService.getById(user.getUserId());
        if (beforeUser == null) {
            return fail("用户不存在");
        }
        
        User u = new User();
        u.setUserId(user.getUserId());
        u.setStatus(user.getStatus());
        if (userService.updateById(u)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "修改";
            String operationModule = "用户管理";
            String targetId = user.getUserId().toString();
            String targetName = beforeUser.getNickname();
            String beforeStatus = beforeUser.getStatus() == 0 ? "正常" : "禁用";
            String afterStatus = user.getStatus() == 0 ? "正常" : "禁用";
            String changeDetails = String.format("管理员{%s}修改用户{%s}状态：%s → %s", 
                operator.getNickname(), targetName, beforeStatus, afterStatus);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, beforeUser.toString(), 
                    user.toString(), null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:user:update')")
    @OperationLog
    @ApiOperation("批量修改用户状态")
    @PostMapping("/status/batch")
    public ApiResult<?> updateStatusBatch(HttpServletRequest request, @RequestBody BatchParam<Integer> batchParam) {
        if (!Arrays.asList(0, 1).contains(batchParam.getData())) {
            return fail("状态值不正确");
        }
        
        List<User> beforeUsers = userService.listByIds(batchParam.getIds());
        if (beforeUsers.isEmpty()) {
            return fail("用户不存在");
        }
        
        if (userService.update(new LambdaUpdateWrapper<User>()
                .in(User::getUserId, batchParam.getIds())
                .set(User::getStatus, batchParam.getData()))) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量修改";
            String operationModule = "用户管理";
            String targetNames = beforeUsers.stream()
                    .map(User::getNickname)
                    .collect(Collectors.joining("、"));
            String afterStatus = batchParam.getData() == 0 ? "正常" : "禁用";
            String changeDetails = String.format("管理员{%s}批量修改用户{%s}状态为{%s}", 
                operator.getNickname(), targetNames, afterStatus);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    batchParam.getIds().toString(), targetNames, changeDetails, beforeUsers.toString(), 
                    null, null);
                    
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('sys:user:update')")
    @OperationLog
    @ApiOperation("重置密码")
    @PostMapping("/password")
    public ApiResult<?> resetPassword(HttpServletRequest request, @RequestBody User user) {
        if (user.getUserId() == null || StrUtil.isBlank(user.getPassword())) {
            return fail("参数不正确");
        }
        
        User beforeUser = userService.getById(user.getUserId());
        if (beforeUser == null) {
            return fail("用户不存在");
        }
        
        User u = new User();
        u.setUserId(user.getUserId());
        u.setPassword(userService.encodePassword(user.getPassword()));
        if (userService.updateById(u)) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "修改";
            String operationModule = "用户管理";
            String targetId = user.getUserId().toString();
            String targetName = beforeUser.getNickname();
            String changeDetails = String.format("管理员{%s}重置用户{%s}密码", 
                operator.getNickname(), targetName);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    targetId, targetName, changeDetails, null, null, null);
                    
            return success("重置成功");
        } else {
            return fail("重置失败");
        }
    }

    @PreAuthorize("hasAuthority('sys:user:update')")
    @OperationLog
    @ApiOperation("批量重置密码")
    @PostMapping("/password/batch")
    public ApiResult<?> resetPasswordBatch(HttpServletRequest request, @RequestBody BatchParam<String> batchParam) {
        if (batchParam.getIds() == null || batchParam.getIds().size() == 0) {
            return fail("请选择用户");
        }
        if (batchParam.getData() == null) {
            return fail("请输入密码");
        }
        
        List<User> beforeUsers = userService.listByIds(batchParam.getIds());
        if (beforeUsers.isEmpty()) {
            return fail("用户不存在");
        }
        
        if (userService.update(new LambdaUpdateWrapper<User>()
                .in(User::getUserId, batchParam.getIds())
                .set(User::getPassword, userService.encodePassword(batchParam.getData())))) {
            // 记录权限变更日志
            User operator = getLoginUser();
            String operationType = "批量修改";
            String operationModule = "用户管理";
            String targetNames = beforeUsers.stream()
                    .map(User::getNickname)
                    .collect(Collectors.joining("、"));
            String changeDetails = String.format("管理员{%s}批量重置用户{%s}密码", 
                operator.getNickname(), targetNames);
            
            permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                    batchParam.getIds().toString(), targetNames, changeDetails, null, 
                    null, null);
                    
            return success("重置成功");
        } else {
            return fail("重置失败");
        }
    }

    @PreAuthorize("hasAuthority('sys:user:list')")
    @OperationLog
    @ApiOperation("检查用户是否存在")
    @GetMapping("/existence")
    public ApiResult<?> existence(ExistenceParam<User> param) {
        if (param.isExistence(userService, User::getUserId)) {
            return success(param.getValue() + "已存在");
        }
        return fail(param.getValue() + "不存在");
    }

    /**
     * excel导入用户
     */
    @PreAuthorize("hasAuthority('sys:user:save')")
    @OperationLog
    @ApiOperation("导入用户")
    @Transactional(rollbackFor = {Exception.class})
    @PostMapping("/import")
    public ApiResult<List<String>> importBatch(HttpServletRequest request, MultipartFile file) {
        ImportParams importParams = new ImportParams();
        try {
            List<UserImportParam> list = ExcelImportUtil.importExcel(file.getInputStream(),
                    UserImportParam.class, importParams);
            // 校验是否重复
            if (CommonUtil.checkRepeat(list, UserImportParam::getUsername)) {
                return fail("账号存在重复", null);
            }
            if (CommonUtil.checkRepeat(list, UserImportParam::getPhone)) {
                return fail("手机号存在重复", null);
            }
            // 校验是否存在
            List<User> usernameExists = userService.list(new LambdaQueryWrapper<User>().in(User::getUsername,
                    list.stream().map(UserImportParam::getUsername).collect(Collectors.toList())));
            if (usernameExists.size() > 0) {
                return fail("账号已经存在",
                        usernameExists.stream().map(User::getUsername).collect(Collectors.toList()));
            }
            List<User> phoneExists = userService.list(new LambdaQueryWrapper<User>().in(User::getPhone,
                    list.stream().map(UserImportParam::getPhone).collect(Collectors.toList())));
            if (phoneExists.size() > 0) {
                return fail("手机号已经存在",
                        phoneExists.stream().map(User::getPhone).collect(Collectors.toList()));
            }
            // 添加
            List<User> users = new ArrayList<>();
            for (UserImportParam one : list) {
                User u = new User();
                u.setStatus(0);
                u.setUsername(one.getUsername());
                u.setPassword(userService.encodePassword(one.getPassword()));
                u.setNickname(one.getNickname());
                u.setPhone(one.getPhone());
                Role role = roleService.getOne(new QueryWrapper<Role>()
                        .eq("role_name", one.getRoleName()), false);
                if (role == null) {
                    return fail("角色不存在", Collections.singletonList(one.getRoleName()));
                } else {
                    u.setRoles(Collections.singletonList(role));
                }
                Organization organization = organizationService.getOne(new QueryWrapper<Organization>()
                        .eq("organization_full_name", one.getOrganizationName()), false);
                if (organization == null) {
                    return fail("机构不存在", Collections.singletonList(one.getOrganizationName()));
                } else {
                    u.setOrganizationId(organization.getOrganizationId());
                }
                DictionaryData sex = dictionaryDataService.getByDictCodeAndName("sex", one.getSexName());
                if (sex == null) {
                    return fail("性别不存在", Collections.singletonList(one.getSexName()));
                } else {
                    u.setSex(sex.getDictDataCode());
                }
                users.add(u);
            }
            if (userService.saveBatch(users)) {
                // 记录权限变更日志
                User operator = getLoginUser();
                String operationType = "批量导入";
                String operationModule = "用户管理";
                String targetNames = users.stream()
                        .map(User::getNickname)
                        .collect(Collectors.joining("、"));
                String changeDetails = String.format("管理员{%s}批量导入用户{%s}", 
                    operator.getNickname(), targetNames);
                
                permissionChangeLogService.recordLog(request, operator, operationType, operationModule,
                        null, targetNames, changeDetails, null, 
                        users.toString(), null);
                        
                return success("导入成功", null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fail("导入失败", null);
    }

}
