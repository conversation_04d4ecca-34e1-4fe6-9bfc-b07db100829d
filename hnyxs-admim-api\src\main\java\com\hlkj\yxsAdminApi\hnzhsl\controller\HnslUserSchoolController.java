package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserSchoolService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSchool;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserSchoolParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户学校关系表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "用户学校关系表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-user-school")
public class HnslUserSchoolController extends BaseController {
    @Autowired
    private HnslUserSchoolService hnslUserSchoolService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:list')")
    @OperationLog
    @ApiOperation("分页查询用户学校关系表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserSchool>> page(@RequestBody HnslUserSchoolParam param) {
        PageParam<HnslUserSchool, HnslUserSchoolParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserSchoolService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslUserSchoolService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:list')")
    @OperationLog
    @ApiOperation("查询全部用户学校关系表")
    @PostMapping("/list")
    public ApiResult<List<HnslUserSchool>> list(@RequestBody HnslUserSchoolParam param) {
        PageParam<HnslUserSchool, HnslUserSchoolParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserSchoolService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslUserSchoolService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:list')")
    @OperationLog
    @ApiOperation("根据id查询用户学校关系表")
    @GetMapping("/{id}")
    public ApiResult<HnslUserSchool> get(@PathVariable("id") Integer id) {
        return success(hnslUserSchoolService.getById(id));
        // 使用关联查询
        //return success(hnslUserSchoolService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:save')")
    @OperationLog
    @ApiOperation("添加用户学校关系表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserSchool hnslUserSchool) {
        if (hnslUserSchoolService.save(hnslUserSchool)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:update')")
    @OperationLog
    @ApiOperation("修改用户学校关系表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserSchool hnslUserSchool) {
        if (hnslUserSchoolService.updateById(hnslUserSchool)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:remove')")
    @OperationLog
    @ApiOperation("删除用户学校关系表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserSchoolService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:save')")
    @OperationLog
    @ApiOperation("批量添加用户学校关系表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserSchool> list) {
        if (hnslUserSchoolService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:update')")
    @OperationLog
    @ApiOperation("批量修改用户学校关系表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUserSchool> batchParam) {
        if (batchParam.update(hnslUserSchoolService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserSchool:remove')")
    @OperationLog
    @ApiOperation("批量删除用户学校关系表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserSchoolService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
