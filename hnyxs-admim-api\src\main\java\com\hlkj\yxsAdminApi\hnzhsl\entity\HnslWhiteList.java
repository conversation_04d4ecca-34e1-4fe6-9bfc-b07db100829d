package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.hlkj.yxsAdminApi.common.core.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 熟卡白名单表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslWhiteList对象", description = "熟卡白名单表")
public class HnslWhiteList implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户号码")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "所属地市编码")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "绑定的ICCID号码")
    @TableField("ICCID")
    private String iccid;

    @ApiModelProperty(value = "校园渠道 1:高校 2:中小学")
    @TableField("SCHOOL_CHANNEL")
    private Integer schoolChannel;

    @ApiModelProperty(value = "姓名")
    @TableField("CUSTOMER_NAME")
    private String customerName;

    @ApiModelProperty(value = "身份证")
    @TableField("CUSTOMER_CARD")
    private String customerCard;

    @ApiModelProperty(value = "失效时间")
    @TableField("FAILURE_DATE")
    private Date failureDate;

    @ApiModelProperty(value = "备注")
    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty(value = "状态 1:未使用 2：已过期 3：激活成功 4：激活失败")
    @TableField("NUMBER_STATUS")
    private Integer numberStatus;

    @ApiModelProperty(value = "批次号(时间+随机数两位数(20210910+01))")
    @TableField("BATCH_CODE")
    private String batchCode;

    @ApiModelProperty(value = "指定积分 没有按原逻辑算")
    @TableField("SPECIFY_POINTS")
    private String specifyPoints;

    @ApiModelProperty(value = "联系号码")
    @TableField("CUSTOMER_PHONE")
    private String customerPhone;

    @ApiModelProperty(value = "学校名称")
    @TableField(exist = false)
    private String schoolName;

    /** 激活状态 */
    @TableField(exist = false)
    private String orderStatus;

    /** 激活时间 */
    @TableField(exist = false)
    private Date orderActivatedDate;

    /**
     * 熟卡号码数量
     */
    @TableField(exist = false)
    @ExcelProperty(value = "totalPhones")
    private Integer totalPhones;

    /**
     * 有效期内未激活熟卡数量
     */
    @TableField(exist = false)
    @ExcelProperty(value = "unactivatedCount")
    private Integer unactivatedCount;

    /**
     * 已激活熟卡数量
     */
    @TableField(exist = false)
    private Integer activatedCount;

    /**
     * 7天内到期的熟卡号码数量
     */
    @TableField(exist = false)
    private Integer expiringCount;

    @TableField(exist = false)
    private String xlsName;
}
