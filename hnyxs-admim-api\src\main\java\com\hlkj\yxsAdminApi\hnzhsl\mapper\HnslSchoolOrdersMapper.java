package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolOrders;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSchoolOrdersParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 派单学校关联表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslSchoolOrdersMapper extends BaseMapper<HnslSchoolOrders> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSchoolOrders>
     */
    List<HnslSchoolOrders> selectPageRel(@Param("page") IPage<HnslSchoolOrders> page,
                             @Param("param") HnslSchoolOrdersParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSchoolOrders> selectListRel(@Param("param") HnslSchoolOrdersParam param);

}
