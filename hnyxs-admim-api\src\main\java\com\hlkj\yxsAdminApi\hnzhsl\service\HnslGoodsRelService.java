package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsRel;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsRelParam;

import java.util.List;

/**
 * 扫楼工具商品关联表Service
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
public interface HnslGoodsRelService extends IService<HnslGoodsRel> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslGoodsRel>
     */
    PageResult<HnslGoodsRel> pageRel(HnslGoodsRelParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslGoodsRel>
     */
    List<HnslGoodsRel> listRel(HnslGoodsRelParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslGoodsRel
     */
    HnslGoodsRel getByIdRel(Integer id);

    /**
     * 多个销售品ID处理
     * @param hnslGoodsRel
     */
    void saveChange(HnslGoodsRel hnslGoodsRel);
}
