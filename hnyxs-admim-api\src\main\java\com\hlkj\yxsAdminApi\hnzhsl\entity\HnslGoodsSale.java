package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 销售品表
 *
 * <AUTHOR>
 * @since 2023-04-24 17:48:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslGoodsSale对象", description = "销售品表")
public class HnslGoodsSale implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "销售品ID")
    @TableField("GOODS_SALE_ID")
    private String goodsSaleId;

    @ApiModelProperty(value = "销售品名称")
    @TableField("GOODS_SALE_NAME")
    private String goodsSaleName;

    @ApiModelProperty(value = "是否校园宽带（0：否 1：是）")
    @TableField("CAMPUS_BROADBAND_SWITCH")
    private Integer campusBroadbandSwitch;

    @ApiModelProperty(value = "宽带速率（0：否 1：是）")
    @TableField("BROADBAND_RATE")
    private String broadbandRate;

    @ApiModelProperty(value = "校园流量")
    @TableField("CAMPUS_TRAFFIC_SWITCH")
    private Integer campusTrafficSwitch;

    @ApiModelProperty(value = "积分")
    @TableField("GOODS_SALE_INTEGRAL")
    private Integer goodsSaleIntegral;

    @ApiModelProperty(value = "不加积分项 多选(0:无或空代表没有 1:号卡新装  2:校园融合  3:业务加装  4:熟卡)")
    @TableField("INTEGRAL_CONFIGURATION")
    private Integer integralConfiguration;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "销售品类型(1:可选包 2:促销包 3:套餐销售品 4:基础包)")
    @TableField("GOODS_SALE_TYPE")
    private Integer goodsSaleType;

}
