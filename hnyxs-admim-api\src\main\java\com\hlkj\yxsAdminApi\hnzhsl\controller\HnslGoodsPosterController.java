package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsPosterService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsPoster;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsPosterParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品关联海报图表控制器
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
@Api(tags = "商品关联海报图表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-goods-poster")
public class HnslGoodsPosterController extends BaseController {
    @Autowired
    private HnslGoodsPosterService hnslGoodsPosterService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:list')")
    @OperationLog
    @ApiOperation("分页查询商品关联海报图表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsPoster>> page(@RequestBody HnslGoodsPosterParam param) {
        PageParam<HnslGoodsPoster, HnslGoodsPosterParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsPosterService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsPosterService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:list')")
    @OperationLog
    @ApiOperation("查询全部商品关联海报图表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoodsPoster>> list(@RequestBody HnslGoodsPosterParam param) {
        PageParam<HnslGoodsPoster, HnslGoodsPosterParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsPosterService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsPosterService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:list')")
    @OperationLog
    @ApiOperation("根据id查询商品关联海报图表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsPoster> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsPosterService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsPosterService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:save')")
    @OperationLog
    @ApiOperation("添加商品关联海报图表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslGoodsPoster hnslGoodsPoster) {
        if (hnslGoodsPosterService.save(hnslGoodsPoster)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:update')")
    @OperationLog
    @ApiOperation("修改商品关联海报图表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoodsPoster hnslGoodsPoster) {
        if (hnslGoodsPosterService.updateById(hnslGoodsPoster)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:remove')")
    @OperationLog
    @ApiOperation("删除商品关联海报图表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsPosterService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:save')")
    @OperationLog
    @ApiOperation("批量添加商品关联海报图表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsPoster> list) {
        if (hnslGoodsPosterService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:update')")
    @OperationLog
    @ApiOperation("批量修改商品关联海报图表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoodsPoster> batchParam) {
        if (batchParam.update(hnslGoodsPosterService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsPoster:remove')")
    @OperationLog
    @ApiOperation("批量删除商品关联海报图表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsPosterService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
