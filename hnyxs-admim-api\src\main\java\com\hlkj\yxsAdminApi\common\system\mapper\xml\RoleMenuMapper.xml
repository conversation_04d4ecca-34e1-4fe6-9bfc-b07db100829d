<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hlkj.yxsAdminApi.common.system.mapper.RoleMenuMapper">

    <!-- 查询用户的菜单 -->
    <select id="listMenuByUserId" resultType="com.hlkj.yxsAdminApi.common.system.entity.Menu">
        SELECT a.*
        FROM hnyxs_sys_menu a
        <where>
            AND a.menu_id IN (
            SELECT menu_id FROM hnyxs_sys_role_menu WHERE role_id IN (
            SELECT ta.role_id FROM hnyxs_sys_user_role ta LEFT JOIN hnyxs_sys_role tb ON ta.role_id = tb.role_id
            WHERE ta.user_id = #{userId} AND tb.deleted = 0
            )
            )
            <if test="menuType != null">
                AND a.menu_type = #{menuType}
            </if>
            AND a.deleted = 0
        </where>
        ORDER BY a.sort_number
    </select>

    <!-- 根据角色id查询菜单 -->
    <select id="listMenuByRoleIds" resultType="com.hlkj.yxsAdminApi.common.system.entity.Menu">
        SELECT a.*
        FROM hnyxs_sys_menu a
        <where>
            AND a.menu_id IN (SELECT menu_id FROM hnyxs_sys_role_menu WHERE role_id IN
            <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">
                #{roleId}
            </foreach>
            )
            <if test="menuType != null">
                AND a.menu_type = #{menuType}
            </if>
            AND a.deleted = 0
        </where>
        ORDER BY a.sort_number
    </select>

</mapper>
