package com.hlkj.yxsAdminApi.hnzhslH5.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.api.R;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.config.AwzS3Service;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.*;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.*;
import com.hlkj.yxsAdminApi.hnzhslH5.param.HnslOrderH5Param;
import com.hlkj.yxsAdminApi.hnzhslH5.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import sun.misc.BASE64Decoder;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扫楼工具订单表控制器
 */
@Api(tags = "扫楼工具订单表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslOrderH5")
public class HnslOrderH5Controller extends BaseController {
    @Autowired
    private HnslOrderH5Service hnslOrderService;

    @Autowired
    private HnslUserH5Service hnslUserService;

    @Autowired
    private HnslGoodsH5Service hnslGoodsService;

    @Autowired
    private HnslGoodsRelH5Service hnslGoodsRelService;

    @Autowired
    private HnslSchoolH5Service hnslSchoolService;

    @Autowired
    private HnslUserSchoolH5Service hnslUserSchoolH5Service;

    @Autowired
    private ConfigProperties config;

    @Autowired
    private RedisUtil redisUtils;
    @Autowired
    private AwzS3Service awzS3Service;

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼工具订单表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslH5Order>> page(@RequestBody HnslOrderH5Param param) {
        if (StringUtil.isNull(String.valueOf(param.getBeginTime()))
                && StringUtil.isNull(String.valueOf(param.getEndTime()))) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cd = Calendar.getInstance();
            cd.add(Calendar.DATE, -7);
            param.setBeginTime(simpleDateFormat.format(cd.getTime()));
            cd = Calendar.getInstance();
            cd.add(Calendar.DATE, 1);
            param.setEndTime(simpleDateFormat.format(cd.getTime()));
        }
        return success(hnslOrderService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼工具订单表")
    @PostMapping("/list")
    public ApiResult<List<HnslH5Order>> list(@RequestBody HnslOrderH5Param param) {
        PageParam<HnslH5Order, HnslOrderH5Param> page = new PageParam<>(param);
        return success(hnslOrderService.list(page.getOrderWrapper()));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼工具订单表")
    @GetMapping("/{id}")
    public ApiResult<HnslH5Order> get(@PathVariable("id") Integer id) {
        LambdaQueryWrapper<HnslOrderH5Param> orderParamLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderParamLambdaQueryWrapper.eq(HnslOrderH5Param::getId, id);
        HnslH5Order hnslOrder = hnslOrderService.queryObject(orderParamLambdaQueryWrapper);
        String codeToName = InterfaceUtil.getCodeToName(hnslOrder.getCitycode());
        hnslOrder.setCityName(codeToName);
        return success(hnslOrder);
        // 使用关联查询
        //return success(hnslOrderService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:save')")
    @OperationLog
    @ApiOperation("添加扫楼工具订单表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslH5Order hnslOrder) {
        if (hnslOrderService.save(hnslOrder)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:update')")
    @OperationLog
    @ApiOperation("修改扫楼工具订单表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslH5Order hnslOrder) {
        if (hnslOrderService.updateById(hnslOrder)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:remove')")
    @OperationLog
    @ApiOperation("删除扫楼工具订单表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslOrderService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @OperationLog
    @ApiOperation("批量添加扫楼工具订单表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslH5Order> list) {
        if (hnslOrderService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @OperationLog
    @ApiOperation("批量修改扫楼工具订单表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslH5Order> batchParam) {
        if (batchParam.update(hnslOrderService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @OperationLog
    @ApiOperation("批量删除扫楼工具订单表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslOrderService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslH5Order:outputOrder')")
    @OperationLog
    @ApiOperation("导出订单")
    @PostMapping("/outputOrder")
    public ApiResult<?> outputOrder(@RequestBody HnslOrderH5Param hnslOrder, HttpServletRequest request,
                                    HttpServletResponse response) {
        logger.info("导出订单入参" + hnslOrder);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat simpleDateFormats = new SimpleDateFormat("yyyy-MM-dd");
        //查询列表数据
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("saflType", hnslOrder.getSaflType());
        requestMap.put("statusSf", hnslOrder.getStatusSf());
        requestMap.put("citycode", hnslOrder.getCitycode());
        requestMap.put("orderStatus", hnslOrder.getOrderStatus());
        requestMap.put("customerPhone", hnslOrder.getCustomerPhone());
        requestMap.put("schoolName", hnslOrder.getSchoolName());
        requestMap.put("beginTime", hnslOrder.getBeginTime());
        requestMap.put("endTime", hnslOrder.getEndTime());
        requestMap.put("hhid", hnslOrder.getHhid());
        requestMap.put("goodsName",hnslOrder.getGoodsName());
        requestMap.put("page", null);

        User user = getLoginUser();
        String UserPhone = redisUtils.getString("thisH5UserPhone" + user.getPhone());
        String UserName = redisUtils.getString("thisH5UserName" + user.getPhone());
        String UserSf = redisUtils.getString("thisH5UserSf" + user.getPhone());
        String cityCode = redisUtils.getString("cityCodeH5" + user.getPhone());
        String hnslType = redisUtils.getString("hnslH5Channel" + user.getPhone());
        List<HnslH5Order> hnslOrderList = new ArrayList<>();
        requestMap.put("hnslChannel", hnslType);

        if(StringUtil.isNull(hnslOrder.getBeginTime())
                && StringUtil.isNull(hnslOrder.getEndTime())){
            Calendar cd = Calendar.getInstance();
            cd.add(Calendar.DATE,-7);
            requestMap.put("beginTime",simpleDateFormats.format(cd.getTime()));
            cd = Calendar.getInstance();
            cd.add(Calendar.DATE,1);
            requestMap.put("endTime",simpleDateFormats.format(cd.getTime()));
        }else{
            requestMap.put("beginTime", hnslOrder.getBeginTime());
            requestMap.put("endTime", hnslOrder.getEndTime());
        }

        if ("1".equals(UserSf)) {
            HnslH5UserSchool hnslH5UserSchool = new HnslH5UserSchool();
            hnslH5UserSchool.setUserPhone(UserPhone);
            List<HnslH5UserSchool> hnslUserSchoolEntities = hnslUserSchoolH5Service.queryObjectBy(hnslH5UserSchool);
            List<String> collect = hnslUserSchoolEntities.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());
            requestMap.put("schoolList", collect);
        } else if ("6".equals(UserSf)) {
            requestMap.put("cityCode", cityCode);
        }
        hnslOrderList = hnslOrderService.outputQueryList(requestMap);

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        for (int i = 0; i <= 26; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("订单类型");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("合伙人姓名");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("合伙人号码");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("合伙人级别");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("订购号码");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("客户联系电话");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("客户姓名");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("支付价格");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("是否有效");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("上级合伙人姓名");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("上级合伙人销售编码");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("上上级合伙人");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("上上级合伙人销售员编码");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("订单时间");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("来源渠道");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("渠道类型");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != hnslOrderList && hnslOrderList.size() != 0) {

            for (int i = 0; i < hnslOrderList.size(); i++) {
                row = sheet.createRow((int) i + 1);
                // 序列
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getSaflType()))) {
                    if ("1".equals(String.valueOf(hnslOrderList.get(i).getSaflType()))) {
                        row.createCell(0).setCellValue("号卡新装");
                    }
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getUserHhrName()))) {
                    row.createCell(1).setCellValue(String.valueOf(hnslOrderList.get(i).getUserHhrName()));
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getUserHhrPhone()))) {
                    row.createCell(2).setCellValue(String.valueOf(hnslOrderList.get(i).getUserHhrPhone()));
                }
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getState())) {
                    if ("1".equals(hnslOrderList.get(i).getState())) {
                        row.createCell(3).setCellValue("渠道经理");
                    } else if ("2".equals(hnslOrderList.get(i).getStatusSf())) {
                        row.createCell(3).setCellValue("一级合伙人");
                    } else if ("3".equals(hnslOrderList.get(i).getStatusSf())) {
                        row.createCell(3).setCellValue("二级合伙人");
                    } else if ("4".equals(hnslOrderList.get(i).getStatusSf())) {
                        row.createCell(3).setCellValue("三级合伙人");
                    } else if ("6".equals(hnslOrderList.get(i).getStatusSf())) {
                        row.createCell(3).setCellValue("地市管理员");
                    } else if ("5".equals(hnslOrderList.get(i).getStatusSf())) {
                        row.createCell(3).setCellValue("省级管理员");
                    } else {
                        row.createCell(3).setCellValue("无");
                    }
                }
                // 订购号码
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerPhone())) {
                    row.createCell(4).setCellValue(hnslOrderList.get(i).getCustomerPhone());
                }
                // 客户联系号码
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getCustomerContactPhone())) {
                    row.createCell(5).setCellValue(hnslOrderList.get(i).getCustomerContactPhone());
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getCustomerName()))) {
                    row.createCell(6).setCellValue(String.valueOf(hnslOrderList.get(i).getCustomerName()));
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getOrderPrice()))) {
                    row.createCell(7).setCellValue(String.valueOf(hnslOrderList.get(i).getOrderPrice()));
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getStatus()))) {
                    String status = String.valueOf(hnslOrderList.get(i).getStatus());
                    if ("0".equals(status)) {
                        row.createCell(8).setCellValue("无效");
                    } else if ("1".equals(status)) {
                        row.createCell(8).setCellValue("有效");
                    }
                }
                // 查询上级合伙人和上上级合伙人信息
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getSjUserName())) {
                    row.createCell(9).setCellValue(hnslOrderList.get(i).getSjUserName());
                }
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getSjSalesCode())) {
                    row.createCell(10).setCellValue(hnslOrderList.get(i).getSjSalesCode());
                }
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getCjUserName())) {
                    row.createCell(11).setCellValue(hnslOrderList.get(i).getCjUserName());
                }
                if (!StringUtil.isEmpty(hnslOrderList.get(i).getCjSalesCode())) {
                    row.createCell(12).setCellValue(hnslOrderList.get(i).getCjSalesCode());
                }
                if (hnslOrderList.get(i).getCreatedDate() != null) {
                    Object createdDate = hnslOrderList.get(i).getCreatedDate();
                    String formattedDate;
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (createdDate instanceof String) {
                        String dateStr = (String) createdDate;
                        dateStr = dateStr.replace("T", " ");
                        try {
                            Date date = dateFormat.parse(dateStr);
                            formattedDate = dateFormat.format(date);
                        } catch (Exception e) {
                            e.printStackTrace();
                            formattedDate = dateStr;
                        }
                    } else if (createdDate instanceof Date) {
                        formattedDate = dateFormat.format((Date) createdDate);
                    } else {
                        formattedDate = createdDate.toString().replace("T", " ");
                    }
                    row.createCell(13).setCellValue(formattedDate);
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getChannelType()))) {
                    String channelType = String.valueOf(hnslOrderList.get(i).getChannelType());
                    if ("1".equals(channelType)) {
                        row.createCell(14).setCellValue("门店代理商");
                    } else if ("2".equals(channelType)) {
                        row.createCell(14).setCellValue("泛渠道（合作直销）");
                    }
                }
                if (!StringUtil.isEmpty(String.valueOf(hnslOrderList.get(i).getSchoolChannelType()))) {
                    String channelType = String.valueOf(hnslOrderList.get(i).getSchoolChannelType());
                    if ("1".equals(channelType)) {
                        row.createCell(15).setCellValue("全渠道");
                    } else if ("2".equals(channelType)) {
                        row.createCell(15).setCellValue("分公司运营渠道");
                    } else if ("3".equals(channelType)) {
                        row.createCell(15).setCellValue("电渠互联网卡渠道");
                    } else if ("4".equals(channelType)) {
                        row.createCell(15).setCellValue("其他");
                    }
                }
            }
            String filepath = config.getHnzhslFilePath() + "uploads" + File.separator + "file" + File.separator;
            String fileName = "订单表";
            File file = new File(filepath + fileName + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) {//判断是否是文件
                    Map<String, Object> map = new HashedMap<>();
                    map.put("code", "6");
                    map.put("fileName", fileName);
                    map.put("msg", "exportDaoUsers");
                    return success(map);
                }
            }
        }
        return fail("导出失败，未查询到订单");
    }

    /**
     * 重新同步未同步成功的接口  订单类型不同 同步的接口不一样
     *
     * @param hnslOrder
     * @return
     */
    @OperationLog
    @PostMapping("/decode/resynchronization")
    public ApiResult<?> resynchronization(@RequestBody HnslH5Order hnslOrder) {
        User loginUser = getLoginUser();
        try {
            if (hnslOrder.getSaflType() == 1) {
                Map<String, Object> resultMap = hnslOrderService.synchronizeBPS(hnslOrder, loginUser);
                if (!Objects.equals(resultMap.get("resultCode"), "0")) {
                    return fail("同步失败!");
                }
            } else {
                return fail("未知订单类型!");
            }
        } catch (Exception e) {
            return fail(e + "");
        }
        return success("同步成功");
    }

    /**
     * 重新推送整个订单 并且直接过费
     */
    @OperationLog
    @PostMapping("/decode/submitCrm")
    public ApiResult<?> OrderCrm(@RequestBody HnslH5Order orderPojo) {
        logger.info("完成暂存单签名后调用入参：{}", orderPojo);
        String yz = ConstantUtil.REPEAT_SUBMIT + orderPojo.getOrderId() + orderPojo.getCustomerPhone();
        if (redisUtils.hasKey(yz)) {
            return fail("请勿重复提交");
        } else {
            redisUtils.set(yz, "1", 180);
        }

        try {
            HnslH5Order hnslH5Order = hnslOrderService.getOne(new LambdaQueryWrapper<HnslH5Order>()
                    .eq(HnslH5Order::getOrderId, orderPojo.getOrderId()));
            if (hnslH5Order == null) {
                return fail("查询订单信息失败");
            }
//            HnslH5User userPojo = hnslUserService.getOne(new LambdaQueryWrapper<HnslH5User>().eq(HnslH5User::getUserPhone, orderPojo.getUserHhrPhone()));
//            if (userPojo == null) {
//                return fail("查询合伙人信息失败");
//            }
            HnslH5Goods goods = hnslGoodsService.getOne(new LambdaQueryWrapper<HnslH5Goods>().eq(HnslH5Goods::getGoodsNumber, orderPojo.getGoodsNumber()));
            if (goods == null) {
                return fail("查询商品信息失败");
            }

            if (orderPojo.getOrderPrice() == 0.0) {
                //同步crm订单确认
                Map<String, Object> crmParamMap = new HashMap<>();
                String custOrderId = orderPojo.getCrmOrderId();
                String tel = orderPojo.getCustomerContactPhone();
                String amout = String.valueOf(orderPojo.getOrderPrice());
                if ("731".equals(orderPojo.getCitycode())) {
                    amout = orderPojo.getCrmOrderPrice();
                }
                crmParamMap.put("cust_order_id", custOrderId);// 客户id
                crmParamMap.put("lan_id", orderPojo.getCitycode());// 本地网
                crmParamMap.put("sms_tel", tel);// 手机号
                crmParamMap.put("pay_amount", amout);// 必须和CRM返回同步
                crmParamMap.put("customerPhone", orderPojo.getCustomerPhone());
                crmParamMap.put("pay_id", orderPojo.getPayOrderno()); //支付流水号
                JSONObject jsonObject = InterfaceUtil.crmSureOrders(crmParamMap);
                if ("0".equals(jsonObject.get("Code"))) {
                    orderPojo.setCrmSureSynchro("1");
                    orderPojo.setOrderStatus(1);
                    orderPojo.setOrderActivateDate(new Date());
                    hnslOrderService.updateById(orderPojo);
                } else {
                    return fail("crm确认订单异常：" + jsonObject.getString("Message"));
                }

                //同步认证比对照片给CRM
                Map<String, Object> custCertData = new HashMap<>();
                custCertData.put("lan_id", orderPojo.getCitycode()); //地市编码
                custCertData.put("cust_order_id", orderPojo.getCrmOrderId());//CRM系统orderId
                custCertData.put("image_best", orderPojo.getIdentityVideoImage1());//人证比对免冠照
                custCertData.put("image_idcard", orderPojo.getIdentityCardImage3()); //身份证免冠照
                custCertData.put("workPermitList", orderPojo.getWorkPermitList()); //工作证图片集合
                String rests = InterfaceUtil.uploadCustOrderImage(custCertData, awzS3Service.getRandomBean());
                if ("0".equals(rests)) {
                    orderPojo.setCrmSynImgStatus("1");
                    logger.info("{}：同步认证比对照片给CRM成功", orderPojo.getOrderId());
                } else {
                    orderPojo.setCrmSynImgStatus("2");
                    logger.info("{}：同步认证比对照片给CRM失败", orderPojo.getOrderId());
                }

                //扫楼H5订单正式单同步到BPS
                Map<String, String> stringStringMap = InterfaceUtil.autoOrdersSyncs(orderPojo, goods);
                if ("0".equals(stringStringMap.get("code"))) {
                    orderPojo.setBpsFormalSynchro("1");
                    logger.info("同步BPS成功:{}", orderPojo.getCustomerPhone());
                } else {
                    logger.info("支付成功，正式单同步BPS失败");
                }
                hnslOrderService.updateById(orderPojo);
            } else {
                return fail("订单金额大于0不能同步订单");
            }
            return success("重新提交CRM成功");
        } catch (Exception e) {
            logger.error("订单同步cem异常：", e);
            return fail("订单同步cem异常：" + e);
        }
    }

    /**
     * 修改订单状态
     *
     * @param hnslOrder
     * @return
     */
    @OperationLog
    @PostMapping("/updateCrm")
    public ApiResult<?> updateCrm(@RequestBody HnslH5Order hnslOrder) {
        hnslOrder.setCrmSureSynchro("1");
        hnslOrderService.updateById(hnslOrder);
        return success();
    }

    /**
     * 支付记录同步到BPS
     */
    @OperationLog
    @PostMapping("/synchronizePayToBps")
    public ApiResult<?> synchronizePayToBps(@RequestBody HnslH5Order hnslOrder) {
        try {
            hnslOrderService.createPayRecordBPS(hnslOrder);
            return success();
        } catch (Exception e) {
            return fail("支付记录同步到BPS异常:" + e.getMessage());
        }
    }

    /**
     * 禁用订单
     *
     * @return
     */
    @OperationLog
    @PostMapping("/disableOrder")
    public ApiResult<?> outputUserJiFei(@RequestBody HnslH5Order hnslOrder) {
        logger.info("禁用用户入参:" + hnslOrder.toString());
        hnslOrderService.disableOrder(hnslOrder);
        return success();
    }
}
