package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslIntegral;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslIntegralParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 操作积分记录表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslIntegralMapper extends BaseMapper<HnslIntegral> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslIntegral>
     */
    List<HnslIntegral> selectPageRel(@Param("page") IPage<HnslIntegral> page,
                             @Param("param") HnslIntegralParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslIntegral> selectListRel(@Param("param") HnslIntegralParam param);

    /**
     * 查询积分条数（周）
     *
     * @param userId
     * @return
     */
    Long queryOrderWeekNum( Long userId);
    /**
     * 查询积分条数（月）
     *
     * @param userId
     * @return
     */
    Long queryOrderMonthNum(Long userId);
    /**
     * 查询积分条数（年）
     *
     * @param userId
     * @return
     */
    Long queryOrderYearNum(Long userId);

    /**
     * 根据条件查询积分记录
     *
     * @param HnslIntegral
     * @return
     */
    List<HnslIntegral> queryIntegral(HnslIntegral HnslIntegral);

    /**
     * 查询积分条数（周）
     *
     * @return
     */
    Long queryIntegralWeekNum( Long userId);
    /**
     * 查询积分条数（月）
     *
     * @return
     */
    Long queryIntegralMonthNum(Long userId);
    /**
     * 查询积分条数（年）
     *
     * @return
     */
    Long queryIntegralyearNum(Long userId);

    /**
     * 查询积分明细集合
     *
     * @return
     */
    List<HnslIntegral> queryIntegralDetailsList(Map<String, Object> map);

    /**
     * 查询积分明细导出
     * @return
     */
    List<Map<String, String>> queryExportIntegralList(Map<String, Object> map);

    List<HnslIntegral> queryList(Map<String, Object> map);

    int queryTotal(Map<String, Object> map);
}
