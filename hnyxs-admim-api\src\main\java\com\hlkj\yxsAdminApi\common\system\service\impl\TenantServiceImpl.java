package com.hlkj.yxsAdminApi.common.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hlkj.yxsAdminApi.common.system.mapper.TenantMapper;
import com.hlkj.yxsAdminApi.common.system.param.TenantParam;
import com.hlkj.yxsAdminApi.common.system.service.TenantService;
import com.hlkj.yxsAdminApi.common.system.entity.Tenant;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户Service实现
 *
 * <AUTHOR>
 * @since 2023-02-16 12:27:16
 */
@Service
public class TenantServiceImpl extends ServiceImpl<TenantMapper, Tenant> implements TenantService {

    @Override
    public PageResult<Tenant> pageRel(TenantParam param) {
        PageParam<Tenant, TenantParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<Tenant> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<Tenant> listRel(TenantParam param) {
        List<Tenant> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<Tenant, TenantParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public Tenant getByIdRel(Integer tenantId) {
        TenantParam param = new TenantParam();
        param.setTenantId(tenantId);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Override
    public List<Tenant> getLoginAllTenantList(String userName) {
        return baseMapper.getLoginAllTenantList(userName);
    }

}
