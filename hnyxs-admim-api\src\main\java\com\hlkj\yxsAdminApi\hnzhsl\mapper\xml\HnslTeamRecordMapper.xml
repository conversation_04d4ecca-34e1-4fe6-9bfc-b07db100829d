<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTeamRecordMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_team_record a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.teamCode != null">
                AND a.TEAM_CODE LIKE CONCAT('%', #{param.teamCode}, '%')
            </if>
            <if test="param.teamDate != null">
                AND a.TEAM_DATE LIKE CONCAT('%', #{param.teamDate}, '%')
            </if>
            <if test="param.orderType1 != null">
                AND a.ORDER_TYPE1 = #{param.orderType1}
            </if>
            <if test="param.orderType2 != null">
                AND a.ORDER_TYPE2 = #{param.orderType2}
            </if>
            <if test="param.orderType3 != null">
                AND a.ORDER_TYPE3 = #{param.orderType3}
            </if>
            <if test="param.orderSum != null">
                AND a.ORDER_SUM = #{param.orderSum}
            </if>
            <if test="param.teamIntegral != null">
                AND a.TEAM_INTEGRAL = #{param.teamIntegral}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamRecord">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamRecord">
        <include refid="selectSql"></include>
    </select>

</mapper>
