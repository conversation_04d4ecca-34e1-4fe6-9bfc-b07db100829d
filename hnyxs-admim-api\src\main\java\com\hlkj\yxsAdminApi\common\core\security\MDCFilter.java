package com.hlkj.yxsAdminApi.common.core.security;


import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * MDC 过滤处理
 * @ClassName MDCFilter
 * @Description TODO
 * @<NAME_EMAIL>
 * @Date 2023/4/20 0:00
 * @Version 1.0
 */
//@Component
public class MDCFilter extends OncePerRequestFilter implements Filter {

    private static Logger logger = LoggerFactory.getLogger(MDCFilter.class);

    @Autowired
    RedisUtil redis;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        //示例为一个固定的登陆用户,请直接修改代码
        User loginUser = null;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Object object = authentication.getPrincipal();
            if (object instanceof User) {
                loginUser=(User) object;
            }
        }
        if(loginUser != null){
            MDC.put("userName", loginUser.getUsername());
        }
        //为每一个请求创建一个ID，方便查找日志时可以根据ID查找出一个http请求所有相关日志
        MDC.put("uuid", StringUtils.remove(UUID.randomUUID().toString(),"-"));
        chain.doFilter(request, response);
    }
}