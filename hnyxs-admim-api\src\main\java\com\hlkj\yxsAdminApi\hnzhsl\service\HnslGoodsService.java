package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoods;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsParam;

import java.util.List;

/**
 * 商品表Service
 *
 * <AUTHOR>
 * @since 2023-04-21 10:50:16
 */
public interface HnslGoodsService extends IService<HnslGoods> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslGoods>
     */
    PageResult<HnslGoods> pageRel(HnslGoodsParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslGoods>
     */
    List<HnslGoods> listRel(HnslGoodsParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslGoods
     */
    HnslGoods getByIdRel(Integer id);

    public void uodateHnslGoodsEntity(HnslGoods hnslGoods);
}
