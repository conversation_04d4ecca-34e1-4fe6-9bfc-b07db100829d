package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 二维码黑名单
 * <AUTHOR>
 * @Since: 2024/10/21
 * @return: null
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslBlackList对象", description = "二维码黑名单")
public class HnslBlackList implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合伙人姓名")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty(value = "合伙人账号")
    @TableField("user_phone")
    private String userPhone;

    @ApiModelProperty(value = "所属地市编码")
    @TableField("city_code")
    private String cityCode;

    @ApiModelProperty(value = "学校编码")
    @TableField("school_code")
    private String schoolCode;

    @ApiModelProperty(value = "线下预约二维码（1. 开启 2.关闭）")
    @TableField("offline_qrcode")
    private Integer offlineQrcode;

    @ApiModelProperty(value = "线上预约二维码（1. 开启 2.关闭）")
    @TableField("online_qrcode")
    private Integer onlineQrcode;

    @ApiModelProperty(value = "线上激活二维码（1. 开启 2.关闭）")
    @TableField("online_active_qrcode")
    private Integer onlineActiveQrcode;

    @ApiModelProperty(value = "自助激活二维码（1. 开启 2.关闭）")
    @TableField("self_active_qrcode")
    private Integer selfActiveQrcode;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_user")
    private String updateUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("remarks")
    private String remarks;

    @TableField(exist = false)
    private String xlsName;
}
