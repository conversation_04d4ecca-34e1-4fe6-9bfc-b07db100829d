package com.hlkj.yxsAdminApi.hnzhsl.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 任务信息表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTask对象", description = "任务信息表")
public class HnslTask implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "任务类型")
    @TableField("TASK_TYPE")
    private String taskType;

    @ApiModelProperty(value = "开始时间")
    @TableField("STARTTIME")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date starttime;

    @ApiModelProperty(value = "结束时间")
    @TableField("ENDTIME")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endtime;

    @ApiModelProperty(value = "任务范围")
    @TableField("TASK_SCOPE")
    private String taskScope;

    @ApiModelProperty(value = "任务内容")
    @TableField("TASK_CONTENT")
    private String taskContent;

    @ApiModelProperty(value = "任务奖励")
    @TableField("TASK_AWARD")
    private Integer taskAward;

    @ApiModelProperty(value = "任务接取人(姓名)")
    @TableField("TASK_COUNT")
    private String taskCount;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "任务种类（1:信息采集 2.老用户升级 3.异网策反 4.单宽带融合 5.线上软文转发 6.Q群概览截图上传7.其他 8.服务截图上传）")
    @TableField("TASK_KIND")
    private String taskKind;

    @ApiModelProperty(value = "任务编码")
    @TableField("TASK_CODE")
    private String taskCode;

    @ApiModelProperty(value = "任务状态")
    @TableField("TASK_STATUS")
    private String taskStatus;

    @ApiModelProperty(value = "任务接取人(手机号码)")
    @TableField("TASK_COUNT_PHONE")
    private String taskCountPhone;

    @ApiModelProperty(value = "任务完成量（随类型变动）")
    @TableField("TASK_AMOUNT")
    private String taskAmount;

    @ApiModelProperty(value = "任务标题")
    @TableField("TASK_TITLE")
    private String taskTitle;

    @ApiModelProperty(value = "任务等级 （1:省级管理员 2：地市管理员 3：校园经理）")
    @TableField("TASK_LEVEL")
    private Integer taskLevel;

    @ApiModelProperty(value = "一级合伙人")
    @TableField(exist = false)
    private boolean onelevel;

    @ApiModelProperty(value = "二级合伙人")
    @TableField(exist = false)
    private boolean twolevel;

    @ApiModelProperty(value = "三级合伙人")
    @TableField(exist = false)
    private boolean threelevel;

    @ApiModelProperty(value = "校园经理")
    @TableField(exist = false)
    private boolean xlevel;

    @ApiModelProperty(value = "楼栋长")
    @TableField(exist = false)
    private boolean buildingLong;

    @ApiModelProperty(value = "姓名模糊查询")
    @TableField(exist = false)
    private String userData;

    @ApiModelProperty(value = "任务范围编码")
    @TableField(exist = false)
    private String taskScopeCode;
}
