package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslLoginWhite;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslLoginWhiteParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Description: 电子围栏管理
 * @Author: zwk
 * @Since: 2024/11/18
 * @return: null
 **/
public interface HnslLoginWhiteMapper extends BaseMapper<HnslLoginWhite> {

    /**
     * 分页查询
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslLoginWhite>
     */
    List<HnslLoginWhite> selectPageRel(@Param("page") IPage<HnslLoginWhite> page,
                                             @Param("param") HnslLoginWhiteParam param);

    List<Map<String, Object>> queryListTable(Map<String, Object> map);

    int updateHhr(HnslLoginWhite loginWhite);
}
