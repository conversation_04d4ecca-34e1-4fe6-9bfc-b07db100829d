package com.hlkj.yxsAdminApi.hnzhsl.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApply;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteApplyParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HnslWhiteApplyMapper extends BaseMapper<HnslWhiteApply> {

    List<HnslWhiteApply> selectPageRel(@Param("page") IPage<HnslWhiteApply> page,
                                       @Param("param") HnslWhiteApplyParam param);

    Integer saveApply(HnslWhiteApply hnslWhiteApply);

    HnslWhiteApply queryBatchCode(String batchCode);
}
