<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslGoodsMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.goodsNumber != null and param.goodsNumber != ''">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.goodsMianNumber != null and param.goodsMianNumber != ''">
                AND a.GOODS_MIAN_NUMBER LIKE CONCAT('%', #{param.goodsMianNumber}, '%')
            </if>
            <if test="param.goodsName != null and param.goodsName != ''">
                AND a.GOODS_NAME LIKE CONCAT('%', #{param.goodsName}, '%')
            </if>
            <if test="param.goodsType != null">
                AND a.GOODS_TYPE LIKE CONCAT('%', #{param.goodsType}, '%')
            </if>
            <if test="param.goodsChildtypeNumber != null">
                AND a.GOODS_CHILDTYPE_NUMBER LIKE CONCAT('%', #{param.goodsChildtypeNumber}, '%')
            </if>
            <if test="param.goodsPrice != null">
                AND a.GOODS_PRICE = #{param.goodsPrice}
            </if>
            <if test="param.goodsImg != null">
                AND a.GOODS_IMG LIKE CONCAT('%', #{param.goodsImg}, '%')
            </if>
            <if test="param.goodsDetailsImg != null">
                AND a.GOODS_DETAILS_IMG LIKE CONCAT('%', #{param.goodsDetailsImg}, '%')
            </if>
            <if test="param.prestore != null">
                AND a.PRESTORE LIKE CONCAT('%', #{param.prestore}, '%')
            </if>
            <if test="param.minPrice != null">
                AND a.MIN_PRICE LIKE CONCAT('%', #{param.minPrice}, '%')
            </if>
            <if test="param.productionPrice != null">
                AND a.PRODUCTION_PRICE LIKE CONCAT('%', #{param.productionPrice}, '%')
            </if>
            <if test="param.installPrice != null">
                AND a.INSTALL_PRICE LIKE CONCAT('%', #{param.installPrice}, '%')
            </if>
            <if test="param.goodIntegral != null">
                AND a.GOOD_INTEGRAL LIKE CONCAT('%', #{param.goodIntegral}, '%')
            </if>
            <if test="param.bandwidth != null">
                AND a.BANDWIDTH LIKE CONCAT('%', #{param.bandwidth}, '%')
            </if>
            <if test="param.bandwidthType != null">
                AND a.BANDWIDTH_TYPE LIKE CONCAT('%', #{param.bandwidthType}, '%')
            </if>
            <if test="param.cardType != null">
                AND a.CARD_TYPE LIKE CONCAT('%', #{param.cardType}, '%')
            </if>
            <if test="param.transactionNum != null">
                AND a.TRANSACTION_NUM LIKE CONCAT('%', #{param.transactionNum}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS LIKE CONCAT('%', #{param.status}, '%')
            </if>
            <if test="param.orderNumber != null">
                AND a.ORDER_NUMBER LIKE CONCAT('%', #{param.orderNumber}, '%')
            </if>
            <if test="param.remark != null">
                AND a.REMARK LIKE CONCAT('%', #{param.remark}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.cardGoodsName != null">
                AND a.CARD_GOODS_NAME LIKE CONCAT('%', #{param.cardGoodsName}, '%')
            </if>
            <if test="param.cardGoodsNumber != null">
                AND a.CARD_GOODS_NUMBER LIKE CONCAT('%', #{param.cardGoodsNumber}, '%')
            </if>
            <if test="param.bandwidthGoodsName != null">
                AND a.BANDWIDTH_GOODS_NAME LIKE CONCAT('%', #{param.bandwidthGoodsName}, '%')
            </if>
            <if test="param.bandwidthGoodsNumber != null">
                AND a.BANDWIDTH_GOODS_NUMBER LIKE CONCAT('%', #{param.bandwidthGoodsNumber}, '%')
            </if>
            <if test="param.itvNumber != null">
                AND a.ITV_NUMBER LIKE CONCAT('%', #{param.itvNumber}, '%')
            </if>
            <if test="param.saflType != null">
                AND a.SAFL_TYPE = #{param.saflType}
            </if>
            <if test="param.goodsDetalt != null">
                AND a.GOODS_DETALT LIKE CONCAT('%', #{param.goodsDetalt}, '%')
            </if>
            <if test="param.minAge != null">
                AND a.MIN_AGE = #{param.minAge}
            </if>
            <if test="param.maxAge != null">
                AND a.MAX_AGE = #{param.maxAge}
            </if>
            <if test="param.astrictGoods != null">
                AND a.ASTRICT_GOODS LIKE CONCAT('%', #{param.astrictGoods}, '%')
            </if>
            <if test="param.goodsExplain != null">
                AND a.GOODS_EXPLAIN LIKE CONCAT('%', #{param.goodsExplain}, '%')
            </if>
            <if test="param.goodsTypeShow != null">
                AND a.GOODS_TYPE_SHOW = #{param.goodsTypeShow}
            </if>
            <if test="param.goodsHtmlUrl != null">
                AND a.GOODS_HTML_URL LIKE CONCAT('%', #{param.goodsHtmlUrl}, '%')
            </if>
            <if test="param.goodsPackageType != null">
                AND a.GOODS_PACKAGE_TYPE = #{param.goodsPackageType}
            </if>
            <if test="param.cpsList != null">
                AND a.CPS_LIST LIKE CONCAT('%', #{param.cpsList}, '%')
            </if>
            <if test="param.certificateSwitch != null">
                AND a.CERTIFICATE_SWITCH = #{param.certificateSwitch}
            </if>
            <if test="param.goodsServiceUrl != null">
                AND a.GOODS_SERVICE_URL LIKE CONCAT('%', #{param.goodsServiceUrl}, '%')
            </if>
            <if test="param.createTimeStart != null">
                AND a.CREATED_DATE &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null">
                AND a.CREATED_DATE &lt;= #{param.createTimeEnd}
            </if>
        </where>
        order by a.id desc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoods">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoods">
        <include refid="selectSql"></include>
    </select>

</mapper>
