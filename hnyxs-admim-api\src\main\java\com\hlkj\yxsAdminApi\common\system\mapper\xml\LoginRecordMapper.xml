<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hlkj.yxsAdminApi.common.system.mapper.LoginRecordMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,
        b.user_id,
        b.nickname
        FROM hnyxs_sys_login_record a
        LEFT JOIN hnyxs_sys_user b ON a.username = b.username
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.username != null">
                AND a.username LIKE CONCAT('%', #{param.username}, '%')
            </if>
            <if test="param.os != null">
                AND a.os LIKE CONCAT('%', #{param.os}, '%')
            </if>
            <if test="param.device != null">
                AND a.device LIKE CONCAT('%', #{param.device}, '%')
            </if>
            <if test="param.browser != null">
                AND a.browser LIKE CONCAT('%', #{param.browser}, '%')
            </if>
            <if test="param.ip != null">
                AND a.ip LIKE CONCAT('%', #{param.ip}, '%')
            </if>
            <if test="param.loginType != null">
                AND a.login_type LIKE CONCAT('%', #{param.loginType}, '%')
            </if>
            <if test="param.comments != null">
                AND a.comments LIKE CONCAT('%', #{param.comments}, '%')
            </if>
            <if test="param.createTimeStart != null">
                AND a.create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null">
                AND a.create_time &lt;= #{param.createTimeEnd}
            </if>
            <if test="param.userId != null">
                AND b.user_id = #{param.userId}
            </if>
            <if test="param.nickname != null">
                AND b.nickname LIKE CONCAT('%', #{param.nickname}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.common.system.entity.LoginRecord">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.common.system.entity.LoginRecord">
        <include refid="selectSql"></include>
    </select>

</mapper>
