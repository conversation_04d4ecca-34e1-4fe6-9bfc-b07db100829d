package com.hlkj.yxsAdminApi.hnzhsl.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTeamMemberRecord对象", description = "")
public class HnslTeamMemberRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "团队记录月（2020-1）")
    @TableField("TEAM_DATE")
    private String teamDate;

    @ApiModelProperty(value = "合伙人编码")
    @TableField("USER_CODE")
    private String userCode;

    @ApiModelProperty(value = "团队编码")
    @TableField("TEAM_CODE")
    private String teamCode;

    @ApiModelProperty(value = "团队身份(1:成员 2：一级团队长 3:二级团队长 4：普通团队长 5:核心团队长 )")
    @TableField("TEAM_IDENTITY")
    private BigDecimal teamIdentity;

    @ApiModelProperty(value = "推荐人编码")
    @TableField("TEAM_REFERRER")
    private String teamReferrer;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private BigDecimal status;

    @ApiModelProperty(value = "团队地位(1:成员 2:团队长 )")
    @TableField("TEAM_IDENTITY_LEVEL")
    private BigDecimal teamIdentityLevel;

    @ApiModelProperty(value = "团队名称")
    @TableField("TEAM_NAME")
    private String teamName;

}
