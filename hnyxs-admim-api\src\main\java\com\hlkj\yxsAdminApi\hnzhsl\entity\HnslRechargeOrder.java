package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 充值订单表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslRechargeOrder对象", description = "充值订单表")
public class HnslRechargeOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "充值号码")
    @TableField("PHONE")
    private String phone;

    @ApiModelProperty(value = "充值金额（单位：人民币：元）如：100")
    @TableField("MONEY")
    private String money;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "时间戳如：1606813789486")
    @TableField("TIMESTAMP")
    private String timestamp;

    @ApiModelProperty(value = "请求流水号YYYYMMDDHH24MISS + 6位随机数，确保唯一性")
    @TableField("REQUESTID")
    private String requestid;

    @ApiModelProperty(value = "订单号")
    @TableField("ORDERID")
    private String orderid;

    @ApiModelProperty(value = "合伙人Id")
    @TableField("PARTNERID")
    private String partnerid;

    @ApiModelProperty(value = "渠道 固定为czdzqd")
    @TableField("CHANNEL")
    private String channel;

    @ApiModelProperty(value = "签名，MD5(content + key)key：私钥 （联调时提供）")
    @TableField("SIGN")
    private String sign;

    @ApiModelProperty(value = "随机字符串")
    @TableField("NONCESTR")
    private String noncestr;

    @ApiModelProperty(value = "合伙人下单学校编码")
    @TableField("SCHOOL_ID")
    private String schoolId;

    @ApiModelProperty(value = "合伙人等级")
    @TableField(exist = false)
    private String statusSf;

    @ApiModelProperty(value = "学校名称")
    @TableField(exist = false)
    private String schoolName;

    @ApiModelProperty(value = "合伙人姓名")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty(value = "地市编码")
    @TableField(exist = false)
    private String cityCode;
}
