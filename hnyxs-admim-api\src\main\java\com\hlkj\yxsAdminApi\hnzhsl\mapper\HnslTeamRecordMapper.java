package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamRecordParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队月记录表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslTeamRecordMapper extends BaseMapper<HnslTeamRecord> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTeamRecord>
     */
    List<HnslTeamRecord> selectPageRel(@Param("page") IPage<HnslTeamRecord> page,
                             @Param("param") HnslTeamRecordParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTeamRecord> selectListRel(@Param("param") HnslTeamRecordParam param);

}
