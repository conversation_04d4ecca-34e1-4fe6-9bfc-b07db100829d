package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskScopeService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskScope;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskScopeParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务范围表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "任务范围表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-task-scope")
public class HnslTaskScopeController extends BaseController {
    @Autowired
    private HnslTaskScopeService hnslTaskScopeService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:list')")
    @OperationLog
    @ApiOperation("分页查询任务范围表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTaskScope>> page(@RequestBody HnslTaskScopeParam param) {
        PageParam<HnslTaskScope, HnslTaskScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskScopeService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTaskScopeService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:list')")
    @OperationLog
    @ApiOperation("查询全部任务范围表")
    @PostMapping("/list")
    public ApiResult<List<HnslTaskScope>> list(@RequestBody HnslTaskScopeParam param) {
        PageParam<HnslTaskScope, HnslTaskScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskScopeService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTaskScopeService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:list')")
    @OperationLog
    @ApiOperation("根据id查询任务范围表")
    @GetMapping("/{id}")
    public ApiResult<HnslTaskScope> get(@PathVariable("id") Integer id) {
        return success(hnslTaskScopeService.getById(id));
        // 使用关联查询
        //return success(hnslTaskScopeService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:save')")
    @OperationLog
    @ApiOperation("添加任务范围表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTaskScope hnslTaskScope) {
        if (hnslTaskScopeService.save(hnslTaskScope)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:update')")
    @OperationLog
    @ApiOperation("修改任务范围表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTaskScope hnslTaskScope) {
        if (hnslTaskScopeService.updateById(hnslTaskScope)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:remove')")
    @OperationLog
    @ApiOperation("删除任务范围表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTaskScopeService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:save')")
    @OperationLog
    @ApiOperation("批量添加任务范围表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTaskScope> list) {
        if (hnslTaskScopeService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:update')")
    @OperationLog
    @ApiOperation("批量修改任务范围表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTaskScope> batchParam) {
        if (batchParam.update(hnslTaskScopeService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskScope:remove')")
    @OperationLog
    @ApiOperation("批量删除任务范围表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTaskScopeService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
