package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserOrdersDataService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrdersData;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserOrdersDataParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-user-orders-data")
public class HnslUserOrdersDataController extends BaseController {
    @Autowired
    private HnslUserOrdersDataService hnslUserOrdersDataService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:list')")
    @OperationLog
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserOrdersData>> page(@RequestBody HnslUserOrdersDataParam param) {
        PageParam<HnslUserOrdersData, HnslUserOrdersDataParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserOrdersDataService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslUserOrdersDataService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:list')")
    @OperationLog
    @ApiOperation("查询全部")
    @PostMapping("/list")
    public ApiResult<List<HnslUserOrdersData>> list(@RequestBody HnslUserOrdersDataParam param) {
        PageParam<HnslUserOrdersData, HnslUserOrdersDataParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserOrdersDataService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslUserOrdersDataService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:list')")
    @OperationLog
    @ApiOperation("根据id查询")
    @GetMapping("/{id}")
    public ApiResult<HnslUserOrdersData> get(@PathVariable("id") Integer id) {
        return success(hnslUserOrdersDataService.getById(id));
        // 使用关联查询
        //return success(hnslUserOrdersDataService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:save')")
    @OperationLog
    @ApiOperation("添加")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserOrdersData hnslUserOrdersData) {
        if (hnslUserOrdersDataService.save(hnslUserOrdersData)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:update')")
    @OperationLog
    @ApiOperation("修改")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserOrdersData hnslUserOrdersData) {
        if (hnslUserOrdersDataService.updateById(hnslUserOrdersData)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:remove')")
    @OperationLog
    @ApiOperation("删除")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserOrdersDataService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:save')")
    @OperationLog
    @ApiOperation("批量添加")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserOrdersData> list) {
        if (hnslUserOrdersDataService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:update')")
    @OperationLog
    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUserOrdersData> batchParam) {
        if (batchParam.update(hnslUserOrdersDataService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrdersData:remove')")
    @OperationLog
    @ApiOperation("批量删除")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserOrdersDataService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
