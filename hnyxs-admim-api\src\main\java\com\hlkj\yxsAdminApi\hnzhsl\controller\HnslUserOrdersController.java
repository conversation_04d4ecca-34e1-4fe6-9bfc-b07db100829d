package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserOrdersService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserOrders;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserOrdersParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-user-orders")
public class HnslUserOrdersController extends BaseController {
    @Autowired
    private HnslUserOrdersService hnslUserOrdersService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:list')")
    @OperationLog
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserOrders>> page(@RequestBody HnslUserOrdersParam param) {
        PageParam<HnslUserOrders, HnslUserOrdersParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserOrdersService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslUserOrdersService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:list')")
    @OperationLog
    @ApiOperation("查询全部")
    @PostMapping("/list")
    public ApiResult<List<HnslUserOrders>> list(@RequestBody HnslUserOrdersParam param) {
        PageParam<HnslUserOrders, HnslUserOrdersParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserOrdersService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslUserOrdersService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:list')")
    @OperationLog
    @ApiOperation("根据id查询")
    @GetMapping("/{id}")
    public ApiResult<HnslUserOrders> get(@PathVariable("id") Integer id) {
        return success(hnslUserOrdersService.getById(id));
        // 使用关联查询
        //return success(hnslUserOrdersService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:save')")
    @OperationLog
    @ApiOperation("添加")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserOrders hnslUserOrders) {
        if (hnslUserOrdersService.save(hnslUserOrders)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:update')")
    @OperationLog
    @ApiOperation("修改")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserOrders hnslUserOrders) {
        if (hnslUserOrdersService.updateById(hnslUserOrders)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:remove')")
    @OperationLog
    @ApiOperation("删除")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserOrdersService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:save')")
    @OperationLog
    @ApiOperation("批量添加")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserOrders> list) {
        if (hnslUserOrdersService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:update')")
    @OperationLog
    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUserOrders> batchParam) {
        if (batchParam.update(hnslUserOrdersService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserOrders:remove')")
    @OperationLog
    @ApiOperation("批量删除")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserOrdersService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
