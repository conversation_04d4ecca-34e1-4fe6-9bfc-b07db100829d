package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsBelong;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsBelongParam;

import java.util.List;

/**
 * 商品学校关系表Service
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
public interface HnslGoodsBelongService extends IService<HnslGoodsBelong> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslGoodsBelong>
     */
    PageResult<HnslGoodsBelong> pageRel(HnslGoodsBelongParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslGoodsBelong>
     */
    List<HnslGoodsBelong> listRel(HnslGoodsBelongParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslGoodsBelong
     */
    HnslGoodsBelong getByIdRel(Integer id);

    /**
     * 自定义修改
     * @param goodsBelong
     */
    public void updateBygoodsNumber(HnslGoodsBelong goodsBelong);
}
