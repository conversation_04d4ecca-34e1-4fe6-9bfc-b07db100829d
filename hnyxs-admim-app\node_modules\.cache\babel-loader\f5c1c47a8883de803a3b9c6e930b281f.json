{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n      _c = _vm._self._c;\n\n  return _c(\"div\", {\n    staticClass: \"ele-body\"\n  }, [_c(\"el-card\", {\n    attrs: {\n      shadow: \"never\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"config-header\"\n  }, [_c(\"h2\", [_vm._v(\"H5即时受理-关联配置管理\")]), _c(\"div\", {\n    staticClass: \"config-desc\"\n  }, [_c(\"p\", [_vm._v(\"在此页面可以配置地市、模块分类、模块、商品属性类别、商品标签之间的关联关系\")])])]), _c(\"div\", {\n    staticClass: \"config-operation\"\n  }, [_c(\"el-steps\", {\n    attrs: {\n      active: _vm.currentStep,\n      \"finish-status\": \"success\",\n      simple: \"\"\n    }\n  }, [_c(\"el-step\", {\n    attrs: {\n      title: \"选择地市\",\n      icon: \"el-icon-map-location\"\n    }\n  }), _c(\"el-step\", {\n    attrs: {\n      title: \"配置模块分类\",\n      icon: \"el-icon-s-grid\"\n    }\n  }), _c(\"el-step\", {\n    attrs: {\n      title: \"配置模块\",\n      icon: \"el-icon-s-platform\"\n    }\n  }), _c(\"el-step\", {\n    attrs: {\n      title: \"配置商品属性和标签\",\n      icon: \"el-icon-s-goods\"\n    }\n  })], 1)], 1), _c(\"div\", {\n    staticClass: \"config-content\"\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.currentStep === 1,\n      expression: \"currentStep === 1\"\n    }],\n    staticClass: \"step-content\"\n  }, [_c(\"div\", {\n    staticClass: \"step-title\"\n  }, [_vm._v(\"第一步: 选择需要配置的地市\")]), _c(\"div\", {\n    staticClass: \"city-selection\"\n  }, [_c(\"div\", {\n    staticClass: \"city-select-header\"\n  }, [_c(\"el-checkbox\", {\n    attrs: {\n      indeterminate: _vm.isIndeterminate\n    },\n    on: {\n      change: _vm.handleCheckAllCitiesChange\n    },\n    model: {\n      value: _vm.selectAllCities,\n      callback: function ($$v) {\n        _vm.selectAllCities = $$v;\n      },\n      expression: \"selectAllCities\"\n    }\n  }, [_vm._v(\"全选\")])], 1), _c(\"div\", {\n    staticClass: \"city-checkbox-group\"\n  }, [_c(\"el-checkbox-group\", {\n    on: {\n      change: _vm.handleCitiesChange\n    },\n    model: {\n      value: _vm.selectedCities,\n      callback: function ($$v) {\n        _vm.selectedCities = $$v;\n      },\n      expression: \"selectedCities\"\n    }\n  }, _vm._l(_vm.cities, function (city) {\n    return _c(\"el-checkbox\", {\n      key: city.cityCode,\n      attrs: {\n        label: city.cityCode\n      }\n    }, [_vm._v(\" \" + _vm._s(city.cityName) + \" \")]);\n  }), 1)], 1)]), _c(\"div\", {\n    staticClass: \"step-actions\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      disabled: _vm.selectedCities.length === 0\n    },\n    on: {\n      click: _vm.nextStep\n    }\n  }, [_vm._v(\"下一步\")])], 1)]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.currentStep === 2,\n      expression: \"currentStep === 2\"\n    }],\n    staticClass: \"step-content\"\n  }, [_c(\"div\", {\n    staticClass: \"step-title\"\n  }, [_vm._v(\"第二步: 为选中的地市配置模块分类\")]), _c(\"div\", {\n    staticClass: \"city-groups\"\n  }, [_c(\"el-collapse\", {\n    model: {\n      value: _vm.expandedCities,\n      callback: function ($$v) {\n        _vm.expandedCities = $$v;\n      },\n      expression: \"expandedCities\"\n    }\n  }, _vm._l(_vm.selectedCities, function (cityCode) {\n    return _c(\"el-collapse-item\", {\n      key: cityCode,\n      attrs: {\n        name: cityCode\n      }\n    }, [_c(\"template\", {\n      slot: \"title\"\n    }, [_c(\"div\", {\n      staticClass: \"group-header\"\n    }, [_c(\"span\", {\n      staticClass: \"group-title\"\n    }, [_vm._v(_vm._s(_vm.getCityName(cityCode)))]), _c(\"el-tag\", {\n      staticClass: \"count-tag\",\n      attrs: {\n        size: \"mini\",\n        type: \"info\"\n      }\n    }, [_vm._v(\" 已选\" + _vm._s(_vm.getCityCategoryCount(cityCode)) + \"项 \")])], 1)]), _c(\"div\", {\n      staticClass: \"category-selection\"\n    }, [_c(\"div\", {\n      staticClass: \"category-transfer\"\n    }, [_c(\"el-transfer\", {\n      attrs: {\n        value: _vm.cityCategories[cityCode] ? _vm.cityCategories[cityCode] : [],\n        data: _vm.categoryData,\n        titles: [\"可选模块分类\", \"已选模块分类\"],\n        \"button-texts\": [\"移除\", \"添加\"],\n        format: {\n          noChecked: \"${total}\",\n          hasChecked: \"${checked}/${total}\"\n        }\n      },\n      on: {\n        input: val => _vm.updateCityCategories(cityCode, val),\n        remove: function ($event) {\n          return _vm.handleRemoveCategories(cityCode, $event, \"left\", $event);\n        }\n      },\n      scopedSlots: _vm._u([{\n        key: \"default\",\n        fn: function ({\n          option\n        }) {\n          return _c(\"span\", {\n            staticClass: \"transfer-item\"\n          }, [_vm._v(\" \" + _vm._s(option.label) + \" \")]);\n        }\n      }], null, true)\n    }), _c(\"div\", {\n      staticClass: \"transfer-tip\"\n    }, [_c(\"el-alert\", {\n      attrs: {\n        title: \"提示：从右侧移除项目将同时删除该地市与模块分类的关联关系，请谨慎操作\",\n        type: \"warning\",\n        closable: false,\n        \"show-icon\": \"\"\n      }\n    })], 1)], 1)])], 2);\n  }), 1)], 1), _c(\"div\", {\n    staticClass: \"step-actions\"\n  }, [_c(\"el-button\", {\n    on: {\n      click: _vm.prevStep\n    }\n  }, [_vm._v(\"上一步\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      disabled: !_vm.hasCategorySelected\n    },\n    on: {\n      click: function ($event) {\n        _vm.syncAllCategories();\n\n        _vm.nextStep();\n      }\n    }\n  }, [_vm._v(\"下一步\")])], 1)]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.currentStep === 3,\n      expression: \"currentStep === 3\"\n    }],\n    staticClass: \"step-content\"\n  }, [_c(\"div\", {\n    staticClass: \"step-title\"\n  }, [_vm._v(\"第三步: 为选中的地市和模块分类配置模块\")]), _c(\"div\", {\n    staticClass: \"city-category-groups\"\n  }, [_c(\"el-collapse\", {\n    model: {\n      value: _vm.expandedCityCategories,\n      callback: function ($$v) {\n        _vm.expandedCityCategories = $$v;\n      },\n      expression: \"expandedCityCategories\"\n    }\n  }, [_vm._l(_vm.selectedCities, function (cityCode) {\n    return [(_vm.cityCategories[cityCode] || []).length > 0 ? _vm._l(_vm.cityCategories[cityCode], function (categoryId) {\n      return _c(\"el-collapse-item\", {\n        key: `${cityCode}-${categoryId}`,\n        attrs: {\n          name: `${cityCode}-${categoryId}`\n        }\n      }, [_c(\"template\", {\n        slot: \"title\"\n      }, [_c(\"div\", {\n        staticClass: \"group-header\"\n      }, [_c(\"span\", {\n        staticClass: \"group-title\"\n      }, [_vm._v(_vm._s(_vm.getCityName(cityCode)) + \" - \" + _vm._s(_vm.getCategoryName(categoryId)))]), _c(\"el-tag\", {\n        staticClass: \"count-tag\",\n        attrs: {\n          size: \"mini\",\n          type: \"info\"\n        }\n      }, [_vm._v(\" 已选\" + _vm._s(_vm.getCityCategoryModuleCount(cityCode, categoryId)) + \"项 \")])], 1)]), _c(\"div\", {\n        staticClass: \"module-selection\"\n      }, [_c(\"div\", {\n        staticClass: \"module-transfer\"\n      }, [_c(\"el-transfer\", {\n        attrs: {\n          value: _vm.getCityModulesArray(cityCode, categoryId),\n          data: _vm.moduleData,\n          titles: [\"可选模块\", \"已选模块\"],\n          \"button-texts\": [\"移除\", \"添加\"],\n          format: {\n            noChecked: \"${total}\",\n            hasChecked: \"${checked}/${total}\"\n          }\n        },\n        on: {\n          input: val => _vm.updateCityModules(cityCode, categoryId, val),\n          remove: function ($event) {\n            return _vm.handleRemoveModules(cityCode, categoryId, $event, \"left\", $event);\n          }\n        },\n        scopedSlots: _vm._u([{\n          key: \"default\",\n          fn: function ({\n            option\n          }) {\n            return _c(\"span\", {\n              staticClass: \"transfer-item\"\n            }, [_c(\"span\", [_vm._v(_vm._s(option.label))])]);\n          }\n        }], null, true)\n      }), _c(\"div\", {\n        staticClass: \"transfer-tip\"\n      }, [_c(\"el-alert\", {\n        attrs: {\n          title: \"提示：从右侧移除项目将同时删除该地市与模块的关联关系，请谨慎操作\",\n          type: \"warning\",\n          closable: false,\n          \"show-icon\": \"\"\n        }\n      })], 1)], 1)])], 2);\n    }) : _vm._e()];\n  })], 2)], 1), _c(\"div\", {\n    staticClass: \"step-actions\"\n  }, [_c(\"el-button\", {\n    on: {\n      click: _vm.prevStep\n    }\n  }, [_vm._v(\"上一步\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      disabled: !_vm.hasModuleSelected\n    },\n    on: {\n      click: function ($event) {\n        _vm.syncAllModules();\n\n        _vm.nextStep();\n      }\n    }\n  }, [_vm._v(\"下一步\")])], 1)]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.currentStep === 4,\n      expression: \"currentStep === 4\"\n    }],\n    staticClass: \"step-content\"\n  }, [_c(\"div\", {\n    staticClass: \"step-title\"\n  }, [_vm._v(\"第四步: 为选中的模块配置商品属性类别和标签\")]), _c(\"div\", {\n    staticClass: \"city-category-module-groups\"\n  }, [_c(\"el-collapse\", {\n    model: {\n      value: _vm.expandedCityModules,\n      callback: function ($$v) {\n        _vm.expandedCityModules = $$v;\n      },\n      expression: \"expandedCityModules\"\n    }\n  }, [_vm._l(_vm.selectedCities, function (cityCode) {\n    return [(_vm.cityCategories[cityCode] || []).length > 0 ? [_vm._l(_vm.cityCategories[cityCode], function (categoryId) {\n      return [_vm.getCityModulesArray(cityCode, categoryId).length > 0 ? _vm._l(_vm.getCityModulesArray(cityCode, categoryId), function (moduleId) {\n        return _c(\"el-collapse-item\", {\n          key: `${cityCode}-${categoryId}-${moduleId}`,\n          attrs: {\n            name: `${cityCode}-${categoryId}-${moduleId}`\n          }\n        }, [_c(\"template\", {\n          slot: \"title\"\n        }, [_c(\"div\", {\n          staticClass: \"group-header\"\n        }, [_c(\"span\", {\n          staticClass: \"group-title\"\n        }, [_vm._v(_vm._s(_vm.getCityName(cityCode)) + \" - \" + _vm._s(_vm.getCategoryName(categoryId)) + \" - \" + _vm._s(_vm.getModuleName(moduleId)))]), _c(\"div\", [_c(\"el-tag\", {\n          staticClass: \"count-tag\",\n          attrs: {\n            size: \"mini\",\n            type: \"success\"\n          }\n        }, [_vm._v(\" 属性\" + _vm._s(_vm.getModuleAttributeCount(cityCode, categoryId, moduleId)) + \"项 \")]), _c(\"el-tag\", {\n          staticClass: \"count-tag\",\n          attrs: {\n            size: \"mini\",\n            type: \"warning\"\n          }\n        }, [_vm._v(\" 标签\" + _vm._s(_vm.getModuleTagCount(cityCode, categoryId, moduleId)) + \"项 \")])], 1)])]), _c(\"div\", {\n          staticClass: \"attribute-tag-selection\"\n        }, [_c(\"el-tabs\", {\n          staticClass: \"attribute-tag-tabs\",\n          attrs: {\n            value: _vm.getModuleTabsValue(cityCode, categoryId, moduleId),\n            type: \"border-card\"\n          },\n          on: {\n            input: val => _vm.updateModuleTab(cityCode, categoryId, moduleId, val)\n          }\n        }, [_c(\"el-tab-pane\", {\n          attrs: {\n            label: \"商品属性类别配置\",\n            name: \"attribute\"\n          }\n        }, [_c(\"div\", {\n          staticClass: \"drag-sort-tip\"\n        }, [_c(\"i\", {\n          staticClass: \"el-icon-sort\"\n        }), _c(\"span\", [_vm._v(\"在右侧已选择区域，您可以通过拖拽 \"), _c(\"i\", {\n          staticClass: \"el-icon-rank\"\n        }), _vm._v(\" 图标调整属性类别的显示顺序\")])]), _c(\"div\", {\n          staticClass: \"attribute-transfer\"\n        }, [_c(\"el-transfer\", {\n          ref: \"sortTransfer\",\n          refInFor: true,\n          attrs: {\n            \"target-order\": \"unshift\",\n            value: _vm.getModuleAttributesArray(cityCode, categoryId, moduleId),\n            data: _vm.attributeTypeData,\n            titles: [\"可选商品属性类别\", \"已选商品属性类别\"],\n            \"button-texts\": [\"移除\", \"添加\"],\n            format: {\n              noChecked: \"${total}\",\n              hasChecked: \"${checked}/${total}\"\n            }\n          },\n          on: {\n            input: val => _vm.updateModuleAttributes(cityCode, categoryId, moduleId, val),\n            remove: function ($event) {\n              return _vm.handleRemoveAttributeTypes(cityCode, categoryId, moduleId, $event, \"left\", $event);\n            }\n          },\n          scopedSlots: _vm._u([{\n            key: \"default\",\n            fn: function ({\n              option\n            }) {\n              return _c(\"span\", {\n                staticClass: \"transfer-item draggable-item\",\n                attrs: {\n                  draggable: \"!option.disabled\",\n                  title: \"拖拽可调整排序：\" + option.label\n                },\n                on: {\n                  dragstart: function ($event) {\n                    return _vm.dragStart($event, option);\n                  },\n                  dragover: function ($event) {\n                    $event.preventDefault();\n                  },\n                  dragenter: function ($event) {\n                    $event.preventDefault();\n                  },\n                  drop: function ($event) {\n                    return _vm.drop($event, option, cityCode, categoryId, moduleId);\n                  }\n                }\n              }, [_c(\"i\", {\n                staticClass: \"el-icon-rank drag-handle\"\n              }), _vm._v(\" \" + _vm._s(option.label) + \" \")]);\n            }\n          }], null, true)\n        })], 1)]), _c(\"el-tab-pane\", {\n          attrs: {\n            label: \"商品标签配置\",\n            name: \"tag\"\n          }\n        }, [_c(\"div\", {\n          staticClass: \"drag-sort-tip\"\n        }, [_c(\"i\", {\n          staticClass: \"el-icon-sort\"\n        }), _c(\"span\", [_vm._v(\"在右侧已选择区域，您可以通过拖拽 \"), _c(\"i\", {\n          staticClass: \"el-icon-rank\"\n        }), _vm._v(\" 图标调整标签的显示顺序\")])]), _c(\"div\", {\n          staticClass: \"tag-transfer\"\n        }, [_c(\"el-transfer\", {\n          attrs: {\n            value: _vm.getModuleTagsArray(cityCode, categoryId, moduleId),\n            \"target-order\": \"unshift\",\n            data: _vm.tagData,\n            titles: [\"可选商品标签\", \"已选商品标签\"],\n            \"button-texts\": [\"移除\", \"添加\"],\n            format: {\n              noChecked: \"${total}\",\n              hasChecked: \"${checked}/${total}\"\n            }\n          },\n          on: {\n            input: val => _vm.updateModuleTags(cityCode, categoryId, moduleId, val),\n            remove: function ($event) {\n              return _vm.handleRemoveTags(cityCode, categoryId, moduleId, $event, \"left\", $event);\n            }\n          },\n          scopedSlots: _vm._u([{\n            key: \"default\",\n            fn: function ({\n              option\n            }) {\n              return _c(\"span\", {\n                staticClass: \"transfer-item draggable-item\",\n                attrs: {\n                  draggable: \"!option.disabled\",\n                  title: \"拖拽可调整排序：\" + option.label\n                },\n                on: {\n                  dragstart: function ($event) {\n                    return _vm.dragStart2($event, option);\n                  },\n                  dragover: function ($event) {\n                    $event.preventDefault();\n                  },\n                  dragenter: function ($event) {\n                    $event.preventDefault();\n                  },\n                  drop: function ($event) {\n                    return _vm.drop2($event, option, cityCode, categoryId, moduleId);\n                  }\n                }\n              }, [_c(\"i\", {\n                staticClass: \"el-icon-rank drag-handle\"\n              }), _vm._v(\" \" + _vm._s(option.label) + \" \")]);\n            }\n          }], null, true)\n        }), _c(\"div\", {\n          staticClass: \"transfer-tip\"\n        }, [_c(\"el-alert\", {\n          attrs: {\n            title: \"提示：从右侧移除项目将同时删除该模块与商品标签的关联关系，请谨慎操作\",\n            type: \"warning\",\n            closable: false,\n            \"show-icon\": \"\"\n          }\n        })], 1)], 1)])], 1)], 1)], 2);\n      }) : _vm._e()];\n    })] : _vm._e()];\n  })], 2)], 1), _c(\"div\", {\n    staticClass: \"step-actions\"\n  }, [_c(\"el-button\", {\n    on: {\n      click: _vm.prevStep\n    }\n  }, [_vm._v(\"上一步\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: function ($event) {\n        _vm.syncAllAttributesAndTags();\n\n        _vm.saveConfiguration();\n      }\n    }\n  }, [_vm._v(\"保存配置\")])], 1)])])])], 1);\n};\n\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "shadow", "_v", "active", "currentStep", "simple", "title", "icon", "directives", "name", "rawName", "value", "expression", "indeterminate", "isIndeterminate", "on", "change", "handleCheckAllCitiesChange", "model", "selectAllCities", "callback", "$$v", "handleCitiesChange", "selectedCities", "_l", "cities", "city", "key", "cityCode", "label", "_s", "cityName", "type", "disabled", "length", "click", "nextStep", "expandedCities", "slot", "getCityName", "size", "getCityCategoryCount", "cityCategories", "data", "categoryData", "titles", "format", "noChecked", "hasChecked", "input", "val", "updateCityCategories", "remove", "$event", "handleRemoveCategories", "scopedSlots", "_u", "fn", "option", "closable", "prevStep", "hasCategorySelected", "syncAllCategories", "expandedCityCategories", "categoryId", "getCategoryName", "getCityCategoryModuleCount", "getCityModulesArray", "moduleData", "updateCityModules", "handleRemoveModules", "_e", "hasModuleSelected", "syncAllModules", "expandedCityModules", "moduleId", "getModuleName", "getModuleAttributeCount", "getModuleTagCount", "getModuleTabsValue", "updateModuleTab", "ref", "refInFor", "getModuleAttributesArray", "attributeTypeData", "updateModuleAttributes", "handleRemoveAttributeTypes", "draggable", "dragstart", "dragStart", "dragover", "preventDefault", "dragenter", "drop", "getModuleTagsArray", "tagData", "updateModuleTags", "handleRemoveTags", "dragStart2", "drop2", "syncAllAttributesAndTags", "saveConfiguration", "staticRenderFns", "_withStripped"], "sources": ["D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app/src/views/hnzsxH5/configRelation/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"ele-body\" },\n    [\n      _c(\"el-card\", { attrs: { shadow: \"never\" } }, [\n        _c(\"div\", { staticClass: \"config-header\" }, [\n          _c(\"h2\", [_vm._v(\"H5即时受理-关联配置管理\")]),\n          _c(\"div\", { staticClass: \"config-desc\" }, [\n            _c(\"p\", [\n              _vm._v(\n                \"在此页面可以配置地市、模块分类、模块、商品属性类别、商品标签之间的关联关系\"\n              ),\n            ]),\n          ]),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"config-operation\" },\n          [\n            _c(\n              \"el-steps\",\n              {\n                attrs: {\n                  active: _vm.currentStep,\n                  \"finish-status\": \"success\",\n                  simple: \"\",\n                },\n              },\n              [\n                _c(\"el-step\", {\n                  attrs: { title: \"选择地市\", icon: \"el-icon-map-location\" },\n                }),\n                _c(\"el-step\", {\n                  attrs: { title: \"配置模块分类\", icon: \"el-icon-s-grid\" },\n                }),\n                _c(\"el-step\", {\n                  attrs: { title: \"配置模块\", icon: \"el-icon-s-platform\" },\n                }),\n                _c(\"el-step\", {\n                  attrs: {\n                    title: \"配置商品属性和标签\",\n                    icon: \"el-icon-s-goods\",\n                  },\n                }),\n              ],\n              1\n            ),\n          ],\n          1\n        ),\n        _c(\"div\", { staticClass: \"config-content\" }, [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentStep === 1,\n                  expression: \"currentStep === 1\",\n                },\n              ],\n              staticClass: \"step-content\",\n            },\n            [\n              _c(\"div\", { staticClass: \"step-title\" }, [\n                _vm._v(\"第一步: 选择需要配置的地市\"),\n              ]),\n              _c(\"div\", { staticClass: \"city-selection\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"city-select-header\" },\n                  [\n                    _c(\n                      \"el-checkbox\",\n                      {\n                        attrs: { indeterminate: _vm.isIndeterminate },\n                        on: { change: _vm.handleCheckAllCitiesChange },\n                        model: {\n                          value: _vm.selectAllCities,\n                          callback: function ($$v) {\n                            _vm.selectAllCities = $$v\n                          },\n                          expression: \"selectAllCities\",\n                        },\n                      },\n                      [_vm._v(\"全选\")]\n                    ),\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"city-checkbox-group\" },\n                  [\n                    _c(\n                      \"el-checkbox-group\",\n                      {\n                        on: { change: _vm.handleCitiesChange },\n                        model: {\n                          value: _vm.selectedCities,\n                          callback: function ($$v) {\n                            _vm.selectedCities = $$v\n                          },\n                          expression: \"selectedCities\",\n                        },\n                      },\n                      _vm._l(_vm.cities, function (city) {\n                        return _c(\n                          \"el-checkbox\",\n                          {\n                            key: city.cityCode,\n                            attrs: { label: city.cityCode },\n                          },\n                          [_vm._v(\" \" + _vm._s(city.cityName) + \" \")]\n                        )\n                      }),\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"step-actions\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        disabled: _vm.selectedCities.length === 0,\n                      },\n                      on: { click: _vm.nextStep },\n                    },\n                    [_vm._v(\"下一步\")]\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentStep === 2,\n                  expression: \"currentStep === 2\",\n                },\n              ],\n              staticClass: \"step-content\",\n            },\n            [\n              _c(\"div\", { staticClass: \"step-title\" }, [\n                _vm._v(\"第二步: 为选中的地市配置模块分类\"),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"city-groups\" },\n                [\n                  _c(\n                    \"el-collapse\",\n                    {\n                      model: {\n                        value: _vm.expandedCities,\n                        callback: function ($$v) {\n                          _vm.expandedCities = $$v\n                        },\n                        expression: \"expandedCities\",\n                      },\n                    },\n                    _vm._l(_vm.selectedCities, function (cityCode) {\n                      return _c(\n                        \"el-collapse-item\",\n                        { key: cityCode, attrs: { name: cityCode } },\n                        [\n                          _c(\"template\", { slot: \"title\" }, [\n                            _c(\n                              \"div\",\n                              { staticClass: \"group-header\" },\n                              [\n                                _c(\"span\", { staticClass: \"group-title\" }, [\n                                  _vm._v(_vm._s(_vm.getCityName(cityCode))),\n                                ]),\n                                _c(\n                                  \"el-tag\",\n                                  {\n                                    staticClass: \"count-tag\",\n                                    attrs: { size: \"mini\", type: \"info\" },\n                                  },\n                                  [\n                                    _vm._v(\n                                      \" 已选\" +\n                                        _vm._s(\n                                          _vm.getCityCategoryCount(cityCode)\n                                        ) +\n                                        \"项 \"\n                                    ),\n                                  ]\n                                ),\n                              ],\n                              1\n                            ),\n                          ]),\n                          _c(\"div\", { staticClass: \"category-selection\" }, [\n                            _c(\n                              \"div\",\n                              { staticClass: \"category-transfer\" },\n                              [\n                                _c(\"el-transfer\", {\n                                  attrs: {\n                                    value: _vm.cityCategories[cityCode]\n                                      ? _vm.cityCategories[cityCode]\n                                      : [],\n                                    data: _vm.categoryData,\n                                    titles: [\"可选模块分类\", \"已选模块分类\"],\n                                    \"button-texts\": [\"移除\", \"添加\"],\n                                    format: {\n                                      noChecked: \"${total}\",\n                                      hasChecked: \"${checked}/${total}\",\n                                    },\n                                  },\n                                  on: {\n                                    input: (val) =>\n                                      _vm.updateCityCategories(cityCode, val),\n                                    remove: function ($event) {\n                                      return _vm.handleRemoveCategories(\n                                        cityCode,\n                                        $event,\n                                        \"left\",\n                                        $event\n                                      )\n                                    },\n                                  },\n                                  scopedSlots: _vm._u(\n                                    [\n                                      {\n                                        key: \"default\",\n                                        fn: function ({ option }) {\n                                          return _c(\n                                            \"span\",\n                                            { staticClass: \"transfer-item\" },\n                                            [\n                                              _vm._v(\n                                                \" \" + _vm._s(option.label) + \" \"\n                                              ),\n                                            ]\n                                          )\n                                        },\n                                      },\n                                    ],\n                                    null,\n                                    true\n                                  ),\n                                }),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"transfer-tip\" },\n                                  [\n                                    _c(\"el-alert\", {\n                                      attrs: {\n                                        title:\n                                          \"提示：从右侧移除项目将同时删除该地市与模块分类的关联关系，请谨慎操作\",\n                                        type: \"warning\",\n                                        closable: false,\n                                        \"show-icon\": \"\",\n                                      },\n                                    }),\n                                  ],\n                                  1\n                                ),\n                              ],\n                              1\n                            ),\n                          ]),\n                        ],\n                        2\n                      )\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"step-actions\" },\n                [\n                  _c(\"el-button\", { on: { click: _vm.prevStep } }, [\n                    _vm._v(\"上一步\"),\n                  ]),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        disabled: !_vm.hasCategorySelected,\n                      },\n                      on: {\n                        click: function ($event) {\n                          _vm.syncAllCategories()\n                          _vm.nextStep()\n                        },\n                      },\n                    },\n                    [_vm._v(\"下一步\")]\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentStep === 3,\n                  expression: \"currentStep === 3\",\n                },\n              ],\n              staticClass: \"step-content\",\n            },\n            [\n              _c(\"div\", { staticClass: \"step-title\" }, [\n                _vm._v(\"第三步: 为选中的地市和模块分类配置模块\"),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"city-category-groups\" },\n                [\n                  _c(\n                    \"el-collapse\",\n                    {\n                      model: {\n                        value: _vm.expandedCityCategories,\n                        callback: function ($$v) {\n                          _vm.expandedCityCategories = $$v\n                        },\n                        expression: \"expandedCityCategories\",\n                      },\n                    },\n                    [\n                      _vm._l(_vm.selectedCities, function (cityCode) {\n                        return [\n                          (_vm.cityCategories[cityCode] || []).length > 0\n                            ? _vm._l(\n                                _vm.cityCategories[cityCode],\n                                function (categoryId) {\n                                  return _c(\n                                    \"el-collapse-item\",\n                                    {\n                                      key: `${cityCode}-${categoryId}`,\n                                      attrs: {\n                                        name: `${cityCode}-${categoryId}`,\n                                      },\n                                    },\n                                    [\n                                      _c(\"template\", { slot: \"title\" }, [\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"group-header\" },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"group-title\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    _vm.getCityName(cityCode)\n                                                  ) +\n                                                    \" - \" +\n                                                    _vm._s(\n                                                      _vm.getCategoryName(\n                                                        categoryId\n                                                      )\n                                                    )\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"el-tag\",\n                                              {\n                                                staticClass: \"count-tag\",\n                                                attrs: {\n                                                  size: \"mini\",\n                                                  type: \"info\",\n                                                },\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \" 已选\" +\n                                                    _vm._s(\n                                                      _vm.getCityCategoryModuleCount(\n                                                        cityCode,\n                                                        categoryId\n                                                      )\n                                                    ) +\n                                                    \"项 \"\n                                                ),\n                                              ]\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ]),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"module-selection\" },\n                                        [\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"module-transfer\" },\n                                            [\n                                              _c(\"el-transfer\", {\n                                                attrs: {\n                                                  value:\n                                                    _vm.getCityModulesArray(\n                                                      cityCode,\n                                                      categoryId\n                                                    ),\n                                                  data: _vm.moduleData,\n                                                  titles: [\n                                                    \"可选模块\",\n                                                    \"已选模块\",\n                                                  ],\n                                                  \"button-texts\": [\n                                                    \"移除\",\n                                                    \"添加\",\n                                                  ],\n                                                  format: {\n                                                    noChecked: \"${total}\",\n                                                    hasChecked:\n                                                      \"${checked}/${total}\",\n                                                  },\n                                                },\n                                                on: {\n                                                  input: (val) =>\n                                                    _vm.updateCityModules(\n                                                      cityCode,\n                                                      categoryId,\n                                                      val\n                                                    ),\n                                                  remove: function ($event) {\n                                                    return _vm.handleRemoveModules(\n                                                      cityCode,\n                                                      categoryId,\n                                                      $event,\n                                                      \"left\",\n                                                      $event\n                                                    )\n                                                  },\n                                                },\n                                                scopedSlots: _vm._u(\n                                                  [\n                                                    {\n                                                      key: \"default\",\n                                                      fn: function ({\n                                                        option,\n                                                      }) {\n                                                        return _c(\n                                                          \"span\",\n                                                          {\n                                                            staticClass:\n                                                              \"transfer-item\",\n                                                          },\n                                                          [\n                                                            _c(\"span\", [\n                                                              _vm._v(\n                                                                _vm._s(\n                                                                  option.label\n                                                                )\n                                                              ),\n                                                            ]),\n                                                          ]\n                                                        )\n                                                      },\n                                                    },\n                                                  ],\n                                                  null,\n                                                  true\n                                                ),\n                                              }),\n                                              _c(\n                                                \"div\",\n                                                { staticClass: \"transfer-tip\" },\n                                                [\n                                                  _c(\"el-alert\", {\n                                                    attrs: {\n                                                      title:\n                                                        \"提示：从右侧移除项目将同时删除该地市与模块的关联关系，请谨慎操作\",\n                                                      type: \"warning\",\n                                                      closable: false,\n                                                      \"show-icon\": \"\",\n                                                    },\n                                                  }),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            1\n                                          ),\n                                        ]\n                                      ),\n                                    ],\n                                    2\n                                  )\n                                }\n                              )\n                            : _vm._e(),\n                        ]\n                      }),\n                    ],\n                    2\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"step-actions\" },\n                [\n                  _c(\"el-button\", { on: { click: _vm.prevStep } }, [\n                    _vm._v(\"上一步\"),\n                  ]),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        disabled: !_vm.hasModuleSelected,\n                      },\n                      on: {\n                        click: function ($event) {\n                          _vm.syncAllModules()\n                          _vm.nextStep()\n                        },\n                      },\n                    },\n                    [_vm._v(\"下一步\")]\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentStep === 4,\n                  expression: \"currentStep === 4\",\n                },\n              ],\n              staticClass: \"step-content\",\n            },\n            [\n              _c(\"div\", { staticClass: \"step-title\" }, [\n                _vm._v(\"第四步: 为选中的模块配置商品属性类别和标签\"),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"city-category-module-groups\" },\n                [\n                  _c(\n                    \"el-collapse\",\n                    {\n                      model: {\n                        value: _vm.expandedCityModules,\n                        callback: function ($$v) {\n                          _vm.expandedCityModules = $$v\n                        },\n                        expression: \"expandedCityModules\",\n                      },\n                    },\n                    [\n                      _vm._l(_vm.selectedCities, function (cityCode) {\n                        return [\n                          (_vm.cityCategories[cityCode] || []).length > 0\n                            ? [\n                                _vm._l(\n                                  _vm.cityCategories[cityCode],\n                                  function (categoryId) {\n                                    return [\n                                      _vm.getCityModulesArray(\n                                        cityCode,\n                                        categoryId\n                                      ).length > 0\n                                        ? _vm._l(\n                                            _vm.getCityModulesArray(\n                                              cityCode,\n                                              categoryId\n                                            ),\n                                            function (moduleId) {\n                                              return _c(\n                                                \"el-collapse-item\",\n                                                {\n                                                  key: `${cityCode}-${categoryId}-${moduleId}`,\n                                                  attrs: {\n                                                    name: `${cityCode}-${categoryId}-${moduleId}`,\n                                                  },\n                                                },\n                                                [\n                                                  _c(\n                                                    \"template\",\n                                                    { slot: \"title\" },\n                                                    [\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"group-header\",\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"span\",\n                                                            {\n                                                              staticClass:\n                                                                \"group-title\",\n                                                            },\n                                                            [\n                                                              _vm._v(\n                                                                _vm._s(\n                                                                  _vm.getCityName(\n                                                                    cityCode\n                                                                  )\n                                                                ) +\n                                                                  \" - \" +\n                                                                  _vm._s(\n                                                                    _vm.getCategoryName(\n                                                                      categoryId\n                                                                    )\n                                                                  ) +\n                                                                  \" - \" +\n                                                                  _vm._s(\n                                                                    _vm.getModuleName(\n                                                                      moduleId\n                                                                    )\n                                                                  )\n                                                              ),\n                                                            ]\n                                                          ),\n                                                          _c(\n                                                            \"div\",\n                                                            [\n                                                              _c(\n                                                                \"el-tag\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"count-tag\",\n                                                                  attrs: {\n                                                                    size: \"mini\",\n                                                                    type: \"success\",\n                                                                  },\n                                                                },\n                                                                [\n                                                                  _vm._v(\n                                                                    \" 属性\" +\n                                                                      _vm._s(\n                                                                        _vm.getModuleAttributeCount(\n                                                                          cityCode,\n                                                                          categoryId,\n                                                                          moduleId\n                                                                        )\n                                                                      ) +\n                                                                      \"项 \"\n                                                                  ),\n                                                                ]\n                                                              ),\n                                                              _c(\n                                                                \"el-tag\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"count-tag\",\n                                                                  attrs: {\n                                                                    size: \"mini\",\n                                                                    type: \"warning\",\n                                                                  },\n                                                                },\n                                                                [\n                                                                  _vm._v(\n                                                                    \" 标签\" +\n                                                                      _vm._s(\n                                                                        _vm.getModuleTagCount(\n                                                                          cityCode,\n                                                                          categoryId,\n                                                                          moduleId\n                                                                        )\n                                                                      ) +\n                                                                      \"项 \"\n                                                                  ),\n                                                                ]\n                                                              ),\n                                                            ],\n                                                            1\n                                                          ),\n                                                        ]\n                                                      ),\n                                                    ]\n                                                  ),\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass:\n                                                        \"attribute-tag-selection\",\n                                                    },\n                                                    [\n                                                      _c(\n                                                        \"el-tabs\",\n                                                        {\n                                                          staticClass:\n                                                            \"attribute-tag-tabs\",\n                                                          attrs: {\n                                                            value:\n                                                              _vm.getModuleTabsValue(\n                                                                cityCode,\n                                                                categoryId,\n                                                                moduleId\n                                                              ),\n                                                            type: \"border-card\",\n                                                          },\n                                                          on: {\n                                                            input: (val) =>\n                                                              _vm.updateModuleTab(\n                                                                cityCode,\n                                                                categoryId,\n                                                                moduleId,\n                                                                val\n                                                              ),\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"el-tab-pane\",\n                                                            {\n                                                              attrs: {\n                                                                label:\n                                                                  \"商品属性类别配置\",\n                                                                name: \"attribute\",\n                                                              },\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"drag-sort-tip\",\n                                                                },\n                                                                [\n                                                                  _c(\"i\", {\n                                                                    staticClass:\n                                                                      \"el-icon-sort\",\n                                                                  }),\n                                                                  _c(\"span\", [\n                                                                    _vm._v(\n                                                                      \"在右侧已选择区域，您可以通过拖拽 \"\n                                                                    ),\n                                                                    _c(\"i\", {\n                                                                      staticClass:\n                                                                        \"el-icon-rank\",\n                                                                    }),\n                                                                    _vm._v(\n                                                                      \" 图标调整属性类别的显示顺序\"\n                                                                    ),\n                                                                  ]),\n                                                                ]\n                                                              ),\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"attribute-transfer\",\n                                                                },\n                                                                [\n                                                                  _c(\n                                                                    \"el-transfer\",\n                                                                    {\n                                                                      ref: \"sortTransfer\",\n                                                                      refInFor: true,\n                                                                      attrs: {\n                                                                        \"target-order\":\n                                                                          \"unshift\",\n                                                                        value:\n                                                                          _vm.getModuleAttributesArray(\n                                                                            cityCode,\n                                                                            categoryId,\n                                                                            moduleId\n                                                                          ),\n                                                                        data: _vm.attributeTypeData,\n                                                                        titles:\n                                                                          [\n                                                                            \"可选商品属性类别\",\n                                                                            \"已选商品属性类别\",\n                                                                          ],\n                                                                        \"button-texts\":\n                                                                          [\n                                                                            \"移除\",\n                                                                            \"添加\",\n                                                                          ],\n                                                                        format:\n                                                                          {\n                                                                            noChecked:\n                                                                              \"${total}\",\n                                                                            hasChecked:\n                                                                              \"${checked}/${total}\",\n                                                                          },\n                                                                      },\n                                                                      on: {\n                                                                        input: (\n                                                                          val\n                                                                        ) =>\n                                                                          _vm.updateModuleAttributes(\n                                                                            cityCode,\n                                                                            categoryId,\n                                                                            moduleId,\n                                                                            val\n                                                                          ),\n                                                                        remove:\n                                                                          function (\n                                                                            $event\n                                                                          ) {\n                                                                            return _vm.handleRemoveAttributeTypes(\n                                                                              cityCode,\n                                                                              categoryId,\n                                                                              moduleId,\n                                                                              $event,\n                                                                              \"left\",\n                                                                              $event\n                                                                            )\n                                                                          },\n                                                                      },\n                                                                      scopedSlots:\n                                                                        _vm._u(\n                                                                          [\n                                                                            {\n                                                                              key: \"default\",\n                                                                              fn: function ({\n                                                                                option,\n                                                                              }) {\n                                                                                return _c(\n                                                                                  \"span\",\n                                                                                  {\n                                                                                    staticClass:\n                                                                                      \"transfer-item draggable-item\",\n                                                                                    attrs:\n                                                                                      {\n                                                                                        draggable:\n                                                                                          \"!option.disabled\",\n                                                                                        title:\n                                                                                          \"拖拽可调整排序：\" +\n                                                                                          option.label,\n                                                                                      },\n                                                                                    on: {\n                                                                                      dragstart:\n                                                                                        function (\n                                                                                          $event\n                                                                                        ) {\n                                                                                          return _vm.dragStart(\n                                                                                            $event,\n                                                                                            option\n                                                                                          )\n                                                                                        },\n                                                                                      dragover:\n                                                                                        function (\n                                                                                          $event\n                                                                                        ) {\n                                                                                          $event.preventDefault()\n                                                                                        },\n                                                                                      dragenter:\n                                                                                        function (\n                                                                                          $event\n                                                                                        ) {\n                                                                                          $event.preventDefault()\n                                                                                        },\n                                                                                      drop: function (\n                                                                                        $event\n                                                                                      ) {\n                                                                                        return _vm.drop(\n                                                                                          $event,\n                                                                                          option,\n                                                                                          cityCode,\n                                                                                          categoryId,\n                                                                                          moduleId\n                                                                                        )\n                                                                                      },\n                                                                                    },\n                                                                                  },\n                                                                                  [\n                                                                                    _c(\n                                                                                      \"i\",\n                                                                                      {\n                                                                                        staticClass:\n                                                                                          \"el-icon-rank drag-handle\",\n                                                                                      }\n                                                                                    ),\n                                                                                    _vm._v(\n                                                                                      \" \" +\n                                                                                        _vm._s(\n                                                                                          option.label\n                                                                                        ) +\n                                                                                        \" \"\n                                                                                    ),\n                                                                                  ]\n                                                                                )\n                                                                              },\n                                                                            },\n                                                                          ],\n                                                                          null,\n                                                                          true\n                                                                        ),\n                                                                    }\n                                                                  ),\n                                                                ],\n                                                                1\n                                                              ),\n                                                            ]\n                                                          ),\n                                                          _c(\n                                                            \"el-tab-pane\",\n                                                            {\n                                                              attrs: {\n                                                                label:\n                                                                  \"商品标签配置\",\n                                                                name: \"tag\",\n                                                              },\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"drag-sort-tip\",\n                                                                },\n                                                                [\n                                                                  _c(\"i\", {\n                                                                    staticClass:\n                                                                      \"el-icon-sort\",\n                                                                  }),\n                                                                  _c(\"span\", [\n                                                                    _vm._v(\n                                                                      \"在右侧已选择区域，您可以通过拖拽 \"\n                                                                    ),\n                                                                    _c(\"i\", {\n                                                                      staticClass:\n                                                                        \"el-icon-rank\",\n                                                                    }),\n                                                                    _vm._v(\n                                                                      \" 图标调整标签的显示顺序\"\n                                                                    ),\n                                                                  ]),\n                                                                ]\n                                                              ),\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"tag-transfer\",\n                                                                },\n                                                                [\n                                                                  _c(\n                                                                    \"el-transfer\",\n                                                                    {\n                                                                      attrs: {\n                                                                        value:\n                                                                          _vm.getModuleTagsArray(\n                                                                            cityCode,\n                                                                            categoryId,\n                                                                            moduleId\n                                                                          ),\n                                                                        \"target-order\":\n                                                                          \"unshift\",\n                                                                        data: _vm.tagData,\n                                                                        titles:\n                                                                          [\n                                                                            \"可选商品标签\",\n                                                                            \"已选商品标签\",\n                                                                          ],\n                                                                        \"button-texts\":\n                                                                          [\n                                                                            \"移除\",\n                                                                            \"添加\",\n                                                                          ],\n                                                                        format:\n                                                                          {\n                                                                            noChecked:\n                                                                              \"${total}\",\n                                                                            hasChecked:\n                                                                              \"${checked}/${total}\",\n                                                                          },\n                                                                      },\n                                                                      on: {\n                                                                        input: (\n                                                                          val\n                                                                        ) =>\n                                                                          _vm.updateModuleTags(\n                                                                            cityCode,\n                                                                            categoryId,\n                                                                            moduleId,\n                                                                            val\n                                                                          ),\n                                                                        remove:\n                                                                          function (\n                                                                            $event\n                                                                          ) {\n                                                                            return _vm.handleRemoveTags(\n                                                                              cityCode,\n                                                                              categoryId,\n                                                                              moduleId,\n                                                                              $event,\n                                                                              \"left\",\n                                                                              $event\n                                                                            )\n                                                                          },\n                                                                      },\n                                                                      scopedSlots:\n                                                                        _vm._u(\n                                                                          [\n                                                                            {\n                                                                              key: \"default\",\n                                                                              fn: function ({\n                                                                                option,\n                                                                              }) {\n                                                                                return _c(\n                                                                                  \"span\",\n                                                                                  {\n                                                                                    staticClass:\n                                                                                      \"transfer-item draggable-item\",\n                                                                                    attrs:\n                                                                                      {\n                                                                                        draggable:\n                                                                                          \"!option.disabled\",\n                                                                                        title:\n                                                                                          \"拖拽可调整排序：\" +\n                                                                                          option.label,\n                                                                                      },\n                                                                                    on: {\n                                                                                      dragstart:\n                                                                                        function (\n                                                                                          $event\n                                                                                        ) {\n                                                                                          return _vm.dragStart2(\n                                                                                            $event,\n                                                                                            option\n                                                                                          )\n                                                                                        },\n                                                                                      dragover:\n                                                                                        function (\n                                                                                          $event\n                                                                                        ) {\n                                                                                          $event.preventDefault()\n                                                                                        },\n                                                                                      dragenter:\n                                                                                        function (\n                                                                                          $event\n                                                                                        ) {\n                                                                                          $event.preventDefault()\n                                                                                        },\n                                                                                      drop: function (\n                                                                                        $event\n                                                                                      ) {\n                                                                                        return _vm.drop2(\n                                                                                          $event,\n                                                                                          option,\n                                                                                          cityCode,\n                                                                                          categoryId,\n                                                                                          moduleId\n                                                                                        )\n                                                                                      },\n                                                                                    },\n                                                                                  },\n                                                                                  [\n                                                                                    _c(\n                                                                                      \"i\",\n                                                                                      {\n                                                                                        staticClass:\n                                                                                          \"el-icon-rank drag-handle\",\n                                                                                      }\n                                                                                    ),\n                                                                                    _vm._v(\n                                                                                      \" \" +\n                                                                                        _vm._s(\n                                                                                          option.label\n                                                                                        ) +\n                                                                                        \" \"\n                                                                                    ),\n                                                                                  ]\n                                                                                )\n                                                                              },\n                                                                            },\n                                                                          ],\n                                                                          null,\n                                                                          true\n                                                                        ),\n                                                                    }\n                                                                  ),\n                                                                  _c(\n                                                                    \"div\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"transfer-tip\",\n                                                                    },\n                                                                    [\n                                                                      _c(\n                                                                        \"el-alert\",\n                                                                        {\n                                                                          attrs:\n                                                                            {\n                                                                              title:\n                                                                                \"提示：从右侧移除项目将同时删除该模块与商品标签的关联关系，请谨慎操作\",\n                                                                              type: \"warning\",\n                                                                              closable: false,\n                                                                              \"show-icon\":\n                                                                                \"\",\n                                                                            },\n                                                                        }\n                                                                      ),\n                                                                    ],\n                                                                    1\n                                                                  ),\n                                                                ],\n                                                                1\n                                                              ),\n                                                            ]\n                                                          ),\n                                                        ],\n                                                        1\n                                                      ),\n                                                    ],\n                                                    1\n                                                  ),\n                                                ],\n                                                2\n                                              )\n                                            }\n                                          )\n                                        : _vm._e(),\n                                    ]\n                                  }\n                                ),\n                              ]\n                            : _vm._e(),\n                        ]\n                      }),\n                    ],\n                    2\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"step-actions\" },\n                [\n                  _c(\"el-button\", { on: { click: _vm.prevStep } }, [\n                    _vm._v(\"上一步\"),\n                  ]),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: {\n                        click: function ($event) {\n                          _vm.syncAllAttributesAndTags()\n                          _vm.saveConfiguration()\n                        },\n                      },\n                    },\n                    [_vm._v(\"保存配置\")]\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n        ]),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CACP,KADO,EAEP;IAAEE,WAAW,EAAE;EAAf,CAFO,EAGP,CACEF,EAAE,CAAC,SAAD,EAAY;IAAEG,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAV;EAAT,CAAZ,EAA4C,CAC5CJ,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA0C,CAC1CF,EAAE,CAAC,IAAD,EAAO,CAACD,GAAG,CAACM,EAAJ,CAAO,eAAP,CAAD,CAAP,CADwC,EAE1CL,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCF,EAAE,CAAC,GAAD,EAAM,CACND,GAAG,CAACM,EAAJ,CACE,uCADF,CADM,CAAN,CADsC,CAAxC,CAFwC,CAA1C,CAD0C,EAW5CL,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,UADA,EAEA;IACEG,KAAK,EAAE;MACLG,MAAM,EAAEP,GAAG,CAACQ,WADP;MAEL,iBAAiB,SAFZ;MAGLC,MAAM,EAAE;IAHH;EADT,CAFA,EASA,CACER,EAAE,CAAC,SAAD,EAAY;IACZG,KAAK,EAAE;MAAEM,KAAK,EAAE,MAAT;MAAiBC,IAAI,EAAE;IAAvB;EADK,CAAZ,CADJ,EAIEV,EAAE,CAAC,SAAD,EAAY;IACZG,KAAK,EAAE;MAAEM,KAAK,EAAE,QAAT;MAAmBC,IAAI,EAAE;IAAzB;EADK,CAAZ,CAJJ,EAOEV,EAAE,CAAC,SAAD,EAAY;IACZG,KAAK,EAAE;MAAEM,KAAK,EAAE,MAAT;MAAiBC,IAAI,EAAE;IAAvB;EADK,CAAZ,CAPJ,EAUEV,EAAE,CAAC,SAAD,EAAY;IACZG,KAAK,EAAE;MACLM,KAAK,EAAE,WADF;MAELC,IAAI,EAAE;IAFD;EADK,CAAZ,CAVJ,CATA,EA0BA,CA1BA,CADJ,CAHA,EAiCA,CAjCA,CAX0C,EA8C5CV,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CF,EAAE,CACA,KADA,EAEA;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MADR;MAEEC,OAAO,EAAE,QAFX;MAGEC,KAAK,EAAEf,GAAG,CAACQ,WAAJ,KAAoB,CAH7B;MAIEQ,UAAU,EAAE;IAJd,CADU,CADd;IASEb,WAAW,EAAE;EATf,CAFA,EAaA,CACEF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,GAAG,CAACM,EAAJ,CAAO,gBAAP,CADuC,CAAvC,CADJ,EAIEL,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CF,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,aADA,EAEA;IACEG,KAAK,EAAE;MAAEa,aAAa,EAAEjB,GAAG,CAACkB;IAArB,CADT;IAEEC,EAAE,EAAE;MAAEC,MAAM,EAAEpB,GAAG,CAACqB;IAAd,CAFN;IAGEC,KAAK,EAAE;MACLP,KAAK,EAAEf,GAAG,CAACuB,eADN;MAELC,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBzB,GAAG,CAACuB,eAAJ,GAAsBE,GAAtB;MACD,CAJI;MAKLT,UAAU,EAAE;IALP;EAHT,CAFA,EAaA,CAAChB,GAAG,CAACM,EAAJ,CAAO,IAAP,CAAD,CAbA,CADJ,CAHA,EAoBA,CApBA,CADyC,EAuB3CL,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,mBADA,EAEA;IACEkB,EAAE,EAAE;MAAEC,MAAM,EAAEpB,GAAG,CAAC0B;IAAd,CADN;IAEEJ,KAAK,EAAE;MACLP,KAAK,EAAEf,GAAG,CAAC2B,cADN;MAELH,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBzB,GAAG,CAAC2B,cAAJ,GAAqBF,GAArB;MACD,CAJI;MAKLT,UAAU,EAAE;IALP;EAFT,CAFA,EAYAhB,GAAG,CAAC4B,EAAJ,CAAO5B,GAAG,CAAC6B,MAAX,EAAmB,UAAUC,IAAV,EAAgB;IACjC,OAAO7B,EAAE,CACP,aADO,EAEP;MACE8B,GAAG,EAAED,IAAI,CAACE,QADZ;MAEE5B,KAAK,EAAE;QAAE6B,KAAK,EAAEH,IAAI,CAACE;MAAd;IAFT,CAFO,EAMP,CAAChC,GAAG,CAACM,EAAJ,CAAO,MAAMN,GAAG,CAACkC,EAAJ,CAAOJ,IAAI,CAACK,QAAZ,CAAN,GAA8B,GAArC,CAAD,CANO,CAAT;EAQD,CATD,CAZA,EAsBA,CAtBA,CADJ,CAHA,EA6BA,CA7BA,CAvByC,CAA3C,CAJJ,EA2DElC,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,WADA,EAEA;IACEG,KAAK,EAAE;MACLgC,IAAI,EAAE,SADD;MAELC,QAAQ,EAAErC,GAAG,CAAC2B,cAAJ,CAAmBW,MAAnB,KAA8B;IAFnC,CADT;IAKEnB,EAAE,EAAE;MAAEoB,KAAK,EAAEvC,GAAG,CAACwC;IAAb;EALN,CAFA,EASA,CAACxC,GAAG,CAACM,EAAJ,CAAO,KAAP,CAAD,CATA,CADJ,CAHA,EAgBA,CAhBA,CA3DJ,CAbA,CADyC,EA6F3CL,EAAE,CACA,KADA,EAEA;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MADR;MAEEC,OAAO,EAAE,QAFX;MAGEC,KAAK,EAAEf,GAAG,CAACQ,WAAJ,KAAoB,CAH7B;MAIEQ,UAAU,EAAE;IAJd,CADU,CADd;IASEb,WAAW,EAAE;EATf,CAFA,EAaA,CACEF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,GAAG,CAACM,EAAJ,CAAO,mBAAP,CADuC,CAAvC,CADJ,EAIEL,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,aADA,EAEA;IACEqB,KAAK,EAAE;MACLP,KAAK,EAAEf,GAAG,CAACyC,cADN;MAELjB,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBzB,GAAG,CAACyC,cAAJ,GAAqBhB,GAArB;MACD,CAJI;MAKLT,UAAU,EAAE;IALP;EADT,CAFA,EAWAhB,GAAG,CAAC4B,EAAJ,CAAO5B,GAAG,CAAC2B,cAAX,EAA2B,UAAUK,QAAV,EAAoB;IAC7C,OAAO/B,EAAE,CACP,kBADO,EAEP;MAAE8B,GAAG,EAAEC,QAAP;MAAiB5B,KAAK,EAAE;QAAES,IAAI,EAAEmB;MAAR;IAAxB,CAFO,EAGP,CACE/B,EAAE,CAAC,UAAD,EAAa;MAAEyC,IAAI,EAAE;IAAR,CAAb,EAAgC,CAChCzC,EAAE,CACA,KADA,EAEA;MAAEE,WAAW,EAAE;IAAf,CAFA,EAGA,CACEF,EAAE,CAAC,MAAD,EAAS;MAAEE,WAAW,EAAE;IAAf,CAAT,EAAyC,CACzCH,GAAG,CAACM,EAAJ,CAAON,GAAG,CAACkC,EAAJ,CAAOlC,GAAG,CAAC2C,WAAJ,CAAgBX,QAAhB,CAAP,CAAP,CADyC,CAAzC,CADJ,EAIE/B,EAAE,CACA,QADA,EAEA;MACEE,WAAW,EAAE,WADf;MAEEC,KAAK,EAAE;QAAEwC,IAAI,EAAE,MAAR;QAAgBR,IAAI,EAAE;MAAtB;IAFT,CAFA,EAMA,CACEpC,GAAG,CAACM,EAAJ,CACE,QACEN,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAAC6C,oBAAJ,CAAyBb,QAAzB,CADF,CADF,GAIE,IALJ,CADF,CANA,CAJJ,CAHA,EAwBA,CAxBA,CAD8B,CAAhC,CADJ,EA6BE/B,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,EAA+C,CAC/CF,EAAE,CACA,KADA,EAEA;MAAEE,WAAW,EAAE;IAAf,CAFA,EAGA,CACEF,EAAE,CAAC,aAAD,EAAgB;MAChBG,KAAK,EAAE;QACLW,KAAK,EAAEf,GAAG,CAAC8C,cAAJ,CAAmBd,QAAnB,IACHhC,GAAG,CAAC8C,cAAJ,CAAmBd,QAAnB,CADG,GAEH,EAHC;QAILe,IAAI,EAAE/C,GAAG,CAACgD,YAJL;QAKLC,MAAM,EAAE,CAAC,QAAD,EAAW,QAAX,CALH;QAML,gBAAgB,CAAC,IAAD,EAAO,IAAP,CANX;QAOLC,MAAM,EAAE;UACNC,SAAS,EAAE,UADL;UAENC,UAAU,EAAE;QAFN;MAPH,CADS;MAahBjC,EAAE,EAAE;QACFkC,KAAK,EAAGC,GAAD,IACLtD,GAAG,CAACuD,oBAAJ,CAAyBvB,QAAzB,EAAmCsB,GAAnC,CAFA;QAGFE,MAAM,EAAE,UAAUC,MAAV,EAAkB;UACxB,OAAOzD,GAAG,CAAC0D,sBAAJ,CACL1B,QADK,EAELyB,MAFK,EAGL,MAHK,EAILA,MAJK,CAAP;QAMD;MAVC,CAbY;MAyBhBE,WAAW,EAAE3D,GAAG,CAAC4D,EAAJ,CACX,CACE;QACE7B,GAAG,EAAE,SADP;QAEE8B,EAAE,EAAE,UAAU;UAAEC;QAAF,CAAV,EAAsB;UACxB,OAAO7D,EAAE,CACP,MADO,EAEP;YAAEE,WAAW,EAAE;UAAf,CAFO,EAGP,CACEH,GAAG,CAACM,EAAJ,CACE,MAAMN,GAAG,CAACkC,EAAJ,CAAO4B,MAAM,CAAC7B,KAAd,CAAN,GAA6B,GAD/B,CADF,CAHO,CAAT;QASD;MAZH,CADF,CADW,EAiBX,IAjBW,EAkBX,IAlBW;IAzBG,CAAhB,CADJ,EA+CEhC,EAAE,CACA,KADA,EAEA;MAAEE,WAAW,EAAE;IAAf,CAFA,EAGA,CACEF,EAAE,CAAC,UAAD,EAAa;MACbG,KAAK,EAAE;QACLM,KAAK,EACH,oCAFG;QAGL0B,IAAI,EAAE,SAHD;QAIL2B,QAAQ,EAAE,KAJL;QAKL,aAAa;MALR;IADM,CAAb,CADJ,CAHA,EAcA,CAdA,CA/CJ,CAHA,EAmEA,CAnEA,CAD6C,CAA/C,CA7BJ,CAHO,EAwGP,CAxGO,CAAT;EA0GD,CA3GD,CAXA,EAuHA,CAvHA,CADJ,CAHA,EA8HA,CA9HA,CAJJ,EAoIE9D,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CAAC,WAAD,EAAc;IAAEkB,EAAE,EAAE;MAAEoB,KAAK,EAAEvC,GAAG,CAACgE;IAAb;EAAN,CAAd,EAA+C,CAC/ChE,GAAG,CAACM,EAAJ,CAAO,KAAP,CAD+C,CAA/C,CADJ,EAIEL,EAAE,CACA,WADA,EAEA;IACEG,KAAK,EAAE;MACLgC,IAAI,EAAE,SADD;MAELC,QAAQ,EAAE,CAACrC,GAAG,CAACiE;IAFV,CADT;IAKE9C,EAAE,EAAE;MACFoB,KAAK,EAAE,UAAUkB,MAAV,EAAkB;QACvBzD,GAAG,CAACkE,iBAAJ;;QACAlE,GAAG,CAACwC,QAAJ;MACD;IAJC;EALN,CAFA,EAcA,CAACxC,GAAG,CAACM,EAAJ,CAAO,KAAP,CAAD,CAdA,CAJJ,CAHA,EAwBA,CAxBA,CApIJ,CAbA,CA7FyC,EA0Q3CL,EAAE,CACA,KADA,EAEA;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MADR;MAEEC,OAAO,EAAE,QAFX;MAGEC,KAAK,EAAEf,GAAG,CAACQ,WAAJ,KAAoB,CAH7B;MAIEQ,UAAU,EAAE;IAJd,CADU,CADd;IASEb,WAAW,EAAE;EATf,CAFA,EAaA,CACEF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,GAAG,CAACM,EAAJ,CAAO,sBAAP,CADuC,CAAvC,CADJ,EAIEL,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,aADA,EAEA;IACEqB,KAAK,EAAE;MACLP,KAAK,EAAEf,GAAG,CAACmE,sBADN;MAEL3C,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBzB,GAAG,CAACmE,sBAAJ,GAA6B1C,GAA7B;MACD,CAJI;MAKLT,UAAU,EAAE;IALP;EADT,CAFA,EAWA,CACEhB,GAAG,CAAC4B,EAAJ,CAAO5B,GAAG,CAAC2B,cAAX,EAA2B,UAAUK,QAAV,EAAoB;IAC7C,OAAO,CACL,CAAChC,GAAG,CAAC8C,cAAJ,CAAmBd,QAAnB,KAAgC,EAAjC,EAAqCM,MAArC,GAA8C,CAA9C,GACItC,GAAG,CAAC4B,EAAJ,CACE5B,GAAG,CAAC8C,cAAJ,CAAmBd,QAAnB,CADF,EAEE,UAAUoC,UAAV,EAAsB;MACpB,OAAOnE,EAAE,CACP,kBADO,EAEP;QACE8B,GAAG,EAAG,GAAEC,QAAS,IAAGoC,UAAW,EADjC;QAEEhE,KAAK,EAAE;UACLS,IAAI,EAAG,GAAEmB,QAAS,IAAGoC,UAAW;QAD3B;MAFT,CAFO,EAQP,CACEnE,EAAE,CAAC,UAAD,EAAa;QAAEyC,IAAI,EAAE;MAAR,CAAb,EAAgC,CAChCzC,EAAE,CACA,KADA,EAEA;QAAEE,WAAW,EAAE;MAAf,CAFA,EAGA,CACEF,EAAE,CACA,MADA,EAEA;QAAEE,WAAW,EAAE;MAAf,CAFA,EAGA,CACEH,GAAG,CAACM,EAAJ,CACEN,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAAC2C,WAAJ,CAAgBX,QAAhB,CADF,IAGE,KAHF,GAIEhC,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAACqE,eAAJ,CACED,UADF,CADF,CALJ,CADF,CAHA,CADJ,EAkBEnE,EAAE,CACA,QADA,EAEA;QACEE,WAAW,EAAE,WADf;QAEEC,KAAK,EAAE;UACLwC,IAAI,EAAE,MADD;UAELR,IAAI,EAAE;QAFD;MAFT,CAFA,EASA,CACEpC,GAAG,CAACM,EAAJ,CACE,QACEN,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAACsE,0BAAJ,CACEtC,QADF,EAEEoC,UAFF,CADF,CADF,GAOE,IARJ,CADF,CATA,CAlBJ,CAHA,EA4CA,CA5CA,CAD8B,CAAhC,CADJ,EAiDEnE,EAAE,CACA,KADA,EAEA;QAAEE,WAAW,EAAE;MAAf,CAFA,EAGA,CACEF,EAAE,CACA,KADA,EAEA;QAAEE,WAAW,EAAE;MAAf,CAFA,EAGA,CACEF,EAAE,CAAC,aAAD,EAAgB;QAChBG,KAAK,EAAE;UACLW,KAAK,EACHf,GAAG,CAACuE,mBAAJ,CACEvC,QADF,EAEEoC,UAFF,CAFG;UAMLrB,IAAI,EAAE/C,GAAG,CAACwE,UANL;UAOLvB,MAAM,EAAE,CACN,MADM,EAEN,MAFM,CAPH;UAWL,gBAAgB,CACd,IADc,EAEd,IAFc,CAXX;UAeLC,MAAM,EAAE;YACNC,SAAS,EAAE,UADL;YAENC,UAAU,EACR;UAHI;QAfH,CADS;QAsBhBjC,EAAE,EAAE;UACFkC,KAAK,EAAGC,GAAD,IACLtD,GAAG,CAACyE,iBAAJ,CACEzC,QADF,EAEEoC,UAFF,EAGEd,GAHF,CAFA;UAOFE,MAAM,EAAE,UAAUC,MAAV,EAAkB;YACxB,OAAOzD,GAAG,CAAC0E,mBAAJ,CACL1C,QADK,EAELoC,UAFK,EAGLX,MAHK,EAIL,MAJK,EAKLA,MALK,CAAP;UAOD;QAfC,CAtBY;QAuChBE,WAAW,EAAE3D,GAAG,CAAC4D,EAAJ,CACX,CACE;UACE7B,GAAG,EAAE,SADP;UAEE8B,EAAE,EAAE,UAAU;YACZC;UADY,CAAV,EAED;YACD,OAAO7D,EAAE,CACP,MADO,EAEP;cACEE,WAAW,EACT;YAFJ,CAFO,EAMP,CACEF,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACM,EAAJ,CACEN,GAAG,CAACkC,EAAJ,CACE4B,MAAM,CAAC7B,KADT,CADF,CADS,CAAT,CADJ,CANO,CAAT;UAgBD;QArBH,CADF,CADW,EA0BX,IA1BW,EA2BX,IA3BW;MAvCG,CAAhB,CADJ,EAsEEhC,EAAE,CACA,KADA,EAEA;QAAEE,WAAW,EAAE;MAAf,CAFA,EAGA,CACEF,EAAE,CAAC,UAAD,EAAa;QACbG,KAAK,EAAE;UACLM,KAAK,EACH,kCAFG;UAGL0B,IAAI,EAAE,SAHD;UAIL2B,QAAQ,EAAE,KAJL;UAKL,aAAa;QALR;MADM,CAAb,CADJ,CAHA,EAcA,CAdA,CAtEJ,CAHA,EA0FA,CA1FA,CADJ,CAHA,CAjDJ,CARO,EA4JP,CA5JO,CAAT;IA8JD,CAjKH,CADJ,GAoKI/D,GAAG,CAAC2E,EAAJ,EArKC,CAAP;EAuKD,CAxKD,CADF,CAXA,EAsLA,CAtLA,CADJ,CAHA,EA6LA,CA7LA,CAJJ,EAmME1E,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CAAC,WAAD,EAAc;IAAEkB,EAAE,EAAE;MAAEoB,KAAK,EAAEvC,GAAG,CAACgE;IAAb;EAAN,CAAd,EAA+C,CAC/ChE,GAAG,CAACM,EAAJ,CAAO,KAAP,CAD+C,CAA/C,CADJ,EAIEL,EAAE,CACA,WADA,EAEA;IACEG,KAAK,EAAE;MACLgC,IAAI,EAAE,SADD;MAELC,QAAQ,EAAE,CAACrC,GAAG,CAAC4E;IAFV,CADT;IAKEzD,EAAE,EAAE;MACFoB,KAAK,EAAE,UAAUkB,MAAV,EAAkB;QACvBzD,GAAG,CAAC6E,cAAJ;;QACA7E,GAAG,CAACwC,QAAJ;MACD;IAJC;EALN,CAFA,EAcA,CAACxC,GAAG,CAACM,EAAJ,CAAO,KAAP,CAAD,CAdA,CAJJ,CAHA,EAwBA,CAxBA,CAnMJ,CAbA,CA1QyC,EAsf3CL,EAAE,CACA,KADA,EAEA;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MADR;MAEEC,OAAO,EAAE,QAFX;MAGEC,KAAK,EAAEf,GAAG,CAACQ,WAAJ,KAAoB,CAH7B;MAIEQ,UAAU,EAAE;IAJd,CADU,CADd;IASEb,WAAW,EAAE;EATf,CAFA,EAaA,CACEF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,GAAG,CAACM,EAAJ,CAAO,wBAAP,CADuC,CAAvC,CADJ,EAIEL,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,aADA,EAEA;IACEqB,KAAK,EAAE;MACLP,KAAK,EAAEf,GAAG,CAAC8E,mBADN;MAELtD,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBzB,GAAG,CAAC8E,mBAAJ,GAA0BrD,GAA1B;MACD,CAJI;MAKLT,UAAU,EAAE;IALP;EADT,CAFA,EAWA,CACEhB,GAAG,CAAC4B,EAAJ,CAAO5B,GAAG,CAAC2B,cAAX,EAA2B,UAAUK,QAAV,EAAoB;IAC7C,OAAO,CACL,CAAChC,GAAG,CAAC8C,cAAJ,CAAmBd,QAAnB,KAAgC,EAAjC,EAAqCM,MAArC,GAA8C,CAA9C,GACI,CACEtC,GAAG,CAAC4B,EAAJ,CACE5B,GAAG,CAAC8C,cAAJ,CAAmBd,QAAnB,CADF,EAEE,UAAUoC,UAAV,EAAsB;MACpB,OAAO,CACLpE,GAAG,CAACuE,mBAAJ,CACEvC,QADF,EAEEoC,UAFF,EAGE9B,MAHF,GAGW,CAHX,GAIItC,GAAG,CAAC4B,EAAJ,CACE5B,GAAG,CAACuE,mBAAJ,CACEvC,QADF,EAEEoC,UAFF,CADF,EAKE,UAAUW,QAAV,EAAoB;QAClB,OAAO9E,EAAE,CACP,kBADO,EAEP;UACE8B,GAAG,EAAG,GAAEC,QAAS,IAAGoC,UAAW,IAAGW,QAAS,EAD7C;UAEE3E,KAAK,EAAE;YACLS,IAAI,EAAG,GAAEmB,QAAS,IAAGoC,UAAW,IAAGW,QAAS;UADvC;QAFT,CAFO,EAQP,CACE9E,EAAE,CACA,UADA,EAEA;UAAEyC,IAAI,EAAE;QAAR,CAFA,EAGA,CACEzC,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CACA,MADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEH,GAAG,CAACM,EAAJ,CACEN,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAAC2C,WAAJ,CACEX,QADF,CADF,IAKE,KALF,GAMEhC,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAACqE,eAAJ,CACED,UADF,CADF,CANF,GAWE,KAXF,GAYEpE,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAACgF,aAAJ,CACED,QADF,CADF,CAbJ,CADF,CANA,CADJ,EA6BE9E,EAAE,CACA,KADA,EAEA,CACEA,EAAE,CACA,QADA,EAEA;UACEE,WAAW,EACT,WAFJ;UAGEC,KAAK,EAAE;YACLwC,IAAI,EAAE,MADD;YAELR,IAAI,EAAE;UAFD;QAHT,CAFA,EAUA,CACEpC,GAAG,CAACM,EAAJ,CACE,QACEN,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAACiF,uBAAJ,CACEjD,QADF,EAEEoC,UAFF,EAGEW,QAHF,CADF,CADF,GAQE,IATJ,CADF,CAVA,CADJ,EAyBE9E,EAAE,CACA,QADA,EAEA;UACEE,WAAW,EACT,WAFJ;UAGEC,KAAK,EAAE;YACLwC,IAAI,EAAE,MADD;YAELR,IAAI,EAAE;UAFD;QAHT,CAFA,EAUA,CACEpC,GAAG,CAACM,EAAJ,CACE,QACEN,GAAG,CAACkC,EAAJ,CACElC,GAAG,CAACkF,iBAAJ,CACElD,QADF,EAEEoC,UAFF,EAGEW,QAHF,CADF,CADF,GAQE,IATJ,CADF,CAVA,CAzBJ,CAFA,EAoDA,CApDA,CA7BJ,CANA,CADJ,CAHA,CADJ,EAkGE9E,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CACA,SADA,EAEA;UACEE,WAAW,EACT,oBAFJ;UAGEC,KAAK,EAAE;YACLW,KAAK,EACHf,GAAG,CAACmF,kBAAJ,CACEnD,QADF,EAEEoC,UAFF,EAGEW,QAHF,CAFG;YAOL3C,IAAI,EAAE;UAPD,CAHT;UAYEjB,EAAE,EAAE;YACFkC,KAAK,EAAGC,GAAD,IACLtD,GAAG,CAACoF,eAAJ,CACEpD,QADF,EAEEoC,UAFF,EAGEW,QAHF,EAIEzB,GAJF;UAFA;QAZN,CAFA,EAwBA,CACErD,EAAE,CACA,aADA,EAEA;UACEG,KAAK,EAAE;YACL6B,KAAK,EACH,UAFG;YAGLpB,IAAI,EAAE;UAHD;QADT,CAFA,EASA,CACEZ,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CAAC,GAAD,EAAM;UACNE,WAAW,EACT;QAFI,CAAN,CADJ,EAKEF,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACM,EAAJ,CACE,mBADF,CADS,EAITL,EAAE,CAAC,GAAD,EAAM;UACNE,WAAW,EACT;QAFI,CAAN,CAJO,EAQTH,GAAG,CAACM,EAAJ,CACE,gBADF,CARS,CAAT,CALJ,CANA,CADJ,EA0BEL,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CACA,aADA,EAEA;UACEoF,GAAG,EAAE,cADP;UAEEC,QAAQ,EAAE,IAFZ;UAGElF,KAAK,EAAE;YACL,gBACE,SAFG;YAGLW,KAAK,EACHf,GAAG,CAACuF,wBAAJ,CACEvD,QADF,EAEEoC,UAFF,EAGEW,QAHF,CAJG;YASLhC,IAAI,EAAE/C,GAAG,CAACwF,iBATL;YAULvC,MAAM,EACJ,CACE,UADF,EAEE,UAFF,CAXG;YAeL,gBACE,CACE,IADF,EAEE,IAFF,CAhBG;YAoBLC,MAAM,EACJ;cACEC,SAAS,EACP,UAFJ;cAGEC,UAAU,EACR;YAJJ;UArBG,CAHT;UA+BEjC,EAAE,EAAE;YACFkC,KAAK,EACHC,GADK,IAGLtD,GAAG,CAACyF,sBAAJ,CACEzD,QADF,EAEEoC,UAFF,EAGEW,QAHF,EAIEzB,GAJF,CAJA;YAUFE,MAAM,EACJ,UACEC,MADF,EAEE;cACA,OAAOzD,GAAG,CAAC0F,0BAAJ,CACL1D,QADK,EAELoC,UAFK,EAGLW,QAHK,EAILtB,MAJK,EAKL,MALK,EAMLA,MANK,CAAP;YAQD;UAtBD,CA/BN;UAuDEE,WAAW,EACT3D,GAAG,CAAC4D,EAAJ,CACE,CACE;YACE7B,GAAG,EAAE,SADP;YAEE8B,EAAE,EAAE,UAAU;cACZC;YADY,CAAV,EAED;cACD,OAAO7D,EAAE,CACP,MADO,EAEP;gBACEE,WAAW,EACT,8BAFJ;gBAGEC,KAAK,EACH;kBACEuF,SAAS,EACP,kBAFJ;kBAGEjF,KAAK,EACH,aACAoD,MAAM,CAAC7B;gBALX,CAJJ;gBAWEd,EAAE,EAAE;kBACFyE,SAAS,EACP,UACEnC,MADF,EAEE;oBACA,OAAOzD,GAAG,CAAC6F,SAAJ,CACLpC,MADK,EAELK,MAFK,CAAP;kBAID,CATD;kBAUFgC,QAAQ,EACN,UACErC,MADF,EAEE;oBACAA,MAAM,CAACsC,cAAP;kBACD,CAfD;kBAgBFC,SAAS,EACP,UACEvC,MADF,EAEE;oBACAA,MAAM,CAACsC,cAAP;kBACD,CArBD;kBAsBFE,IAAI,EAAE,UACJxC,MADI,EAEJ;oBACA,OAAOzD,GAAG,CAACiG,IAAJ,CACLxC,MADK,EAELK,MAFK,EAGL9B,QAHK,EAILoC,UAJK,EAKLW,QALK,CAAP;kBAOD;gBAhCC;cAXN,CAFO,EAgDP,CACE9E,EAAE,CACA,GADA,EAEA;gBACEE,WAAW,EACT;cAFJ,CAFA,CADJ,EAQEH,GAAG,CAACM,EAAJ,CACE,MACEN,GAAG,CAACkC,EAAJ,CACE4B,MAAM,CAAC7B,KADT,CADF,GAIE,GALJ,CARF,CAhDO,CAAT;YAiED;UAtEH,CADF,CADF,EA2EE,IA3EF,EA4EE,IA5EF;QAxDJ,CAFA,CADJ,CANA,EAkJA,CAlJA,CA1BJ,CATA,CADJ,EA0LEhC,EAAE,CACA,aADA,EAEA;UACEG,KAAK,EAAE;YACL6B,KAAK,EACH,QAFG;YAGLpB,IAAI,EAAE;UAHD;QADT,CAFA,EASA,CACEZ,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CAAC,GAAD,EAAM;UACNE,WAAW,EACT;QAFI,CAAN,CADJ,EAKEF,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACM,EAAJ,CACE,mBADF,CADS,EAITL,EAAE,CAAC,GAAD,EAAM;UACNE,WAAW,EACT;QAFI,CAAN,CAJO,EAQTH,GAAG,CAACM,EAAJ,CACE,cADF,CARS,CAAT,CALJ,CANA,CADJ,EA0BEL,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CACA,aADA,EAEA;UACEG,KAAK,EAAE;YACLW,KAAK,EACHf,GAAG,CAACkG,kBAAJ,CACElE,QADF,EAEEoC,UAFF,EAGEW,QAHF,CAFG;YAOL,gBACE,SARG;YASLhC,IAAI,EAAE/C,GAAG,CAACmG,OATL;YAULlD,MAAM,EACJ,CACE,QADF,EAEE,QAFF,CAXG;YAeL,gBACE,CACE,IADF,EAEE,IAFF,CAhBG;YAoBLC,MAAM,EACJ;cACEC,SAAS,EACP,UAFJ;cAGEC,UAAU,EACR;YAJJ;UArBG,CADT;UA6BEjC,EAAE,EAAE;YACFkC,KAAK,EACHC,GADK,IAGLtD,GAAG,CAACoG,gBAAJ,CACEpE,QADF,EAEEoC,UAFF,EAGEW,QAHF,EAIEzB,GAJF,CAJA;YAUFE,MAAM,EACJ,UACEC,MADF,EAEE;cACA,OAAOzD,GAAG,CAACqG,gBAAJ,CACLrE,QADK,EAELoC,UAFK,EAGLW,QAHK,EAILtB,MAJK,EAKL,MALK,EAMLA,MANK,CAAP;YAQD;UAtBD,CA7BN;UAqDEE,WAAW,EACT3D,GAAG,CAAC4D,EAAJ,CACE,CACE;YACE7B,GAAG,EAAE,SADP;YAEE8B,EAAE,EAAE,UAAU;cACZC;YADY,CAAV,EAED;cACD,OAAO7D,EAAE,CACP,MADO,EAEP;gBACEE,WAAW,EACT,8BAFJ;gBAGEC,KAAK,EACH;kBACEuF,SAAS,EACP,kBAFJ;kBAGEjF,KAAK,EACH,aACAoD,MAAM,CAAC7B;gBALX,CAJJ;gBAWEd,EAAE,EAAE;kBACFyE,SAAS,EACP,UACEnC,MADF,EAEE;oBACA,OAAOzD,GAAG,CAACsG,UAAJ,CACL7C,MADK,EAELK,MAFK,CAAP;kBAID,CATD;kBAUFgC,QAAQ,EACN,UACErC,MADF,EAEE;oBACAA,MAAM,CAACsC,cAAP;kBACD,CAfD;kBAgBFC,SAAS,EACP,UACEvC,MADF,EAEE;oBACAA,MAAM,CAACsC,cAAP;kBACD,CArBD;kBAsBFE,IAAI,EAAE,UACJxC,MADI,EAEJ;oBACA,OAAOzD,GAAG,CAACuG,KAAJ,CACL9C,MADK,EAELK,MAFK,EAGL9B,QAHK,EAILoC,UAJK,EAKLW,QALK,CAAP;kBAOD;gBAhCC;cAXN,CAFO,EAgDP,CACE9E,EAAE,CACA,GADA,EAEA;gBACEE,WAAW,EACT;cAFJ,CAFA,CADJ,EAQEH,GAAG,CAACM,EAAJ,CACE,MACEN,GAAG,CAACkC,EAAJ,CACE4B,MAAM,CAAC7B,KADT,CADF,GAIE,GALJ,CARF,CAhDO,CAAT;YAiED;UAtEH,CADF,CADF,EA2EE,IA3EF,EA4EE,IA5EF;QAtDJ,CAFA,CADJ,EAyIEhC,EAAE,CACA,KADA,EAEA;UACEE,WAAW,EACT;QAFJ,CAFA,EAMA,CACEF,EAAE,CACA,UADA,EAEA;UACEG,KAAK,EACH;YACEM,KAAK,EACH,oCAFJ;YAGE0B,IAAI,EAAE,SAHR;YAIE2B,QAAQ,EAAE,KAJZ;YAKE,aACE;UANJ;QAFJ,CAFA,CADJ,CANA,EAsBA,CAtBA,CAzIJ,CANA,EAwKA,CAxKA,CA1BJ,CATA,CA1LJ,CAxBA,EAkaA,CAlaA,CADJ,CANA,EA4aA,CA5aA,CAlGJ,CARO,EAyhBP,CAzhBO,CAAT;MA2hBD,CAjiBH,CAJJ,GAuiBI/D,GAAG,CAAC2E,EAAJ,EAxiBC,CAAP;IA0iBD,CA7iBH,CADF,CADJ,GAkjBI3E,GAAG,CAAC2E,EAAJ,EAnjBC,CAAP;EAqjBD,CAtjBD,CADF,CAXA,EAokBA,CApkBA,CADJ,CAHA,EA2kBA,CA3kBA,CAJJ,EAilBE1E,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CAAC,WAAD,EAAc;IAAEkB,EAAE,EAAE;MAAEoB,KAAK,EAAEvC,GAAG,CAACgE;IAAb;EAAN,CAAd,EAA+C,CAC/ChE,GAAG,CAACM,EAAJ,CAAO,KAAP,CAD+C,CAA/C,CADJ,EAIEL,EAAE,CACA,WADA,EAEA;IACEG,KAAK,EAAE;MAAEgC,IAAI,EAAE;IAAR,CADT;IAEEjB,EAAE,EAAE;MACFoB,KAAK,EAAE,UAAUkB,MAAV,EAAkB;QACvBzD,GAAG,CAACwG,wBAAJ;;QACAxG,GAAG,CAACyG,iBAAJ;MACD;IAJC;EAFN,CAFA,EAWA,CAACzG,GAAG,CAACM,EAAJ,CAAO,MAAP,CAAD,CAXA,CAJJ,CAHA,EAqBA,CArBA,CAjlBJ,CAbA,CAtfyC,CAA3C,CA9C0C,CAA5C,CADJ,CAHO,EAkqCP,CAlqCO,CAAT;AAoqCD,CAvqCD;;AAwqCA,IAAIoG,eAAe,GAAG,EAAtB;AACA3G,MAAM,CAAC4G,aAAP,GAAuB,IAAvB;AAEA,SAAS5G,MAAT,EAAiB2G,eAAjB"}, "metadata": {}, "sourceType": "module"}