package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分享图片表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTaskUploadPictures对象", description = "分享图片表")
public class HnslTaskUploadPictures implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "图片编码")
    @TableField("UPLOAD_CODE")
    private String uploadCode;

    @ApiModelProperty(value = "上传图片类型(1.朋友圈截图 2.qq群截图 3.服务截图)")
    @TableField("UPLOAD_TYPE")
    private Integer uploadType;

    @ApiModelProperty(value = "上传图片路径")
    @TableField("UPLOAD_IMAGE")
    private String uploadImage;

    @ApiModelProperty(value = "用户手机号码")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("UPLOAD_STATUS")
    private Integer uploadStatus;

    @ApiModelProperty(value = "上传时间")
    @TableField("UPLOAD_DATE")
    private LocalDate uploadDate;

    @ApiModelProperty(value = "QQ群号码")
    @TableField("QQ_GROUP_NUMBER")
    private String qqGroupNumber;

    @ApiModelProperty(value = "QQ群人数")
    @TableField("QQ_GROUP_PEOPLES")
    private Integer qqGroupPeoples;

    @ApiModelProperty(value = "QQ群昵称")
    @TableField("QQ_GROUP_NAME")
    private String qqGroupName;

    @ApiModelProperty(value = "点赞数")
    @TableField("THUMBS_UP")
    private Integer thumbsUp;

    /**
     * 用于封装数据
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(exist = false)
    private String beginTime;

    @ApiModelProperty(value = "借宿时间")
    @TableField(exist = false)
    private String endTime;
}
