<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslUserCardpoolMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,s.SCHOOL_NAME
        FROM hnsl_user_cardpool a
        left join hnsl_school s on a.SCHOOL_CODE=s.SCHOOL_CODE
        <where>
            <if test="param.cardPoolNumber != null and param.cardPoolNumber != ''">
                AND a.CARD_POOL_NUMBER LIKE CONCAT('%', #{param.cardPoolNumber}, '%')
            </if>
            <if test="param.cardPoolName != null and param.cardPoolName != ''">
                AND a.CARD_POOL_NAME LIKE CONCAT('%', #{param.cardPoolName}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.userId != null">
                AND a.USER_ID LIKE CONCAT('%', #{param.userId}, '%')
            </if>
            <if test="param.userName != null">
                AND a.USER_NAME LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.cityCode != null">
                AND a.CITY_CODE LIKE CONCAT('%', #{param.cityCode}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.schoolName != null and param.schoolName != ''">
                AND s.SCHOOL_NAME LIKE CONCAT('%', #{param.schoolName}, '%')
            </if>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserCardpool">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserCardpool">
        <include refid="selectSql"></include>
    </select>

</mapper>
