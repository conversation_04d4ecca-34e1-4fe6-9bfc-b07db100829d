package com.hlkj.yxsAdminApi.common.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@ConfigurationProperties(prefix = "slauth")
@Component
public class SlAuthConfig {

    private String privateKey;

    private String methodGetAccessToken;

    private String appid;

    private String appkey;

    private String methodGetCode;

    private String clientId;

    private String clientSecret;

    private String redirectUri;

    private String grantType;

    private String survival;

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getMethodGetAccessToken() {
        return methodGetAccessToken;
    }

    public void setMethodGetAccessToken(String methodGetAccessToken) {
        this.methodGetAccessToken = methodGetAccessToken;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getMethodGetCode() {
        return methodGetCode;
    }

    public void setMethodGetCode(String methodGetCode) {
        this.methodGetCode = methodGetCode;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getSurvival() {
        return survival;
    }

    public void setSurvival(String survival) {
        this.survival = survival;
    }
} 