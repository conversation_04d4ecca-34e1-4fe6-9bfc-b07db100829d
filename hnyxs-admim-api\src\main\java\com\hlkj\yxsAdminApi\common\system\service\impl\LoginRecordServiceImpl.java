package com.hlkj.yxsAdminApi.common.system.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.LoginRecord;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.mapper.LoginRecordMapper;
import com.hlkj.yxsAdminApi.common.system.param.LoginRecordParam;
import com.hlkj.yxsAdminApi.common.system.service.LoginRecordService;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 登录日志Service实现
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:14
 */
@Service
public class LoginRecordServiceImpl extends ServiceImpl<LoginRecordMapper, LoginRecord>
        implements LoginRecordService {
    @Autowired
    private UserService userService;
    private static Logger logger = LogManager.getLogger(InterfaceUtil.class);

    @Override
    public PageResult<LoginRecord> pageRel(LoginRecordParam param) {
        PageParam<LoginRecord, LoginRecordParam> page = new PageParam<>(param);
        page.setDefaultOrder("create_time desc");
        return new PageResult<>(baseMapper.selectPageRel(page, param), page.getTotal());
    }

    @Override
    public List<LoginRecord> listRel(LoginRecordParam param) {
        PageParam<LoginRecord, LoginRecordParam> page = new PageParam<>(param);
        page.setDefaultOrder("create_time desc");
        return page.sortRecords(baseMapper.selectListRel(param));
    }

    @Override
    public LoginRecord getByIdRel(Integer id) {
        LoginRecordParam param = new LoginRecordParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Async
    @Override
    public void saveAsync(String username, Integer type, String comments, Integer tenantId,
                          HttpServletRequest request) {
        if (username == null) {
            return;
        }
        LoginRecord loginRecord = new LoginRecord();
        loginRecord.setUsername(username);
        loginRecord.setLoginType(type);
        loginRecord.setComments(comments);
        loginRecord.setTenantId(tenantId);
        UserAgent ua = UserAgentUtil.parse(ServletUtil.getHeaderIgnoreCase(request, "User-Agent"));
        if (ua != null) {
            if (ua.getPlatform() != null) {
                loginRecord.setOs(ua.getPlatform().toString());
            }
            if (ua.getOs() != null) {
                loginRecord.setDevice(ua.getOs().toString());
            }
            if (ua.getBrowser() != null) {
                loginRecord.setBrowser(ua.getBrowser().toString());
            }
        }
        loginRecord.setIp(ServletUtil.getClientIP(request));
        baseMapper.insert(loginRecord);
        // 推送登录日志到湘安平台
        pushLogLogin(username, request, loginRecord);
    }
    private void pushLogLogin(String username, HttpServletRequest request, LoginRecord log) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        List<User> entities = userService.list(queryWrapper);
        if (!entities.isEmpty()) {
            User entity = entities.get(0);
            Map<String, String> personnelInfo = userService.getPortalPersonnelInfo(entity.getIdCard());
            String uid = Optional.ofNullable(personnelInfo)
                    .map(info -> info.get("uid"))
                    .orElse("E000000");
            //标识默认为扫楼系统
            String clientId = "HN-DQ-ZHSL-0024";
            String systemName = "智慧扫楼";
            if (entity.getTenantId()==2){
                clientId="HN-DQ-JYSD-0018";
                systemName = "集约甩单及掌上销H5能力系统";
            }
            String result = InterfaceUtil.logLoginDetails(log, uid, entity.getUsername(), request,clientId,systemName);
            JSONObject jsonObject = JSONObject.parseObject(result);
            int code = jsonObject.getIntValue("code");
            // 如果失败则重新推送一次
            if (code != 0) {
                try {
                    logger.info("————》湘安平台登录日志开始请求重试");
                    Thread.sleep(10000);
                    InterfaceUtil.logLoginDetails(log, uid, entity.getUsername(), request,clientId,systemName);
                } catch (InterruptedException e) {
                    logger.error("湘安平台登录日志重试异常：", e);
                }
            }
        }
    }
}
