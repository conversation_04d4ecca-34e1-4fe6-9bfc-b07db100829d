<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslBlackListMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_black_list a
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.userName != null">
                AND a.user_name LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.userPhone != null">
                AND a.user_phone = #{param.userPhone}
            </if>
            <if test="param.cityCode != null">
                AND a.city_code = #{param.cityCode}
            </if>
            <if test="param.schoolCode != null">
                AND a.school_code = #{param.schoolCode}
            </if>
            <if test="param.offlineQrcode != null">
                AND a.offline_qrcode = #{param.offlineQrcode}
            </if>
            <if test="param.onlineQrcode != null">
                AND a.online_qrcode = #{param.onlineQrcode}
            </if>
            <if test="param.onlineActiveQrcode != null">
                AND a.online_active_qrcode = #{param.onlineActiveQrcode}
            </if>
            <if test="param.selfActiveQrcode != null">
                AND a.self_active_qrcode = #{param.selfActiveQrcode}
            </if>
            <if test="param.createUser != null">
                AND a.create_user LIKE CONCAT('%', #{param.createUser}, '%')
            </if>
            <if test="param.createTime != null">
                AND a.create_time LIKE CONCAT('%', #{param.createTime}, '%')
            </if>
            <if test="param.updateUser != null">
                AND a.update_user LIKE CONCAT('%', #{param.updateUser}, '%')
            </if>
            <if test="param.updateTime != null">
                AND a.update_time LIKE CONCAT('%', #{param.updateTime}, '%')
            </if>
            <if test="param.remarks != null">
                AND a.remarks = #{param.remarks}
            </if>
        </where>
    </sql>

<!--    &lt;!&ndash; 分页查询 &ndash;&gt;-->
<!--    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBlackList">-->
<!--        <include refid="selectSql"></include>-->
<!--    </select>-->

    <select id="queryUserPhone" resultType="java.util.HashMap">
        SELECT * FROM `hnsl_black_list` t
        <where>
            <if test="userName != null and userName !='' ">
                AND t.user_name = #{userName}
            </if>
            <if test="userPhone != null and userPhone !='' ">
                AND t.user_phone = #{userPhone}
            </if>
            <if test="cityCode != null and cityCode != '' ">
                AND t.city_code = #{cityCode}
            </if>
        </where>
    </select>


    <select id="queryListTable" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBlackList">
        SELECT * FROM `hnsl_black_list` t where 1 = 1 and t.user_phone = #{userPhone}
    </select>
</mapper>
