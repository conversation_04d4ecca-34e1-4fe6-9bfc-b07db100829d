package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteApprove;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteApproveParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HnslWhiteApproveMapper extends BaseMapper<HnslWhiteApprove> {

    List<HnslWhiteApprove> selectPageRel(@Param("page") IPage<HnslWhiteApprove> page,
                                      @Param("param") HnslWhiteApproveParam param);
}
