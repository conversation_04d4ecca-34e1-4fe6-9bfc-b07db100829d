package com.hlkj.yxsAdminApi.common.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.amazonaws.services.s3.model.*;
import com.hlkj.yxsAdminApi.common.core.config.AwzS3Service;
import com.hlkj.yxsAdminApi.common.core.utils.AwsS3Utils;
import com.hlkj.yxsAdminApi.common.core.utils.ConstantUtil;
import com.hlkj.yxsAdminApi.common.core.utils.DateUtils;
import com.hlkj.yxsAdminApi.common.core.utils.FileUtil;
import com.hlkj.yxsAdminApi.common.system.entity.R;
import com.hlkj.yxsAdminApi.common.system.service.UploadFileService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.Arrays;

@Slf4j
@Service("uploadFileService")
public class UploadFileServiceImpl implements UploadFileService {
	private static Logger logger = LoggerFactory.getLogger(UploadFileServiceImpl.class);

	@Resource
	AwzS3Service awzS3Service;

	@Autowired
	private AwsS3Utils awsS3Utils;

	@Override
	public R uploadFile(MultipartFile file, Integer maxSizeKb, String bucketPhoto) {
		try {
			if (file.isEmpty()) {
				return R.error(1, "文件内容不可为空！");
			}

			// 文件类型拦截
			String[] allowedFileTypes = { "jpg", "jpeg", "png" }; // 可以根据需要添加或删除文件类型
			// 文件类型
			String imageType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
			if (!Arrays.asList(allowedFileTypes).contains(imageType)) {
				return R.error(1, "不支持的文件类型！");
			}

			// 文件大小拦截
			long maxSize = 5 * 1024 * 1024; // 5MB
			if (file.getSize() > maxSize) {
				return R.error(1, "文件大小超过限制！");
			}

			// 文件名
			String imageName = IdUtil.fastSimpleUUID();

			// 获取文件的字节数组
			byte[] imageBytes = file.getBytes();
			// 使用 Base64 编码器将字节数组编码为 Base64 字符串
			BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));

			// 获取桶名称按月份生成
			String bucketPhotoTop = DateUtils.thisMonthDate;

			// 检查桶是否存在
			boolean isBucketExist = awsS3Utils.getRandomBean().doesBucketExistV2(bucketPhotoTop);
			if (!isBucketExist) {
				try {
					awsS3Utils.getRandomBean().createBucket(bucketPhotoTop);
					log.info("掌上销项目-创建桶成功" + bucketPhotoTop);
				} catch (AmazonS3Exception e) {
					log.error("掌上销项目-创建桶失败 " + bucketPhotoTop + ": " + e.getMessage());
					return R.error(1, "掌上销项目-创建桶失败：" + e.getMessage());
				}
			}

			// 保存到ceph
			ObjectMetadata objectMetadata = new ObjectMetadata();
			objectMetadata.setContentType(imageType);
			objectMetadata.setContentLength(
					new ByteArrayInputStream(FileUtil.getBytesFromBufferedImage(originalImage, imageType)).available());
			PutObjectResult result = awsS3Utils.getRandomBean().putObject(bucketPhotoTop,
					bucketPhoto + "/" + imageName + "." + imageType,
					new ByteArrayInputStream(FileUtil.getBytesFromBufferedImage(originalImage, imageType)),
					objectMetadata);
			log.info("掌上销保存图片到ceph返回结果：", result);
			return R.ok().put("path", bucketPhotoTop + "/" + bucketPhoto + "/" + imageName + "." + imageType).put("url",
					ConstantUtil.INTRANET_URL_IP + bucketPhotoTop + "/" + bucketPhoto + "/" + imageName + "." + imageType)
					.put("name", imageName + "." + imageType);

		} catch (Exception e) {
			logger.error("uploadFileName>>>异常", e);
			return R.error(1, "文件上传异常：" + e.getMessage());
		}
	}

}
