package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslBuildingUserService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBuildingUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslBuildingUserParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 楼栋管理人表控制器
 *
 * <AUTHOR>
 * @since 2023-06-19 10:07:51
 */
@Api(tags = "楼栋管理人表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslBuildingUser")
public class HnslBuildingUserController extends BaseController {
    @Autowired
    private HnslBuildingUserService hnslBuildingUserService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:list')")
    @OperationLog
    @ApiOperation("分页查询楼栋管理人表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslBuildingUser>> page(@RequestBody HnslBuildingUserParam param) {
        PageParam<HnslBuildingUser, HnslBuildingUserParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslBuildingUserService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslBuildingUserService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:list')")
    @OperationLog
    @ApiOperation("查询全部楼栋管理人表")
    @PostMapping("/list")
    public ApiResult<List<HnslBuildingUser>> list(@RequestBody HnslBuildingUserParam param) {
        PageParam<HnslBuildingUser, HnslBuildingUserParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslBuildingUserService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslBuildingUserService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:list')")
    @OperationLog
    @ApiOperation("根据id查询楼栋管理人表")
    @GetMapping("/{id}")
    public ApiResult<HnslBuildingUser> get(@PathVariable("id") Integer id) {
        return success(hnslBuildingUserService.getById(id));
        // 使用关联查询
        //return success(hnslBuildingUserService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:save')")
    @OperationLog
    @ApiOperation("添加楼栋管理人表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslBuildingUser hnslBuildingUser) {
        if (hnslBuildingUserService.save(hnslBuildingUser)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:update')")
    @OperationLog
    @ApiOperation("修改楼栋管理人表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslBuildingUser hnslBuildingUser) {
        if (hnslBuildingUserService.removeById(hnslBuildingUser)) {
            User loginUser = getLoginUser();
            String userPhones=hnslBuildingUser.getUserPhone();
            if(userPhones.indexOf(",")!=-1){
                String[] userPhone=userPhones.split(",");
                for(int i=0;i<userPhone.length;i++){
                    HnslBuildingUser buildingUser=hnslBuildingUser;
                    buildingUser.setStatus(1);
                    buildingUser.setUserId(userPhone[i]);
                    buildingUser.setCreatedUser(loginUser.getUsername());
                    hnslBuildingUserService.save(buildingUser);
                }
            }else{
                if(!StringUtil.isEmpty(userPhones)){
                    hnslBuildingUser.setStatus(1);
                    hnslBuildingUser.setUserId(userPhones);
                    hnslBuildingUser.setCreatedUser(loginUser.getUsername());
                    hnslBuildingUserService.save(hnslBuildingUser);
                }
            }
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:remove')")
    @OperationLog
    @ApiOperation("删除楼栋管理人表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslBuildingUserService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:save')")
    @OperationLog
    @ApiOperation("批量添加楼栋管理人表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslBuildingUser> list) {
        if (hnslBuildingUserService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:update')")
    @OperationLog
    @ApiOperation("批量修改楼栋管理人表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslBuildingUser> batchParam) {
        if (batchParam.update(hnslBuildingUserService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuildingUser:remove')")
    @OperationLog
    @ApiOperation("批量删除楼栋管理人表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslBuildingUserService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
