package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSchool;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslStudentApprovalService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslStudentApproval;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslStudentApprovalParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserSchoolService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import com.hlkj.yxsAdminApi.hnzhsl.utils.ExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-07-21 11:31:45
 */
@Api(tags = "管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslStudentApproval")
public class HnslStudentApprovalController extends BaseController {
    @Autowired
    private HnslStudentApprovalService hnslStudentApprovalService;
    @Autowired
    private HnslUserService hnslUserService;
    @Autowired
    private QueryUserManagerUtil queryUserManagerUtil;
    @Autowired
    private AwsS3Utils awsS3Utils;
    @Autowired
    private RedisUtil redisUtils;
    @Autowired
    private HnslUserSchoolService hnslUserSchoolService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:list')")
    @OperationLog
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslStudentApproval>> page(@RequestBody HnslStudentApprovalParam param) {
        PageParam<HnslStudentApproval, HnslStudentApprovalParam> page = new PageParam<>(param);
        QueryWrapper<HnslStudentApproval> wrapper = page.getWrapper();
        //page.setDefaultOrder("created_date desc");
        
        // 获取当前用户信息
        User user = getLoginUser();
        String hnslType = redisUtils.getString("hnslChannel" + user.getPhone());
        String userSf = redisUtils.getString("thisUserSf" + user.getPhone());
        String cityCode = redisUtils.getString("cityCode" + user.getPhone());
        String userName = redisUtils.getString("thisUserName" + user.getPhone());
        String userPhone = redisUtils.getString("thisUserPhone" + user.getPhone());
        
        // 根据用户角色设置查询条件
        if ("6".equals(userSf)) { // 地市管理员
            wrapper.eq("CITY_CODE", cityCode);
        } else if ("1".equals(userSf)) { // 校园经理
            LambdaQueryWrapper<HnslUserSchool> hnslSchoolWrapper = new LambdaQueryWrapper<>();
            hnslSchoolWrapper.eq(HnslUserSchool::getUserPhone, userPhone);
            List<HnslUserSchool> userSchoolList = hnslUserSchoolService.list(hnslSchoolWrapper);
            List<String> schoolNames = userSchoolList.stream().map(HnslUserSchool::getSchoolName).collect(Collectors.toList());
            if (!schoolNames.isEmpty()) {
                wrapper.in("SCHOOL_NAME", schoolNames);
            }
        }
        
        // 处理手机号/姓名搜索条件
        String userValue = param.getUserValue();
        if (!StringUtil.isNull(userValue)) {
            userValue = userValue.trim();
            if (userValue.length() == 11) {
                wrapper.eq("USER_PHONE", userValue);
            } else {
                wrapper.like("USER_NAME", userValue);
            }
        }
        
        // 处理渠道类型
        wrapper.eq(!"1".equals(hnslType), "HNSL_CHANNEL", hnslType);
        
        // 处理账期查询
        wrapper.eq(StringUtil.isNotNull(param.getAuditDate()), "AUDIT_DATE", param.getAuditDate());
        
        // 处理学校名称查询
        wrapper.like(StringUtil.isNotNull(param.getSchoolName()), "SCHOOL_NAME", param.getSchoolName());
        
        // 处理团队类型查询
        wrapper.eq(StringUtil.isNotNull(param.getTeamLevel()) && !"0".equals(param.getTeamLevel()), 
                "TEAM_LEVEL", param.getTeamLevel());
        
        // 处理时间范围查询
        if (StringUtil.isNotNull(param.getBeginTime())) {
            wrapper.apply("CREATED_DATE>={0} and CREATED_DATE<={1}", param.getBeginTime(), param.getEndTime());
        }
        
        // 处理积分范围查询
        wrapper.ge(param.getUserJF1() != null, "USER_INTEGRATION", param.getUserJF1());
        wrapper.le(param.getUserJF2() != null, "USER_INTEGRATION", param.getUserJF2());
        wrapper.ge(param.getTeamJF1() != null, "TEAM_INTEGRATION", param.getTeamJF1());
        wrapper.le(param.getTeamJF2() != null, "TEAM_INTEGRATION", param.getTeamJF2());
        
        // 处理状态查询
        if (param.getStatus() != null && !"-1".equals(String.valueOf(param.getStatus())) && !"0".equals(String.valueOf(param.getStatus()))) {
            wrapper.eq("STATUS", param.getStatus());
        } else {
            wrapper.ne("STATUS", 0);
        }
        
        // 获取分页结果
        IPage<HnslStudentApproval> iPage = hnslStudentApprovalService.page(page, wrapper);
        PageResult<HnslStudentApproval> pageResult = new PageResult<>(iPage.getRecords(), iPage.getTotal());
        
        // 设置用户ID
        if (pageResult != null && pageResult.getList() != null) {
            for (HnslStudentApproval approval : pageResult.getList()) {
                // 设置合伙人ID
                List<HnslUser> users = hnslUserService.queryUser(approval.getUserPhone());
                if (users != null && !users.isEmpty()) {
                    approval.setHhrUserId(String.valueOf(users.get(0).getId()));
                }
                
                // 敏感数据脱敏
                approval.setUserName(DesensitizationUtil.desensitizedName(approval.getUserName()));
                approval.setUserCard(DesensitizationUtil.idCardDesensitization(approval.getUserCard()));
                approval.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(approval.getUserPhone()));
                approval.setUserBankNumber(DesensitizationUtil.acctNoDesensitization(approval.getUserBankNumber()));
            }
        }
        
        return success(pageResult);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:list')")
    @OperationLog
    @ApiOperation("查询全部")
    @PostMapping("/list")
    public ApiResult<List<HnslStudentApproval>> list(@RequestBody HnslStudentApprovalParam param) {
        PageParam<HnslStudentApproval, HnslStudentApprovalParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<HnslStudentApproval> list = hnslStudentApprovalService.list(page.getOrderWrapper());
        
        // 对敏感数据进行脱敏处理
        if (list != null && !list.isEmpty()) {
            for (HnslStudentApproval approval : list) {
                // 敏感数据脱敏
                approval.setUserName(DesensitizationUtil.desensitizedName(approval.getUserName()));
                approval.setUserCard(DesensitizationUtil.idCardDesensitization(approval.getUserCard()));
                approval.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(approval.getUserPhone()));
                approval.setUserBankNumber(DesensitizationUtil.acctNoDesensitization(approval.getUserBankNumber()));
            }
        }
        
        return success(list);
        // 使用关联查询
        //return success(hnslStudentApprovalService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:list')")
    @OperationLog
    @ApiOperation("根据id查询")
    @GetMapping("/{id}")
    public ApiResult<HnslStudentApproval> get(@PathVariable("id") Integer id) {
        HnslStudentApproval approval = hnslStudentApprovalService.getById(id);
        
        // 对敏感数据进行脱敏处理
        if (approval != null) {
            approval.setUserName(DesensitizationUtil.desensitizedName(approval.getUserName()));
            approval.setUserCard(DesensitizationUtil.idCardDesensitization(approval.getUserCard()));
            approval.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(approval.getUserPhone()));
            approval.setUserBankNumber(DesensitizationUtil.acctNoDesensitization(approval.getUserBankNumber()));
        }
        
        return success(approval);
        // 使用关联查询
        //return success(hnslStudentApprovalService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:save')")
    @OperationLog
    @ApiOperation("添加")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslStudentApproval hnslStudentApproval) {
        if (hnslStudentApprovalService.save(hnslStudentApproval)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:update')")
    @OperationLog
    @ApiOperation("修改")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslStudentApproval hnslStudentApproval) {
        // 获取原始数据，用于替换脱敏字段
        HnslStudentApproval originalApproval = hnslStudentApprovalService.getById(hnslStudentApproval.getId());
        
        // 处理敏感字段脱敏问题
        // 1. 检查学子姓名
        if (hnslStudentApproval.getUserName() != null && hnslStudentApproval.getUserName().contains("*")) {
            hnslStudentApproval.setUserName(originalApproval.getUserName());
        }
        
        // 2. 检查身份证号
        if (hnslStudentApproval.getUserCard() != null && hnslStudentApproval.getUserCard().contains("*")) {
            hnslStudentApproval.setUserCard(originalApproval.getUserCard());
        }
        
        // 3. 检查手机号码
        if (hnslStudentApproval.getUserPhone() != null && hnslStudentApproval.getUserPhone().contains("*")) {
            hnslStudentApproval.setUserPhone(originalApproval.getUserPhone());
        }
        
        // 4. 检查银行账号
        if (hnslStudentApproval.getUserBankNumber() != null && hnslStudentApproval.getUserBankNumber().contains("*")) {
            hnslStudentApproval.setUserBankNumber(originalApproval.getUserBankNumber());
        }
        
        if (hnslStudentApprovalService.updateById(hnslStudentApproval)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:remove')")
    @OperationLog
    @ApiOperation("删除")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslStudentApprovalService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:save')")
    @OperationLog
    @ApiOperation("批量添加")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslStudentApproval> list) {
        User loginUser = getLoginUser();
        for(HnslStudentApproval ae:list){
            QueryWrapper<HnslStudentApproval> hnslStudentApprovalEntityQueryWrapper = new QueryWrapper<>();
            hnslStudentApprovalEntityQueryWrapper.eq("USER_PHONE",ae.getUserPhone())
                    .eq("STATUS",1)
                    .eq("TEAM_NAME",ae.getTeamName())
                    .eq("AUDIT_DATE",ae.getAuditDate());
            int count = hnslStudentApprovalService.count(hnslStudentApprovalEntityQueryWrapper);
            if(count>0){
                return fail("提交失败,已有待审核数据");
            }else{
                ae.setStatus(1);
                ae.setCreatedDate(new Date());
                ae.setCreatedUser(loginUser.getUsername());
            }
        }
        if (hnslStudentApprovalService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:update')")
    @OperationLog
    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody List<HnslStudentApproval> batchParam) {
        User loginUser = getLoginUser();
        List<Integer> collect = batchParam.stream().map(id -> id.getId()).collect(Collectors.toList());
        QueryWrapper<HnslStudentApproval> hnslStudentApprovalEntityQueryWrapper = new QueryWrapper<>();
        hnslStudentApprovalEntityQueryWrapper.in("ID",collect);
        List<HnslStudentApproval> list = hnslStudentApprovalService.list(hnslStudentApprovalEntityQueryWrapper);
        for(HnslStudentApproval pojo:list){
            for(HnslStudentApproval id:batchParam){
                if(pojo.getId().equals(id.getId())){
                    // 处理敏感字段脱敏问题
                    // 1. 检查学子姓名
                    if (id.getUserName() != null && id.getUserName().contains("*")) {
                        id.setUserName(pojo.getUserName());
                    }
                    
                    // 2. 检查身份证号
                    if (id.getUserCard() != null && id.getUserCard().contains("*")) {
                        id.setUserCard(pojo.getUserCard());
                    }
                    
                    // 3. 检查手机号码
                    if (id.getUserPhone() != null && id.getUserPhone().contains("*")) {
                        id.setUserPhone(pojo.getUserPhone());
                    }
                    
                    // 4. 检查银行账号
                    if (id.getUserBankNumber() != null && id.getUserBankNumber().contains("*")) {
                        id.setUserBankNumber(pojo.getUserBankNumber());
                    }
                    
                    pojo.setStatus(id.getStatus());
                    if("5".equals(loginUser.getRoles())){
                        pojo.setProvincialDate(new Date());
                        pojo.setProvincialUser(loginUser.getUsername());
                    }else{
                        pojo.setReportPicture(id.getReportPicture());
                        pojo.setUpdatedDate(new Date());
                        pojo.setUpdatedUser(loginUser.getUsername());
                    }
                    break;
                }
            }
        }
        if (hnslStudentApprovalService.updateBatchById(batchParam)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:remove')")
    @OperationLog
    @ApiOperation("批量删除")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslStudentApprovalService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:output')")
    @OperationLog
    @ApiOperation("导出审核管理表")
    @PostMapping("/outputReviewList")
    public ApiResult<?> outputReviewList(@RequestBody Map<String,Object> params, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        ServletContext servletContext = request.getSession().getServletContext();
        QueryWrapper<HnslStudentApproval> hnslStudentApprovalEntityQueryWrapper = new QueryWrapper<>();

        String value=String.valueOf(params.get("userValue"));
        if(!StringUtil.isEmpty(value)){
            value=value.trim();
            if(value.length()==11){
                hnslStudentApprovalEntityQueryWrapper.eq("USER_PHONE",value);
            }else{
                hnslStudentApprovalEntityQueryWrapper.like("USER_NAME",value);
            }
        }
        User loginUser = getLoginUser();
//        if ("6".equals(UserSf)) {//地市管理员
//            hnslStudentApprovalEntityQueryWrapper.eq("CITY_CODE", cityCode);
//        }else if ("1".equals(UserSf)) {//校园经理
//            HnslUserSchoolEntity hnslUserSchoolEntity = new HnslUserSchoolEntity();
//            hnslUserSchoolEntity.setUserPhone(UserPhone);
//            List<HnslUserSchoolEntity> hnslUserSchoolEntities = hnslUserSchoolService.queryObjectBy(hnslUserSchoolEntity);
//            List<String> collect = hnslUserSchoolEntities.stream().map(w -> w.getSchoolName()).collect(Collectors.toList());
//            hnslStudentApprovalEntityQueryWrapper.in("SCHOOL_NAME", collect);
//        }
        hnslStudentApprovalEntityQueryWrapper.eq(StringUtil.isNotNull(String.valueOf(params.get("auditDate")))
                ,"AUDIT_DATE",params.get("auditDate"));
        hnslStudentApprovalEntityQueryWrapper.like(StringUtil.isNotNull(String.valueOf(params.get("schoolName")))
                ,"SCHOOL_NAME",params.get("schoolName"));

        hnslStudentApprovalEntityQueryWrapper.eq(StringUtil.isNotNull(String.valueOf(params.get("teamLevel"))) && !"0".equals(String.valueOf(params.get("teamLevel")))
                ,"TEAM_LEVEL",params.get("teamLevel"));
        if(StringUtil.isNotNull(String.valueOf(params.get("beginTime")))){
            hnslStudentApprovalEntityQueryWrapper.apply("CREATED_DATE>={0} and CREATED_DATE<={1}",params.get("beginTime"),params.get("endTime"));
        }
        hnslStudentApprovalEntityQueryWrapper.ge(StringUtil.isNotNull(String.valueOf(params.get("userJF1")))
                ,"USER_INTEGRATION",params.get("userJF1"));
        hnslStudentApprovalEntityQueryWrapper.le(StringUtil.isNotNull(String.valueOf(params.get("userJF2")))
                ,"USER_INTEGRATION",params.get("userJF2"));
        hnslStudentApprovalEntityQueryWrapper.ge(StringUtil.isNotNull(String.valueOf(params.get("teamJF1")))
                ,"TEAM_INTEGRATION",params.get("teamJF1"));
        hnslStudentApprovalEntityQueryWrapper.le(StringUtil.isNotNull(String.valueOf(params.get("teamJF2")))
                ,"TEAM_INTEGRATION",params.get("teamJF2"));
        //查询列表数据
        if(StringUtil.isNotNull(String.valueOf(params.get("status")))
                && !"-1".equals(String.valueOf(params.get("status")))
                && !"0".equals(String.valueOf(params.get("status")))){
            hnslStudentApprovalEntityQueryWrapper.eq("STATUS",params.get("status"));
        }else{
            hnslStudentApprovalEntityQueryWrapper.ne("STATUS",0);
        }
        //查询列表数据
        List<HnslStudentApproval> list = hnslStudentApprovalService.list(hnslStudentApprovalEntityQueryWrapper);
        list.forEach(item -> {
            List<HnslUser> entity = hnslUserService.queryUser(item.getUserPhone());
            if (!entity.isEmpty() && entity.size() > 0) {
                for (HnslUser entity1 : entity) {
                    item.setHhrUserId(String.valueOf(entity1.getId()));
                }
            }
        });
        // 生成一条记录
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf1.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "薪资审批表" + batchCode;
        String filePath = "uploads/" + fileName;
        Integer activeEvent = 9;
        Integer appSettingId = (Integer) params.get("appSettingId");
        Integer lists = list.size();
        User user = getLoginUser();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, lists, appSettingId);


        SimpleDateFormat sdf=new SimpleDateFormat("YYYY-MM-dd");
        //List<HnslSalaryReviewEntity> hnslSalaryReviewList = hnslSalaryReviewService.queryList(params);
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 20 * 256);
        sheet.setColumnWidth(7, 20 * 300);
        sheet.setColumnWidth(8, 20 * 256);
        sheet.setColumnWidth(9, 20 * 256);
        sheet.setColumnWidth(10, 20 * 256);
        sheet.setColumnWidth(11, 20 * 256);
        sheet.setColumnWidth(12, 20 * 300);
        sheet.setColumnWidth(13, 20 * 256);
        sheet.setColumnWidth(14, 20 * 256);
        sheet.setColumnWidth(15, 20 * 256);
        sheet.setColumnWidth(16, 20 * 256);
        sheet.setColumnWidth(17, 20 * 300);
        sheet.setColumnWidth(18, 20 * 256);
        sheet.setColumnWidth(19, 20 * 256);
        sheet.setColumnWidth(20, 20 * 256);
        sheet.setColumnWidth(21, 20 * 256);
        sheet.setColumnWidth(22, 20 * 300);
        sheet.setColumnWidth(23, 20 * 256);
        sheet.setColumnWidth(24, 20 * 256);
        sheet.setColumnWidth(25, 20 * 256);
        sheet.setColumnWidth(26, 20 * 256);
        sheet.setColumnWidth(27, 20 * 300);
        sheet.setColumnWidth(28, 20 * 256);
        sheet.setColumnWidth(29, 20 * 256);
        sheet.setColumnWidth(30, 20 * 256);
        sheet.setColumnWidth(31, 20 * 256);
        sheet.setColumnWidth(32, 20 * 256);
        sheet.setColumnWidth(33, 20 * 300);
        sheet.setColumnWidth(34, 20 * 256);
        sheet.setColumnWidth(35, 20 * 256);
        sheet.setColumnWidth(36, 20 * 256);
        sheet.setColumnWidth(37, 20 * 256);
        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
        HSSFPatriarch drawingPatriarch = sheet.createDrawingPatriarch();
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("账期");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("提交时间");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("纸质签章报告");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("状态");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("地市审批人");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("地市审核时间");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("省级审批人");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("省级审核时间");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("学校");
        cell = row.createCell(10);
        cell.setCellValue("姓名");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("团队类型");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("身份证号");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("年龄");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("工号");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("服务时长");
        cell.setCellStyle(style);
        cell = row.createCell(16);
        cell.setCellValue("团队名称");
        cell.setCellStyle(style);
        cell = row.createCell(17);
        cell.setCellValue("团队人数");
        cell.setCellStyle(style);
        cell = row.createCell(18);
        cell.setCellValue("KPI考核");
        cell.setCellStyle(style);
        cell = row.createCell(38);
        cell.setCellValue("注册时间");
        cell.setCellStyle(style);
        cell = row.createCell(19);
        cell.setCellValue("新装数量");
        cell.setCellStyle(style);
        cell = row.createCell(20);
        cell.setCellValue("新装积分");
        cell.setCellStyle(style);
        cell = row.createCell(21);
        cell.setCellValue("维系数量");
        cell.setCellStyle(style);
        cell = row.createCell(22);
        cell.setCellValue("维系积分");
        cell.setCellStyle(style);
        cell = row.createCell(23);
        cell.setCellValue("院系新装数量");
        cell.setCellStyle(style);
        cell = row.createCell(24);
        cell.setCellValue("院系新装积分");
        cell.setCellStyle(style);
        cell = row.createCell(25);
        cell.setCellValue("院系维系数量");
        cell.setCellStyle(style);
        cell = row.createCell(26);
        cell.setCellValue("院系维系积分");
        cell.setCellStyle(style);
        cell = row.createCell(27);
        cell.setCellValue("其他积分");
        cell.setCellStyle(style);
        cell = row.createCell(28);
        cell.setCellValue("个人积分");
        cell.setCellStyle(style);
        cell = row.createCell(29);
        cell.setCellValue("团队积分");
        cell.setCellStyle(style);
        cell = row.createCell(30);
        cell.setCellValue("院系团队积分");
        cell.setCellStyle(style);
        cell = row.createCell(31);
        cell.setCellValue("个人服务费");
        cell.setCellStyle(style);
        cell = row.createCell(32);
        cell.setCellValue("团队管理费");
        cell.setCellStyle(style);
        cell = row.createCell(33);
        cell.setCellValue("应发");
        cell.setCellStyle(style);
        cell = row.createCell(34);
        cell.setCellValue("个税");
        cell.setCellStyle(style);
        cell = row.createCell(35);
        cell.setCellValue("实发");
        cell.setCellStyle(style);
        cell = row.createCell(36);
        cell.setCellValue("银行卡号");
        cell.setCellStyle(style);
        cell = row.createCell(37);
        cell.setCellValue("银行名称");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != list && list.size() != 0) {

            for (int i = 0; i < list.size(); i++) {
                HnslStudentApproval hnslStudentApprovalEntity = list.get(i);
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(hnslStudentApprovalEntity.getAuditDate()));
                row.createCell(1).setCellValue(sdf.format(hnslStudentApprovalEntity.getCreatedDate()));

                if(hnslStudentApprovalEntity.getStatus()==1){
                    row.createCell(3).setCellValue("审核中");
                }else if(hnslStudentApprovalEntity.getStatus()==2){
                    row.createCell(3).setCellValue("地市审核通过");
                }else if(hnslStudentApprovalEntity.getStatus()==3){
                    row.createCell(3).setCellValue("地市驳回");
                }else if(hnslStudentApprovalEntity.getStatus()==4){
                    row.createCell(3).setCellValue("省级审核通过");
                }else if(hnslStudentApprovalEntity.getStatus()==5){
                    row.createCell(3).setCellValue("省级驳回");
                }else{
                    row.createCell(3).setCellValue("其他");
                }
                if(StringUtil.isNotNull(hnslStudentApprovalEntity.getUpdatedUser())){
                    row.createCell(4).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUpdatedUser()));
                    row.createCell(5).setCellValue(sdf.format(hnslStudentApprovalEntity.getUpdatedDate()));
                }else{
                    row.createCell(4).setCellValue("");
                    row.createCell(5).setCellValue("");
                }
                if(StringUtil.isNotNull(hnslStudentApprovalEntity.getProvincialUser())){
                    row.createCell(6).setCellValue(String.valueOf(hnslStudentApprovalEntity.getProvincialUser()));
                    row.createCell(7).setCellValue(sdf.format(hnslStudentApprovalEntity.getProvincialDate()));
                }else{
                    row.createCell(6).setCellValue("");
                    row.createCell(7).setCellValue("");
                }

                row.createCell(8).setCellValue(String.valueOf(hnslStudentApprovalEntity.getCityCode()));
                row.createCell(9).setCellValue(String.valueOf(hnslStudentApprovalEntity.getSchoolName()));
                row.createCell(10).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserName()));
                row.createCell(11).setCellValue(hnslStudentApprovalEntity.getTeamLevel());
                row.createCell(12).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserCard()));
                row.createCell(13).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserAge()));
                row.createCell(14).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserPhone()));
                row.createCell(15).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserServiceTime()));
                row.createCell(16).setCellValue(String.valueOf(hnslStudentApprovalEntity.getTeamName()));
                String teamCount="0";
                if(StringUtil.isNotNull(String.valueOf(hnslStudentApprovalEntity.getTeamCount()))){
                    teamCount=String.valueOf(hnslStudentApprovalEntity.getTeamCount());
                }
                row.createCell(17).setCellValue(teamCount);
                row.createCell(18).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserKpi()));
                row.createCell(38).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserDate()));
                row.createCell(19).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber1()));
                row.createCell(20).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration1()));
                row.createCell(21).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber2()));
                row.createCell(22).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration2()));
                row.createCell(23).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber4()));
                row.createCell(24).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration4()));
                row.createCell(25).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber5()));
                row.createCell(26).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration5()));
                row.createCell(27).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration3()));

                row.createCell(28).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserIntegration()));
                row.createCell(29).setCellValue(String.valueOf(hnslStudentApprovalEntity.getTeamIntegration()));
                row.createCell(30).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration6()));

                row.createCell(31).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney1()));
                row.createCell(32).setCellValue(hnslStudentApprovalEntity.getMoney2());
                row.createCell(33).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney3()));
                row.createCell(34).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney4()));
                row.createCell(35).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney5()));
                row.createCell(36).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserBankNumber()));
                row.createCell(37).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserBankName()));
                if(StringUtil.isNull(hnslStudentApprovalEntity.getReportPicture())){
                    row.createCell(2).setCellValue("未上传");
                }else if(!hnslStudentApprovalEntity.getReportPicture().contains("uploads")){
                    row.createCell(2).setCellValue("上传图片异常");
                }else{
                    row.setHeight((short) (50*20));
                    try{
                        RequestUtil.drawPictureInfoExcel(wb,drawingPatriarch,i + 1,"http://134.175.22.213/hnzhsls/"+hnslStudentApprovalEntity.getReportPicture());
                    }catch (Exception e){
                        logger.error("下载图片异常!"+e.getMessage());
                    }
                }
            }
            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("薪资审批表导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }



    @Autowired
    private ConfigProperties config;

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:output')")
    @OperationLog
    @ApiOperation("导出薪资清单表")
    @PostMapping("/outputSalaryStatement")
    public ApiResult<?> outputSalaryStatement(@RequestBody Map<String,Object> params, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        ServletContext servletContext = request.getSession().getServletContext();
        QueryWrapper<HnslStudentApproval> hnslStudentApprovalEntityQueryWrapper = new QueryWrapper<>();

        String value=String.valueOf(params.get("userValue"));
        if(!StringUtil.isEmpty(value)){
            value=value.trim();
            if(value.length()==11){
                hnslStudentApprovalEntityQueryWrapper.eq("USER_PHONE",value);
            }else{
                hnslStudentApprovalEntityQueryWrapper.like("USER_NAME",value);
            }
        }
        hnslStudentApprovalEntityQueryWrapper.eq(StringUtil.isNotNull(String.valueOf(params.get("auditDate")))
                ,"AUDIT_DATE",params.get("auditDate"));
        hnslStudentApprovalEntityQueryWrapper.like(StringUtil.isNotNull(String.valueOf(params.get("schoolName")))
                ,"SCHOOL_NAME",params.get("schoolName"));

        hnslStudentApprovalEntityQueryWrapper.eq(StringUtil.isNotNull(String.valueOf(params.get("teamLevel"))) && !"0".equals(String.valueOf(params.get("teamLevel")))
                ,"TEAM_LEVEL",params.get("teamLevel"));
        if(StringUtil.isNotNull(String.valueOf(params.get("beginTime")))){
            hnslStudentApprovalEntityQueryWrapper.apply("CREATED_DATE>={0} and CREATED_DATE<={1}",params.get("beginTime"),params.get("endTime"));
        }
        hnslStudentApprovalEntityQueryWrapper.ge(StringUtil.isNotNull(String.valueOf(params.get("userJF1")))
                ,"USER_INTEGRATION",params.get("userJF1"));
        hnslStudentApprovalEntityQueryWrapper.le(StringUtil.isNotNull(String.valueOf(params.get("userJF2")))
                ,"USER_INTEGRATION",params.get("userJF2"));
        hnslStudentApprovalEntityQueryWrapper.ge(StringUtil.isNotNull(String.valueOf(params.get("teamJF1")))
                ,"TEAM_INTEGRATION",params.get("teamJF1"));
        hnslStudentApprovalEntityQueryWrapper.le(StringUtil.isNotNull(String.valueOf(params.get("teamJF2")))
                ,"TEAM_INTEGRATION",params.get("teamJF2"));

        //查询列表数据
        hnslStudentApprovalEntityQueryWrapper.in("STATUS",Arrays.asList(4));
        List<HnslStudentApproval> list = hnslStudentApprovalService.list(hnslStudentApprovalEntityQueryWrapper);
        list.forEach(item -> {
            List<HnslUser> entity = hnslUserService.queryUser(item.getUserPhone());
            if (!entity.isEmpty() && entity.size() > 0) {
                for (HnslUser entity1 : entity) {
                    item.setHhrUserId(String.valueOf(entity1.getId()));
                }
            }
        });

        // 生成一条记录
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf1.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "薪资清单" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath);
        Integer activeEvent = 11;
        Integer appSettingId = (Integer) params.get("appSettingId");
        Integer lists = list.size();
        User user = getLoginUser();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, lists, appSettingId);

        SimpleDateFormat sdf=new SimpleDateFormat("YYYY-MM-dd");
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        //设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 20 * 256);
        sheet.setColumnWidth(7, 20 * 300);
        sheet.setColumnWidth(8, 20 * 256);
        sheet.setColumnWidth(9, 20 * 256);
        sheet.setColumnWidth(10, 20 * 256);
        sheet.setColumnWidth(11, 20 * 256);
        sheet.setColumnWidth(12, 20 * 300);
        sheet.setColumnWidth(13, 20 * 256);
        sheet.setColumnWidth(14, 20 * 256);
        sheet.setColumnWidth(15, 20 * 256);
        sheet.setColumnWidth(16, 20 * 256);
        sheet.setColumnWidth(17, 20 * 300);
        sheet.setColumnWidth(18, 20 * 256);
        sheet.setColumnWidth(19, 20 * 256);
        sheet.setColumnWidth(20, 20 * 256);
        sheet.setColumnWidth(21, 20 * 256);
        sheet.setColumnWidth(22, 20 * 300);
        sheet.setColumnWidth(23, 20 * 256);
        sheet.setColumnWidth(24, 20 * 256);
        sheet.setColumnWidth(25, 20 * 256);
        sheet.setColumnWidth(26, 20 * 256);
        sheet.setColumnWidth(27, 20 * 300);
        sheet.setColumnWidth(28, 20 * 256);
        sheet.setColumnWidth(29, 20 * 256);
        sheet.setColumnWidth(30, 20 * 256);
        sheet.setColumnWidth(31, 20 * 256);
        sheet.setColumnWidth(32, 20 * 256);
        sheet.setColumnWidth(33, 20 * 300);
        sheet.setColumnWidth(34, 20 * 256);
        sheet.setColumnWidth(35, 20 * 256);
        sheet.setColumnWidth(36, 20 * 256);
        sheet.setColumnWidth(37, 20 * 256);
        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
        HSSFPatriarch drawingPatriarch = sheet.createDrawingPatriarch();
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("账期");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("提交时间");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("纸质签章报告");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("状态");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("地市审批人");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("地市审核时间");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("省级审批人");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("省级审核时间");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("学校");
        cell = row.createCell(10);
        cell.setCellValue("姓名");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("团队类型");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("身份证号");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("年龄");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("工号");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("服务时长");
        cell.setCellStyle(style);
        cell = row.createCell(16);
        cell.setCellValue("团队名称");
        cell.setCellStyle(style);
        cell = row.createCell(17);
        cell.setCellValue("团队人数");
        cell.setCellStyle(style);
        cell = row.createCell(18);
        cell.setCellValue("KPI考核");
        cell.setCellStyle(style);
        cell = row.createCell(38);
        cell.setCellValue("注册时间");
        cell.setCellStyle(style);
        cell = row.createCell(19);
        cell.setCellValue("新装数量");
        cell.setCellStyle(style);
        cell = row.createCell(20);
        cell.setCellValue("新装积分");
        cell.setCellStyle(style);
        cell = row.createCell(21);
        cell.setCellValue("维系数量");
        cell.setCellStyle(style);
        cell = row.createCell(22);
        cell.setCellValue("维系积分");
        cell.setCellStyle(style);
        cell = row.createCell(23);
        cell.setCellValue("院系新装数量");
        cell.setCellStyle(style);
        cell = row.createCell(24);
        cell.setCellValue("院系新装积分");
        cell.setCellStyle(style);
        cell = row.createCell(25);
        cell.setCellValue("院系维系数量");
        cell.setCellStyle(style);
        cell = row.createCell(26);
        cell.setCellValue("院系维系积分");
        cell.setCellStyle(style);
        cell = row.createCell(27);
        cell.setCellValue("其他积分");
        cell.setCellStyle(style);
        cell = row.createCell(28);
        cell.setCellValue("个人积分");
        cell.setCellStyle(style);
        cell = row.createCell(29);
        cell.setCellValue("团队积分");
        cell.setCellStyle(style);
        cell = row.createCell(30);
        cell.setCellValue("院系团队积分");
        cell.setCellStyle(style);
        cell = row.createCell(31);
        cell.setCellValue("个人服务费");
        cell.setCellStyle(style);
        cell = row.createCell(32);
        cell.setCellValue("团队管理费");
        cell.setCellStyle(style);
        cell = row.createCell(33);
        cell.setCellValue("应发");
        cell.setCellStyle(style);
        cell = row.createCell(34);
        cell.setCellValue("个税");
        cell.setCellStyle(style);
        cell = row.createCell(35);
        cell.setCellValue("实发");
        cell.setCellStyle(style);
        cell = row.createCell(36);
        cell.setCellValue("银行卡号");
        cell.setCellStyle(style);
        cell = row.createCell(37);
        cell.setCellValue("银行名称");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != list && list.size() != 0) {

            for (int i = 0; i < list.size(); i++) {
                HnslStudentApproval hnslStudentApprovalEntity = list.get(i);
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(hnslStudentApprovalEntity.getAuditDate()));
                row.createCell(1).setCellValue(sdf.format(hnslStudentApprovalEntity.getCreatedDate()));

                if(hnslStudentApprovalEntity.getStatus()==1){
                    row.createCell(3).setCellValue("审核中");
                }else if(hnslStudentApprovalEntity.getStatus()==2){
                    row.createCell(3).setCellValue("地市审核通过");
                }else if(hnslStudentApprovalEntity.getStatus()==3){
                    row.createCell(3).setCellValue("地市驳回");
                }else if(hnslStudentApprovalEntity.getStatus()==4){
                    row.createCell(3).setCellValue("省级审核通过");
                }else if(hnslStudentApprovalEntity.getStatus()==5){
                    row.createCell(3).setCellValue("省级驳回");
                }else{
                    row.createCell(3).setCellValue("其他");
                }
                if(StringUtil.isNotNull(hnslStudentApprovalEntity.getUpdatedUser())){
                    row.createCell(4).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUpdatedUser()));
                    row.createCell(5).setCellValue(sdf.format(hnslStudentApprovalEntity.getUpdatedDate()));
                }else{
                    row.createCell(4).setCellValue("");
                    row.createCell(5).setCellValue("");
                }
                if(StringUtil.isNotNull(hnslStudentApprovalEntity.getProvincialUser())){
                    row.createCell(6).setCellValue(String.valueOf(hnslStudentApprovalEntity.getProvincialUser()));
                    row.createCell(7).setCellValue(sdf.format(hnslStudentApprovalEntity.getProvincialDate()));
                }else{
                    row.createCell(6).setCellValue("");
                    row.createCell(7).setCellValue("");
                }

                row.createCell(8).setCellValue(String.valueOf(hnslStudentApprovalEntity.getCityCode()));
                row.createCell(9).setCellValue(String.valueOf(hnslStudentApprovalEntity.getSchoolName()));
                row.createCell(10).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserName()));
                row.createCell(11).setCellValue(hnslStudentApprovalEntity.getTeamLevel());
                row.createCell(12).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserCard()));
                row.createCell(13).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserAge()));
                row.createCell(14).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserPhone()));
                row.createCell(15).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserServiceTime()));
                row.createCell(16).setCellValue(String.valueOf(hnslStudentApprovalEntity.getTeamName()));
                String teamCount="0";
                if(StringUtil.isNotNull(String.valueOf(hnslStudentApprovalEntity.getTeamCount()))){
                    teamCount=String.valueOf(hnslStudentApprovalEntity.getTeamCount());
                }
                row.createCell(17).setCellValue(teamCount);
                row.createCell(18).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserKpi()));
                row.createCell(38).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserDate()));
                row.createCell(19).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber1()));
                row.createCell(20).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration1()));
                row.createCell(21).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber2()));
                row.createCell(22).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration2()));
                row.createCell(23).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber4()));
                row.createCell(24).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration4()));
                row.createCell(25).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber5()));
                row.createCell(26).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration5()));
                row.createCell(27).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration3()));

                row.createCell(28).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserIntegration()));
                row.createCell(29).setCellValue(String.valueOf(hnslStudentApprovalEntity.getTeamIntegration()));
                row.createCell(30).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration6()));

                row.createCell(31).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney1()));
                row.createCell(32).setCellValue(hnslStudentApprovalEntity.getMoney2());
                row.createCell(33).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney3()));
                row.createCell(34).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney4()));
                row.createCell(35).setCellValue(String.valueOf(hnslStudentApprovalEntity.getMoney5()));
                row.createCell(36).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserBankNumber()));
                row.createCell(37).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserBankName()));
                if(StringUtil.isNull(hnslStudentApprovalEntity.getReportPicture())){
                    row.createCell(2).setCellValue("未上传");
                }else if(!hnslStudentApprovalEntity.getReportPicture().contains("uploads")){
                    row.createCell(2).setCellValue("上传图片异常");
                }else{
                    row.createCell(2).setCellValue("https://wx.hn.189.cn/hnzhsls/"+hnslStudentApprovalEntity.getReportPicture());
                }
            }

            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("学子清单导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:output')")
    @OperationLog
    @ApiOperation("导出薪资总表")
    @PostMapping("/outputPayList")
    public ApiResult<?> outputPayList(@RequestBody Map<String,Object> params, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        ServletContext servletContext = request.getSession().getServletContext();
        QueryWrapper<HnslStudentApproval> hnslStudentApprovalEntityQueryWrapper = new QueryWrapper<>();

        String value=String.valueOf(params.get("userValue"));
        if(!StringUtil.isEmpty(value)){
            value=value.trim();
            if(value.length()==11){
                hnslStudentApprovalEntityQueryWrapper.eq("USER_PHONE",value);
            }else{
                hnslStudentApprovalEntityQueryWrapper.like("USER_NAME",value);
            }
        }
        hnslStudentApprovalEntityQueryWrapper.eq(StringUtil.isNotNull(String.valueOf(params.get("auditDate")))
                ,"AUDIT_DATE",params.get("auditDate"));
        hnslStudentApprovalEntityQueryWrapper.like(StringUtil.isNotNull(String.valueOf(params.get("schoolName")))
                ,"SCHOOL_NAME",params.get("schoolName"));

        hnslStudentApprovalEntityQueryWrapper.eq(StringUtil.isNotNull(String.valueOf(params.get("teamLevel"))) && !"0".equals(String.valueOf(params.get("teamLevel")))
                ,"TEAM_LEVEL",params.get("teamLevel"));
        if(StringUtil.isNotNull(String.valueOf(params.get("beginTime")))){
            hnslStudentApprovalEntityQueryWrapper.apply("CREATED_DATE>={0} and CREATED_DATE<={1}",params.get("beginTime"),params.get("endTime"));
        }
        hnslStudentApprovalEntityQueryWrapper.ge(StringUtil.isNotNull(String.valueOf(params.get("userJF1")))
                ,"USER_INTEGRATION",params.get("userJF1"));
        hnslStudentApprovalEntityQueryWrapper.le(StringUtil.isNotNull(String.valueOf(params.get("userJF2")))
                ,"USER_INTEGRATION",params.get("userJF2"));
        hnslStudentApprovalEntityQueryWrapper.ge(StringUtil.isNotNull(String.valueOf(params.get("teamJF1")))
                ,"TEAM_INTEGRATION",params.get("teamJF1"));
        hnslStudentApprovalEntityQueryWrapper.le(StringUtil.isNotNull(String.valueOf(params.get("teamJF2")))
                ,"TEAM_INTEGRATION",params.get("teamJF2"));
        //查询列表数据
        hnslStudentApprovalEntityQueryWrapper.in("STATUS",Arrays.asList(4));

        List<HnslStudentApproval> list = hnslStudentApprovalService.list(hnslStudentApprovalEntityQueryWrapper);
        list.forEach(item -> {
            List<HnslUser> entity = hnslUserService.queryUser(item.getUserPhone());
            if (!entity.isEmpty() && entity.size() > 0) {
                for (HnslUser entity1 : entity) {
                    item.setHhrUserId(String.valueOf(entity1.getId()));
                }
            }
        });

        // 生成一条记录
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf1.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "薪资总表" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath);
        Integer activeEvent = 10;
        Integer appSettingId = (Integer) params.get("appSettingId");
        Integer lists = list.size();
        User user = getLoginUser();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, lists, appSettingId);
        //查询列表数据
        SimpleDateFormat sdf=new SimpleDateFormat("YYYY-MM-dd");
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 20 * 256);
        sheet.setColumnWidth(7, 20 * 300);
        sheet.setColumnWidth(8, 20 * 256);
        sheet.setColumnWidth(9, 20 * 256);
        sheet.setColumnWidth(10, 20 * 256);
        sheet.setColumnWidth(11, 20 * 256);
        sheet.setColumnWidth(12, 20 * 300);
        sheet.setColumnWidth(13, 20 * 256);
        sheet.setColumnWidth(14, 20 * 256);
        sheet.setColumnWidth(15, 20 * 256);
        sheet.setColumnWidth(16, 20 * 256);
        sheet.setColumnWidth(17, 20 * 300);

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
        HSSFPatriarch drawingPatriarch = sheet.createDrawingPatriarch();
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("账期");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("学校");
        cell = row.createCell(3);
        cell.setCellValue("姓名");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("团队类型");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("身份证号");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("年龄");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("工号");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("服务时长");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("团队名称");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("团队人数");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("KPI考核");
        cell.setCellStyle(style);
        cell = row.createCell(18);
        cell.setCellValue("注册时间");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("新装数量");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("新装积分");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("维系数量");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("维系积分");
        cell.setCellStyle(style);
        cell = row.createCell(16);
        cell.setCellValue("个人积分");
        cell.setCellStyle(style);
        cell = row.createCell(17);
        cell.setCellValue("实发");
        cell.setCellStyle(style);
        // 循环将数据写入Excel
        if (null != list && list.size() != 0) {

            for (int i = 0; i < list.size(); i++) {
                HnslStudentApproval hnslStudentApprovalEntity = list.get(i);
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(hnslStudentApprovalEntity.getAuditDate()));
                row.createCell(1).setCellValue(String.valueOf(hnslStudentApprovalEntity.getCityCode()));
                row.createCell(2).setCellValue(String.valueOf(hnslStudentApprovalEntity.getSchoolName()));
                row.createCell(3).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserName()));
                row.createCell(4).setCellValue(hnslStudentApprovalEntity.getTeamLevel());
                row.createCell(5).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserCard()));
                row.createCell(6).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserAge()));
                row.createCell(7).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserPhone()));
                row.createCell(8).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserServiceTime()));
                row.createCell(9).setCellValue(String.valueOf(hnslStudentApprovalEntity.getTeamName()));
                String teamCount="0";
                if(StringUtil.isNotNull(String.valueOf(hnslStudentApprovalEntity.getTeamCount()))){
                    teamCount=String.valueOf(hnslStudentApprovalEntity.getTeamCount());
                }
                row.createCell(10).setCellValue(teamCount);
                row.createCell(11).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserKpi()));
                row.createCell(18).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserDate()));
                row.createCell(12).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber1()));
                row.createCell(13).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration1()));
                row.createCell(14).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessNumber2()));
                row.createCell(15).setCellValue(String.valueOf(hnslStudentApprovalEntity.getBusinessIntegration2()));
                row.createCell(16).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserIntegration()));
                //薪资报表 实发 个人积分*1
                row.createCell(17).setCellValue(String.valueOf(hnslStudentApprovalEntity.getUserIntegration()));
            }
            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("薪资总表导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:output')")
    @OperationLog
    @ApiOperation("导入批量审批")
    @PostMapping("/importAuditManagement")
    public ApiResult<?> importAuditManagement(@RequestParam(value = "uploadFile", required = true) MultipartFile[] files
            ,String imageUrl,String type,HttpServletRequest request, HttpServletResponse response) {
        MultipartFile file = files[0];

        JSONObject result = new JSONObject();
        String image = "xls,xlsx";

        User loginUser = getLoginUser();
        if (!file.isEmpty()) {
            ServletContext servletContext = request.getSession().getServletContext();
            String uploadPath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 2, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 2, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMDD");
                String batchCode=sdf.format(new Date())+((int)((Math.random()*9+1)*10));
                if (numberList != null && numberList.size()!=0) {
                    //将数据转化成user对象
                    List<HnslStudentApproval> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslStudentApproval listPojo = new HnslStudentApproval();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //工号 手机号码
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                listPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                            }
                            //账期 2022-09
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                listPojo.setAuditDate(StringUtil.trimString(userMap.get(name)));
                            }
                            //审批结果填数字 1-通过   2-驳回
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                listPojo.setStatus(Integer.parseInt(userMap.get(name)));
                            }
                            if ("2".equals(type)) {
                                listPojo.setReportPicture(imageUrl);//图片默认路径
                                listPojo.setUpdatedUser(loginUser.getUsername());
                                listPojo.setUpdatedDate(new Date());
                            }else if ("3".equals(type)) {
                                listPojo.setProvincialUser(loginUser.getUsername());
                                listPojo.setProvincialDate(new Date());
                            }

                        }
                        hnslUserList.add(listPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslStudentApprovalService.saveUserArray(hnslUserList, request);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            Map<String, Object> map = new HashedMap();
                            map.put("code", "6");
                            map.put("fileName", saveUserArray.get("fileName"));
                            map.put("msg", "exportDaoUsers");
                            return success(map);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        return fail("批量插入审批工号信息失败:" + e);
                    }
                } else {
                    return fail("文件内容为空，解析失败");
                }
            } catch (Exception e) {
                return fail("文件上传接口上传异常:" + e);
            }
        } else {
            return fail("上传文件为空");
        }
        return fail("文件导入失败！");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslStudentApproval:output')")
    @OperationLog
    @ApiOperation("导入学子工号进入审核")
    @PostMapping("/importDetailed")
    public ApiResult<?> importDetailed(@RequestParam(value = "uploadFile", required = true) MultipartFile[] files, HttpServletRequest request, HttpServletResponse response) {
        MultipartFile file = files[0];

        User loginUser = getLoginUser();
        JSONObject result = new JSONObject();
        String image = "xls,xlsx";

        if (!file.isEmpty()) {
            ServletContext servletContext = request.getSession().getServletContext();
            String uploadPath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 2, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 2, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMDD");
                if (numberList != null && numberList.size()!=0) {
                    //将数据转化成user对象
                    List<HnslStudentApproval> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslStudentApproval listPojo = new HnslStudentApproval();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //工号 手机号码
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                listPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                            }
                            //账期 2022-09
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                listPojo.setAuditDate(StringUtil.trimString(userMap.get(name)));
                            }//考核分为A/B/C/D三个等级
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                listPojo.setUserKpi(StringUtil.trimString(userMap.get(name)));
                            }
                        }
                        hnslUserList.add(listPojo);
                    }
                    try {
                        logger.info("解析结果装入学子清单集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslStudentApprovalService.saveStudentArray(hnslUserList, request,loginUser);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            Map<String, Object> map = new HashedMap();
                            map.put("code", "6");
                            map.put("fileName", saveUserArray.get("fileName"));
                            map.put("msg", "exportDaoUsers");
                            return success(map);
                        }
                    } catch (Exception e) {
                        return fail("批量插入学子清单失败:" + e);
                    }
                } else {
                    return fail("文件内容为空，或者解析失败");
                }
            } catch (Exception e) {
                return fail("文件上传接口上传异常:" + e);
            }
        } else {
            return fail("上传文件为空");
        }
        return fail("导入文件失败！");
    }

}
