package com.hlkj.yxsAdminApi.common.core.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName RSA2Util
 * @Description TODO
 * @<NAME_EMAIL>
 * @Date 2023/4/10 21:02
 * @Version 1.0
 */
public class RSA2Util {
    //公钥，可以写前端（前端加密公钥）最长512位
    public static String public_key="MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAlvL0SavfinY+fVcbzFJ0TuYbpbeXcfEM\r\n" +
            "qCPkEXMtb+Q+6+UGVl4397B8UkklPo5845VrapMzBScE+qztq3+AbVqsrc1yopzMlcAJrR3rEfuT\r\n" +
            "uW83189JzPvPD8Te8UG5Qwl7lzQuZKnAsUqrVttQkA7caKGqvJz6NAMvxMNcGXp5eD7zwJIPxq2x\r\n" +
            "7qbsKhKi1nEcXMsXmMWa6C20U69zqfvHb5WLRxjQngWn1R0wQnuQT8alTR8r6p3iReCXzbVTTXcU\r\n" +
            "J6nP/uakvGP0InZb/gokoO40Zp9yNhkKWSltPNQmlQjANwuCcCBfxAwMdIXaMrKCt6RruZheeIaM\r\n" +
            "kbXB2TTlPqLmTMcc8kgt4e5mPs35Ov7G26Ot5FBGTjsqfMiY3Vg25LehlAeixr2Y5xRROeX1SnKx\r\n" +
            "KasBzFg2yXEPEoCs5NOVx159NHCLWti14rnxefB59LtlgptVEVlfiJrJ3/Dt+Yfg08OJmjeIy3Zy\r\n" +
            "Er6YIzsMadqk1Vidl0NzEkRFXZ3rNZ7OApHnp+lC/Wc3CSBBNC61L8q5iElicZ8RaelHrMeFx7nD\r\n" +
            "yGFRG4ibqvvA4HnfPnWjrgaNfLtwDvppgWrpyDsYPxKgNY+Ao+DEaCM4OGcQ5z9hKExayll5W1Cw\r\n" +
            "h4QWsBky330sX6vtjmT7exzXQNDF2uQe6l1KkIzdOwMCAwEAAQ==";
    //私钥，只能放后端(Java端解密私钥)
    public static String private_key="MIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggkoAgEAAoICAQCW8vRJq9+Kdj59VxvMUnRO5hul\r\n" +
            "t5dx8QyoI+QRcy1v5D7r5QZWXjf3sHxSSSU+jnzjlWtqkzMFJwT6rO2rf4BtWqytzXKinMyVwAmt\r\n" +
            "HesR+5O5bzfXz0nM+88PxN7xQblDCXuXNC5kqcCxSqtW21CQDtxooaq8nPo0Ay/Ew1wZenl4PvPA\r\n" +
            "kg/GrbHupuwqEqLWcRxcyxeYxZroLbRTr3Op+8dvlYtHGNCeBafVHTBCe5BPxqVNHyvqneJF4JfN\r\n" +
            "tVNNdxQnqc/+5qS8Y/Qidlv+CiSg7jRmn3I2GQpZKW081CaVCMA3C4JwIF/EDAx0hdoysoK3pGu5\r\n" +
            "mF54hoyRtcHZNOU+ouZMxxzySC3h7mY+zfk6/sbbo63kUEZOOyp8yJjdWDbkt6GUB6LGvZjnFFE5\r\n" +
            "5fVKcrEpqwHMWDbJcQ8SgKzk05XHXn00cIta2LXiufF58Hn0u2WCm1URWV+Imsnf8O35h+DTw4ma\r\n" +
            "N4jLdnISvpgjOwxp2qTVWJ2XQ3MSREVdnes1ns4Ckeen6UL9ZzcJIEE0LrUvyrmISWJxnxFp6Ues\r\n" +
            "x4XHucPIYVEbiJuq+8Dged8+daOuBo18u3AO+mmBaunIOxg/EqA1j4Cj4MRoIzg4ZxDnP2EoTFrK\r\n" +
            "WXlbULCHhBawGTLffSxfq+2OZPt7HNdA0MXa5B7qXUqQjN07AwIDAQABAoICADe1hLjnyMF2MEvb\r\n" +
            "6TN4PtqbjBqV7jusBcrMLDDQfDVuXFZ9GOCpNyifptJLkoZK2ga4L4OOveap5umJBtIjZUd2joI6\r\n" +
            "b5NaIRsnW6rbftUNLxDh3g80vnz7dgjQ1KLfabs29bjxFioqiCYmc6PDnzD7exbWi4e+u4yUnH2W\r\n" +
            "yBfTHp9lRFqOzWm60w9L5Z8Fb7mK1zfb8GJ+k1QbAhFckGhstscUvulHXD7lwXIiqpvutGzs2Dbl\r\n" +
            "STT2ffriD8RopkzRpMuanVvqzWOF1CUf34WbQrduEkLJl8Ilba9IXbt0ZKTeLOpjwgkVuaJF/BbJ\r\n" +
            "C6VoQlv6P5Ooiz0aEyj3lom8zY9+jmrkkWRbOJRSaeqhIgnkttHdPS+yuHfiTyzLqfY26r8kZgw6\r\n" +
            "Y/RN+P2+hBjDoRT/v74NKEN0jlu03aaxIhz3r1jgLiAouVXoH7mD9Xw4JZv1ERv9GeE/rAZHkTpr\r\n" +
            "SCCIMk/xtWK1D63Rhan3ftOWVFKpKGecbLWKVqAjIds4Onum5Hqz2FdLO3YbMEfrHjxgq1ZfgtX/\r\n" +
            "ryORjTTiJKx9kc4+YIco2UbnfLB5ywJGVHGUtDfUhZg25PLKV7vNgOTCde/JjE1rY1HsUnddDjzq\r\n" +
            "xch/EDmgUZ+JNzO4KGGSWH6khqZGPrzPaDOvpbDIoJxScTtIR68p+QISKemRAoIBAQDSjxwXk3n/\r\n" +
            "WCsIAZ0XheXngxkFuXu2nYLUBCmqQjcxRAtn2zKX5av2wH3cEN+U9r+qreawU3WiytR1rAzjOqwW\r\n" +
            "ut4BeZP8f+XaGFWeR8FZhvsZ8Nm6QPYGIS7OL2vokXlwBzypbB2yH6gtg6iPN+U/Ec+WpLDFRYBW\r\n" +
            "wLBFAubz6uhgqg0chq+GcapbsBjw8ffH/QNtwTU7rLUgOhkllnWLzNR7XmWb/bc3gAwY8xL8ci2g\r\n" +
            "Ei8c3fqJt2besN2xfjT1FDGtWAgdyaeImVhEgmsMuTK+ufd2fJ63hr1cy/9+7GI1CaNs6sOPPhsD\r\n" +
            "uBkbrjSL6Z8s4G2IjtgdD5pi/IE5AoIBAQC3hoiIRrXVLQMml3MHZEzNTmum7rd9DQa6FDN12n13\r\n" +
            "gdi2zT4REqiWfzWQyr6WkPZClWYvT9nEDJUrA5AegwtD2ChlXWIC29NYzTvDhUgmX0ulaobRi4BF\r\n" +
            "a54ca+Gu2WLeJG9eOCsIpYpqRMfIIDLtGDKAXe7FpBQxAxnfI5D6SGBdyGzxPfDEXcsiaVBp94Ql\r\n" +
            "uaYMftfNSHCzeG79iq2VvVQAxSHTcXuoVeUEZxE+qMev/xTxugVmyO6ZrwM625cWQyG7b3nNEoO+\r\n" +
            "DiLVUCSdRNy6doGoZD/o2oeguXUEPb1oF60Y8lDR3H8qPE4dpg0ku36d1g/UjRT0GKsWimobAoIB\r\n" +
            "AQDJTltcSvorX18cs3Y+ZByu387Pg0s9B5BqJWS0S8Q6zMRe0dmoEcP8EIMEH/mykMokY0iXseNY\r\n" +
            "9bR0oD5eDN00ignwvQz4LktR3UwWHKJFduChJ3zyLmNI7vax1/LCEjtZGQItNJmbj9qZUTOZYaZC\r\n" +
            "S0P90YzTviP6TRT5ldG6yA7NRCdu63HrBscKC+18hsoPoN1jVIIRgIhWvFUIc7YEJ5vKKzDYF5TQ\r\n" +
            "chKe096GDp+CV4BFiBCdFAQ8yjnaFUCKQ3lPMkfoOsB6v6j9Lvcr3fAfGur1lCWmbyKiEsM91L/7\r\n" +
            "1zAw1K6NR6JKRvtyVHYWghBVtmfiVDdYaW+ATcs5AoIBAGphDRsRAa1DniPsfby3It2Oq0/j53AQ\r\n" +
            "8OlDdGDH+RogpeGwLl3vFtHFguMe4kKcXGJT/xtFidDeCkkYk51cEFePIDtBWeKWUXeT3ySCSUQU\r\n" +
            "0OBdUzB7UCrtKoUG85ZKY8ioDfXrUOZ+LjFTFQqnv103PW6zBAiEB0Axr/lrwEmTUmiKGLvtT3ZD\r\n" +
            "d8/Gg9ooouzLI7jyfVx8tPcSxCMrE1tMlzirC+RoT2lQAKQAqjpL0xPtI9LKJP9jUQq8K38ecgFx\r\n" +
            "gEgJYezA4qRzxTUoc1DF7bwXnShik7kBksRTO8TtlLCVqLxhGP/Mc9mjgxQkgFHebJJI1nw6Gv8y\r\n" +
            "nVk/eikCggEAMRS14jHK6rAP3adveAE6JWRqTiRBl8HZBdR1PYwoahoh97TyRhvig8Z+bR2uXMu1\r\n" +
            "neGrYm7hlJLPy1wsaievx6T5UwWhbJb0cvD1RocwDCOXUHGek+Q82CfnvkTMGTeoQDOeUkwgZLyE\r\n" +
            "38HZkjlJYtibsxpZBHOciIa5ExlOpSOSKGnOH+Zm70Do9Y9datiydkP1CojHRtXWU/RuZkmW33/p\r\n" +
            "ZXChXhTz0Kt7dhGVSS6DEew8P8Ib8k/DZctNhZzzH/HW8+sR8ndJLoatRaz7CTua2u8nT7v+zavR\r\n" +
            "gGUhvTuc/d3DIIYJ11+oZeAk/2px3f3/kLbq2uHSRL/2LGsgxA==";

    public static void main(String[] args) throws Exception {
//        String str = encrypt("{qweqe:qweqweqwwe}",public_key);
        String txt = "Qbv7mYwEuEv5gohnjDtMd+5mAkT8zEpVgtOMcIbG8NxPtGwmBPFiwcyMSAeZtIhTxdZgBSorfDYZLAHtJBx6Q94Giki55Z7sSFwwodll+YxgEP/xMTnRax0UtiQZAk/54OJYsxSMbXRstC5HmRlfh04Z+bSA4TV5HB29HBPuyJm/DZ0y89MD3COBnh5YdxSe/8k1ATlXCO+VXI3GnS9/P29dSwhQ9ZwCUahFIvqOZ0pyf8MwiI2/1RUYU4C386SJY8+mp4eDfOzGPSdgMn8XxR8OazgqE+FA8k+ZBm0lK4KrC1XxXFHbbIClk50pOOOVxbDVSY5dQYirh61hxKHTwA==";
        System.out.println("=========="+fordecrypt(txt));
//        System.out.println(fordecrypt(str));
    }


    private static Map<Integer, String> keyMap = new HashMap<Integer, String>();  //用于封装随机产生的公钥与私钥

    /**
     * 随机生成密钥对
     * @throws NoSuchAlgorithmException
     */
    public static Map genKeyPair() throws NoSuchAlgorithmException {
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024,new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();   // 得到私钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();  // 得到公钥
        String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
        // 得到私钥字符串
        String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));
        // 将公钥和私钥保存到Map
        keyMap.put(0,publicKeyString);  //0表示公钥
        keyMap.put(1,privateKeyString);  //1表示私钥
        return keyMap;
    }
    /**
     * RSA公钥加密
     *
     * @param str
     *            加密字符串
     * @param publicKey
     *            公钥
     * @return 密文
     * @throws Exception
     *             加密过程中的异常信息
     */
    public static String encrypt( String str, String publicKey ) throws Exception{
        //base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    /**
     * RSA私钥解密
     *
     * @param str
     *            加密字符串
     * @param privateKey
     *            私钥
     * @return 铭文
     * @throws Exception
     *             解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) throws Exception{
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }

    public static Map<String, Object> decryptMap(Map<String, Object> map){
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            entry.setValue(fordecrypt(String.valueOf(entry.getValue())));
        }
        return map;
    }
    public static JSONObject decryptJson(JSONObject jsonObject){
        Map<String, Object> map = new HashMap<String, Object>();
        map.putAll(jsonObject);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            entry.setValue(fordecrypt(String.valueOf(entry.getValue())));
        }
        JSONObject json = new JSONObject(map);
        return json;
    }
    public static String fordecrypt(String str) {
        String outStr = "";
        try {
            //64位解码加密后的字符串
            byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
            //base64编码的私钥
            byte[] decoded = Base64.decodeBase64(private_key);
            RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
            //RSA解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, priKey);
            outStr = new String(cipher.doFinal(inputByte));
        } catch (Exception e) {

        }

        return outStr;
    }
}