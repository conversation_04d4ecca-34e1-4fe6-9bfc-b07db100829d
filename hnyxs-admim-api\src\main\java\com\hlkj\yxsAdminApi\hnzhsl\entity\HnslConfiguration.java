package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫楼配置项表
 *
 * <AUTHOR>
 * @since 2025-05-06 09:36:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslConfiguration对象", description = "扫楼配置项表")
public class HnslConfiguration implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "配置名")
    private String configurationName;

    @ApiModelProperty(value = "配置编码")
    private String configurationNumber;

    @ApiModelProperty(value = "配置类型")
    private String configurationType;

    @ApiModelProperty(value = "配置状态 0开启  1关闭")
    private String status;

    @ApiModelProperty(value = "配置内容")
    private String configurationSet;

    @ApiModelProperty(value = "修改时间")
    private Date updatedDate;

}
