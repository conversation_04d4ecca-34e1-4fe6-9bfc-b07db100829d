package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserBankcard;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserBankcardParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户银行卡表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:39
 */
public interface HnslUserBankcardMapper extends BaseMapper<HnslUserBankcard> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserBankcard>
     */
    List<HnslUserBankcard> selectPageRel(@Param("page") IPage<HnslUserBankcard> page,
                             @Param("param") HnslUserBankcardParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserBankcard> selectListRel(@Param("param") HnslUserBankcardParam param);

}
