package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户管理表
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslUser对象", description = "用户管理表")
@TableName("HNSL_USER")
public class HnslUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "管理者姓名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "管理者身份证号")
    @TableField("USER_SFZ")
    private String userSfz;

    @ApiModelProperty(value = "审批id")
    @TableField(exist = false)
    private String appSettingId;

    @ApiModelProperty(value = "管理者手机号")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "管理者地址")
    @TableField("USER_SITE")
    private String userSite;

    @ApiModelProperty(value = "BPS揽机工号")
    @TableField("NUMBERS")
    private String numbers;

    @ApiModelProperty(value = "入职时间")
    @TableField("ENTRYDATE")
    private Date entrydate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "第二联系电话")
    @TableField("USER_PHONE_TWO")
    private String userPhoneTwo;

    @ApiModelProperty(value = "楼栋长（0:否 1:是)")
    @TableField("BUILDING")
    private Integer building;

    @ApiModelProperty(value = "群长 QQ群号")
    @TableField("FLOCK")
    private String flock;

    @ApiModelProperty(value = "积分")
    @TableField("INTEGRAL")
    private Double integral;

    @ApiModelProperty(value = "证件正面")
    @TableField("IMAGE_FRONT")
    private String imageFront;

    @ApiModelProperty(value = "证件反面")
    @TableField("IMAGE_VERSO")
    private String imageVerso;

    @ApiModelProperty(value = "正面免冠")
    @TableField("FULL_FACED")
    private String fullFaced;

    @ApiModelProperty(value = "实时绩效(A  B   C ")
    @TableField("PERFORMANCE")
    private String performance;

    @ApiModelProperty(value = "证件有效期开始")
    @TableField("PAPERS_DATA_AGO")
    private Date papersDataAgo;

    @ApiModelProperty(value = "证件有效期结束")
    @TableField("PAPERS_DATA_LATER")
    private Date papersDataLater;

    @ApiModelProperty(value = "身份（1.校园经理 2.一级合伙人 3.二级合伙人 4.三级合伙人 5省级管理员 6 地市管理员")
    @TableField("STATUS_SF")
    private Integer statusSf;

    @ApiModelProperty(value = "上级手机号码(0:没有上级，代表为校园经理)")
    @TableField("STATUS_SUPERIOR")
    private String statusSuperior;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "族")
    @TableField("ETHNIC")
    private String ethnic;

    @ApiModelProperty(value = "性别（2:女  1:男  0:不祥)")
    @TableField("USER_GENDER")
    private Integer userGender;

    @ApiModelProperty(value = "本地网")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty(value = "微信公众号唯一标识")
    @TableField("OPENID")
    private String openid;

    @ApiModelProperty(value = "用户头像")
    @TableField("IMAGE_URL")
    private String imageUrl;

    @ApiModelProperty(value = "微信号（代客需）")
    @TableField("USER_WECHAT")
    private String userWechat;

    @ApiModelProperty(value = "销售员编码")
    @TableField("SALES_CODE")
    private String salesCode;

    @ApiModelProperty(value = "部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty(value = "邮箱")
    @TableField("USER_MAIL")
    private String userMail;

    @ApiModelProperty(value = "校园经理手机号码")
    @TableField("USER_MANGER")
    private String userManger;

    @ApiModelProperty(value = "个人专属二维码")
    @TableField("QRCODE")
    private String qrcode;

    @ApiModelProperty(value = "合伙人编码 SLID+YYYYMMDDHHMMSS+随机6位数")
    @TableField("USER_CODE")
    private String userCode;

    @ApiModelProperty(value = "开卡上传身份证图片规范记录(0:未记录 1:已记录)")
    @TableField("UPLOAD_NORM")
    private Integer uploadNorm;

    @ApiModelProperty(value = "合伙人条约展示(0:未记录 1:已记录)")
    @TableField("EXPLAIN_NORM")
    private Integer explainNorm;

    @ApiModelProperty(value = "泛渠道标识;MOD：修改必填 添加三级合伙人才有")
    @TableField("CHANNEL_ID")
    private String channelId;

    @ApiModelProperty(value = "三级合伙人的 订单信息传输字段")
    @TableField("CHANNEL_NBR")
    private String channelNbr;

    @ApiModelProperty(value = "用户最近登陆时间")
    @TableField("LOGIN_DATE")
    private Date loginDate;

    @ApiModelProperty(value = "用户当前记住的学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "用户最后进入的套餐类型")
    @TableField("CHOICE_SET_MEAL")
    private String choiceSetMeal;

    @ApiModelProperty(value = "修改上级次数(0:未修改 1:已修改)")
    @TableField("UPDATE_TIMES")
    private Integer updateTimes;

    @ApiModelProperty(value = "当前版本号")
    @TableField("VERSION_HINT")
    private String versionHint;

    @ApiModelProperty(value = "地理位置拦截（0:关闭 1:开启  默认开启）")
    @TableField("POSITION_LIMITATION")
    private Integer positionLimitation;

    @ApiModelProperty(value = "人脸活体视频识别拦截（0:关闭 1:开启  默认开启）")
    @TableField("LIVING_LIMITATION")
    private Integer livingLimitation;

    @ApiModelProperty(value = "人脸图片评分拦截（0:关闭 1:开启  默认开启）")
    @TableField("FACE_SCORE_LIMITATION")
    private Integer faceScoreLimitation;

    @ApiModelProperty(value = "开卡拦截切换拦截（0:关闭 1:主动活体 2：评分 3：自动活体  默认1开启）")
    @TableField("CARD_LIMITATION_SWITCH")
    private Integer cardLimitationSwitch;

    @ApiModelProperty(value = "合伙人的驿站功能限制（0:关闭 1:开启  默认开启）")
    @TableField("COURIER_STATION_SWITCH")
    private Integer courierStationSwitch;

    @ApiModelProperty(value = "用户异常额外积分")
    @TableField("ABNORMAL_INTEGRAL")
    private Integer abnormalIntegral;

    @ApiModelProperty(value = "激活同步BPS开关（0:关闭 1:开启  默认开启）")
    @TableField("ACTIVATE_BPS_SWITCH")
    private Integer activateBpsSwitch;

    @ApiModelProperty(value = "推荐人ID")
    @TableField("REFERRER_ID")
    private String referrerId;

    @ApiModelProperty(value = "派单人类型 1:分派员 2:接收员")
    @TableField("SEND_ORDERS_TYPE")
    private Integer sendOrdersType;

    @ApiModelProperty(value = "渠道-1 中小学2 高校（校园经理/管理员/学子支撑）3 学子公司（学子合伙人）4 校园店（门店代理商）5 泛渠道（合作直销）")
    @TableField("CHANNEL_TYPE")
    private Integer channelType;

    @ApiModelProperty(value = "智慧扫楼渠道 1:全渠道 2:校园渠道 3：电渠互联网卡渠道 4：其他")
    @TableField("HNSL_CHANNEL")
    private int hnslChannel;

    @ApiModelProperty(value = "学校名称")
    @TableField(exist = false)
    private String schoolName;

    /**以下字段数据表中没有，用于多表查询数据封装*/
     @ApiModelProperty(value = "学校名称修改")
    @TableField(exist = false)
    private String schoolNameUpdate;
     @ApiModelProperty(value = "管理楼栋")
    @TableField(exist = false)
    private String buildingName;
     @ApiModelProperty(value = "积分记录")
    @TableField(exist = false)
    private List<HnslIntegral> IntegralList;
     @ApiModelProperty(value = "上月绩效")
    @TableField(exist = false)
    private String lastPerformance;
     @ApiModelProperty(value = "银行卡对象")
    @TableField(exist = false)
    private String  bankNumber;
     @ApiModelProperty(value = "查询条件（身份证|号码|姓名）")
    @TableField(exist = false)
    private String condition;
     @ApiModelProperty(value = "查询条件判断（1、sql参与查询）")
    @TableField(exist = false)
    private String conditionStatus;
     @ApiModelProperty(value = "个人任务完成进度")
    @TableField(exist = false)
    private int taskStatusRel;
     @ApiModelProperty(value = "个人任务审核")
    @TableField(exist = false)
    private int receiveAward;
     @ApiModelProperty(value = "任务完成时间")
    @TableField(exist = false)
    private Date finishTime;
     @ApiModelProperty(value = "朋友圈截图")
    @TableField(exist = false)
    private String friendsJieTu;
     @ApiModelProperty(value = "QQ群截图")
    @TableField(exist = false)
    private String qqJieTu;
     @ApiModelProperty(value = "服务截图")
    @TableField(exist = false)
    private String serviceJieTu;
     @ApiModelProperty(value = "用户姓名或手机号码")
    @TableField(exist = false)
    private String userNameOrUuser;
     @ApiModelProperty(value = "绩效")
    @TableField(exist = false)
    private String  levels;
     @ApiModelProperty(value = "分组名称")
    @TableField(exist = false)
    private String  grouping;
     @ApiModelProperty(value = "分组名称(对比)")
    @TableField(exist = false)
    private String  groupingUpdate;

     @ApiModelProperty(value = "分组编码")
    @TableField(exist = false)
    private String  groupingCode;

    @ApiModelProperty(value = "团队名称")
    @TableField(exist = false)
    private String teamName;

}
