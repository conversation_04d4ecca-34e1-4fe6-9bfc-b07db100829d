package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslPerformanceService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPerformance;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslPerformanceParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 绩效表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "绩效表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslPerformance")
public class HnslPerformanceController extends BaseController {
    @Autowired
    private HnslPerformanceService hnslPerformanceService;

    @Autowired
    private HnslUserService hnslUserService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:list')")
    @OperationLog
    @ApiOperation("分页查询绩效表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslPerformance>> page(@RequestBody HnslPerformanceParam param) {
        //PageParam<HnslPerformance, HnslPerformanceParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        // return success(hnslPerformanceService.page(page, page.getWrapper()));
        // 使用关联查询
        String firstDay=null;
        String lastDay=null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        if(StringUtil.isNull(param.getBeginTime()) || StringUtil.isNull(param.getEndTime()) ){
            //获取上月时间参数
            Calendar cal_1=Calendar.getInstance();//获取当前日期
            cal_1.add(Calendar.MONTH, -1);
            cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为上月第一天
            firstDay = format.format(cal_1.getTime());
            //获取前月的最后一天
            Calendar cale = Calendar.getInstance();
            cale.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为上月第一天
            lastDay = format.format(cale.getTime());
            param.setBeginTime(firstDay);
            param.setEndTime(lastDay);
        }else{
            try{
                //判断查询时间最大值是否为当前年月,如果为当前年月就是查询上月绩效,因为规则是本月打上月以及绩效
                //获取时间段的最新时间
                firstDay=format.format(format.parse(param.getBeginTime()));
                lastDay=format.format(format.parse(param.getEndTime()));
                logger.info("最新时间"+lastDay);
                //new日期对象
                Date date = new Date(System.currentTimeMillis());
                //转换提日期输出格式
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String time =dateFormat.format(date);
                logger.info("当前日期"+time);
                if(lastDay.equals(time)){//如果最新时间和当前时间一样，查询上月绩效
                    //获取上月时间参数
                    Calendar   cal_1=Calendar.getInstance();//获取当前日期
                    cal_1.add(Calendar.MONTH, -1);
                    cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为上月第一天
                    firstDay = format.format(cal_1.getTime());
                    //获取前月的最后一天
                    Calendar cale = Calendar.getInstance();
                    cale.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为上月第一天
                    lastDay = format.format(cale.getTime());
                }else {
                    //获得月份起始结束时间
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Calendar c = Calendar.getInstance();
                    try {
                        c.setTime(sdf.parse(lastDay+" 00:00:00"));
                        c.add(Calendar.MONTH, 0);
                        //设置为1号,当前日期既为本月第一天
                        c.set(Calendar.DAY_OF_MONTH, 1);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    firstDay = sdf.format(c.getTime());
                    logger.info("月份起始时间"+firstDay);
                    c.setTime(sdf.parse(lastDay+" 23:59:59"));
                    c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                    c.set(Calendar.HOUR_OF_DAY, 23);
                    c.set(Calendar.MINUTE, 59);
                    c.set(Calendar.SECOND, 59);
                    lastDay= sdf.format(c.getTime());
                    logger.info("月份结束时间"+lastDay);
                }
            }catch (Exception e){
                logger.error("转化日期异常:"+e);
            }
        }
        param.setFirstDay(firstDay);
        param.setLastDay(lastDay);
        if(StringUtil.isNull(param.getConditionStatus())){
            param.setConditionStatus("1");
        }
        return success(hnslPerformanceService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:list')")
    @OperationLog
    @ApiOperation("查询全部绩效表")
    @PostMapping("/list")
    public ApiResult<List<HnslPerformance>> list(@RequestBody HnslPerformanceParam param) {
        PageParam<HnslPerformance, HnslPerformanceParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslPerformanceService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslPerformanceService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:list')")
    @OperationLog
    @ApiOperation("根据id查询绩效表")
    @GetMapping("/{id}")
    public ApiResult<HnslPerformance> get(@PathVariable("id") Integer id) {
        return success(hnslPerformanceService.getById(id));
        // 使用关联查询
        //return success(hnslPerformanceService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:save')")
    @OperationLog
    @ApiOperation("添加绩效表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslPerformance hnslPerformance) {
        if (hnslPerformanceService.save(hnslPerformance)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:update')")
    @OperationLog
    @ApiOperation("修改绩效表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslPerformance hnslPerformance) {
        if (hnslPerformanceService.updateById(hnslPerformance)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:remove')")
    @OperationLog
    @ApiOperation("删除绩效表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslPerformanceService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:save')")
    @OperationLog
    @ApiOperation("批量添加绩效表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslPerformance> list) {
        if (hnslPerformanceService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:update')")
    @OperationLog
    @ApiOperation("批量修改绩效表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslPerformance> batchParam) {
        if (batchParam.update(hnslPerformanceService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:remove')")
    @OperationLog
    @ApiOperation("批量删除绩效表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslPerformanceService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:save')")
    @OperationLog
    @ApiOperation("保存或修改绩效表")
    @PostMapping("/savePerformanceEntity")
    public ApiResult<?> savePerformanceEntity(@RequestBody HnslPerformance hnslPerformance){

        User loginUser = getLoginUser();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId",hnslPerformance.getUserId());
        hashMap.put("dates",hnslPerformance.getDates());
        int i = hnslPerformanceService.queryTotal(hashMap);
        boolean saveBool=false;
        if(i>0){//修改
            LambdaQueryWrapper<HnslPerformance> hnslPerformanceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslPerformanceLambdaQueryWrapper.eq(HnslPerformance::getUserId,hnslPerformance.getUserId())
                    .apply("DATE_FORMAT(CREATED_DATE,'%Y-%m')={0}",hnslPerformance.getDates())
                    .orderByDesc(HnslPerformance::getId);
            List<HnslPerformance> hnslPerformanceEntities = hnslPerformanceService.list(hnslPerformanceLambdaQueryWrapper);
            HnslPerformance hnslPerformance1 = hnslPerformanceEntities.get(0);
            hnslPerformance.setCreationtime(new Date());
            hnslPerformance.setId(hnslPerformance1.getId());
            hnslPerformanceService.updateById(hnslPerformance);
        }else{//新增
            hnslPerformance.setCreatedDate(new Date());
            hnslPerformance.setCreatedUser(loginUser.getUsername());
            hnslPerformance.setStatus(1);
            hnslPerformanceService.save(hnslPerformance);
        }
        if(saveBool){
            return success("保存成功");
        }else{
            return fail("保存失败");
        }
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslPerformance:update')")
    @OperationLog
    @ApiOperation("修改绩效表")
    @PostMapping("/updatePerformance")
    public ApiResult<?> updatePerformance(@RequestBody HnslPerformanceParam params) throws ParseException{
        logger.info("修改绩效入参"+params);
        String[] split = params.getLevels().split(",");
        String id =split[0];
        String levels = split[1];

        HnslPerformance hnslPerformance = new HnslPerformance();
        String firstDay =null;
        String lastDay = null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String beginTime = params.getBeginTime();
        String endTime = params.getEndTime();

        if(beginTime==null || beginTime=="" || endTime==null || endTime==""){//时间查询为空,默认操作上一个月绩效
            //获取上月时间参数
            Calendar   cal_1=Calendar.getInstance();//获取当前日期 
            cal_1.add(Calendar.MONTH, -1);
            cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天  
            firstDay = format.format(cal_1.getTime());
            //获取前月的最后一天
            Calendar cale = Calendar.getInstance();
            cale.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为本月第一天 
            lastDay = format.format(cale.getTime());
        }else{//时间条件查询
            //new日期对象
            Date date = new Date(System.currentTimeMillis());
            //转换提日期输出格式
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String time =dateFormat.format(date);
            logger.info("当前日期"+time);
            if(endTime.equals(time)){//如果最新时间和当前时间一样，查询上月绩效
                //获取上月时间参数
                Calendar   cal_1=Calendar.getInstance();//获取当前日期
                cal_1.add(Calendar.MONTH, -1);
                cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为上月第一天
                firstDay = format.format(cal_1.getTime());
                //获取前月的最后一天
                Calendar cale = Calendar.getInstance();
                cale.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为上月第一天
                lastDay = format.format(cale.getTime());
            }else {
                //获得月份起始结束时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Calendar c = Calendar.getInstance();
                try {
                    c.setTime(sdf.parse(endTime+" 00:00:00"));
                    c.add(Calendar.MONTH, 0);
                    //设置为1号,当前日期既为本月第一天
                    c.set(Calendar.DAY_OF_MONTH, 1);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                firstDay = sdf.format(c.getTime());
                c.setTime(sdf.parse(endTime+" 23:59:59"));
                c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
                c.set(Calendar.HOUR_OF_DAY, 23);
                c.set(Calendar.MINUTE, 59);
                c.set(Calendar.SECOND, 59);
                lastDay= sdf.format(c.getTime());
            }

        }

        try {
            //注意是打上月绩效还是补绩效
            HnslUser hnslUserEntity = hnslUserService.getById(id);
            //根据合伙人和绩效时间查询绩效
            LambdaQueryWrapper<HnslPerformance> hnslPerformanceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslPerformanceLambdaQueryWrapper.eq(HnslPerformance::getUserId,hnslUserEntity.getUserPhone())
                    .eq(HnslPerformance::getStatus,1)
                    .apply("CREATED_DATE  between " +
                            "str_to_date({0},'%Y-%m-%d %T') and " +
                            "str_to_date({1},'%Y-%m-%d %T')",firstDay,lastDay)
                    .orderByDesc(HnslPerformance::getId);
            List<HnslPerformance> queryPerformanceList = hnslPerformanceService.list(hnslPerformanceLambdaQueryWrapper);

            HnslPerformance hnslPerformanceParam =null;
            if(queryPerformanceList!=null && queryPerformanceList.size()>0){
                HnslPerformance queryPerformanceBy =queryPerformanceList.get(0);
                //修改绩效记录
                if("0".equals(levels)){
                    //修改绩效状态
                    hnslPerformanceParam = new HnslPerformance();
                    hnslPerformanceParam.setStatus(0);
                    hnslPerformanceParam.setId(queryPerformanceBy.getId());
                    hnslPerformanceService.updateById(hnslPerformanceParam);
                }else{
                    //修改数据(A,B,C)
                    hnslPerformanceParam = new HnslPerformance();
                    hnslPerformanceParam.setId(queryPerformanceBy.getId());
                    hnslPerformanceParam.setLevels(levels);
                    hnslPerformanceService.updateById(hnslPerformanceParam);
                }
            }else{
                //添加绩效记录
                hnslPerformanceParam = new HnslPerformance();
                hnslPerformanceParam.setLevels(levels);
                hnslPerformanceParam.setUserId(hnslUserEntity.getUserPhone());
                hnslPerformanceParam.setCreatedDate(format.parse(firstDay));
                hnslPerformanceService.save(hnslPerformanceParam);
            }
        }catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            return fail("获取信息失败");
        }
        return success("修改成功");
    }

}
