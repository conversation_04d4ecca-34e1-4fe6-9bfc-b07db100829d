package com.hlkj.yxsAdminApi.hnzhsl.utils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 查询参数工具类
 * 
 * <AUTHOR>
 * @date 2024/06/30
 */
public class QueryUtils extends LinkedHashMap<String, Object> {
    private static final long serialVersionUID = 1L;

    // 当前页码
    private int page = 1;
    // 每页条数
    private int limit = 10;
    // 排序字段
    private String sidx;
    // 排序方式
    private String order;

    public QueryUtils(Map<String, Object> params) {
        this.putAll(params);

        // 分页参数
        if (params.get("page") != null) {
            this.page = Integer.parseInt(params.get("page").toString());
        }
        if (params.get("limit") != null) {
            this.limit = Integer.parseInt(params.get("limit").toString());
        }
        if (params.get("sidx") != null) {
            this.sidx = params.get("sidx").toString();
        }
        if (params.get("order") != null) {
            this.order = params.get("order").toString();
        }

        this.put("offset", (page - 1) * limit);
        this.put("page", page);
        this.put("limit", limit);

        // 防止SQL注入
        if (sidx != null && sidx.trim().length() > 0) {
            // 检查sidx是否只包含合法字符（字母、数字、下划线）
            if (sidx.matches("[a-zA-Z0-9_\\.]+")) {
                this.put("sidx", sidx);
            }
        }

        if (order != null && order.trim().length() > 0) {
            // 只接受 ASC 或 DESC
            if ("ASC".equalsIgnoreCase(order) || "DESC".equalsIgnoreCase(order)) {
                this.put("order", order);
            }
        }
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
} 