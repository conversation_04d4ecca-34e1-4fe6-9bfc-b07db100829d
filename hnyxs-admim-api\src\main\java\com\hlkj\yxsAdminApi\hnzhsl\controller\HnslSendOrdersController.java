package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSendOrdersService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrders;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSendOrdersParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.utils.ExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 派单总表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "派单总表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslSendOrders")
public class HnslSendOrdersController extends BaseController {
    @Autowired
    private HnslSendOrdersService hnslSendOrdersService;
    @Autowired
    private QueryUserManagerUtil   queryUserManagerUtil;
    @Autowired
    private AwsS3Utils awsS3Utils;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:list')")
    @OperationLog
    @ApiOperation("分页查询派单总表")
    @PostMapping("/page")
    public ApiResult<PageResult<Map<String,Object>>> page(@RequestBody HnslSendOrdersParam param) {

        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
        String time;
        if(StringUtil.isEmpty(String.valueOf(param.getAuditDate()))){
            time=sdf.format(new Date());
            param.setAuditDate(sdf.format(new Date()));
        }else{
            time=param.getAuditDate();
        }
        try{
            Date parse = sdf.parse(time);
            Calendar instance = Calendar.getInstance();
            instance.setTime(parse);
            instance.add(Calendar.MONTH,1);
            param.setStartTime(time+"-01");
            param.setEndTime(sdf.format(instance.getTime())+"-01");
        }catch (Exception e){
            logger.error("查询学子清单时间转化异常{}",e.getMessage());
        }

        return success(hnslSendOrdersService.pageMapRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:list')")
    @OperationLog
    @ApiOperation("查询全部派单总表")
    @PostMapping("/list")
    public ApiResult<List<HnslSendOrders>> list(@RequestBody HnslSendOrdersParam param) {
        PageParam<HnslSendOrders, HnslSendOrdersParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSendOrdersService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSendOrdersService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:list')")
    @OperationLog
    @ApiOperation("根据id查询派单总表")
    @GetMapping("/{id}")
    public ApiResult<HnslSendOrders> get(@PathVariable("id") Integer id) {
        return success(hnslSendOrdersService.getById(id));
        // 使用关联查询
        //return success(hnslSendOrdersService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:save')")
    @OperationLog
    @ApiOperation("添加派单总表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSendOrders hnslSendOrders) {
        if (hnslSendOrdersService.save(hnslSendOrders)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:update')")
    @OperationLog
    @ApiOperation("修改派单总表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSendOrders hnslSendOrders) {
        if (hnslSendOrdersService.updateById(hnslSendOrders)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:remove')")
    @OperationLog
    @ApiOperation("删除派单总表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSendOrdersService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:save')")
    @OperationLog
    @ApiOperation("批量添加派单总表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSendOrders> list) {
        if (hnslSendOrdersService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:update')")
    @OperationLog
    @ApiOperation("批量修改派单总表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSendOrders> batchParam) {
        if (batchParam.update(hnslSendOrdersService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:remove')")
    @OperationLog
    @ApiOperation("批量删除派单总表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSendOrdersService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrders:output')")
    @OperationLog
    @ApiOperation("导出派单报表")
    @PostMapping("/outputListTable")
    public ApiResult<?> outputListTable(@RequestBody Map<String, Object> params, HttpServletRequest request,
                             HttpServletResponse response) {
        ServletContext servletContext = request.getSession().getServletContext();
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
        String time;
        if(StringUtil.isEmpty(String.valueOf(params.get("auditDate")))){
            time=sdf.format(new Date());
            params.put("auditDate",sdf.format(new Date()));
        }else{
            time=String.valueOf(params.get("auditDate"));
        }
        JSONArray jsonArray = null;
        if("all".equals(String.valueOf(params.get("queryType")))){
            jsonArray =hnslSendOrdersService.queryAllList(params);
        }else{
            HnslSendOrdersParam hnslSendOrdersParam = new HnslSendOrdersParam();
            hnslSendOrdersParam.setAuditDate(time);
            try{
                Date parse = sdf.parse(time);
                Calendar instance = Calendar.getInstance();
                instance.setTime(parse);
                instance.add(Calendar.MONTH,1);
                hnslSendOrdersParam.setStartTime(time+"-01");
                hnslSendOrdersParam.setEndTime(sdf.format(instance.getTime())+"-01");
            }catch (Exception e){
                logger.error("查询学子清单时间转化异常{}",e.getMessage());
            }
            jsonArray =hnslSendOrdersService.listMapRel(hnslSendOrdersParam);
        }

        // 生成一条记录
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf1.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "派单报表" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath);
        Integer activeEvent = 5;
        Integer appSettingId = (Integer) params.get("appSettingId");
        Integer lists = jsonArray.size();
        User user = getLoginUser();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, lists, appSettingId);

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        for (int i = 0; i <= 12; i++) {
            sheet.setColumnWidth(i, 20 * 346);
        }

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("活动名称");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("月份");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("地市");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("学校");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("账期");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("分派量");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("派单量");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("外呼量");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("外呼率");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("回收量");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("回单量");
        cell.setCellStyle(style);
        cell = row.createCell(11);
        cell.setCellValue("逾期量");
        cell.setCellStyle(style);
        cell = row.createCell(12);
        cell.setCellValue("逾期率");
        cell.setCellStyle(style);
        cell = row.createCell(13);
        cell.setCellValue("营销成功量");
        cell.setCellStyle(style);
        cell = row.createCell(14);
        cell.setCellValue("成功率");
        cell.setCellStyle(style);
        cell = row.createCell(15);
        cell.setCellValue("营销失败量");
        cell.setCellStyle(style);
        cell = row.createCell(16);
        cell.setCellValue("营销失败率");
        cell.setCellStyle(style);

        // 循环将数据写入Excel
        if (null != jsonArray && jsonArray.size() != 0) {

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                row.createCell(0).setCellValue(String.valueOf(jsonObject.getString("ORDERS_NAME")));
                if("all".equals(String.valueOf(params.get("queryType")))){
                    row.createCell(1).setCellValue("全量");
                }else{
                    row.createCell(1).setCellValue(String.valueOf(jsonObject.getString("month")));
                }
                row.createCell(2).setCellValue(InterfaceUtil.city_code.get(String.valueOf(jsonObject.getString("cityCode"))));
                row.createCell(3).setCellValue(String.valueOf(jsonObject.getString("SCHOOL_NAME")));
                row.createCell(4).setCellValue(String.valueOf(jsonObject.getString("ordersDate")));
                row.createCell(5).setCellValue(String.valueOf(jsonObject.getString("count")));
                row.createCell(6).setCellValue(String.valueOf(jsonObject.getString("ordersCount")));
                row.createCell(7).setCellValue(String.valueOf(jsonObject.getString("yw")));
                row.createCell(8).setCellValue(String.valueOf(jsonObject.getString("yhRate")));
                row.createCell(9).setCellValue(String.valueOf(jsonObject.getString("hs")));
                row.createCell(10).setCellValue(String.valueOf(jsonObject.getString("hd")));
                row.createCell(11).setCellValue(String.valueOf(jsonObject.getString("yq")));
                row.createCell(12).setCellValue(String.valueOf(jsonObject.getString("yqRate")));
                row.createCell(13).setCellValue(String.valueOf(jsonObject.getString("yxSuccess")));
                if(StringUtil.isNotNull(jsonObject.getString("successRate"))){
                    row.createCell(14).setCellValue(String.valueOf(jsonObject.getString("successRate")));
                }else{
                    row.createCell(14).setCellValue("0.00%");

                }
                row.createCell(15).setCellValue(String.valueOf(jsonObject.getString("yxFail")));
                if(StringUtil.isNotNull(jsonObject.getString("failRate"))){
                    row.createCell(16).setCellValue(String.valueOf(jsonObject.getString("failRate")));
                }else{
                    row.createCell(16).setCellValue("0.00%");

                }
            }
            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("派单报表导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }
}
