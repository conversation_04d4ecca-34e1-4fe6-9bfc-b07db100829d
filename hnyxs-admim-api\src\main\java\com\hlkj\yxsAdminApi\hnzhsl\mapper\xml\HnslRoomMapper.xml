<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslRoomMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_room a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.roomName != null">
                AND a.ROOM_NAME LIKE CONCAT('%', #{param.roomName}, '%')
            </if>
            <if test="param.roomRegisterExisting != null">
                AND a.ROOM_REGISTER_EXISTING = #{param.roomRegisterExisting}
            </if>
            <if test="param.networkPhoneExisting != null">
                AND a.NETWORK_PHONE_EXISTING = #{param.networkPhoneExisting}
            </if>
            <if test="param.networkExisting != null">
                AND a.NETWORK_EXISTING = #{param.networkExisting}
            </if>
            <if test="param.buildingId != null">
                AND a.BUILDING_ID LIKE CONCAT('%', #{param.buildingId}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.roomNumber != null">
                AND a.ROOM_NUMBER LIKE CONCAT('%', #{param.roomNumber}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRoom">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRoom">
        <include refid="selectSql"></include>
    </select>

</mapper>
