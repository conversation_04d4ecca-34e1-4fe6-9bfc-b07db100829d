package com.hlkj.yxsAdminApi.common.core.constant;

/**
 * 系统常量
 * Created by EleAdmin on 2019-10-29 15:55
 */
public class Constants {
    /**
     * 默认成功码
     */
    public static final int RESULT_OK_CODE = 0;

    /**
     * 默认失败码
     */
    public static final int RESULT_ERROR_CODE = 1;

    /**
     * 默认成功信息
     */
    public static final String RESULT_OK_MSG = "操作成功";

    /**
     * 默认失败信息
     */
    public static final String RESULT_ERROR_MSG = "操作失败";

    /**
     * 无权限错误码
     */
    public static final int UNAUTHORIZED_CODE = 403;

    /**
     * 无权限提示信息
     */
    public static final String UNAUTHORIZED_MSG = "没有访问权限";

    /**
     * 未认证错误码
     */
    public static final int UNAUTHENTICATED_CODE = 401;

    /**
     * 未认证提示信息
     */
    public static final String UNAUTHENTICATED_MSG = "请先登录";

    /**
     * 登录过期错误码
     */
    public static final int TOKEN_EXPIRED_CODE = 401;

    /**
     * 登录过期提示信息
     */
    public static final String TOKEN_EXPIRED_MSG = "登录已过期";

    /**
     * 非法token错误码
     */
    public static final int BAD_CREDENTIALS_CODE = 401;

    /**
     * 非法token提示信息
     */
    public static final String BAD_CREDENTIALS_MSG = "请退出重新登录";

    /**
     * 账号冻结错误码
     */
    public static final int ACCOUNT_FROZEN_CODE = 402;

    /**
     * 账号冻结提示信息
     */
    public static final String ACCOUNT_FROZEN_MSG = "账号已被冻结";

    /**
     * 表示升序的值
     */
    public static final String ORDER_ASC_VALUE = "asc";

    /**
     * 表示降序的值
     */
    public static final String ORDER_DESC_VALUE = "desc";

    /**
     * token通过header传递的名称
     */
    public static final String TOKEN_HEADER_NAME = "Authorization";

    /**
     * token通过参数传递的名称
     */
    public static final String TOKEN_PARAM_NAME = "access_token";

    /**
     * token认证类型
     */
    public static final String TOKEN_TYPE = "Bearer";

    /**
     * 必输项为空
     */
    public static final int MUST_PARAM_IS_NULL = 999;

    /**
     * 必输项为空
     */
    public static final String MUST_PARAM_IS_NULL_MSG = "必输项为空";

    /**
     * hnzsxh5_category表状态定义
     */
    public static final int HNZSXH5_CATEGORY_STATE_DEL = 2;

    /**
     * hnzsxh5_category表状态定义
     */
    public static final int HNZSXH5_CATEGORY_STATE_EFFECT = 1;

    public static final String limitUser = "user_call_count_user_";

    public static final String limitOrder = "user_call_count_order_";
    public static final String limitManager = "user_call_count_manager_";

}
