package com.hlkj.yxsAdminApi.hnzhsl.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品学校关系表
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslGoodsBelong对象", description = "商品学校关系表")
public class HnslGoodsBelong implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编码")
    @TableField("GOODS_NUMBER")
    private String goodsNumber;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private Integer status;

}
