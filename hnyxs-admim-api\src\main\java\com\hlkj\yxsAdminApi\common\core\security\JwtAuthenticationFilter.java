package com.hlkj.yxsAdminApi.common.core.security;

import cn.hutool.core.util.StrUtil;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import com.hlkj.yxsAdminApi.common.core.config.InputStreamHttpServletRequestWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.CommonUtil;
import com.hlkj.yxsAdminApi.common.system.entity.LoginRecord;
import com.hlkj.yxsAdminApi.common.system.entity.Menu;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.LoginRecordService;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理携带token的请求过滤器
 *
 * <AUTHOR>
 * @since 2020-03-30 20:48:05
 */
//@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private UserService userService;
    @Autowired
    private LoginRecordService loginRecordService;

    private static Logger logger =  LogManager.getLogger(JwtAuthenticationFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String access_token = JwtUtil.getAccessToken(request);
        if (StrUtil.isNotBlank(access_token)) {
            try {
                // 解析token
                Claims claims = JwtUtil.parseToken(access_token, configProperties.getTokenKey());
                JwtSubject jwtSubject = JwtUtil.getJwtSubject(claims);
                User user = userService.getByUsername(jwtSubject.getUsername(), jwtSubject.getTenantId());
                if (user == null) {
                    throw new UsernameNotFoundException("Username not found");
                }
                // 检查用户状态是否正常
                if (user.getStatus() != null && !user.getStatus().equals(0)) {
                    // 用户被冻结，返回错误信息
                    CommonUtil.responseError(response, Constants.ACCOUNT_FROZEN_CODE, Constants.ACCOUNT_FROZEN_MSG, null);
                    return;
                }
                List<Menu> authorities = user.getAuthorities().stream().filter(m -> StrUtil.isNotBlank(m.getAuthority())).collect(Collectors.toList());
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(user, null, authorities);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                // token将要过期签发新token, 防止突然退出登录
                long expiration = (claims.getExpiration().getTime() - new Date().getTime()) / 1000 / 60;
                if (expiration < configProperties.getTokenRefreshTime()) {
                    String token = JwtUtil.buildToken(jwtSubject, configProperties.getTokenExpireTime(),configProperties.getTokenKey());
                    response.addHeader(Constants.TOKEN_HEADER_NAME, token);
                    loginRecordService.saveAsync(user.getUsername(), LoginRecord.TYPE_REFRESH, null,user.getTenantId(), request);
                }
            } catch (ExpiredJwtException e) {
                CommonUtil.responseError(response, Constants.TOKEN_EXPIRED_CODE, Constants.TOKEN_EXPIRED_MSG,e.getMessage());
                return;
            } catch (Exception e) {
                CommonUtil.responseError(response, Constants.BAD_CREDENTIALS_CODE, Constants.BAD_CREDENTIALS_MSG,e.toString());
                return;
            }
        }

        String uri = request.getRequestURI();
        String method = request.getMethod();
        logger.info("请求进来=================={}",uri);
        if (uri.contains("/avoid/") || uri.contains("/decode/")){ //请求地址包含avoid不解密入参
            chain.doFilter(request, response);
        }else{//暂定域名禁止转发和重定向
            //创建InputStreamHttpServletRequestWrapper类继承HttpServletRequestWrapper，并且将http请求 req1放入到创建的LogHttpServletRequestWrapper类中。并且在此类中做解密操作。
            InputStreamHttpServletRequestWrapper req = new InputStreamHttpServletRequestWrapper((HttpServletRequest) request);
            //放行访问
            chain.doFilter(req, response);
        }
    }

}
