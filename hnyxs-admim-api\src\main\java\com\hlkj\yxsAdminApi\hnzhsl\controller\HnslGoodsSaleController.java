package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsSaleService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsSale;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsSaleParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 销售品表控制器
 *
 * <AUTHOR>
 * @since 2023-04-24 17:48:29
 */
@Api(tags = "销售品表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoodsSale")
public class HnslGoodsSaleController extends BaseController {
    @Resource
    private HnslGoodsSaleService hnslGoodsSaleService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:list')")
    @OperationLog
    @ApiOperation("分页查询销售品表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsSale>> page(@RequestBody HnslGoodsSaleParam param) {
        PageParam<HnslGoodsSale, HnslGoodsSaleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsSaleService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsSaleService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:list')")
    @OperationLog
    @ApiOperation("查询全部销售品表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoodsSale>> list(@RequestBody HnslGoodsSaleParam param) {
        PageParam<HnslGoodsSale, HnslGoodsSaleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsSaleService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsSaleService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:list')")
    @OperationLog
    @ApiOperation("根据id查询销售品表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsSale> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsSaleService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsSaleService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:save')")
    @OperationLog
    @ApiOperation("添加销售品表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslGoodsSale hnslGoodsSale) {
        User loginUser = getLoginUser();
        hnslGoodsSale.setCreatedUser(loginUser.getUsername());
        hnslGoodsSale.setCreatedDate(new Date());
        hnslGoodsSale.setStatus(1);
        if (hnslGoodsSaleService.save(hnslGoodsSale)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:update')")
    @OperationLog
    @ApiOperation("修改销售品表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoodsSale hnslGoodsSale) {
        User loginUser = getLoginUser();
        hnslGoodsSale.setUpdatedUser(loginUser.getUsername());
        hnslGoodsSale.setUpdatedDate(new Date());
        if (hnslGoodsSaleService.updateById(hnslGoodsSale)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:remove')")
    @OperationLog
    @ApiOperation("删除销售品表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsSaleService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:save')")
    @OperationLog
    @ApiOperation("批量添加销售品表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsSale> list) {
        if (hnslGoodsSaleService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:update')")
    @OperationLog
    @ApiOperation("批量修改销售品表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<HnslGoodsSale> batchParam) {
        if (batchParam.update(hnslGoodsSaleService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsSale:remove')")
    @OperationLog
    @ApiOperation("批量删除销售品表")
    @PostMapping("/removeBatchId")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsSaleService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
