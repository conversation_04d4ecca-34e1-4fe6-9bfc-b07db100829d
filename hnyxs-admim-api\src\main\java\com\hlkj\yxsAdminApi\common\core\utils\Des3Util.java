package com.hlkj.yxsAdminApi.common.core.utils;

import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

public class Des3Util {

	// 采用3des算法.
	private static final String Algorithm = "desede";
	// 加密向量
	private static byte[] iv = new byte[] { 0x12, 0x34, 0x56, 0x78, (byte) 0x90, (byte) 0xAB, (byte) 0xCD,
			(byte) 0xEF };

	/**
	 * 执行3des加密
	 *
	 * @param secretKey  秘钥
	 * @param dataString 明文
	 * @return base64编码文本
	 */
	public static String EncryptDES(String secretKey, String dataString) {
		if (StringUtils.isBlank(dataString))
			return "";
		try {
			// 生成密钥
			DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
			SecretKeyFactory keyfactory = SecretKeyFactory.getInstance(Algorithm);
			Key deskey = keyfactory.generateSecret(spec);
			IvParameterSpec ivspec = new IvParameterSpec(iv);
			// 加密
			Cipher c1 = Cipher.getInstance("DESede/CBC/PKCS5Padding");
			c1.init(Cipher.ENCRYPT_MODE, deskey, ivspec);
			return Des3Base64Utils.Des3encode(c1.doFinal(dataString.getBytes()));
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return "";
	}

	/**
	 * 3des解密
	 *
	 * @param secretKey     秘钥
	 * @param decryptString base64编码文本
	 * @return 明文
	 */
	public static String DecryptDES(String secretKey, String decryptString) {
		if (StringUtils.isBlank(decryptString))
			return "";
		try {
			byte[] data = Base64Constant.Des3decode(decryptString);
			DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
			SecretKeyFactory keyfactory = SecretKeyFactory.getInstance(Algorithm);
			Key deskey = keyfactory.generateSecret(spec);
			IvParameterSpec ivspec = new IvParameterSpec(iv);
//加密
			Cipher c1 = Cipher.getInstance("DESede/CBC/PKCS5Padding");
			c1.init(Cipher.DECRYPT_MODE, deskey, ivspec);
			return new String(c1.doFinal(data));
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return "";
	}

	/**
	 * 加密
	 *
	 * @param data
	 * @param keyHex
	 * @param ivHex
	 * @return
	 * @throws Exception
	 */
	public static String desEncrypt(byte[] data, String keyHex, String ivHex) throws Exception {
		Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");

		SecretKey secretKey = new SecretKeySpec(hex2ba(keyHex), "DESede");
		IvParameterSpec iv = new IvParameterSpec(hex2ba(ivHex));
		cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

		return encryptBASE64(cipher.doFinal(data));
	}

	/**
	 * 解密
	 *
	 * @param data
	 * @param keyHex
	 * @param ivHex
	 * @return
	 * @throws Exception
	 */
	public static String desDecrypt(String data, String keyHex, String ivHex) throws Exception {
		byte[] dataByte = decryptBASE64(data);
		Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding"); // DESede/CBC/PKCS5Padding

		SecretKey secretKey = new SecretKeySpec(hex2ba(keyHex), "DESede");
		IvParameterSpec iv = new IvParameterSpec(hex2ba(ivHex));
		cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

		return new String(cipher.doFinal(dataByte), "GBK");
	}

	/**
	 * @param hexString
	 * @return
	 */
	public static byte[] hex2ba(String hexString) {
		byte[] bytes = new byte[hexString.length() / 2];
		for (int i = 0; i < bytes.length; i++) {
			bytes[i] = (byte) Integer.parseInt(hexString.substring(2 * i, 2 * i + 2), 16);
		}
		return bytes;
	}

	/**
	 * BASE64解密
	 *
	 * @param key = 需要解密的密码字符串
	 * @return
	 * @throws Exception
	 */
	public static byte[] decryptBASE64(String key) throws Exception {
		return (new BASE64Decoder()).decodeBuffer(key);
	}

	/**
	 * BASE64加密
	 *
	 * @param key = 需要加密的字符数组
	 * @return
	 * @throws Exception
	 */
	public static String encryptBASE64(byte[] key) throws Exception {
		return (new BASE64Encoder()).encodeBuffer(key);
	}
}
