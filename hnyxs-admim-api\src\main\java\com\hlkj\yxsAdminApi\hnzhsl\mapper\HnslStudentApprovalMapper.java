package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslStudentApproval;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslStudentApprovalParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @since 2023-07-21 11:31:45
 */
public interface HnslStudentApprovalMapper extends BaseMapper<HnslStudentApproval> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslStudentApproval>
     */
    List<HnslStudentApproval> selectPageRel(@Param("page") IPage<HnslStudentApproval> page,
                             @Param("param") HnslStudentApprovalParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslStudentApproval> selectListRel(@Param("param") HnslStudentApprovalParam param);

}
