package com.hlkj.yxsAdminApi.common.core.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.mybatis.logging.LoggerFactory;

import javax.imageio.ImageIO;
import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.hlkj.yxsAdminApi.common.core.utils.ConstantUtil.*;
import static com.hlkj.yxsAdminApi.common.core.utils.StringUtil.logger;

/**
 * 请求工具类
 * <AUTHOR> 2019-03-23
 *
 */
public class RequestUtil {
	private static Logger log = LogManager.getLogger(RequestUtil.class);

	private static final String DEFAULT_CHARSET = "UTF-8";
	private static final int DEFAULT_CONNECT_TIMEOUT = 60000;
	private static final int DEFAULT_READ_TIMEOUT = 60000;

	/**
	 * post提交
	 * 
	 * @param url
	 *            接收的url
	 * @param parameter
	 *            接收的参数
	 * @return 返回结果
	 */
	public static String sendPost(String url, String parameter) {
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("POST");
			byte contentbyte[] = parameter.toString().getBytes();
			// 设置请求类型
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			// 设置表单长度
			conn.setRequestProperty("Content-Length", (new StringBuilder()).append(contentbyte.length).toString());
			// 设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");// zh-CN代表中国
																	// 默认为美式英语?
			conn.setRequestProperty("Accept-Charset", "UTF-8");
			conn.setRequestProperty("contentType", "UTF-8");
			// 设置编码

			// 连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(60000);
			// 从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(60000);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在 2
			// http正文内，如果你打算将URL连接进行输出, 默认情况下是false;
			conn.setDoOutput(true);
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream()));
			bWriter.write(parameter.toString());
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			// 此方法是用Reader读取BufferedReader reader = new BufferedReader(new
			// InputStreamReader( connection.getInputStream()));
			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
		} catch (Exception ex) {
			log.info("sendPost:" + ex.getMessage());
			// LogUtil.Log(ex.getMessage());
		}
		return result;
	}

	public static String sendPostWx(String url, String parameter) {
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("POST");
			byte contentbyte[] = parameter.toString().getBytes();
			// 设置请求类型
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			// 设置表单长度
			conn.setRequestProperty("Content-Length", (new StringBuilder()).append(contentbyte.length).toString());
			// 设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");// zh-CN代表中国
																	// 默认为美式英文
			conn.setRequestProperty("Accept-Charset", "UTF-8");
			conn.setRequestProperty("contentType", "UTF-8");
			// 设置编码

			// 连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(60000);
			// 从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(60000);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在 2
			// http正文内，如果你打算将URL连接进行输出, 默认情况下是false;
			conn.setDoOutput(true);
			// BufferedWriter bWriter = new BufferedWriter(new
			// OutputStreamWriter(conn.getOutputStream()));
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream(), "UTF-8"));
			bWriter.write(parameter.toString());
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			// 此方法是用Reader读取BufferedReader reader = new BufferedReader(new
			// InputStreamReader( connection.getInputStream()));
			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
		} catch (Exception ex) {
			log.info("sendPost:" + ex.getMessage());
			// LogUtil.Log(ex.getMessage());
		}
		return result;
	}

	public static String transport(String url, String message) {
		StringBuffer sb = new StringBuffer();
		try {
			URL urls = new URL(url);
			HttpURLConnection uc = (HttpURLConnection) urls.openConnection();

			// uc.setRequestProperty("accept", "*/*");
			// uc.setRequestProperty("connection", "Keep-Alive");
			// uc.setRequestProperty("user-agent",
			// "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// // 发送POST请求必须设置如下两行
			// uc.setRequestProperty("Accept-Charset", "UTF-8");
			// uc.setRequestProperty("contentType", "UTF-8");

			uc.setRequestMethod("POST");
			uc.setRequestProperty("content-type", "application/x-www-form-urlencoded");
			uc.setRequestProperty("charset", "UTF-8");
			uc.setDoOutput(true);
			uc.setDoInput(true);
			uc.setReadTimeout(10000);
			uc.setConnectTimeout(10000);
			OutputStream os = uc.getOutputStream();
			DataOutputStream dos = new DataOutputStream(os);
			dos.write(message.getBytes("utf-8"));
			dos.flush();
			os.close();
			BufferedReader in = new BufferedReader(new InputStreamReader(uc.getInputStream(), "utf-8"));
			String readLine = "";
			while ((readLine = in.readLine()) != null) {
				sb.append(readLine);
			}
			in.close();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return sb.toString();
	}

	/**
	 * get提交
	 * 
	 * @param url
	 *            接收的url
	 * @return 返回结果
	 */
	public static String sendGet(String url) {
		String result = "";
		try {
			URL u0 = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) u0.openConnection();
			conn.setRequestMethod("GET");
			// 设置请求类型
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			// 设置默认语言
			conn.setRequestProperty("Content-Language", "en-US");// zh-CN代表中国
																	// 默认为美式英文
			conn.setRequestProperty("Accept-Charset", "UTF-8");
			conn.setRequestProperty("contentType", "UTF-8");
			// 连接主机的超时时间（单位：毫秒）
			conn.setConnectTimeout(60000);
			// 从主机读取数据的超时时间（单位：毫秒)
			conn.setReadTimeout(60000);
			// Post 请求不能使用缓存
			conn.setUseCaches(false);
			// 设置是否从httpUrlConnection读入，默认情况下是true;
			conn.setDoInput(true);
			// 设置是否向httpUrlConnection输出，因为这个是post请求，参数要放在 2
			// http正文内，因此送��设为true, 默认情况下是false;
			conn.setDoOutput(true);
			BufferedWriter bWriter = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream()));
			bWriter.flush();
			bWriter.close();
			// 调用HttpURLConnection连接对象的getInputStream()函数,
			// 将内存缓冲区中封装好的完整的HTTP请求电文发送到服务端
			InputStream in = conn.getInputStream();
			StringBuffer buffer = new StringBuffer();
			for (int i = 0; i != -1;) {
				i = in.read();
				if (i != -1)
					buffer.append((char) i);
			}
			in.close();
			// 此方法是用Reader读取BufferedReader reader = new BufferedReader(new
			// InputStreamReader( connection.getInputStream()));
			result = new String(buffer.toString().getBytes("iso-8859-1"), "UTF-8");
		} catch (Exception ex) {
			log.info("sendGet:" + ex.getMessage());
			// LogUtil.Log(ex.getMessage());
		}
		return result;
	}

	public static String sendPost_2(String url, String param) {
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setRequestProperty("Accept-Charset", "UTF-8");
			conn.setRequestProperty("contentType", "UTF-8");
			// 获取URLConnection对象对应的输出流
			out = new PrintWriter(conn.getOutputStream());
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响送
			in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送Post请求异常" + e);
			e.printStackTrace();
		}
		// 使用finally块来关闭输出流送输入送
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}


	/**
	 * 发送Http连接openapi新版 XML 格式
	 *
	 * @param content
	 * @param url
	 * @return
	 */
	public static StringBuffer doServerHttpOpenapiXML(String content, String url,String version) throws Exception {
		StringBuffer resultBuffer = new StringBuffer();
		try {
			URL console = new URL(url);
			HttpURLConnection connection = (HttpURLConnection) console.openConnection();
			connection.setRequestMethod("POST");
			// URL 连接可用于输入和/或输出
			connection.setDoOutput(true);
			// URL 连接可用于输入和/或输出
			connection.setDoInput(true);
			// 禁用缓存
			connection.setUseCaches(false);
			// 设置请求头
			// 设置http头信息
			connection.setRequestProperty("Content-Type", "application/xml;charset=utf-8");
			connection.setRequestProperty("X-APP-ID", xAppId);
			connection.setRequestProperty("X-APP-KEY", xAppKey);
			/*connection.setRequestProperty("X-APP-ID", "b538e5c1313f215f2a25f03708ceca5a");
			connection.setRequestProperty("X-APP-KEY", "a874f542653915d47b888ea888e43939");*/
			connection.setRequestProperty("X-App-Secret", "22");
			connection.setRequestProperty("spanId", "1");
			connection.setRequestProperty("parentSpanId", "1");
			connection.setRequestProperty("sampledFlag", "1");
			connection.setRequestProperty("debugFlag", "1");
			connection.setRequestProperty("traceId", "1");
			connection.setRequestProperty("Accept", "application/json");
			connection.setRequestProperty("X-CTG-VERSION",version);
			connection.setRequestProperty("X-CTG-Request-ID",new SimpleDateFormat("yyyyMMddhhmmss").format(System.currentTimeMillis()));

			String timeoutstr = "30000";
			connection.setConnectTimeout(Integer.parseInt(timeoutstr));

			// 发送请求
			connection.getOutputStream().write(content.getBytes("UTF-8"));
			connection.getOutputStream().flush();
			connection.getOutputStream().close();

			// 获取请求结果
			BufferedReader resultReader = new BufferedReader(
					new InputStreamReader(connection.getInputStream(), "UTF-8"));
			String line = "";
			while ((line = resultReader.readLine()) != null) {
				resultBuffer.append(line);
			}
			resultReader.close();

			// 关闭Http链接
			connection.disconnect();
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}

		return resultBuffer;
	}

	/*
	 * 流获取参数
	 */
	public static String getParams(HttpServletRequest request) {
		String param = "";
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
			String line = null;
			StringBuilder sb = new StringBuilder();
			while ((line = br.readLine()) != null) {
				sb.append(line);
			}
			// 将资料解送
			param = sb.toString();
			System.out.println("param==" + param);
		} catch (Exception e) {
			return "";
		}
		return param;
	}

	public static String sendToPost(String postUrl, String params) throws IOException {
		BufferedReader in = null;
		OutputStreamWriter out = null;
		byte[] bytes = params.getBytes("UTF-8");
		StringBuffer result = new StringBuffer("");
		try {
			URL url = new URL(postUrl);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(3000);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/json; charset=utf-8"); // application/json;
																						// charset=utf-8
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("Content-Length", String.valueOf(bytes.length));
			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
			conn.setUseCaches(false);
			conn.setDoOutput(true);
			out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
			out.write(params);
			out.flush();
			out.close();

			// 获取响应状态
			if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
				log.info("connect failed!");
			}
			// 获取响应内容体
			String line;
			in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
			while ((line = in.readLine()) != null) {
				result.append(line);
			}
			in.close();
			return result.toString();
		} catch (IOException e) {
			e.printStackTrace(System.out);
			return result.toString();
		} finally {
			if (in != null) {
				in.close();
			}
			if (out != null) {
				out.close();
			}
		}
	}

	/**
	 * 向指定 URL 发送POST方法的请求
	 * 
	 * @param url
	 *            发送请求的 URL
	 * @param params
	 *            请求参数
	 * @return 所代表远程资源的响应结果
	 */
	public static String doPost(String url, String params) {
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			/** 打开和URL之间的连接 **/
			URLConnection conn = realUrl.openConnection();
			/** 设置通用的请求属性 **/
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			conn.setRequestProperty("Content-Type", "application/json; charset=utf-8"); // application/json;
			/** 发送POST请求必须设置如下两行 **/
			conn.setDoOutput(true);
			conn.setDoInput(true);
			/** 获取URLConnection对象对应的输出流 **/
			out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), "UTF-8"));
			/** 发送请求参数 **/
			String param = params;
			out.print(param);
			/** flush输出流的缓冲 **/
			out.flush();
			/** 定义BufferedReader输入流来读取URL的响应 **/
			in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送 POST 请求出现异常！" + e);
			e.printStackTrace();
		} finally {
			/** 使用finally块来关闭输出流、输入流 **/
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}

	public static void drawPictureInfoExcel(HSSFWorkbook wb, HSSFPatriarch patriarch, int rowIndex, String pictureUrl) {
		try {
			//anchor主要用于设置图片的属性
			HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 1000, 255, (short) 2, rowIndex, (short)2, rowIndex);
			//Sets the anchor type （图片在单元格的位置）
			//0 = Move and size with Cells, 2 = Move but don't size with cells, 3 = Don't move or size with cells.
			anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
			URL url = new URL(pictureUrl);
			BufferedImage bufferImg = ImageIO.read(url);
			ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
			ImageIO.write(bufferImg, "jpg", byteArrayOut);
			byte[] data = byteArrayOut.toByteArray();
			patriarch.createPicture(anchor, wb.addPicture(data, HSSFWorkbook.PICTURE_TYPE_JPEG));
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 向指定URL发送GET方法的请求
	 *
	 * @param url 发送请求的URL 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return URL 所代表远程资源的响应结果
	 */
	public static String getHttpByUrl(String url, String interfaceName) {
		log.info(interfaceName + ">>>>>>HttpUtil中sendPost请求地址:" + url);
		String result = "";
		BufferedReader in = null;
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection connection = realUrl.openConnection();
			// 设置通用的请求属性
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("Content-Type", "application/json;charset=utf-8");
			connection.setRequestProperty("X-APP-ID", xAppId);
			connection.setRequestProperty("X-APP-KEY", xAppKey);
			connection.setRequestProperty("X-CTG-Request-ID", new SimpleDateFormat("yyyyMMddhhmmss").format(new Date().getTime()));
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 建立实际的连接
			connection.connect();
			// 获取所有响应头字段
			Map<String, List<String>> map = connection.getHeaderFields();
			// 遍历所有的响应头字段
			/*
			 * for (String key : map.keySet()) { System.out.println(key + "--->" +
			 * map.get(key)); }
			 */
			// 定义 BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送GET请求出现异常！" + e);
			e.printStackTrace();
		}
		// 使用finally块来关闭输入流
		finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
		}

		log.info(interfaceName + ">>>>>>HttpUtil中sendPost请求响应:" + result.toString());
		return result;
	}
	
	/* 发送Http连接openapi新版
	 * 
	 * @param content
	 * @param url
	 * @param version
	 * @return
	 * @throws Exception
	 */
	public static StringBuffer doServerHttpOpenapiNew(String content, String url, String version, String interfaceName)
			throws Exception {
		log.info(interfaceName + ">>>>>>HttpUtil中sendPost请求地址:" + url + ",请求参数:" + content);
		StringBuffer resultBuffer = new StringBuffer();
		try {
			URL console = new URL(url);
			HttpURLConnection connection = (HttpURLConnection) console.openConnection();
			connection.setRequestMethod("POST");
			// URL 连接可用于输入和/或输出
			connection.setDoOutput(true);
			// URL 连接可用于输入和/或输出
			connection.setDoInput(true);
			// 禁用缓存
			connection.setUseCaches(false);
			// 设置请求头
			// 设置http头信息
			connection.setRequestProperty("Content-Type", "application/json;charset=utf-8");
			connection.setRequestProperty("X-APP-ID", xAppId);
			connection.setRequestProperty("X-APP-KEY", xAppKey);
			connection.setRequestProperty("X-App-Secret", "22");
			connection.setRequestProperty("spanId", "1");
			connection.setRequestProperty("parentSpanId", "1");
			connection.setRequestProperty("sampledFlag", "1");
			connection.setRequestProperty("debugFlag", "1");
			connection.setRequestProperty("traceId", "1");
			connection.setRequestProperty("Accept", "application/json");
			connection.setRequestProperty("X-CTG-VERSION", version);
			connection.setRequestProperty("X-CTG-Request-ID",
					new SimpleDateFormat("yyyyMMddhhmmss").format(System.currentTimeMillis()));

			String timeoutstr = "30000";
			connection.setConnectTimeout(Integer.parseInt(timeoutstr));

			// 发送请求
			connection.getOutputStream().write(content.getBytes("UTF-8"));
			connection.getOutputStream().flush();
			connection.getOutputStream().close();

			// 获取请求结果
			BufferedReader resultReader = new BufferedReader(
					new InputStreamReader(connection.getInputStream(), "UTF-8"));
			String line = "";
			while ((line = resultReader.readLine()) != null) {
				resultBuffer.append(line);
			}
			resultReader.close();

			// 关闭Http链接
			connection.disconnect();
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}

		log.info(interfaceName + ">>>>>>HttpUtil中sendPost请求响应:" + resultBuffer.toString());
		return resultBuffer;
	}

	/**
	 * 向指定 URL 发送POST方法的请求
	 *
	 * @param url           发送请求的 URL
	 * @param params        请求参数
	 * @param interfaceName 请求接口名称说明
	 * @return 所代表远程资源的响应结果，如果请求过程中出现异常，返回空字符串
	 */
	public static String doPost(String url, String params, String interfaceName) {
		return sendPostRequest(url, params, interfaceName, "application/json; charset=utf-8", null);
	}

	/**
	 * 发送HTTP POST请求
	 *
	 * @param url           请求地址
	 * @param parameter     请求参数
	 * @param interfaceName 请求接口名称说明，用于日志记录，方便识别请求来源
	 * @param contentType   请求内容类型
	 * @param headers       请求头
	 * @return 响应结果，如果请求过程中出现异常，返回空字符串
	 */
	private static String sendPostRequest(String url, String parameter, String interfaceName, String contentType, Map<String, String> headers) {
		log.info(interfaceName + ">>>>>>HttpUtil中sendPost请求地址:" + url + ",请求参数:" + parameter);
		HttpURLConnection conn = null;
		try {
			// 创建URL对象
			URL u = new URL(url);
			// 打开连接
			conn = (HttpURLConnection) u.openConnection();
			// 设置请求方法为POST
			conn.setRequestMethod("POST");
			// 设置通用请求属性
			setCommonRequestProperties(conn, contentType, headers, parameter.getBytes(DEFAULT_CHARSET).length);
			// 设置连接超时时间
			conn.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
			// 设置读取超时时间
			conn.setReadTimeout(DEFAULT_READ_TIMEOUT);
			// 不使用缓存
			conn.setUseCaches(false);
			// 允许输入
			conn.setDoInput(true);
			// 允许输出
			conn.setDoOutput(true);

			// 将请求参数写入输出流
			try (BufferedWriter bWriter = new BufferedWriter(
					new OutputStreamWriter(conn.getOutputStream(), DEFAULT_CHARSET))) {
				bWriter.write(parameter);
				bWriter.flush();
			}

			// 读取响应结果
			try (InputStream in = conn.getInputStream();
				 InputStreamReader inputStreamReader = new InputStreamReader(in, DEFAULT_CHARSET);
				 BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
				StringBuilder buffer = new StringBuilder();
				String line;
				while ((line = bufferedReader.readLine()) != null) {
					buffer.append(line);
				}
				return buffer.toString();
			}
		} catch (Exception ex) {
			// 记录请求异常信息
			log.error("HttpUtil中sendPost请求异常:", ex);
			return "";
		} finally {
			if (conn != null) {
				// 断开连接
				conn.disconnect();
			}
		}
	}

	/**
	 * 设置通用请求属性
	 *
	 * @param conn          HttpURLConnection对象
	 * @param contentType   请求内容类型
	 * @param headers       请求头
	 * @param contentLength 请求内容长度
	 */
	private static void setCommonRequestProperties(HttpURLConnection conn, String contentType,
												   Map<String, String> headers, int contentLength) {
		// 设置内容类型
		conn.setRequestProperty("Content-Type", contentType);
		// 设置内容长度
		conn.setRequestProperty("Content-Length", String.valueOf(contentLength));
		// 设置内容语言
		conn.setRequestProperty("Content-Language", "en-US");
		// 设置接受字符集
		conn.setRequestProperty("Accept-Charset", DEFAULT_CHARSET);
		if (headers != null) {
			// 添加自定义请求头
			for (Map.Entry<String, String> entry : headers.entrySet()) {
				conn.setRequestProperty(entry.getKey(), entry.getValue());
			}
		}
	}

	/**
	 * HTTP POST请求 content-Type:application/ x-json;charset=UTF-8
	 *
	 * @param url           请求地址
	 * @param parameter     请求参数
	 * @param interfaceName 请求接口名称说明
	 * @return 响应结果，如果请求过程中出现异常，返回空字符串
	 */
	public static String sendPost(String url, String parameter, String interfaceName) {
		return sendPostRequest(url, parameter, interfaceName, "application/x-json;charset=UTF-8", null);
	}

	/**
	 * 发送Http连接openapi新版
	 *
	 * @param content       请求内容
	 * @param url           请求地址
	 * @param interfaceName 请求接口名称说明
	 * @return 响应结果，封装在StringBuffer中
	 * @throws Exception 异常
	 */
	public static StringBuffer doServerHttpOpenapiNew(String content, String url, String interfaceName)
			throws Exception {
		// 创建请求头
		Map<String, String> headers = new HashMap<>();
		headers.put("X-APP-ID", xAppId);
		headers.put("X-APP-KEY", xAppKey);
		headers.put("X-App-Secret", "22");
		headers.put("spanId", "1");
		headers.put("parentSpanId", "1");
		headers.put("sampledFlag", "1");
		headers.put("debugFlag", "1");
		headers.put("traceId", "1");
		headers.put("Accept", "application/json");
		headers.put("X-CTG-VERSION", "V1.0.00");
		headers.put("X-CTG-Request-ID", new SimpleDateFormat("yyyyMMddhhmmss").format(System.currentTimeMillis()));

		// 发送POST请求
		String response = sendPostRequest(url, content, interfaceName, "application/json;charset=utf-8", headers);
		return new StringBuffer(response);
	}

	/**
	 * 向指定URL发送POST方法的请求
	 *
	 * @param url
	 *            发送请求的URL
	 *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return URL 所代表远程资源的响应结果
	 */
	public static String postHttpByUrlID(String url,String content,String appid,String appkey) throws Exception {
		StringBuffer resultBuffer = new StringBuffer();
		BufferedReader resultReader=null;
		HttpURLConnection connection=null;
		try {
			URL console = new URL(url);
			connection = (HttpURLConnection) console.openConnection();
			connection.setRequestMethod("POST");
			// URL 连接可用于输入和/或输出
			connection.setDoOutput(true);
			// URL 连接可用于输入和/或输出
			connection.setDoInput(true);
			// 禁用缓存
			connection.setUseCaches(false);
			// 设置请求头
			// 设置http头信息
			connection.setRequestProperty("Content-Type", "application/json;charset=utf-8");
			connection.setRequestProperty("X-APP-ID", appid);
			connection.setRequestProperty("X-APP-KEY", appkey);
			connection.setRequestProperty("X-CTG-Request-ID",new SimpleDateFormat("yyyyMMddhhmmss").format(new Date().getTime()));

			String timeoutstr = "30000";
			connection.setConnectTimeout(Integer.parseInt(timeoutstr));

			// 发送请求
			connection.getOutputStream().write(content.getBytes("UTF-8"));
			connection.getOutputStream().flush();
			connection.getOutputStream().close();

			// 获取请求结果
			resultReader = new BufferedReader(
					new InputStreamReader(connection.getInputStream(), "UTF-8"));
			String line = "";
			while ((line = resultReader.readLine()) != null) {
				resultBuffer.append(line);
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}finally {
			try {
				if (resultReader != null) {
					resultReader.close();
				}
				if (connection != null) {
					// 关闭Http链接
					connection.disconnect();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
				throw e2;
			}
		}
		return resultBuffer.toString();
	}

	/**
	 * HttpClient发送post请求
	 *
	 * @param url           请求地址
	 * @param headers       请求头
	 * @param charset       字符集
	 * @param interfaceName 接口名称
	 * @param jsonBody      请求体
	 * @return 响应结果，如果请求过程中出现异常，返回空
	 *
	 *         /** HttpClient发送post请求
	 *
	 * @param url           请求地址
	 * @param headers       请求头
	 * @param charset       字符集
	 * @param interfaceName 接口名称
	 * @param jsonBody      请求体
	 * @return 响应结果
	 */
	public static String httpClientdoPost(String url, Map<String, String> headers, String charset, String interfaceName,
										  String jsonBody) {
		log.info(
				interfaceName + ">>>请求>>>地址:" + url + ",请求头信息:" + headers + ",charset:" + charset + ",请求体:" + jsonBody);
		try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
			HttpPost httpPost = new HttpPost(url);
			setCommonHttpClientHeaders(httpPost, headers);
			RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(10000).setSocketTimeout(30000)
					.build();
			httpPost.setConfig(requestConfig);

			if (StringUtils.isEmpty(charset)) {
				charset = DEFAULT_CHARSET;
			}

			if (jsonBody != null && !jsonBody.isEmpty()) {
				httpPost.setEntity(new StringEntity(jsonBody, charset));
			}

			try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
				HttpEntity resEntity = response.getEntity();
				String restData = resEntity != null ? EntityUtils.toString(resEntity, charset) : "";
				log.info(interfaceName + ">>>响应>>>" + restData);
				return restData;
			}
		} catch (IOException | ParseException ex) {
			log.error("HttpClient发送post请求出现异常！", ex);
			return "";
		}
	}

	/**
	 * 设置HttpClient通用请求头
	 *
	 * @param httpRequest 请求对象
	 * @param headers     请求头
	 */
	private static void setCommonHttpClientHeaders(org.apache.http.client.methods.HttpRequestBase httpRequest,
												   Map<String, String> headers) {
		// 设置保持连接
		httpRequest.addHeader("connection", "Keep-Alive");
		// 设置内容类型
		httpRequest.addHeader("Content-Type", "application/json;charset=utf-8");
		// 设置应用ID
		httpRequest.addHeader("X-APP-ID", ConstantUtil.DCOOS_AppId);
		// 设置应用密钥
		httpRequest.addHeader("X-APP-KEY", ConstantUtil.DCOOS_AppKey);
		// 设置版本号
		httpRequest.addHeader("X-CTG-VERSION", "V1.0.00");
		// 设置请求ID
		httpRequest.addHeader("X-CTG-Request-ID", new SimpleDateFormat("yyyyMMddhhmmss").format(new Date().getTime()));
		// 设置用户代理
		httpRequest.addHeader("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
		if (headers != null) {
			// 添加自定义请求头
			for (Map.Entry<String, String> entry : headers.entrySet()) {
				httpRequest.addHeader(entry.getKey(), entry.getValue());
			}
		}
	}

	/**
	 * 向指定的URL发送GET方法的请求
	 *
	 * @param url   发送请求的URL
	 * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式
	 * @return 远程资源的响应结果
	 */
	public static String custandfunclistSendGet(String url, String param) {
		String result = "";
		BufferedReader bufferedReader = null;
		try {
			// 1、读取初始URL
			String urlNameString = url + "?" + param;
			// 2、将url转变为URL类对象
			URL realUrl = new URL(urlNameString);

			// 3、打开和URL之间的连接
			URLConnection connection = realUrl.openConnection();
			// 4、设置通用的请求属性
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			connection.setRequestProperty("Content-Type", "application/json;charset=utf-8");
			connection.setRequestProperty("X-APP-ID", Test_AppId);
			connection.setRequestProperty("X-APP-KEY", Test_AppKey);
			connection.setRequestProperty("X-CTG-Request-ID", new SimpleDateFormat("yyyyMMddhhmmss").format(new Date().getTime()));

			// 5、建立实际的连接
			connection.connect();
			// 获取所有响应头字段
			Map<String, List<String>> map = connection.getHeaderFields();
			// 遍历所有的响应头字段
			for (String key : map.keySet()) {
				System.out.println(key + "---->" + map.get(key));
			}

			// 6、定义BufferedReader输入流来读取URL的响应内容 ，UTF-8是后续自己加的设置编码格式，也可以去掉这个参数
			bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
			String line = "";
			while (null != (line = bufferedReader.readLine())) {
				result += line;
			}
		} catch (Exception e) {
			// TODO: handle exception
			System.out.println("发送GET请求出现异常！！！" + e);
			e.printStackTrace();
		} finally { // 使用finally块来关闭输入流
			try {
				if (null != bufferedReader) {
					bufferedReader.close();
				}
			} catch (Exception e2) {
				// TODO: handle exception
				e2.printStackTrace();
			}
		}
		return result;
	}


	/**
	 * 发送Http连接openapi
	 *
	 * @param content
	 * @param url
	 * @return
	 */
	public static StringBuffer doServerHttpOpenapi(String content, String url) throws Exception {
		StringBuffer resultBuffer = new StringBuffer();
		try {
			URL console = new URL(url);
			HttpURLConnection connection = (HttpURLConnection) console.openConnection();
			connection.setRequestMethod("POST");
			// URL 连接可用于输入和/或输出
			connection.setDoOutput(true);
			// URL 连接可用于输入和/或输出
			connection.setDoInput(true);
			// 禁用缓存
			connection.setUseCaches(false);
			// 设置请求头
			// 设置http头信息

			// 生产环境：
			// APPKey：
			// d4c0ea55942b6d1c957e3a3842aadfff
			// AppSecret：
			// 4a4d717fa94e683ce7beaa7ef9c8b5fa
			//
			// 测试环境
			// APPKey：f4ed79707b94c966ae416dd117b01606
			// AppSecret：72ded0ae988af73ff1518b6cb42f5fa6
			connection.setRequestProperty("Content-Type", "application/json;charset=utf-8");
			connection.setRequestProperty("X-APP-ID", xAppId);
			connection.setRequestProperty("X-APP-KEY", xAppKey);
			connection.setRequestProperty("X-App-Secret", "22");
			connection.setRequestProperty("spanId", "1");
			connection.setRequestProperty("parentSpanId", "1");
			connection.setRequestProperty("sampledFlag", "1");
			connection.setRequestProperty("debugFlag", "1");
			connection.setRequestProperty("traceId", "1");
			connection.setRequestProperty("Accept", "application/json");

			String timeoutstr = "30000";
			connection.setConnectTimeout(Integer.parseInt(timeoutstr));

			// 发送请求
			connection.getOutputStream().write(content.getBytes("UTF-8"));
			connection.getOutputStream().flush();
			connection.getOutputStream().close();

			// 获取请求结果
			BufferedReader resultReader = new BufferedReader(
					new InputStreamReader(connection.getInputStream(), "UTF-8"));
			String line = "";
			while ((line = resultReader.readLine()) != null) {
				resultBuffer.append(line);
			}
			resultReader.close();

			// 关闭Http链接
			connection.disconnect();
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}

		return resultBuffer;
	}

}
