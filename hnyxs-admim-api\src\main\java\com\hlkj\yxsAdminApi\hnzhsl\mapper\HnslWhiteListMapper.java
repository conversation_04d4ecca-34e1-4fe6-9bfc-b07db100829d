package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslWhiteList;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslWhiteListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 熟卡白名单表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslWhiteListMapper extends BaseMapper<HnslWhiteList> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslWhiteList>
     */
    List<HnslWhiteList> selectPageRel(@Param("page") IPage<HnslWhiteList> page,
                             @Param("param") HnslWhiteListParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslWhiteList> selectListRel(@Param("param") HnslWhiteListParam param);

    /**
     * 导出白名单报表
     * @param map
     * @return
     */
    public List<Map<String, Object>> queryListTable(Map<String,Object> map);

    List<HnslWhiteList> queryWhiteObject(String userPhone, String cityCode, String schoolCode);
}
