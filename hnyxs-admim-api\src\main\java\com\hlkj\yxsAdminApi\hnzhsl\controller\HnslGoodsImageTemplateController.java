package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsImageTemplateService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsImageTemplate;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsImageTemplateParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-06-16 10:28:05
 */
@Api(tags = "扫楼商品图片模板表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoodsImageTemplate")
public class HnslGoodsImageTemplateController extends BaseController {
    @Autowired
    private HnslGoodsImageTemplateService hnslGoodsImageTemplateService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼商品图片模板表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsImageTemplate>> page(@RequestBody HnslGoodsImageTemplateParam param) {
        PageParam<HnslGoodsImageTemplate, HnslGoodsImageTemplateParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsImageTemplateService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsImageTemplateService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼商品图片模板表")
    @GetMapping("/list")
    public ApiResult<List<HnslGoodsImageTemplate>> list(@RequestBody HnslGoodsImageTemplateParam param) {
        PageParam<HnslGoodsImageTemplate, HnslGoodsImageTemplateParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsImageTemplateService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsImageTemplateService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼商品图片模板表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsImageTemplate> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsImageTemplateService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsImageTemplateService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:save')")
    @OperationLog
    @ApiOperation("添加扫楼商品图片模板表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslGoodsImageTemplate hnslGoodsImageTemplate) {
        if (hnslGoodsImageTemplateService.save(hnslGoodsImageTemplate)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:update')")
    @OperationLog
    @ApiOperation("修改扫楼商品图片模板表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoodsImageTemplate hnslGoodsImageTemplate) {
        if (hnslGoodsImageTemplateService.updateById(hnslGoodsImageTemplate)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:remove')")
    @OperationLog
    @ApiOperation("删除扫楼商品图片模板表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsImageTemplateService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:save')")
    @OperationLog
    @ApiOperation("批量添加扫楼商品图片模板表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsImageTemplate> list) {
        if (hnslGoodsImageTemplateService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:update')")
    @OperationLog
    @ApiOperation("批量修改扫楼商品图片模板表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoodsImageTemplate> batchParam) {
        if (batchParam.update(hnslGoodsImageTemplateService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsImageTemplate:remove')")
    @OperationLog
    @ApiOperation("批量删除扫楼商品图片模板表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsImageTemplateService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
