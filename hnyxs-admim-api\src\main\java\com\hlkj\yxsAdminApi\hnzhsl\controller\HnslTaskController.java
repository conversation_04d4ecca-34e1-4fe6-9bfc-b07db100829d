package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.utils.DateUtils;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskReceive;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskReceiveService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskScopeService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTask;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 任务信息表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "任务信息表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslTask")
public class HnslTaskController extends BaseController {
    @Autowired
    private HnslTaskService hnslTaskService;

    @Autowired
    private HnslTaskReceiveService hnslTaskReceiveService;

    @Autowired
    private HnslTaskScopeService hnslTaskScopeService;

    @Autowired
    private HnslUserService hnslUserService;
    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:list')")
    @OperationLog
    @ApiOperation("分页查询任务信息表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTask>> page(@RequestBody HnslTaskParam param) {
        //PageParam<HnslTask, HnslTaskParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        //return success(hnslTaskService.page(page, page.getWrapper()));
        // 使用关联查询
        return success(hnslTaskService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:list')")
    @OperationLog
    @ApiOperation("查询全部任务信息表")
    @PostMapping("/list")
    public ApiResult<List<HnslTask>> list(@RequestBody HnslTaskParam param) {
        PageParam<HnslTask, HnslTaskParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTaskService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:list')")
    @OperationLog
    @ApiOperation("根据id查询任务信息表")
    @GetMapping("/{id}")
    public ApiResult<HnslTask> get(@PathVariable("id") Integer id) {
        HnslTask hnslTask = hnslTaskService.getById(id);
        if ("1".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("信息采集");
        } else if ("2".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("老用户升级");
        } else if ("3".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("异网策反");
        } else if ("4".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("单宽带融合");
        } else if ("5".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("线上软文转发");
        } else if ("6".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("Q群截图上传");
        } else if ("7".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("其他");
        } else if ("8".equals(hnslTask.getTaskKind())) {
            hnslTask.setTaskTitle("服务截图上传");
        }

        return success(hnslTask);
        // 使用关联查询
        //return success(hnslTaskService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:save')")
    @OperationLog
    @ApiOperation("添加任务信息表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTask hnslTask) {
        User loginUser = getLoginUser();
        hnslTask.setTaskLevel(1);
        hnslTask.setTaskStatus("进行中");
        hnslTask.setTaskType("通用任务");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String taskCode = "HNSL" + sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 1000000));
        hnslTask.setTaskCode(taskCode);
        if (!hnslTaskService.save(hnslTask)) {
            return fail("添加失败");
        }
        String  content="";
        String  contentDetails="";
        if("1".equals(hnslTask.getTaskKind())) {
            content="您用一条信息采集的任务，请及时完成";
        }else if("2".equals(hnslTask.getTaskKind())) {
            content="您用一条老用户升级的任务，请及时完成";
        }else if("3".equals(hnslTask.getTaskKind())) {
            content="您用一条异网策反的任务，请及时完成";
        } else if("4".equals(hnslTask.getTaskKind())) {
            content="您用一条单宽带融合的任务，请及时完成";
        } else if("5".equals(hnslTask.getTaskKind())) {
            content="您用一条线上软文转发的任务，请及时完成";
        } else if("6".equals(hnslTask.getTaskKind())) {
            content="您用一条Q群概览截图上传的任务，请及时完成";
        } else if("7".equals(hnslTask.getTaskKind())) {
            content="您用一条其他的任务，请及时完成";
        }else if("8".equals(hnslTask.getTaskKind())) {
            content="您用一条服务截图上传的任务，请及时完成";
        }else {
            content="您用一条未知的任务，请及时完成";
        }
        contentDetails="您收到任务： 《"+hnslTask.getTaskTitle()+"》，任务截止至"+hnslTask.getEndtime()+"，还请及时完成任务！";

//        HnslMessageNotification mnPojo=new HnslMessageNotification();
//        mnPojo.setCreatedUser(UserName);
//        mnPojo.setOrderId(hnslTask.getTaskCode());
//        mnPojo.setNotificationContent(content);
//        mnPojo.setNotificationContentDetails(contentDetails);
//        mnPojo.setNotificationType("1");
//        mnPojo.setReadStatus(1);
//        mnPojo.setStatus(1);


        String phones = hnslTask.getTaskCountPhone();
        if (phones != null) {
            if ("all".equals(phones)) {
                Map<String, Object> params = new HashMap<>();
                List<String> list = new ArrayList<>();
                if (hnslTask.isOnelevel()) {
                    list.add("2");
                }
                if (hnslTask.isTwolevel()) {
                    list.add("3");
                }
                if (hnslTask.isThreelevel()) {
                    list.add("4");
                }
                if (hnslTask.isXlevel()) {
                    list.add("1");
                }
                params.put("level", list);
                if (!hnslTask.isBuildingLong()) {
                    params.put("buildingLong", null);
                }
                if (!StringUtil.isEmpty(hnslTask.getUserData())) {
                    String name = String.valueOf(hnslTask.getUserData());
                    if (name.length() == 18) {
                        params.put("userSfz", name);
                    } else if (name.length() == 11) {
                        params.put("userPhone", name);
                    } else {
                        String regEx = "[\u4e00-\u9fa5]";
                        Pattern pat = Pattern.compile(regEx);
                        Matcher matcher = pat.matcher(name);
                        if (matcher.find()) {
                            params.put("userName", name);
                        }
                    }
                }

                String taskScope = String.valueOf(hnslTask.getTaskScopeCode());
                if (taskScope != null && !"".equals(taskScope)) {
                    boolean status = taskScope.contains(",");
                    List<String> scopeList = new ArrayList<>();
                    if (status) {
                        String[] scope = taskScope.split(",");
                        for (String s : scope) {
                            scopeList.add(s);
                        }
                    } else {
                        scopeList.add(taskScope);
                    }
                    params.put("list", scopeList);
                } else {
                    params.put("list", null);
                }

                List<HnslUser> hnslUserList =  hnslUserService.queryListUserBy(params);
                HnslTaskReceive hnslTaskReceive = new HnslTaskReceive();
                hnslTaskReceive.setTaskStatusRel(1);
                hnslTaskReceive.setTaskCode(hnslTask.getTaskCode());
                for (HnslUser s : hnslUserList) {
                    hnslTaskReceive.setUserPhone(s.getUserPhone());
                    hnslTaskReceive.setTaskPublish(loginUser.getPhone());
                    hnslTaskReceiveService.save(hnslTaskReceive);
//                    String notificationCode = "HNSLMN" + sdf.format(new Date().getTime())
//                            + String.valueOf((int) ((Math.random() * 6 + 1) * 100000));
//                    mnPojo.setNotificationCode(notificationCode);
//                    mnPojo.setNotificationUser(s.getUserPhone());
//                    hnslMessageNotificationService.save(mnPojo);
                }
            } else {
                boolean status = phones.contains(",");
                HnslTaskReceive hnslTaskReceive = new HnslTaskReceive();
                if (status) {
                    hnslTaskReceive.setTaskStatusRel(1);
                    hnslTaskReceive.setTaskCode(hnslTask.getTaskCode());
                    String[] phone = phones.split(",");
                    for (String p : phone) {
                        hnslTaskReceive.setUserPhone(p);
                        hnslTaskReceive.setTaskPublish(loginUser.getPhone());
                        hnslTaskReceiveService.save(hnslTaskReceive);
//                        String notificationCode = "HNSLMN" + sdf.format(new Date().getTime())
//                                + String.valueOf((int) ((Math.random() * 6 + 1) * 100000));
//                        mnPojo.setNotificationCode(notificationCode);
//                        mnPojo.setNotificationUser(p);
//                        hnslMessageNotificationService.save(mnPojo);
                    }
                } else {
                    hnslTaskReceive.setTaskStatusRel(1);
                    hnslTaskReceive.setTaskCode(hnslTask.getTaskCode());
                    hnslTaskReceive.setUserPhone(phones);
                    hnslTaskReceive.setTaskPublish(hnslTask.getTaskCode());
                    hnslTaskReceiveService.save(hnslTaskReceive);
//                    String notificationCode = "HNSLMN" + sdf.format(new Date().getTime())
//                            + String.valueOf((int) ((Math.random() * 6 + 1) * 100000));
//                    mnPojo.setNotificationCode(notificationCode);
//                    mnPojo.setNotificationUser(phones);
//                    hnslMessageNotificationService.save(mnPojo);
                }
            }
        }
        String taskScope = hnslTask.getTaskScopeCode();
        if (taskScope != null) {
            hnslTaskScopeService.saveList(hnslTask);
        }
        hnslTaskService.noteAllSend(phones);
        return success("添加成功");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:update')")
    @OperationLog
    @ApiOperation("修改任务信息表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTask hnslTask) {
        User loginUser = getLoginUser();
        hnslTask.setUpdatedDate(new Date());
        hnslTask.setUpdatedUser(loginUser.getUsername());
        if (hnslTaskService.updateById(hnslTask)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:remove')")
    @OperationLog
    @ApiOperation("删除任务信息表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTaskService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:save')")
    @OperationLog
    @ApiOperation("批量添加任务信息表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTask> list) {
        if (hnslTaskService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:update')")
    @OperationLog
    @ApiOperation("批量修改任务信息表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTask> batchParam) {
        if (batchParam.update(hnslTaskService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:remove')")
    @OperationLog
    @ApiOperation("批量删除任务信息表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTaskService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:list')")
    @OperationLog
    @ApiOperation("导出任务信息表")
    @PostMapping("/exportTask")
    public ApiResult<?> exportTask(@RequestParam(value = "taskCode") String taskCode, HttpServletRequest request, HttpServletResponse response) {
        logger.info("导出查询条件 任务编码 : " + taskCode);
        if (StringUtil.isEmpty(taskCode)) {
            return fail("导出异常，未获取无任务单号");
        }
        ServletContext servletContext = request.getSession().getServletContext();
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        //设置6列及列宽
        for (int i = 0; i < 6; i++) {
            if(i == 3){
                sheet.setColumnWidth(i, 20 * 300);
                continue;
            }else{
                sheet.setColumnWidth(i, 20 * 200);
            }
        }

        // 在sheet表中添加第一行表头
        HSSFRow row = sheet.createRow((int) 0);
        // 合并第一行单元格
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, 6);
        sheet.addMergedRegion(region);
        // 创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();

        style.setAlignment(HorizontalAlignment.CENTER);

        // 设置字体
        HSSFFont font = wb.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);

        style.setFont(font);

        HSSFCell cell = row.createCell(0);
        cell.setCellValue("任务列表派发人员身份信息");
        cell.setCellStyle(style);

        // 设置第二行数据字段 添加至excel
        Map<String, String> columnMap = new HashMap<>();
        columnMap.put("0", "姓名");
        columnMap.put("1", "合伙人级别");
        columnMap.put("2", "地市");
        columnMap.put("3", "学校名称");
        columnMap.put("4", "学校编码");
        columnMap.put("5", "上传记录");

        HSSFRow row2 = sheet.createRow(1);

        HSSFCellStyle style2 = wb.createCellStyle();

        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        for (int i = 0; i < 6; i++) {
            HSSFCell cell2 = row2.createCell(i);
            cell2.setCellValue(columnMap.get(String.valueOf(i)));
            cell2.setCellStyle(style2);
        }

        // 查询数据 并添加至excel中
        try {
            List<Map<String, String>> resultList = hnslTaskService.exportTask(taskCode);
            // 将数据写入excel
            if (null != resultList && resultList.size() != 0) {
                // 遍历结果集
                for (int i = 0; i < resultList.size(); i++) {
                    Map<String, String> resultMap = resultList.get(i);
                    // 填写数据从第二行开始
                    row = sheet.createRow((int) i + 2);
                    // 姓名
                    row.createCell(0).setCellValue(resultMap.get("USER_NAME"));
                    // 合伙人级别
                    row.createCell(1).setCellValue(String.valueOf(resultMap.get("STATUS_SF")));
                    // 地市
                    row.createCell(2).setCellValue(resultMap.get("CITY_CODE"));
                    // 学校名称
                    row.createCell(3).setCellValue(resultMap.get("SCHOOL_NAME"));
                    // 学校编码
                    row.createCell(4).setCellValue(resultMap.get("SCHOOL_CODE"));
                    // 上传记录
                    if (!StringUtil.isEmpty(resultMap.get("UPLOAD_STATUS"))) {
                        row.createCell(5).setCellValue("已上传");
                    } else {
                        row.createCell(5).setCellValue("未上传");
                    }
                }
            }

            String filepath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
            logger.info("file文件" + filepath);
            String fileName="任务派发接收人信息列表";
            File file = new File(filepath + fileName + ".xls");

            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) {//判断是否是文
                    Map<String, Object> map = new HashedMap();
                    map.put("code", "6");
                    map.put("fileName", fileName);
                    map.put("msg", "exportDaoUsers");
                    return success(map);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("hnslTask exportTask exception error : " + e.getMessage());
            return fail();
        }

        return fail();
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslTask:list')")
    @OperationLog
    @ApiOperation("用户信息以及楼栋信息")
    @PostMapping("/userList")
    public ApiResult<?> userList(@RequestParam HnslUserParam params) {
        logger.info("获取用户列表入参" + params);
        List<String> list = new ArrayList<>();
        if (params.getOnelevel() != null && "true".equals(params.getOnelevel())) {
            list.add("2");
        }
        if (params.getTwolevel() != null && "true".equals(params.getTwolevel())) {
            list.add("3");
        }
        if (params.getThreelevel() != null && "true".equals(params.getThreelevel())) {
            list.add("4");
        }
        if (params.getXlevel() != null && "true".equals(params.getXlevel())) {
            list.add("1");
        }
        params.setLevel(list);
        if (params.getBuildingLong() == null || "false".equals(params.getBuildingLong())) {
            params.setBuildingLong(null);
        }
        if (params.getUserData() != null & !"".equals(params.getUserData())) {
            String name = String.valueOf(params.getUserData());
            if (name.length() == 18) {
                params.setUserSfz( name);
            } else if (name.length() == 11) {
                params.setUserPhone(name);
            } else {
                String regEx = "[\u4e00-\u9fa5]";
                Pattern pat = Pattern.compile(regEx);
                Matcher matcher = pat.matcher(name);
                if (matcher.find()) {
                    params.setUserName(name);
                }
            }
        }
        User loginUser = getLoginUser();
        String UserSf = loginUser.getUsername();
        logger.info("用户等级权限:{}" , UserSf);

        String taskScope = String.valueOf(params.getTaskScope());
        if (taskScope != null && !"".equals(taskScope)) {
            boolean status = taskScope.contains(",");
            List<String> scopeList = new ArrayList<>();
            if (status) {
                String[] scope = taskScope.split(",");
                for (String s : scope) {
                    scopeList.add(s);
                }
            } else {
                scopeList.add(taskScope);
            }
            params.setList(scopeList);
        } else {
            params.setList(null);
        }

        //查询列表数据
        return success(hnslUserService.queryListUserRel(params));
    }
}
