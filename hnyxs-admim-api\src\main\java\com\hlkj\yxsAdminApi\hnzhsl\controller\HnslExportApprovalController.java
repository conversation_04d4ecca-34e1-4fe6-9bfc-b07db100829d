package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.constant.SysLogConstant;
import com.hlkj.yxsAdminApi.common.core.utils.AwsS3Utils;
import com.hlkj.yxsAdminApi.common.core.utils.ConstantUtil;
import com.hlkj.yxsAdminApi.common.core.utils.DesensitizationUtil;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.utils.QueryUserManagerUtil;
import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslExportApprovalEntity;
import com.hlkj.yxsAdminApi.hnzhsl.entity.hnslApproverSettingEntity;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslExportApprovalParam;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslApproverSettingService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslExportApprovalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api(tags = "导出审批管理")
@RestController
@RequestMapping("/api/hnzhsl/exportapproval")
public class HnslExportApprovalController extends BaseController {

    Logger logger = LogManager.getLogger(HnslExportApprovalController.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private HnslExportApprovalService hnslExportApprovalService;

    @Autowired
    private UserService userService;

    @Autowired
    private AmazonS3 amazonS3;

    @Autowired
    private HnslApproverSettingService hnslApproverSettingService;


    /**
     * 列表
     */
    @PreAuthorize("hasAuthority('hnzhsl:exportapproval:list')")
    @OperationLog(value = "查询审批列表", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "智慧扫楼/审批管理")
    @PostMapping("/page")
    @ApiOperation("分页查询审批列表")
    public ApiResult<PageResult<HnslExportApprovalEntity>> page(@RequestBody HnslExportApprovalParam param) {
        User user = DesensitizationUtil.getUser();
        //String userSf = (String) redisUtil.get("thisUserSf" + user.getPhone());

        logger.info("审批列表入参" + param);
        param.setType(2);

        try {
            // 设置查询参数
            if (user.getUserId() != null) {
                param.setUserId(Long.valueOf(user.getUserId()));
            }

            if (user.getPhone() != null) {
                param.setMobile(user.getPhone());
            }

            PageResult<HnslExportApprovalEntity> pageResult = hnslExportApprovalService.pageRel(param);
            List<HnslExportApprovalEntity> approvalList = pageResult.getList();

            // 审批通过时间大于72小时则删除文件
            if (approvalList != null && !approvalList.isEmpty()) {
                for (HnslExportApprovalEntity item : approvalList) {
                    if (user.getUserId() != null && Long.valueOf(user.getUserId()).equals(item.getSubmitUserId())) {
                        item.setShowDownload(1);
                    }
                    if (user.getPhone() != null && user.getPhone().equals(item.getApproveUserPhone())) {
                        item.setShowApproval(1);
                    }

                    if (item.getApproveDate() != null) {
                        long applyTimeMillis = item.getApproveDate().getTime();
                        long currentTimeMillis = System.currentTimeMillis();
                        long diff = currentTimeMillis - applyTimeMillis;

                        // 72小时的毫秒数
                        long seventyTwoHoursMillis = 72 * 60 * 60 * 1000;
                        // 超过72小时删除文件
                        if (diff >= seventyTwoHoursMillis) {
                            // 已删除的文件不再进入删除，避免找不到文件导致错误
                            if (item.getFilterConditions() != null && !"1".equals(item.getFilterConditions())) {
                                amazonS3.deleteObject(ConstantUtil.BUCKET_KEY, item.getFilePath());
                                item.setFilterConditions("1");
                                hnslExportApprovalService.updateById(item);
                                logger.info(item.getFileName() + "文件到期已被删除");
                            }
                        }
                    }
                }
            }

            return ApiResult.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询审批记录异常:" + e.getMessage());
            return ApiResult.error("查询审批记录异常:" + e.getMessage());
        }
    }

    /**
     * 审批通过
     */
    @PreAuthorize("hasAuthority('hnzhsl:exportapproval:approve')")
    @OperationLog(value = "审批通过", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "智慧扫楼/审批管理")
    @PostMapping("/approved")
    @ApiOperation("审批通过或拒绝")
    public ApiResult<?> approved(@RequestBody HnslExportApprovalEntity exportApproval, HttpServletResponse response) {
        User user = DesensitizationUtil.getUser();

        List<hnslApproverSettingEntity> list = hnslApproverSettingService.list();
        if (list == null || list.isEmpty()) {
            return ApiResult.error("您没有审批的权限");
        }
        boolean hasPermission = list.stream()
                .anyMatch(entity -> entity.getUserPhone().equals(user.getPhone()));
        if (!hasPermission) {
            return ApiResult.error("您没有审批的权限");
        }

        HnslExportApprovalEntity entity = hnslExportApprovalService.getById(exportApproval.getId());
        if (entity == null) {
            return ApiResult.error("当前审批记录不存在异常");
        }
        entity.setStatus(exportApproval.getStatus());
        entity.setApproveDate(new Date());
        if (exportApproval.getStatus() == 2) {
            entity.setRemark(exportApproval.getRemark());
        }
        entity.setDataSourceStatus(2);
        if (hnslExportApprovalService.updateById(entity)) {
            User submitUser = userService.getById(entity.getSubmitUserId());
            if (submitUser != null) {
                Date submitDate = entity.getSubmitDate();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                String formattedDate = sdf.format(submitDate);
                JSONObject param = new JSONObject();
                param.put("phone_num", submitUser.getPhone()); //必填，号码
                param.put("source", "智慧扫楼");// 必填，电渠提供
                param.put("sms_id", "sms_00313"); // 必填，审核通过的模板ID
                param.put("srctermid", "");// 接入码，可选
                JSONObject params = new JSONObject();
                params.put("param1", entity.getSubmitUser());
                params.put("param2", formattedDate);
                params.put("param3", entity.getFileName());
                param.put("params", params);
                String code = InterfaceUtil.dxtz3wnew(param);
                if ("0".equals(code)) {
                    return ApiResult.success("短信发送成功");
                } else {
                    return ApiResult.error("短信发送失败");
                }
            }
        }
        return ApiResult.success("操作成功");
    }

    /**
     * 下载
     */
    @PreAuthorize("hasAuthority('hnzhsl:exportapproval:download')")
    @OperationLog(value = "审批下载", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "智慧扫楼/审批管理")
    @GetMapping("/download/{id}")
    @ApiOperation("下载审批文件")
    public void download(@PathVariable("id") Long id, HttpServletResponse response) {
        S3ObjectInputStream inputStream = null;
        try {
            HnslExportApprovalEntity entity = hnslExportApprovalService.getById(id);
            if (entity == null) {
                throw new Exception("当前下载记录异常");
            }
            String fileName = entity.getFileName();
            String filePath = entity.getFilePath();
            S3Object s3Object = amazonS3.getObject(ConstantUtil.BUCKET_KEY, filePath);
            inputStream = s3Object.getObjectContent();

            response.reset();
            //设置输出文件格式
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
            ServletOutputStream outputStream = response.getOutputStream();

            byte[] buff = new byte[1024];
            int length;
            while ((length = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, length);
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }

            // 下载后删除文件
            amazonS3.deleteObject(ConstantUtil.BUCKET_NAME, filePath);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
