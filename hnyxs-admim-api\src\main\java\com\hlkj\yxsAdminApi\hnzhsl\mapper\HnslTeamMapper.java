package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeam;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合伙人团队表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslTeamMapper extends BaseMapper<HnslTeam> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslTeam>
     */
    List<HnslTeam> selectPageRel(@Param("page") IPage<HnslTeam> page,
                             @Param("param") HnslTeamParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslTeam> selectListRel(@Param("param") HnslTeamParam param);

}
