package com.hlkj.yxsAdminApi.common.core.utils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class IPUtils {
	/**
	 * 获取用户的ip
	 * @param request
	 * @return
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}

	public static String getUserAgent(String userAgent) {
		String os = null;
		// 简单解析常见浏览器和操作系统
		if (userAgent.contains("Windows")) {
			os = "Windows";
		} else if (userAgent.contains("Mac OS")) {
			os = "Mac OS";
		} else if (userAgent.contains("Linux")) {
			os = "Linux";
		} else if (userAgent.contains("Android")) {
			os = "Android";
		} else if (userAgent.contains("iOS") || userAgent.contains("iPhone") || userAgent.contains("iPad")) {
			os = "iOS";
		}
		return os;
	}

	public static String getDeviceType(String userAgent) {
		String deviceType = null;
		// 判断设备类型
		if (userAgent.contains("Mobile")) {
			deviceType = "Mobile";
		} else if (userAgent.contains("Tablet") || userAgent.contains("iPad")) {
			deviceType = "Tablet";
		} else {
			deviceType = "PC";
		}
		return deviceType;
	}

}
