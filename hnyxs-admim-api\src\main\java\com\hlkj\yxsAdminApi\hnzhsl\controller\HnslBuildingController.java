package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolUser;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslBuildingService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBuilding;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslBuildingParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 楼栋表控制器
 *
 * <AUTHOR>
 * @since 2023-06-19 10:07:51
 */
@Api(tags = "楼栋表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslBuilding")
public class HnslBuildingController extends BaseController {
    @Autowired
    private HnslBuildingService hnslBuildingService;

    @Autowired
    private HnslSchoolUserService hnslSchoolUserService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:list')")
    @OperationLog
    @ApiOperation("分页查询楼栋表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslBuilding>> page(@RequestBody HnslBuildingParam param) {
        PageParam<HnslBuilding, HnslBuildingParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslBuildingService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslBuildingService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:list')")
    @OperationLog
    @ApiOperation("查询全部楼栋表")
    @PostMapping("/list")
    public ApiResult<List<HnslBuilding>> list(@RequestBody HnslBuildingParam param) {
        PageParam<HnslBuilding, HnslBuildingParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslBuildingService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslBuildingService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:list')")
    @OperationLog
    @ApiOperation("根据id查询楼栋表")
    @GetMapping("/{id}")
    public ApiResult<HnslBuilding> get(@PathVariable("id") Integer id) {
        return success(hnslBuildingService.getById(id));
        // 使用关联查询
        //return success(hnslBuildingService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:save')")
    @OperationLog
    @ApiOperation("添加楼栋表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslBuilding hnslBuilding) {
        User loginUser = getLoginUser();
        SimpleDateFormat id = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat data = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String buildingId = hnslBuilding.getSchoolId() + ((int) (1 + Math.random() * (10000)));
        hnslBuilding.setBuildingId(buildingId);
        hnslBuilding.setBuildingRegisterExisting(0);
        hnslBuilding.setNetworkExisting(0);
        hnslBuilding.setCreatedDate(new Date());
        hnslBuilding.setCreatedUser(loginUser.getUsername());
        hnslBuilding.setNetworkPhoneExisting(0);
        hnslBuilding.setStatus(1);
        if (hnslBuildingService.save(hnslBuilding)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:update')")
    @OperationLog
    @ApiOperation("修改楼栋表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslBuilding hnslBuilding) {
        User loginUser = getLoginUser();
        hnslBuilding.setUpdatedDate(new Date());
        hnslBuilding.setUpdatedUser(loginUser.getUsername());
        if (hnslBuildingService.updateById(hnslBuilding)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:remove')")
    @OperationLog
    @ApiOperation("删除楼栋表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslBuildingService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:save')")
    @OperationLog
    @ApiOperation("批量添加楼栋表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslBuilding> list) {
        if (hnslBuildingService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:update')")
    @OperationLog
    @ApiOperation("批量修改楼栋表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslBuilding> batchParam) {
        if (batchParam.update(hnslBuildingService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:remove')")
    @OperationLog
    @ApiOperation("批量删除楼栋表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslBuildingService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslBuilding:update')")
    @OperationLog
    @ApiOperation("修改楼栋下的年级")
    @PostMapping("/updateGrade")
    public ApiResult<?> updateGrade(@RequestBody Map<String, Object> params) {
        try{
            User loginUser = getLoginUser();
            Number num = Float.parseFloat(String.valueOf(params.get("id")));
            int oamount = num.intValue();
            long id = Long.valueOf(oamount);
            logger.info("获取楼栋信息入参：{}" , id);
            HnslBuilding hnslBuilding = hnslBuildingService.getById(id);
            HnslSchoolUser hnslSchoolUserEntity = new HnslSchoolUser();
            hnslSchoolUserEntity.setUpdatedDate(new Date());
            hnslSchoolUserEntity.setUpdatedUser(loginUser.getUsername());
            hnslSchoolUserEntity.setBuildingId(hnslBuilding.getBuildingId());
            hnslSchoolUserEntity.setEnroltime(String.valueOf(params.get("dates"))+"年");
//            UpdateWrapper<HnslSchoolUser> hnslSchoolUserUpdateWrapper = new UpdateWrapper<>();
//            hnslSchoolUserUpdateWrapper.set("ENROLTIME",)
//                    .eq("BUILDING_ID",hnslBuilding.getBuildingId());
//            hnslSchoolUserService.update(hnslSchoolUserUpdateWrapper);
        }catch (Exception e){
            logger.error("修改年级异常报错:"+e);
            return fail("系统修改异常，请联系管理员！");
        }
        return success("修改成功");
    }
}
