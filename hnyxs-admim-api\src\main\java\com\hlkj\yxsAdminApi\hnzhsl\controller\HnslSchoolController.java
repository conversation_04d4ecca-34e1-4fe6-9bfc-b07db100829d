package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslBuilding;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslBuildingService;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchool;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSchoolParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学校表控制器
 *
 * <AUTHOR>
 * @since 2023-05-04 14:46:39
 */
@Api(tags = "学校表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslSchool")
public class HnslSchoolController extends BaseController {
    @Resource
    private HnslSchoolService hnslSchoolService;

    @Resource
    private HnslBuildingService hnslBuildingService;
    @Resource
    private HnslUserService hnslUserService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:list')")
    @OperationLog
    @ApiOperation("分页查询学校表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSchool>> page(@RequestBody HnslSchoolParam param) {
        PageParam<HnslSchool, HnslSchoolParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        PageParam<HnslSchool, HnslSchoolParam> page1 = hnslSchoolService.page(page, page.getWrapper());
        List<HnslSchool> hnslSchoolList = page1.getRecords();
        Map<String, Object> map = new HashMap<>();
        for (HnslSchool hnslSchoolEntity : hnslSchoolList) {
            map.put("schoolCode",hnslSchoolEntity.getSchoolCode());
            List<HnslUser> queryManagerList = hnslUserService.queryManager(map);
            logger.info("校园经理出参"+queryManagerList);
            String manager = "";
            for (int i = 0; i < queryManagerList.size(); i++) {
                if(i==0){
                    manager=queryManagerList.get(i).getUserName();
                }else {
                    manager=manager+","+queryManagerList.get(i).getUserName();
                }
            }
            hnslSchoolEntity.setManager(manager);
        }
        return success(page1);
        // 使用关联查询
        //return success(hnslSchoolService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:list')")
    @OperationLog
    @ApiOperation("查询全部学校表")
    @PostMapping("/list")
    public ApiResult<List<HnslSchool>> list(@RequestBody HnslSchoolParam param) {
        PageParam<HnslSchool, HnslSchoolParam> page = new PageParam<>(param);
        List<HnslSchool> list = hnslSchoolService.list(page.getOrderWrapper());

        //查询学校相关楼栋
//        List<String> collect = list.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());
//        LambdaQueryWrapper<HnslBuilding> hnslBuildingLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        hnslBuildingLambdaQueryWrapper.in(HnslBuilding::getSchoolId,collect);
//        List<HnslBuilding> list1 = hnslBuildingService.list(hnslBuildingLambdaQueryWrapper);
//        list.stream().forEach(w -> {
//            List<HnslBuilding> collect1 = list1.stream().filter(b -> Objects.equals(w.getSchoolCode(), b.getSchoolId())).collect(Collectors.toList());
//            w.setHnslBuilding(collect1);
//        });
        //page.setDefaultOrder("create_time desc");
        return success(list);
        // 使用关联查询
        //return success(hnslSchoolService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:list')")
    @OperationLog
    @ApiOperation("根据id查询学校表")
    @GetMapping("/info/{id}")
    public ApiResult<JSONObject> get(@PathVariable("id") Integer id) {
        JSONObject jsonObject = new JSONObject();
        HnslSchool schoolServiceById = hnslSchoolService.getById(id);
        jsonObject.put("hnslSchool",schoolServiceById);
        //获取学校对应的校园经理
        HashMap<String,Object> hashMap = new HashMap<>();
        hashMap.put("schoolCode", schoolServiceById.getSchoolCode());
        List<HnslUser> managerList = hnslUserService.queryManager(hashMap);
        jsonObject.put("managerList",managerList);
        return success(jsonObject);
        // 使用关联查询
        //return success(hnslSchoolService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:save')")
    @OperationLog
    @ApiOperation("添加学校表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSchool hnslSchool) {

        LambdaQueryWrapper<HnslSchool> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HnslSchool::getSchoolCode,hnslSchool.getSchoolCode());
        int count = hnslSchoolService.count(queryWrapper);
        if(count>0){
            return fail("学校编码已存在");
        }
        hnslSchool.setStatus(1);
        hnslSchool.setSchoolType(1);
        if (hnslSchoolService.save(hnslSchool)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:update')")
    @OperationLog
    @ApiOperation("修改学校表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSchool hnslSchool) {
        if (hnslSchoolService.updateById(hnslSchool)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:remove')")
    @OperationLog
    @ApiOperation("删除学校表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSchoolService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:save')")
    @OperationLog
    @ApiOperation("批量添加学校表")
    @PostMapping("/batchSave")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSchool> list) {
        if (hnslSchoolService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:update')")
    @OperationLog
    @ApiOperation("批量修改学校表")
    @PostMapping("/batchUpdate")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<HnslSchool> batchParam) {
        if (batchParam.update(hnslSchoolService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:remove')")
    @OperationLog
    @ApiOperation("批量删除学校表")
    @PostMapping("/batchRemove")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSchoolService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslSchool:list')")
    @OperationLog
    @ApiOperation("查询学校信息及下楼栋数据")
    @PostMapping("/querySchoolDownBuildingList")
    public ApiResult<List<HnslSchool>> querySchoolDownBuildingList(@RequestBody Map<String,Object> param) {
        return success(hnslSchoolService.querySchoolDownBuildingList(param));
    }
}
