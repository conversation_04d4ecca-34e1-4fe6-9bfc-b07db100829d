package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 楼栋表
 *
 * <AUTHOR>
 * @since 2023-06-19 10:07:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslBuilding对象", description = "楼栋表")
public class HnslBuilding implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "楼栋名称")
    @TableField("BUILDING_NAME")
    private String buildingName;

    @ApiModelProperty(value = "层数")
    @TableField("BUILDING_TIER")
    private Integer buildingTier;

    @ApiModelProperty(value = "现有登记人数")
    @TableField("BUILDING_REGISTER_EXISTING")
    private Integer buildingRegisterExisting;

    @ApiModelProperty(value = "本网手机用户现有人数")
    @TableField("NETWORK_PHONE_EXISTING")
    private Integer networkPhoneExisting;

    @ApiModelProperty(value = "本网宽带用户现有人数")
    @TableField("NETWORK_EXISTING")
    private Integer networkExisting;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_ID")
    private String schoolId;

    @ApiModelProperty(value = "管理人手机号")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty(value = "每层房间数")
    @TableField("BUILDING_ROOM")
    private Integer buildingRoom;

    @ApiModelProperty(value = "每间房人数")
    @TableField("BUILDING_NUMBER")
    private Integer buildingNumber;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "楼栋编码")
    @TableField("BUILDING_ID")
    private String buildingId;

    @ApiModelProperty(value = "登记总人数")
    @TableField("SCHOOL_REGISTER")
    private Integer schoolRegister;

    @ApiModelProperty(value = "QQ群号")
    @TableField("QQFORM")
    private String qqform;

    @ApiModelProperty(value = "楼栋图片")
    @TableField("BUILDING_IMAGE")
    private String buildingImage;

}
