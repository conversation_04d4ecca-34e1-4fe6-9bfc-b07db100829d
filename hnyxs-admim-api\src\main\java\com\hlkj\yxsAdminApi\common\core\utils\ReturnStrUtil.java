package com.hlkj.yxsAdminApi.common.core.utils;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;

import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * 返回数据封装工具类
 * @ClassName ReturnStrUtil
 * @Description TODO
 * @<NAME_EMAIL>
 * @Date 2023/4/18 15:10
 * @Version 1.0
 */
public class ReturnStrUtil {
    private static final Logger logger = LoggerFactory.getLogger(ReturnStrUtil.class);

    /**
     *
     * @param code
     *            返回码，0，表示成功，1，表示异常
     * @param message
     *            返回说明，base64编码
     * @return
     */
    public static Map<String, Object> jsonObjectStr(String code, String message, String data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        if (message != null && message.trim().length() > 0) {
            BASE64Encoder base64Encoder = new BASE64Encoder();
            try {
                String base64Msg = base64Encoder.encode(message.getBytes("utf-8"));
                jsonObject.put("base64Msg", base64Msg);
                jsonObject.put("message", message);
                jsonObject.put("data", data);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        } else {
            jsonObject.put("message", null);
        }
        return jsonObject;
    }

    /**
     *
     * @param code
     *            返回码，0，表示成功，1，表示异常
     * @param message
     *            返回说明，base64编码
     * @return 字符串
     */
    public static String jsonStrBase64(String code, String message, String data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        if (message != null && message.trim().length() > 0) {
            BASE64Encoder base64Encoder = new BASE64Encoder();
            try {
                String base64Msg = base64Encoder.encode(message.getBytes("utf-8"));
                jsonObject.put("base64Msg", base64Msg);
                jsonObject.put("message", message);
                jsonObject.put("data", data);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        } else {
            jsonObject.put("message", null);
        }
        return jsonObject.toJSONString();
    }

    /**
     *
     * @param code
     *            返回码，0，表示成功，1，表示异常
     * @param message
     *            返回说明，base64编码
     * @return
     */
    public static Map<String, Object> jsonStrNoBase(String code, String message, Object data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        jsonObject.put("message", message);
        jsonObject.put("data", data);
        return jsonObject;
    }

    /**
     *
     * @param code
     *            返回码，0，表示成功，1，表示异常
     * @param message
     *            返回说明，base64编码
     * @return
     */
    public static JSONObject jsonObjStrNoBase(String code, String message, Object data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        jsonObject.put("message", message);
        jsonObject.put("data", data);
        return jsonObject;
    }

    /**
     * 返回字符串
     *
     * @param code
     *            返回码，0，表示成功，1，表示异常
     * @param message
     *            返回说明，base64编码
     * @return
     */
    public static String jsonStrNoBases(String code, String message, Object data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        jsonObject.put("message", message);
        jsonObject.put("data", data);
        return jsonObject.toJSONString();
    }
}