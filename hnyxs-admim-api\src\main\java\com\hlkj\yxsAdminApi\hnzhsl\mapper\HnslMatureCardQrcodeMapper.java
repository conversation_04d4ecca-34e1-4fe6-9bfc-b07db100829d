package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslMatureCardQrcode;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslMatureCardQrcodeParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface HnslMatureCardQrcodeMapper extends BaseMapper<HnslMatureCardQrcode> {

    /**
     * 分页查询
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslMatureCardQrcode>
     */
    List<HnslMatureCardQrcode> selectPageRel(@Param("page") IPage<HnslMatureCardQrcode> page,
                                 @Param("param") HnslMatureCardQrcodeParam param);

    /**
     * 导出熟卡二维码
     * @param param
     * @return
     */
    List<Map<String, Object>> queryMatureCardList(@Param("param") HnslMatureCardQrcode param);
}
