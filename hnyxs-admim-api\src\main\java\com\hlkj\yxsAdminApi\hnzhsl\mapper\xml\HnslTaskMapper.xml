<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslTaskMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,t2.task_count
        FROM hnsl_task a left join ( select t.task_code,GROUP_CONCAT(t1.USER_NAME) as task_count
        from hnsl_task_receive t  left join (select t.* from hnsl_user t where t.status=1) t1 on t.user_phone=t1.user_phone   group by t.task_code) t2 on a.task_code=t2.task_code

        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.taskType != null">
                AND a.TASK_TYPE LIKE CONCAT('%', #{param.taskType}, '%')
            </if>
            <if test="param.starttime != null">
                AND a.STARTTIME LIKE CONCAT('%', #{param.starttime}, '%')
            </if>
            <if test="param.endtime != null">
                AND a.ENDTIME LIKE CONCAT('%', #{param.endtime}, '%')
            </if>
            <if test="param.taskScope != null">
                AND a.TASK_SCOPE LIKE CONCAT('%', #{param.taskScope}, '%')
            </if>
            <if test="param.taskContent != null">
                AND a.TASK_CONTENT LIKE CONCAT('%', #{param.taskContent}, '%')
            </if>
            <if test="param.taskAward != null">
                AND a.TASK_AWARD = #{param.taskAward}
            </if>
            <if test="param.taskCount != null">
                AND a.TASK_COUNT LIKE CONCAT('%', #{param.taskCount}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.taskKind != null">
                AND a.TASK_KIND LIKE CONCAT('%', #{param.taskKind}, '%')
            </if>
            <if test="param.taskCode != null">
                AND a.TASK_CODE LIKE CONCAT('%', #{param.taskCode}, '%')
            </if>
            <if test="param.taskStatus != null">
                AND a.TASK_STATUS LIKE CONCAT('%', #{param.taskStatus}, '%')
            </if>
            <if test="param.taskCountPhone != null">
                AND a.TASK_COUNT_PHONE LIKE CONCAT('%', #{param.taskCountPhone}, '%')
            </if>
            <if test="param.taskAmount != null">
                AND a.TASK_AMOUNT LIKE CONCAT('%', #{param.taskAmount}, '%')
            </if>
            <if test="param.taskTitle != null">
                AND a.TASK_TITLE LIKE CONCAT('%', #{param.taskTitle}, '%')
            </if>
            <if test="param.taskLevel != null">
                AND a.TASK_LEVEL = #{param.taskLevel}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTask">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTask">
        <include refid="selectSql"></include>
    </select>

</mapper>
