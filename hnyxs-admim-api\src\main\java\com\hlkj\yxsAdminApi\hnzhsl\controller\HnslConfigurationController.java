package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslConfigurationService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslConfiguration;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslConfigurationParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 扫楼配置项表控制器
 *
 * <AUTHOR>
 * @since 2025-05-06 09:36:18
 */
@Api(tags = "扫楼配置项表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslConfiguration")
public class HnslConfigurationController extends BaseController {
    @Autowired
    private HnslConfigurationService hnslConfigurationService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslConfiguration:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼配置项表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslConfiguration>> page(HnslConfigurationParam param) {
        PageParam<HnslConfiguration, HnslConfigurationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslConfigurationService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslConfigurationService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslConfiguration:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼配置项表")
    @PostMapping("/list")
    public ApiResult<List<HnslConfiguration>> list(HnslConfigurationParam param) {
        PageParam<HnslConfiguration, HnslConfigurationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslConfigurationService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslConfigurationService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslConfiguration:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼配置项表")
    @PostMapping("/get/{id}")
    public ApiResult<HnslConfiguration> get(@PathVariable("id") Integer id) {
        return success(hnslConfigurationService.getById(id));
        // 使用关联查询
        //return success(hnslConfigurationService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslConfiguration:save')")
    @OperationLog
    @ApiOperation("添加扫楼配置项表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslConfiguration hnslConfiguration) {
        if (hnslConfigurationService.save(hnslConfiguration)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslConfiguration:update')")
    @OperationLog
    @ApiOperation("修改扫楼配置项表")
    @PostMapping("/avoid/update")
    public ApiResult<?> update(@RequestBody HnslConfiguration hnslConfiguration) {
        if (hnslConfigurationService.updateById(hnslConfiguration)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslConfiguration:remove')")
    @OperationLog
    @ApiOperation("删除扫楼配置项表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslConfigurationService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }
}
