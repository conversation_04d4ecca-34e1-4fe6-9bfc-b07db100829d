package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsLimitService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsLimit;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsLimitParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品限制对应表控制器
 *
 * <AUTHOR>
 * @since 2023-07-31 16:59:50
 */
@Api(tags = "商品限制对应表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-goods-limit")
public class HnslGoodsLimitController extends BaseController {
    @Autowired
    private HnslGoodsLimitService hnslGoodsLimitService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:list')")
    @OperationLog
    @ApiOperation("分页查询商品限制对应表")
    @GetMapping("/page")
    public ApiResult<PageResult<HnslGoodsLimit>> page(HnslGoodsLimitParam param) {
        PageParam<HnslGoodsLimit, HnslGoodsLimitParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsLimitService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsLimitService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:list')")
    @OperationLog
    @ApiOperation("查询全部商品限制对应表")
    @GetMapping()
    public ApiResult<List<HnslGoodsLimit>> list(HnslGoodsLimitParam param) {
        PageParam<HnslGoodsLimit, HnslGoodsLimitParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsLimitService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsLimitService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:list')")
    @OperationLog
    @ApiOperation("根据id查询商品限制对应表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsLimit> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsLimitService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsLimitService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:save')")
    @OperationLog
    @ApiOperation("添加商品限制对应表")
    @PostMapping()
    public ApiResult<?> save(@RequestBody HnslGoodsLimit hnslGoodsLimit) {
        if (hnslGoodsLimitService.save(hnslGoodsLimit)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:update')")
    @OperationLog
    @ApiOperation("修改商品限制对应表")
    @PutMapping()
    public ApiResult<?> update(@RequestBody HnslGoodsLimit hnslGoodsLimit) {
        if (hnslGoodsLimitService.updateById(hnslGoodsLimit)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:remove')")
    @OperationLog
    @ApiOperation("删除商品限制对应表")
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsLimitService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:save')")
    @OperationLog
    @ApiOperation("批量添加商品限制对应表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsLimit> list) {
        if (hnslGoodsLimitService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:update')")
    @OperationLog
    @ApiOperation("批量修改商品限制对应表")
    @PutMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<HnslGoodsLimit> batchParam) {
        if (batchParam.update(hnslGoodsLimitService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsLimit:remove')")
    @OperationLog
    @ApiOperation("批量删除商品限制对应表")
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsLimitService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
