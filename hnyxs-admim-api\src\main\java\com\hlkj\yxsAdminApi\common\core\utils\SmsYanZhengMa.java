package com.hlkj.yxsAdminApi.common.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hlkj.yxsAdminApi.common.core.config.ConfigProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Random;

/**
 * 验证信息
 * <AUTHOR>
 *
 */
@Component
public class SmsYanZhengMa {
	private static Logger log = LoggerFactory.getLogger(SmsYanZhengMa.class);

	
	@Autowired
    RedisUtil redis;
	@Autowired
	private ConfigProperties staticFinalUtil;

	public static final String user = "fengchuhui"; // 调用方账号（到时分配）

    // 能力平台地址
	public static final String url = "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/rest";// 内网http://**************:8081/api/rest
	public static final String INTERFACE_URL = "http://**************:8088/hnxtApiServe/bss/toPost.do";//范小白接口地址
	// 查询增值业务能力平台分配token以及bs3.0割接接口
    public static final String ACCESS_TOKEN = "MzMyN2YxNmExMjdjMjE0NTU2MWI0MDk5MDczZTE0ZjM=";
	
	/**
	 * 发送短信
	 * <AUTHOR>
	 * @date 2020年1月17日
	 * @param moblie
	 * @param ipAddres
	 * @return
	 * @version V1.0
	 */
	public String getSms(String moblie,String ipAddres) {
		try {
			//限制发送
			String one_minute_limit_key = staticFinalUtil.getOne_minute_limit() + moblie.trim();
			long one_minute_limit_nums = redis.incr(one_minute_limit_key, 1);
			if(1 == one_minute_limit_nums) {
				//设置多少秒
				redis.expire(one_minute_limit_key, staticFinalUtil.getOne_minute_limit_time());
				//ip限制
				String ipArress_one_day_limit_key = staticFinalUtil.getIpArress_one_day_limit() + ipAddres;
				long ipArress_one_day_limit_nums = redis.incr(ipArress_one_day_limit_key, 1);
				log.info("手机号码:{},ip地址:{},ip发送次数:{}",moblie,ipAddres,ipArress_one_day_limit_nums);
				if(ipArress_one_day_limit_nums <= staticFinalUtil.getDuanxin_xiangzhi_nums()) {
					//限制IP一天次数
					redis.expire(ipArress_one_day_limit_key, Integer.parseInt(String.valueOf(DateUtils.curTimes())));
					//限制手机号码一天的次数
					String moblie_one_day_limit_key = staticFinalUtil.getMoblie_one_day_limit() + moblie.trim();
					long moblie_one_day_limit_nums = redis.incr(moblie_one_day_limit_key, 1);
					log.info("手机号码:{},ip地址:{},手机号码发送次数:{}",moblie,ipAddres,moblie_one_day_limit_nums);
					if(moblie_one_day_limit_nums <= staticFinalUtil.getDuanxin_xiangzhi_data_nums()) {
						//限制手机号码一天能发送的次数
						redis.expire(moblie_one_day_limit_key, Integer.parseInt(String.valueOf(DateUtils.curTimes())));
						//最终的短信验证码编码
						String zuizhong_duanxin_nums_key = staticFinalUtil.getZuizhong_duanxin_nums() + moblie.trim();
						//验证码信息
						String code = "";
						// 判断最终缓存是否存在短信验证码
						if (redis.hasKey(zuizhong_duanxin_nums_key)) {
							if (staticFinalUtil.getIsduanxin()) {
								code = redis.getString(zuizhong_duanxin_nums_key);
							}else{
								redis.decr(ipArress_one_day_limit_key, 1);
								redis.decr(moblie_one_day_limit_key, 1);
								return "请注意查收短信,耐心等待";
							}
						}
						//判断验证码是否为空
						if(StringUtils.isBlank(code)){
							code = getCode(staticFinalUtil.getDuanxin_length());
						}
						log.info("短信验证码发送内容,手机号码:{},发送的验证码:{},发送ip地址:{}",moblie,code,ipAddres);
						String text = "您的翼销售管理平台验证码为：" + code;
						Boolean smsSending = false;
						if("dev".equals(staticFinalUtil.getAcctiond())) {
							smsSending = true;
						}else {
							smsSending = sendMsg(moblie,text);
						}
						if(smsSending){//判断短信接口是否成功
							boolean set = redis.set(zuizhong_duanxin_nums_key, code, staticFinalUtil.getDuanxin_time());
							log.info("{}:短信发送成功后保存的结果:{}",moblie,set);
							if(!set){
								redis.decr(ipArress_one_day_limit_key, 1);
								redis.decr(moblie_one_day_limit_key, 1);
							}
							return "短信已成功发送,请注意查收短信!";
						}else{
							redis.decr(ipArress_one_day_limit_key, 1);
							redis.decr(moblie_one_day_limit_key, 1);
							return "短信接口发送失败,敬请谅解,请稍后重试!";
						}
					}else {
						//虽然这个号码次数用完了但是ip得放行
						redis.decr(ipArress_one_day_limit_key, 1);
						return "今天您得次数已用完";
					}
				}else {
					return "今天的区域限制次数发送用完";
				}
			}else {
				return "发送太频繁,请稍等";
			}
		} catch (Exception e) {
			log.error("手机号:" + moblie + ",ip地址:" + ipAddres + ",短信发送异常", e);
			return "短信异常,敬请谅解";
		}
	}

	/**
	 * 生成指定长度的随机数
	 */
	public String getCode(int length) {
		String codes = "";
		for (int i = 0; i < length; i++) {
			codes = codes + new Random().nextInt(9);
		}
		return codes;
	}
	
	/**
	 * 验证码验证
	 * <AUTHOR>
	 * @date 2020年1月17日
	 * @param moblie 手机号码
	 * @param code 验证码
	 * @return
	 * @version V1.0
	 */
	public String isSms(String moblie,String code){
		try {
			//最终的短信验证码编码
			String zuizhong_duanxin_nums_key = staticFinalUtil.getZuizhong_duanxin_nums() + moblie.trim();
			String duanxin_error_nums_key = staticFinalUtil.getDuanxin_error_nums() + moblie.trim();
			String one_minute_limit_key = staticFinalUtil.getOne_minute_limit() + moblie.trim();
			//判断服务器的缓存中是否有验证码
			if(redis.hasKey(zuizhong_duanxin_nums_key)){
				String yzm = redis.getString(zuizhong_duanxin_nums_key);
				log.info("短信验证的手机号码:{},缓存验证码:{},用户输入:{}",moblie,yzm,code);
				if(StringUtils.isNotBlank(yzm) && code.equals(yzm)){
					redis.del(zuizhong_duanxin_nums_key,one_minute_limit_key,duanxin_error_nums_key);
					return "true";
				}else{
					long error_duanxin = redis.incr(duanxin_error_nums_key, 1);
					if(error_duanxin > staticFinalUtil.getDuanxin_error_xianzhi_nums()){
						redis.del(zuizhong_duanxin_nums_key,one_minute_limit_key,duanxin_error_nums_key);
						return "您输入的次数过多,验证码已失效";
					}else{
						return "您还有" + (staticFinalUtil.getDuanxin_error_xianzhi_nums()-error_duanxin) + "机会进行验证!";
					}
				}
			}else{
				log.info("验证码已过期,信验证的手机号码:{},验证码:{}",moblie,code);
				return "验证码已过期,请重新获取";
			}
		} catch (Exception e) {
			log.info("验证码验证异常,信验证的手机号码:{},验证码:{}",moblie,code);
			return "验证码验证异常";
		}
	}
	
	/**
     * 	异网短信发送
     * <p>Title: sendMsg</p>  
     * <p>Description: </p>  
     * @param mobile
     * @param message
     * @return
     * <AUTHOR>
     * @date 2022年9月3日  
     * @version 1.0
     */
    public static Boolean sendMsg(String mobile, String message){
    	JSONObject body = new JSONObject();
        JSONObject content = new JSONObject();

        body.put("access_token", ACCESS_TOKEN);
        body.put("method", "push.sms.sendsms");
        body.put("version", "1.0");

        content.put("phone_num", mobile);
        content.put("source", "FXB");
        content.put("message", message);//短信告知内容（不超过325个汉字），每70个汉字会拆分成一条短信
        content.put("priority", "0");//0普通，1紧急
        content.put("type", "1");
        body.put("content", content);
        log.info("短信发送请求报文：" + body.toString());
        String rest = null;
        try {
            rest = ConnectionUrlUtil.sendJsonPost(url, body.toJSONString());
            log.info("掌上销工具异网短信发送返回报文：" + rest);
            JSONObject restJson = JSONObject.parseObject(rest);
            if ("00000".equals(restJson.getString("res_code")) && "Success".equals(restJson.getString("res_message"))) {
            	return "0".equals(restJson.getJSONObject("result").getString("RETFLAG"));
            }
        } catch (Exception e) {
        	log.info("短信发送异常", e);
        }
		return false;
    }
}
