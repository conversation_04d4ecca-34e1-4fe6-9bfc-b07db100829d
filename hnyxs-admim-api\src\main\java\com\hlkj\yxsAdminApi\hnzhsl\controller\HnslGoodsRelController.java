package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsRelService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsRel;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsRelParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 扫楼工具商品关联表控制器
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
@Api(tags = "扫楼工具商品关联表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-goods-rel")
public class HnslGoodsRelController extends BaseController {
    @Autowired
    private HnslGoodsRelService hnslGoodsRelService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼工具商品关联表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsRel>> page(@RequestBody HnslGoodsRelParam param) {
        PageParam<HnslGoodsRel, HnslGoodsRelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsRelService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsRelService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼工具商品关联表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoodsRel>> list(@RequestBody HnslGoodsRelParam param) {
        PageParam<HnslGoodsRel, HnslGoodsRelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsRelService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsRelService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼工具商品关联表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsRel> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsRelService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsRelService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:save')")
    @OperationLog
    @ApiOperation("添加扫楼工具商品关联表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslGoodsRel hnslGoodsRel) {
        if (hnslGoodsRelService.save(hnslGoodsRel)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:update')")
    @OperationLog
    @ApiOperation("修改扫楼工具商品关联表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoodsRel hnslGoodsRel) {
        if (hnslGoodsRelService.updateById(hnslGoodsRel)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:remove')")
    @OperationLog
    @ApiOperation("删除扫楼工具商品关联表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsRelService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:save')")
    @OperationLog
    @ApiOperation("批量添加扫楼工具商品关联表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsRel> list) {
        if (hnslGoodsRelService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:update')")
    @OperationLog
    @ApiOperation("批量修改扫楼工具商品关联表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoodsRel> batchParam) {
        if (batchParam.update(hnslGoodsRelService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsRel:remove')")
    @OperationLog
    @ApiOperation("批量删除扫楼工具商品关联表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsRelService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
