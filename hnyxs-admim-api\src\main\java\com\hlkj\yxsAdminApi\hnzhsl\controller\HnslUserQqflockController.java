package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserQqflockService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserQqflock;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserQqflockParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 楼栋服务QQ群控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "楼栋服务QQ群管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-user-qqflock")
public class HnslUserQqflockController extends BaseController {
    @Autowired
    private HnslUserQqflockService hnslUserQqflockService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:list')")
    @OperationLog
    @ApiOperation("分页查询楼栋服务QQ群")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserQqflock>> page(@RequestBody HnslUserQqflockParam param) {
        PageParam<HnslUserQqflock, HnslUserQqflockParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserQqflockService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslUserQqflockService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:list')")
    @OperationLog
    @ApiOperation("查询全部楼栋服务QQ群")
    @PostMapping("/list")
    public ApiResult<List<HnslUserQqflock>> list(@RequestBody HnslUserQqflockParam param) {
        PageParam<HnslUserQqflock, HnslUserQqflockParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserQqflockService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslUserQqflockService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:list')")
    @OperationLog
    @ApiOperation("根据id查询楼栋服务QQ群")
    @GetMapping("/{id}")
    public ApiResult<HnslUserQqflock> get(@PathVariable("id") Integer id) {
        return success(hnslUserQqflockService.getById(id));
        // 使用关联查询
        //return success(hnslUserQqflockService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:save')")
    @OperationLog
    @ApiOperation("添加楼栋服务QQ群")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserQqflock hnslUserQqflock) {
        if (hnslUserQqflockService.save(hnslUserQqflock)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:update')")
    @OperationLog
    @ApiOperation("修改楼栋服务QQ群")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserQqflock hnslUserQqflock) {
        if (hnslUserQqflockService.updateById(hnslUserQqflock)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:remove')")
    @OperationLog
    @ApiOperation("删除楼栋服务QQ群")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserQqflockService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:save')")
    @OperationLog
    @ApiOperation("批量添加楼栋服务QQ群")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserQqflock> list) {
        if (hnslUserQqflockService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:update')")
    @OperationLog
    @ApiOperation("批量修改楼栋服务QQ群")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUserQqflock> batchParam) {
        if (batchParam.update(hnslUserQqflockService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserQqflock:remove')")
    @OperationLog
    @ApiOperation("批量删除楼栋服务QQ群")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserQqflockService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
