package com.hlkj.yxsAdminApi.common.core.config;

import com.amazonaws.services.s3.AmazonS3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class AwzS3Service {

    private final ApplicationContext context;
    private static int state = 0;
    public static int delState = 0;

    @Autowired
    public AwzS3Service(ApplicationContext context) {
        this.context = context;
    }

    public AmazonS3 getRandomBean() {
        Map<String, AmazonS3> beans = context.getBeansOfType(AmazonS3.class);
        if (delState > 0) {
            log.info("存储文件指定>>>>>>>>>" + delState);
            return beans.get("getAmazon" + delState);
        } else {
            state = 0 == state || 3 == state ? 1 : ++state;
            return beans.get("getAmazon" + state);
        }
    }
}