package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 海报模板表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslPosterTemplate对象", description = "海报模板表")
public class HnslPosterTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识 主键 ")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "海报名称 ")
    @TableField("POSTER_NAME")
    private String posterName;

    @ApiModelProperty(value = "海报图片地址 ")
    @TableField("POSTER_URL")
    private String posterUrl;

    @ApiModelProperty(value = "状态(1:在架 0：下架) ")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人 ")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间 ")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人 ")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间 ")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "有效开始时间")
    @TableField("BEGIN_DATE")
    private Date beginDate;

    @ApiModelProperty(value = "有效结束时间")
    @TableField("END_DATE")
    private Date endDate;

}
