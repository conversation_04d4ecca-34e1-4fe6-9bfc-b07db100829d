package com.hlkj.yxsAdminApi.hnzhsl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsCps;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsCpsParam;

import java.util.List;

/**
 * 商品关联CPS表Service
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
public interface HnslGoodsCpsService extends IService<HnslGoodsCps> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<HnslGoodsCps>
     */
    PageResult<HnslGoodsCps> pageRel(HnslGoodsCpsParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<HnslGoodsCps>
     */
    List<HnslGoodsCps> listRel(HnslGoodsCpsParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return HnslGoodsCps
     */
    HnslGoodsCps getByIdRel(Integer id);

}
