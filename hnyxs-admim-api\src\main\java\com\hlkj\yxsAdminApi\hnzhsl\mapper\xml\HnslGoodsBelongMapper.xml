<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslGoodsBelongMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM hnsl_goods_belong a
        <where>
            <if test="param.goodsNumber != null">
                AND a.GOODS_NUMBER LIKE CONCAT('%', #{param.goodsNumber}, '%')
            </if>
            <if test="param.schoolCode != null">
                AND a.SCHOOL_CODE LIKE CONCAT('%', #{param.schoolCode}, '%')
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsBelong">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsBelong">
        <include refid="selectSql"></include>
    </select>

    <update id="updateBygoodsNumber" parameterType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsBelong">
        update HNSL_GOODS_REL
        <set>
            <if test="relType != null">REL_TYPE = #{relType}, </if>
            <if test="goodsNumberRelMsg != null">GOODS_NUMBER_REL_MSG = #{goodsNumberRelMsg}, </if>
            <if test="status != null">STATUS = #{status}, </if>
            <if test="goodsNumberRel != null">GOODS_NUMBER_REL = #{goodsNumberRel}, </if>
        </set>
        where GOODS_NUMBER = #{goodsNumber}
        <if test="relType != null">
            AND REL_TYPE = #{relType}
        </if>
        <if test="goodsNumberRel != null">
            AND GOODS_NUMBER_REL = #{goodsNumberRel}
        </if>
    </update>


</mapper>
