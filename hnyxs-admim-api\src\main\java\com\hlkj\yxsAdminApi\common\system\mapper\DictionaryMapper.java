package com.hlkj.yxsAdminApi.common.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hlkj.yxsAdminApi.common.system.entity.Dictionary;
import com.hlkj.yxsAdminApi.common.system.param.DictionaryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 字典Mapper
 *
 * <AUTHOR>
 * @since 2020-03-14 11:29:03
 */
public interface DictionaryMapper extends BaseMapper<Dictionary> {

    /**
     *  查询字典大类下关联内容
     * @param param
     * @return
     */
    List<Dictionary> queryDictionary(@Param("param")DictionaryParam param);
}
