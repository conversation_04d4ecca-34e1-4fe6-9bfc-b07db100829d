package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUser;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 用户管理表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
public interface HnslUserMapper extends BaseMapper<HnslUser> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUser>
     */
    List<HnslUser> selectPageRel(@Param("page") IPage<HnslUser> page,
                             @Param("param") HnslUserParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUser> selectListRel(@Param("param") HnslUserParam param);

    /**
     * 获取学校对应的校园经理
     * @param map
     * @return List<HnslUserEntity>
     */
    List<HnslUser> queryManager(Map<String, Object> map);


    /**
     * 查询合伙人上两级管理人员信息
     * @param userPhone
     * @return
     */
    @Select("select * from hnsl_user where USER_PHONE = (select STATUS_SUPERIOR from hnsl_user where USER_PHONE = #{userPhone}  AND STATUS_SUPERIOR IS NOT NULL LIMIT 1 )\n" +
            "        UNION ALL\n" +
            "       select * from hnsl_user where user_phone = (select STATUS_SUPERIOR from hnsl_user where user_phone = (select STATUS_SUPERIOR from hnsl_user where USER_PHONE = #{userPhone} AND STATUS_SUPERIOR IS NOT NULL)  AND STATUS_SUPERIOR IS NOT NULL LIMIT 1)")
    List<HnslUser> queryIsEmptyStatusSuperior(@Param("userPhone") String userPhone);

    /**
     * 更换用户账号
     * @param map
     * @return
     */
    int updateBYPhone(Map<String, Object> map);

    /**
     * 删除用户相关信息
     * @param map
     * @return
     */
    int deleteBYPhone(Map<String, Object> map);

    /**
     * 查询两个号码绑定学校是否有一样的
     * @param map
     * @return
     */
    int querySchoolByUserPhoneTwo(Map<String, Object> map);

    /**
     * 存储过程修改 所有用户号码关联
     * @param map
     * @return
     */
    void updateAllPhone(Map<String, Object> map);

    /**
     * 删除合伙人下级的上级记录
     * @param map
     * @return
     */
    int updateBySuperior(Map<String, Object> map);

    /**
     * 查询合伙人及关了学校、楼栋、分组信息
     * @param map
     * @return
     */
    List<HnslUser> queryListUserBy(Map<String, Object> map);

    /**
     * 修改用户积分
     * @param user
     * @return
     */
    int updateUserByIntegral(HnslUser user);

    /**
     * 查询合伙人下的所有人和楼栋长
     * @param map
     * @return
     */
    List<Map<String,String>> queryALLStatusSuperior(Map<String, Object> map);

    List<Map<String,String>> queryExportUserList(Map<String, Object> conditionMap);

    /**
     * 导出用户20210310需求积分表
     * @param map
     * @return
     */
    List<Map<String,String>> queryExportUserIntegral(Map<String, Object> map);

    /**
     * 任务用户分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUser>
     */
    List<HnslUser> queryListUserRel(@Param("page") IPage<HnslUser> page,
                                 @Param("param") HnslUserParam param);

	List<String> querySchoolCodeByPhone(String userPhone);

	List<HnslUser> queryUser(String userPhone);

	List<HnslUser> queryList(Map<String, Object> requestMap);
}
