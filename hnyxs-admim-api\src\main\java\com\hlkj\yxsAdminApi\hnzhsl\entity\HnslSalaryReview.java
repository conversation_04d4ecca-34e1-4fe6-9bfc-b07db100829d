package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 薪资审核表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSalaryReview对象", description = "薪资审核表")
public class HnslSalaryReview implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "团队编码")
    @TableField("TEAM_CODE")
    private String teamCode;

    @ApiModelProperty(value = "手机号码")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "审核记录月（2020-1）")
    @TableField("AUDIT_DATE")
    private String auditDate;

    @ApiModelProperty(value = "审核状态 1:待审核/2:审核中/3:审核通过/4:审核未通过")
    @TableField("AUDIT_STATUS")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核人姓名")
    @TableField("AUDIT_NAME")
    private String auditName;

    @ApiModelProperty(value = "是否发放 1:发放 0：否")
    @TableField("AUDIT_SALARY_GRANT")
    private Integer auditSalaryGrant;

    @ApiModelProperty(value = "应发底薪")
    @TableField("MONEY1")
    private String money1;

    @ApiModelProperty(value = "劳务费扣税金额     *应发工资超过800的要扣除超过部分的20%的税费。例如1000的应发工资，实际劳务费扣税金额为（1000-800）*0.2=40")
    @TableField("MONEY2")
    private String money2;

    @ApiModelProperty(value = "服务费     应发金额的11%服务费，结算给号百，例如1000的工资，服务费是1000*0.11=110，此功能字段仅对省级管理员开放")
    @TableField("MONEY3")
    private String money3;

    @ApiModelProperty(value = "实发金额      应发金额-劳务费扣费金额=实发金额。如1000的应发工资，实际发放金额等于1000-（1000-800）*0.2=960")
    @TableField("MONEY4")
    private String money4;

    @ApiModelProperty(value = "号百应结算金额      应发底薪+服务费，例如1000的应发工资，服务费是1000*0.11=110，实际结算金额为1000+110=1110，此功能字段仅对省级管理员开放")
    @TableField("MONEY5")
    private String money5;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "地市编码")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty(value = "学校名称")
    @TableField("SCHOOL_NAME")
    private String schoolName;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "学子姓名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "级别(*校园经理/123级合伙人)")
    @TableField("USER_LEVEL")
    private Integer userLevel;

    @ApiModelProperty(value = "角色 *核心团队长/普通团队长/直销员")
    @TableField("TEAM_LEVEL")
    private Integer teamLevel;

    @ApiModelProperty(value = "客户经理姓名")
    @TableField("CLIENT_NAME")
    private String clientName;

    @ApiModelProperty(value = "身份证号码")
    @TableField("USER_CARD")
    private String userCard;

    @ApiModelProperty(value = "注册时间")
    @TableField("USER_DATE")
    private String userDate;

    @ApiModelProperty(value = "服务时长(单位天)")
    @TableField("USER_SERVICE_TIME")
    private Integer userServiceTime;

    @ApiModelProperty(value = "上级工号")
    @TableField("STATUS_SUPERIOR")
    private String statusSuperior;

    @ApiModelProperty(value = "生卡发展示数")
    @TableField("BUSINESS_NUMBER1")
    private Integer businessNumber1;

    @ApiModelProperty(value = "生卡发展业务积分")
    @TableField("BUSINESS_INTEGRATION1")
    private Integer businessIntegration1;

    @ApiModelProperty(value = "熟卡激活数")
    @TableField("BUSINESS_NUMBER2")
    private Integer businessNumber2;

    @ApiModelProperty(value = "熟卡积分")
    @TableField("BUSINESS_INTEGRATION2")
    private Integer businessIntegration2;

    @ApiModelProperty(value = "维系用户数")
    @TableField("BUSINESS_NUMBER3")
    private Integer businessNumber3;

    @ApiModelProperty(value = "维系积分")
    @TableField("BUSINESS_INTEGRATION3")
    private Integer businessIntegration3;

    @ApiModelProperty(value = "自由预存数")
    @TableField("BUSINESS_NUMBER4")
    private Integer businessNumber4;

    @ApiModelProperty(value = "自由预存积分")
    @TableField("BUSINESS_INTEGRATION4")
    private Integer businessIntegration4;

    @ApiModelProperty(value = "四升五用户数")
    @TableField("BUSINESS_NUMBER5")
    private Integer businessNumber5;

    @ApiModelProperty(value = "其他积分")
    @TableField("BUSINESS_INTEGRATION5")
    private Integer businessIntegration5;

    @ApiModelProperty(value = "个人总积分")
    @TableField("USER_INTEGRATION")
    private Integer userIntegration;

    @ApiModelProperty(value = "团队人数")
    @TableField("TEAM_NUMBER")
    private Integer teamNumber;

    @ApiModelProperty(value = "团队总积分")
    @TableField("TEAM_INTEGRATION")
    private Integer teamIntegration;

    @ApiModelProperty(value = "KPI打分")
    @TableField("USER_KPI")
    private String userKpi;

    @ApiModelProperty(value = "工商银行账号")
    @TableField("USER_BANK")
    private String userBank;

    @ApiModelProperty(value = "团队名称")
    @TableField("TEAM_NAME")
    private String teamName;

    @ApiModelProperty(value = "是否 代办点 （默认 0:不是 1:是）")
    @TableField("AGENT_POINT")
    private Integer agentPoint;

    @ApiModelProperty(value = "今年累计积分")
    @TableField("USER_YEAR_INTEGRATION")
    private String userYearIntegration;

    @ApiModelProperty(value = "历年累计积分")
    @TableField("USER_TOTAL_INTEGRATION")
    private String userTotalIntegration;

}
