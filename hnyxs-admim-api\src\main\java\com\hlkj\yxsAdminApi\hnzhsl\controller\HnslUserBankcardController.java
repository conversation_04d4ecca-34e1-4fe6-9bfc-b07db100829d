package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslUserBankcardService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserBankcard;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserBankcardParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户银行卡表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
@Api(tags = "用户银行卡表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-user-bankcard")
public class HnslUserBankcardController extends BaseController {
    @Autowired
    private HnslUserBankcardService hnslUserBankcardService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:list')")
    @OperationLog
    @ApiOperation("分页查询用户银行卡表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUserBankcard>> page(@RequestBody HnslUserBankcardParam param) {
        PageParam<HnslUserBankcard, HnslUserBankcardParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserBankcardService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslUserBankcardService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:list')")
    @OperationLog
    @ApiOperation("查询全部用户银行卡表")
    @PostMapping("/list")
    public ApiResult<List<HnslUserBankcard>> list(@RequestBody HnslUserBankcardParam param) {
        PageParam<HnslUserBankcard, HnslUserBankcardParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslUserBankcardService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslUserBankcardService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:list')")
    @OperationLog
    @ApiOperation("根据id查询用户银行卡表")
    @GetMapping("/{id}")
    public ApiResult<HnslUserBankcard> get(@PathVariable("id") Integer id) {
        return success(hnslUserBankcardService.getById(id));
        // 使用关联查询
        //return success(hnslUserBankcardService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:save')")
    @OperationLog
    @ApiOperation("添加用户银行卡表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUserBankcard hnslUserBankcard) {
        if (hnslUserBankcardService.save(hnslUserBankcard)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:update')")
    @OperationLog
    @ApiOperation("修改用户银行卡表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUserBankcard hnslUserBankcard) {
        if (hnslUserBankcardService.updateById(hnslUserBankcard)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:remove')")
    @OperationLog
    @ApiOperation("删除用户银行卡表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserBankcardService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:save')")
    @OperationLog
    @ApiOperation("批量添加用户银行卡表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUserBankcard> list) {
        if (hnslUserBankcardService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:update')")
    @OperationLog
    @ApiOperation("批量修改用户银行卡表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUserBankcard> batchParam) {
        if (batchParam.update(hnslUserBankcardService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserBankcard:remove')")
    @OperationLog
    @ApiOperation("批量删除用户银行卡表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserBankcardService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
