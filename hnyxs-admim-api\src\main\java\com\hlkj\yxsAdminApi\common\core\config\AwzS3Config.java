package com.hlkj.yxsAdminApi.common.core.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.Data;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 2024年04月10日16:09:00
 * LFH 创建ceph对象存储
 */
@Data
@Configuration
public class AwzS3Config {

    private static final String accessKeyID = "M5LGR55DFMU035DJO2JF";
    private static final String secretKey = "yx6pQO8Kgn8upOaIGKDzYs4MmxZes9rbyPDOGuk8";
    private static final String endpoint = "http://134.188.232.35:8080";

    @Bean
    public AmazonS3 getAmazon() {
        AWSCredentials credentials = new BasicAWSCredentials(accessKeyID, secretKey);
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(Protocol.HTTP);
        clientConfig.setRequestTimeout(60000);
        clientConfig.setConnectionTimeout(60000);
        AwsClientBuilder.EndpointConfiguration endpointConfiguration = new AwsClientBuilder.EndpointConfiguration(
                // 设置要用于请求的端点配置（服务端点和签名区域）
                endpoint,
                Regions.US_EAST_2.name());
        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withEndpointConfiguration(endpointConfiguration)
                .withPathStyleAccessEnabled(true)
                .withClientConfiguration(clientConfig)
                .build();
    }

}
