package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslPerformance;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslPerformanceParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 绩效表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslPerformanceMapper extends BaseMapper<HnslPerformance> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslPerformance>
     */
    List<HnslPerformance> selectPageRel(@Param("page") IPage<HnslPerformance> page,
                             @Param("param") HnslPerformanceParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslPerformance> selectListRel(@Param("param") HnslPerformanceParam param);

    /**
     * 获取绩效数量
     * @param map
     * @return
     */
    int queryTotal(Map<String, Object> map);
}
