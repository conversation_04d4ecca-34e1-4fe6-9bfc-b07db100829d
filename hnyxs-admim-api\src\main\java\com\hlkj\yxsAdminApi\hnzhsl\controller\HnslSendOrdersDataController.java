package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSendOrdersDataService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersData;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSendOrdersDataParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 派单详情表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "派单详情表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-send-orders-data")
public class HnslSendOrdersDataController extends BaseController {
    @Autowired
    private HnslSendOrdersDataService hnslSendOrdersDataService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:list')")
    @OperationLog
    @ApiOperation("分页查询派单详情表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSendOrdersData>> page(@RequestBody HnslSendOrdersDataParam param) {
        PageParam<HnslSendOrdersData, HnslSendOrdersDataParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSendOrdersDataService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslSendOrdersDataService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:list')")
    @OperationLog
    @ApiOperation("查询全部派单详情表")
    @PostMapping("/list")
    public ApiResult<List<HnslSendOrdersData>> list(@RequestBody HnslSendOrdersDataParam param) {
        PageParam<HnslSendOrdersData, HnslSendOrdersDataParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSendOrdersDataService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSendOrdersDataService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:list')")
    @OperationLog
    @ApiOperation("根据id查询派单详情表")
    @GetMapping("/{id}")
    public ApiResult<HnslSendOrdersData> get(@PathVariable("id") Integer id) {
        return success(hnslSendOrdersDataService.getById(id));
        // 使用关联查询
        //return success(hnslSendOrdersDataService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:save')")
    @OperationLog
    @ApiOperation("添加派单详情表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSendOrdersData hnslSendOrdersData) {
        if (hnslSendOrdersDataService.save(hnslSendOrdersData)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:update')")
    @OperationLog
    @ApiOperation("修改派单详情表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSendOrdersData hnslSendOrdersData) {
        if (hnslSendOrdersDataService.updateById(hnslSendOrdersData)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:remove')")
    @OperationLog
    @ApiOperation("删除派单详情表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSendOrdersDataService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:save')")
    @OperationLog
    @ApiOperation("批量添加派单详情表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSendOrdersData> list) {
        if (hnslSendOrdersDataService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:update')")
    @OperationLog
    @ApiOperation("批量修改派单详情表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSendOrdersData> batchParam) {
        if (batchParam.update(hnslSendOrdersDataService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSendOrdersData:remove')")
    @OperationLog
    @ApiOperation("批量删除派单详情表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSendOrdersDataService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
