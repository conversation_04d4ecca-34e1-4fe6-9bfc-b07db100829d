package com.hlkj.yxsAdminApi.hnzhsl.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫楼工具商品关联表
 *
 * <AUTHOR>
 * @since 2023-05-12 16:04:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslGoodsRel对象", description = "扫楼工具商品关联表")
public class HnslGoodsRel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "商品编码")
    @TableField("GOODS_NUMBER")
    private String goodsNumber;

    @ApiModelProperty(value = "关联商品编码")
    @TableField("GOODS_NUMBER_REL")
    private String goodsNumberRel;

    @ApiModelProperty(value = "关联类型（1可选包,2优惠包，3：叠加包 4：100元预存 5200元预存 6300元预存 7：X元预存 8:宽带 9:基本包）")
    @TableField("REL_TYPE")
    private String relType;

    @ApiModelProperty(value = "当前状态（1：上架，0：下架）")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "关联商品说明")
    @TableField("GOODS_NUMBER_REL_MSG")
    private String goodsNumberRelMsg;

    @ApiModelProperty(value = "商品校内流量是否使用（0：否 1：是）")
    @TableField("GOODS_SCHOOL_DISCERN")
    private Integer goodsSchoolDiscern;

}
