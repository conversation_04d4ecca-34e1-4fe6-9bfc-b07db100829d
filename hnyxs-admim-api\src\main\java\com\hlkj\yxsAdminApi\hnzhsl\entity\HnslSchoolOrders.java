package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 派单学校关联表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSchoolOrders对象", description = "派单学校关联表")
public class HnslSchoolOrders implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识 主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动标识")
    @TableField("ORDERS_ID")
    private String ordersId;

    @ApiModelProperty(value = "学校6级编码")
    @TableField("SCHOOL_SIX_ID")
    private String schoolSixId;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

}
