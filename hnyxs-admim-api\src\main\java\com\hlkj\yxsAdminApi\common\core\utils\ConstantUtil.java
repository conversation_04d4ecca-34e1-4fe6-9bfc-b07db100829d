package com.hlkj.yxsAdminApi.common.core.utils;


import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 常量类
 *
 * <AUTHOR>
 * @version [V1.0, 2019-04-19]
 */
public class ConstantUtil {
    /**
     * 本地
     */
    /*public static final String MAIN_URL = "http://wangmeng.vipgz1.idcfengye.com/hnzsx";
     */

    //猛哥
//	public static final String MAIN_URL = "http://cxshwm.gnway.cc:8000/hnzsx";//本地
//	public static final String MAIN_URL = "https://wx.hn.189.cn/xtsit/hnzsx";//测试
    /*  public static final String SAVE_FILE_PATH = "E:/hnzsx/uploads/";//文件保存位置
     */  //李鹏飞
//  public static final String SAVE_FILE_PATH = "F:/WorkSpace/Eclipse2/hnzsx/src/main/webapp/uploads/";//文件保存位置

    //测试82
//  public static final String MAIN_URL = "https://wx.hn.189.cn/xtsit/hnzsx";//项目地址
//    public static final String MAIN_URL = "http://***************:8080/hnzsx";
//  public static final String MAIN_URL = "http://wx.hn.189.cn/test1/hnzsx";//项目地址
//  public static final String INTRANET_URL = "http://**************/test1/hnzsx";//图片获取F5内网地址
//  public static final String notifyUrl= "http://**************:9080/api/openapi/paySuccess";//支付中心支付结果通知地址
//  public static final String payRequseUrl= "http://**************:9080/api/openapi/preCreateOrder";//支付中心支付二维码获取地址
//  public static final String xAppId="f4ed79707b94c966ae416dd117b01606";
//  public static final String xAppKey="72ded0ae988af73ff1518b6cb42f5fa6";
//  public static final String busiChannel= "80002";//业务渠道编码 测试环境---> busiChannel":"80002","accountId":"**********"
//  public static final String accountId="**********";//操作岗位

    public static final String F5_INSIDE_URL = "http://***************:31530/hnzhsl/smallFile/image2.do?fileName=";
    /**
     * 正式
     */
    public static final String MAIN_URL = "https://wx.hn.189.cn/hnzsx";//项目地址

    public static final String INTRANET_URL_IP = "http://***************:31766/hnzsxserve/download/";// //掌上销H5使用：图片获取内网地址-vip地址
    public static final String SAVE_FILE_PATH = "/app/nfs2/WM/hnzsx/uploads/";//文件保存位置
    public static final String SAVE_IMAGE_PATH = "/app/nfs2/PJY/zhsl/";//文件保存位置
    public static final String INTRANET_URL = "http://**************/hnzsx";//图片获取F5内网地址
    public static final String WX_URL = "https://api.weixin.qq.com/cgi-bin/token";//微信access_token的url
    public static final String APPID = "wx799700e7415454f4";//小程序appid
    public static final String SECRET = "8ea3e08179232ecb80dd1a1d8740e240"; //小程序回话秘钥SECRET
    public static final String WX_OCR = "http://api.weixin.qq.com/cv/ocr/idcard";//OCR识别地址
    public static final String INTERFACE_URL = "http://**************:8088/fxb/bss/toPost.do";//范小白接口地址
    public static final String notifyUrl = "http://**************:8081/api/openapi/payCenterSuccess";//支付中心支付结果通知地址
    public static final String payRequseUrl = "http://**************:8081/api/openapi";//支付中心支付二维码获取地址
    public static final String xAppId = "d4c0ea55942b6d1c957e3a3842aadfff";
    public static final String xAppKey = "4a4d717fa94e683ce7beaa7ef9c8b5fa";

    public static final String Test_AppId = "2bd1aded58f6b38ed943ecfa1c09e60b";
    public static final String Test_AppKey = "74a7732dbff67f84a9cfb9b60a502c8f";
    public static final String YXS_AppId = "716af8a4d597f09d0e749e9f641e4632";
    public static final String YXS_AppKey = "28b30f834eec91476c0cd53c91077637";

    //订单提交保存redis key
    public static final String REPEAT_SUBMIT = "hnsl:h5:submit";
    public static final String ACCOUNT_ID = "**********";// 业务主账号

    //测试
//    public static final String xAppId="2bd1aded58f6b38ed943ecfa1c09e60b";
//    public static final String xAppKey="74a7732dbff67f84a9cfb9b60a502c8f";

    public static final String DCOOS_AppId = "d4c0ea55942b6d1c957e3a3842aadfff";
    public static final String DCOOS_AppKey = "4a4d717fa94e683ce7beaa7ef9c8b5fa";

    //扫楼DCOOS ID
    public static final String SL_AppId="cebed08fc3377f3174f72133cc0179c6";
    public static final String SL_AppKey="e0f9657f537ce0eed59399b635499449";
    public static final String UID_PREFIX ="UID";

    public static final String busiChannel = "80001";//业务渠道编码 正式环境---> busiChannel":"80001","accountId":"**********"
    public static final String accountId = "**********";//操作岗位
    //  public static final String INTERFACE_URL = "http://fxb.hn.189.cn/fxb/bss/toPost.do";//范小白接口地址
    public static final String requestUrl = "http://wx.hn.189.cn";//83到88的域名
    public static final String LAISSER_PASSER_LOGIN_CALL_BACK_URL = MAIN_URL + "/callBack/avoidLogin.do";//免登录回调接口URL
    /**
     * 企业微信id
     */
    public static final String ENTERPRISE_WECHAT = "ww19c129aff303a9bf";
    /**
     * 成功状态码
     */
    public static final String SUCCESS_CODE = "0";
    /**
     * 失败状态码
     */
    public static final String FAIL_CODE = "1";
    /**
     * 字符串code
     */
    public static final String CODE = "code";
    /**
     * 字符串data
     */
    public static final String DATA = "data";
    /**
     * 获取微信扫一扫参数地址
     */
    public static final String WECHAT_SCAB_URL = "http://**************/cdyxport/enterprise/getJsApi.do";
    /**
     * 0000
     */
    public static final String FOUR_ZERO = "0000";
    /**
     * 00000
     */
    public static final String FIVE_ZERO = "00000";
    /**
     * 404
     */
    public static final String NOT_FOUND = "404";
    /**
     * O2O中台系统api账户(李鹏飞)
     */
    public static final String ACCOUNT_LPF = "hlkjlipengfei";
    /**
     * O2O中台系统密钥(李鹏飞)
     */
    public static final String PASSWORD_LPF = "hlkj_lipengfei852";
    /**
     * O2O中台系统key(李鹏飞)
     */
    public static final String KEY_LPF = "hlkj!$@#lipnegfei94571";
    /**
     * O2O中台系统测试地址
     */
    public static final String HNMIDDLE_URL = "http://**************/hnmiddle";
    /**
     * 响应成功
     */
    public static final String RESPONSE_SUCCESS = "10000";
    /**
     * 白名单额度查询api加密key
     */
    public static final String QUERY_LINES_KEY = "20200316CFQHNBLKJsekhyzf";
    /**
     * 外呼加密密钥
     */
    public static final String CCBAR_DES3 = "123456781234567812345678";
    /**
     * 外呼解密密钥
     */
    public static final String CCBAR_DES3_DECRYPT = "69IC71TU28A2T96I8S12TQCI";

    /**
     * 外呼加密偏移量
     */
    public static final byte[] CCBAR_IV = {0x12, 0x34, 0x56, 0x78, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};

    /**
     * 数值0
     */
    public static final String ZERO = "0";

    /**
     * 数值1
     */
    public static final String ONE = "1";

    /**
     * 数值2
     */
    public static final String TWO = "2";

    /**
     * message 消息
     */
    public static final String MESSAGE = "message";


    /**
     * res_code  响应状态码
     */
    public static final String RES_CODE = "res_code";

    /**
     * 政企客户类型编码
     */
    public static final String ENTERPRISE_CUST_TYPE_CODE = "1000";

    /**
     * 公众客户类型编码
     */
    public static final String PUBLIC_CUST_TYPE_CODE = "1100";

    /**
     * cust_type
     */
    public static final String CUST_TYPE = "cust_type";

    /**
     * 移动电话产品类型id
     */
    public static final String YI_DONG_PHONE_PROD_ID = "80000045";

    /**
     * 移动电话
     */
    public static final String YI_DONG_PHONE = "移动电话";

    /**
     * 普通电话(固话)产品类型id
     */
    public static final String PU_TONG_PROD_ID = "80000000";

    /**
     * 普通电话(固话)
     */
    public static final String PU_TONG_PHONE = "普通电话";

    /**
     * 宽带产品类型id
     */
    public static final String KUAN_DAI_PROD_ID = "80000030";

    /**
     * 宽带
     */
    public static final String KUAN_DAI = "宽带";

    /**
     * ITV产品类型id
     */
    public static final String ITV_PROD_ID = "80000140";

    /**
     * ITV
     */
    public static final String ITV = "ITV";

    /**
     * 合同产品id
     */
    public static final String HE_TONG_PROD_ID = "80044796";

    /**
     * 合同产品
     */
    public static final String HE_TONG = "合同产品标识";


    /**
     * Success
     */
    public static final String SUCCESS = "Success";

    /**
     * res_message
     */
    public static final String RES_MESSAGE = "res_message";

    public static final String BUCKET_NAME = "blackqrcode";
    public static final String BUCKET_NAME_KEY = "hnzhsl";
    public static final String BUCKET_WHITE_NAME = "hnslwhitepicture";

    private static final String thisMonthDate = new SimpleDateFormat("yyyyMM").format(new Date());
    public static final String BUCKET_KEY = thisMonthDate + "hnzhsl";
    /**
     * 微信免押参数
     */
    public static final String customerNo = "HN_ONLINE_DQ";

    public static final String keyHex = "5e963b52f388532839f873f78e6e79ec73aa1db8c5c2595c";
    public static final String ivHex = "ca2989f828c46c29";
    public static final String newCustomerNo = "HUNAN_ELEC";
    public static final String newKeyHex = "6e963b52f3885456839f873f78e6e79ec73aa1db8ccc259f";


}