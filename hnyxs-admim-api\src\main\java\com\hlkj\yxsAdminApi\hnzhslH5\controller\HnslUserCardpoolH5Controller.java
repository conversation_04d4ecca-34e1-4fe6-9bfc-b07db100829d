package com.hlkj.yxsAdminApi.hnzhslH5.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserCardpool;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5School;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserCardpool;
import com.hlkj.yxsAdminApi.hnzhslH5.entity.HnslH5UserUpdateRecord;
import com.hlkj.yxsAdminApi.hnzhslH5.param.HnslUserCardpoolH5Param;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslSchoolH5Service;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslUserCardpoolH5Service;
import com.hlkj.yxsAdminApi.hnzhslH5.service.HnslUserUpdateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 私人号池控制器
 *
 * @Author: zwk
 * @Since: 2025/3/15
 **/
@Api(tags = "私人号池管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslUserCardpoolH5")
public class HnslUserCardpoolH5Controller extends BaseController {
    @Autowired
    private HnslUserCardpoolH5Service hnslUserCardpoolH5Service;

    @Autowired
    private HnslSchoolH5Service hnslSchoolH5Service;

    @Autowired
    private HnslUserUpdateRecordService hnslUserUpdateRecordService;

    @Autowired
    private RedisUtil redisUtil;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpoolH5:list')")
    @OperationLog
    @ApiOperation("分页查询私人号池")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslH5UserCardpool>> page(@RequestBody HnslUserCardpoolH5Param param) {
        User user = getLoginUser();
        String hnslType = redisUtil.getString("hnslH5Channel" + user.getPhone());
        param.setHnslType(hnslType);
        return success(hnslUserCardpoolH5Service.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpoolH5:list')")
    @OperationLog
    @ApiOperation("查询全部私人号池")
    @PostMapping("/list")
    public ApiResult<List<HnslH5UserCardpool>> list(@RequestBody HnslUserCardpoolH5Param param) {
        PageParam<HnslH5UserCardpool, HnslUserCardpoolH5Param> page = new PageParam<>(param);
        return success(hnslUserCardpoolH5Service.list(page.getOrderWrapper()));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpoolH5:list')")
    @OperationLog
    @ApiOperation("根据id查询私人号池")
    @GetMapping("/{id}")
    public ApiResult<?> get(@PathVariable("id") Integer id) {
        HnslH5UserCardpool hnslUserCardpool = hnslUserCardpoolH5Service.getById(id);
        //根据cardPoolNumber查询schoolCode
        List<HnslH5UserCardpool> hnslUserCardpoolEntityList = hnslUserCardpoolH5Service.querySchoolCodesBycardPoolNumber(hnslUserCardpool);
        //根据schoolCode查询schoolName
        HnslH5School hnslSchool = new HnslH5School();
        String schoolName = "";
        String schoolCode = "";
        for (HnslH5UserCardpool userCardpoolEntity : hnslUserCardpoolEntityList) {
            hnslSchool.setSchoolCode(userCardpoolEntity.getSchoolCode());
            HnslH5School hnslSchoolEntity = hnslSchoolH5Service.queryObject(hnslSchool);
            if (hnslSchoolEntity==null || StringUtil.isEmpty(hnslSchoolEntity.getSchoolCode()) || StringUtil.isEmpty(hnslSchoolEntity.getSchoolName())) {
                continue;
            }
            if (schoolName == "") {
                schoolName = schoolName + hnslSchoolEntity.getSchoolName();
                schoolCode = hnslSchoolEntity.getSchoolCode();
            } else {
                schoolName = schoolName + "," + hnslSchoolEntity.getSchoolName();
                schoolCode = schoolCode + "," + hnslSchoolEntity.getSchoolCode();
            }
        }
        hnslUserCardpool.setSchoolCode(schoolCode);
        hnslUserCardpool.setSchoolName(schoolName);
        return success(hnslUserCardpool);
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpoolH5:save')")
    @OperationLog
    @ApiOperation("添加私人号池")
    @PostMapping("/decode/save")
    public ApiResult<?> save(@RequestBody HnslUserCardpoolH5Param param) {
        try {
            User user = getLoginUser();
            String UserName = redisUtil.getString("thisH5UserName" + user.getPhone());
            if (null == param.getChannelId() || null == param.getSchoolCode() || null == param.getStatus()) {
                return fail("缺少参数，清检查");
            }
            HnslH5UserCardpool hnslUserCardpool = new HnslH5UserCardpool();
            hnslUserCardpool.setCardPoolNumber(param.getCardPoolNumber());
            hnslUserCardpool.setChannelId(param.getChannelId());
            hnslUserCardpool.setStatus(param.getStatus());
            hnslUserCardpool.setCityCode(param.getCityCode());
            hnslUserCardpool.setCardPoolName(param.getCardPoolName());
            hnslUserCardpool.setCreatedUser(UserName);
            hnslUserCardpool.setCreatedDate(new Date());
            String schoolCode = param.getSchoolCode();
            if (schoolCode.contains(",")) {
                String[] schoolCodeArray = schoolCode.split(",");
                for (String scCode : schoolCodeArray) {
                    hnslUserCardpool.setSchoolCode(scCode);
                    hnslUserCardpoolH5Service.save(hnslUserCardpool);
                }
            } else {
                hnslUserCardpoolH5Service.save(hnslUserCardpool);
            }
            return success("添加成功");
        } catch (Exception e) {
            e.printStackTrace();
            return fail("添加失败");
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpoolH5:update')")
    @OperationLog
    @ApiOperation("修改私人号池")
    @PostMapping("/decode/update")
    public ApiResult<?> update(@RequestBody HnslUserCardpoolH5Param param) {
        User user = getLoginUser();
        String UserName = redisUtil.getString("thisH5UserName" + user.getPhone());
        if (null == param.getChannelId() || null == param.getSchoolCode() || null == param.getStatus()) {
            return fail("缺少参数，清检查");
        }
        HnslH5UserCardpool hnslUserCardpool = new HnslH5UserCardpool();
        hnslUserCardpool.setUpdatedUser(UserName);
        hnslUserCardpool.setUpdatedDate(new Date());
        hnslUserCardpool.setId(param.getId());
        hnslUserCardpool.setUserId(param.getUserId());
        hnslUserCardpool.setCityCode(param.getCityCode());
        hnslUserCardpool.setChannelId(param.getChannelId());
        hnslUserCardpool.setCardPoolNumber(param.getCardPoolNumber());
        hnslUserCardpool.setCardPoolName(param.getCardPoolName());
        hnslUserCardpool.setCardPoolType(param.getCardPoolType());
        hnslUserCardpool.setStatus(param.getStatus());
        //先查到删后增
        String schoolCode = param.getSchoolCode();

        String details = "";
        if (!StringUtil.isEmpty(schoolCode)) { // 如果存在多个学校 切割逐条保存
            hnslUserCardpoolH5Service.removeById(param.getId());
            hnslUserCardpoolH5Service.deleteBycardPoolNumber(param);
            details = "管理员:("+UserName+")修改号码池:号池编码("+param.getCardPoolNumber()+") 学校编码("+schoolCode+")";
            logger.info("update hnslUserCardpool detail----->" + param.getId() );
            if(schoolCode.contains(",")){
                String[] schoolCodeArray = schoolCode.split(",");
                for (String scCode : schoolCodeArray) {
                    hnslUserCardpool.setSchoolCode(scCode);
                    hnslUserCardpoolH5Service.save(hnslUserCardpool);
                }
            }else {
                hnslUserCardpool.setSchoolCode(schoolCode);
                hnslUserCardpoolH5Service.save(hnslUserCardpool);
            }
        } else {
            logger.info("disable hnslUserCardpool status----->" + param.getId() );
            if(param.getStatus() == 1L){
                details = "管理员:("+UserName+")启用号码池:号池id("+param.getId()+")";
            }else if(param.getStatus() == 0L){
                details = "管理员:("+UserName+")禁用号码池:号池id("+param.getId()+")";
            }
            hnslUserCardpoolH5Service.updateCardPool(hnslUserCardpool);
        }

        // 保存修改记录表
        HnslH5UserUpdateRecord record = new HnslH5UserUpdateRecord();
        record.setUpdatedUser(user.getPhone());
        record.setUpdatedDate(new Date());
        record.setUserupdatePhone(user.getPhone());
        record.setStatus(1L);
        record.setUserupdateDetails(details);
        hnslUserUpdateRecordService.save(record);
        return success("修改成功");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUserCardpoolH5:remove')")
    @OperationLog
    @ApiOperation("删除私人号池")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserCardpoolH5Service.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @OperationLog
    @ApiOperation("批量添加私人号池")
    @PostMapping("/decode/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslH5UserCardpool> list) {
        User loginUser = getLoginUser();
        list.forEach(w -> {
            w.setCreatedDate(new Date());
            w.setCreatedUser(loginUser.getUsername());
        });
        if (hnslUserCardpoolH5Service.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @OperationLog
    @ApiOperation("批量修改私人号池")
    @PostMapping("/decode/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody List<HnslH5UserCardpool> batchParam) {
        if (hnslUserCardpoolH5Service.updateBatchById(batchParam)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @OperationLog
    @ApiOperation("批量删除私人号池")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslUserCardpoolH5Service.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
