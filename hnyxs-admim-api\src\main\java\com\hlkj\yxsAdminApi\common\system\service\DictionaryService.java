package com.hlkj.yxsAdminApi.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.system.entity.Dictionary;
import com.hlkj.yxsAdminApi.common.system.param.DictionaryParam;

import java.util.List;

/**
 * 字典Service
 *
 * <AUTHOR>
 * @since 2020-03-14 11:29:03
 */
public interface DictionaryService extends IService<Dictionary> {

    /**
     *  查询字典大类下关联内容
     * @param param
     * @return
     */
    List<Dictionary> queryDictionary(DictionaryParam param);
}
