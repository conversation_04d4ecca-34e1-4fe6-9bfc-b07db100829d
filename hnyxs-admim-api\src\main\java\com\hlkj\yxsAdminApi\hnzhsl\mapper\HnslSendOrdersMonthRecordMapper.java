package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSendOrdersMonthRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSendOrdersMonthRecordParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 派单每月统计表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslSendOrdersMonthRecordMapper extends BaseMapper<HnslSendOrdersMonthRecord> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSendOrdersMonthRecord>
     */
    List<HnslSendOrdersMonthRecord> selectPageRel(@Param("page") IPage<HnslSendOrdersMonthRecord> page,
                             @Param("param") HnslSendOrdersMonthRecordParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSendOrdersMonthRecord> selectListRel(@Param("param") HnslSendOrdersMonthRecordParam param);

}
