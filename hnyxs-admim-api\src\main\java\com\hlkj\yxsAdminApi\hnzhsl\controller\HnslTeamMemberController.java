package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTeamMemberService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamMember;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamMemberParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 团队成员表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "团队成员表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-team-member")
public class HnslTeamMemberController extends BaseController {
    @Autowired
    private HnslTeamMemberService hnslTeamMemberService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:list')")
    @OperationLog
    @ApiOperation("分页查询团队成员表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTeamMember>> page(@RequestBody HnslTeamMemberParam param) {
        PageParam<HnslTeamMember, HnslTeamMemberParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamMemberService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTeamMemberService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:list')")
    @OperationLog
    @ApiOperation("查询全部团队成员表")
    @PostMapping("/list")
    public ApiResult<List<HnslTeamMember>> list(@RequestBody HnslTeamMemberParam param) {
        PageParam<HnslTeamMember, HnslTeamMemberParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamMemberService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTeamMemberService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:list')")
    @OperationLog
    @ApiOperation("根据id查询团队成员表")
    @GetMapping("/{id}")
    public ApiResult<HnslTeamMember> get(@PathVariable("id") Integer id) {
        return success(hnslTeamMemberService.getById(id));
        // 使用关联查询
        //return success(hnslTeamMemberService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:save')")
    @OperationLog
    @ApiOperation("添加团队成员表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTeamMember hnslTeamMember) {
        if (hnslTeamMemberService.save(hnslTeamMember)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:update')")
    @OperationLog
    @ApiOperation("修改团队成员表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTeamMember hnslTeamMember) {
        if (hnslTeamMemberService.updateById(hnslTeamMember)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:remove')")
    @OperationLog
    @ApiOperation("删除团队成员表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTeamMemberService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:save')")
    @OperationLog
    @ApiOperation("批量添加团队成员表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTeamMember> list) {
        if (hnslTeamMemberService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:update')")
    @OperationLog
    @ApiOperation("批量修改团队成员表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTeamMember> batchParam) {
        if (batchParam.update(hnslTeamMemberService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamMember:remove')")
    @OperationLog
    @ApiOperation("批量删除团队成员表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTeamMemberService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
