package com.hlkj.yxsAdminApi.hnzhsl.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.utils.*;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.UserService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.*;
import com.hlkj.yxsAdminApi.hnzhsl.service.*;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.utils.ExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 用户管理表控制器
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
@Api(tags = "用户管理表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslUser")
public class HnslUserController extends BaseController {
    @Autowired
    private HnslUserService hnslUserService;

    @Autowired
    private HnslUserSchoolService hnslUserSchoolService;

    @Autowired
    private HnslSchoolService hnslSchoolService;

    @Autowired
    private HnslBuildingService hnslBuildingService;

    @Autowired
    private HnslUserBankcardService hnslUserBankcardService;

    @Autowired
    private HnslTeamService hnslTeamService;

    @Autowired
    private HnslTeamMemberService hnslTeamMemberService;

    @Autowired
    private HnslIntegralService hnslIntegralService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserService userService;
    @Autowired
    private QueryUserManagerUtil queryUserManagerUtil;
    @Autowired
    private  AwsS3Utils awsS3Utils;

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:list')")
    @OperationLog
    @ApiOperation("分页查询用户管理表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslUser>> page(@RequestBody HnslUserParam param) {
        //PageParam<HnslUser, HnslUserParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        User user = getLoginUser();
        if (redisUtil.limitRateCount(Constants.limitUser, user)) {
            // 熔断账号冻结
            user.setStatus(1);
            userService.updateById(user);
            // 推送熔断日志用户信息
            InterfaceUtil.breakerLog(user, "用户信息查询");
            return new ApiResult<>(Constants.RESULT_ERROR_CODE, "当前查询次数已被限制");
        }
        PageResult<HnslUser> pageResult = hnslUserService.pageRel(param);
        // 对敏感数据进行脱敏处理
        if (pageResult != null && pageResult.getList() != null) {
            for (HnslUser hnslUser : pageResult.getList()) {
                // 敏感数据脱敏
                hnslUser.setUserName(DesensitizationUtil.desensitizedName(hnslUser.getUserName()));
                hnslUser.setUserSfz(DesensitizationUtil.idCardDesensitization(hnslUser.getUserSfz()));
                hnslUser.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhone()));
                hnslUser.setUserPhoneTwo(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhoneTwo()));
                hnslUser.setUserMail(DesensitizationUtil.emailDesensitization(hnslUser.getUserMail()));
                hnslUser.setStatusSuperior(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getStatusSuperior()));
                hnslUser.setUserManger(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserManger()));
                hnslUser.setReferrerId(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getReferrerId()));
            }
        }
        return success(pageResult);
        // 使用关联查询
        //return success(hnslUserService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:list')")
    @OperationLog
    @ApiOperation("查询全部用户管理表")
    @PostMapping("/list")
    public ApiResult<List<HnslUser>> list(@RequestBody HnslUserParam param) {
        PageParam<HnslUser, HnslUserParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<HnslUser> userList = hnslUserService.list(page.getOrderWrapper());

        // 对敏感数据进行脱敏处理
        if (userList != null && !userList.isEmpty()) {
            for (HnslUser hnslUser : userList) {
                hnslUser.setUserName(DesensitizationUtil.desensitizedName(hnslUser.getUserName()));
                hnslUser.setUserSfz(DesensitizationUtil.idCardDesensitization(hnslUser.getUserSfz()));
                hnslUser.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhone()));
                hnslUser.setUserPhoneTwo(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhoneTwo()));
                hnslUser.setUserMail(DesensitizationUtil.emailDesensitization(hnslUser.getUserMail()));
                hnslUser.setStatusSuperior(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getStatusSuperior()));
                hnslUser.setUserManger(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserManger()));
                hnslUser.setReferrerId(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getReferrerId()));
            }
        }

        return success(userList);
        // 使用关联查询
        //return success(hnslUserService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:list')")
    @OperationLog
    @ApiOperation("根据id查询用户管理表")
    @GetMapping("/{id}")
    public ApiResult<JSONObject> get(@PathVariable("id") Integer id) {
        JSONObject jsonObject = new JSONObject();
        HnslUser hnslUser = hnslUserService.getById(id);

        // 对hnslUser进行脱敏处理
        if (hnslUser != null) {
            hnslUser.setUserName(DesensitizationUtil.desensitizedName(hnslUser.getUserName()));
            hnslUser.setUserSfz(DesensitizationUtil.idCardDesensitization(hnslUser.getUserSfz()));
            hnslUser.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhone()));
            hnslUser.setUserPhoneTwo(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserPhoneTwo()));
            hnslUser.setUserSite(DesensitizationUtil.desensitizedAddress(hnslUser.getUserSite()));
            hnslUser.setUserMail(DesensitizationUtil.emailDesensitization(hnslUser.getUserMail()));
            hnslUser.setStatusSuperior(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getStatusSuperior()));
            hnslUser.setUserManger(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getUserManger()));
            hnslUser.setReferrerId(DesensitizationUtil.mobilePhoneDesensitization(hnslUser.getReferrerId()));
        }

        jsonObject.put("hnslUser",hnslUser);
        //查询所有下级
        LambdaQueryWrapper<HnslUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(HnslUser::getStatus,1)
                .eq(HnslUser::getStatusSuperior,hnslUser.getUserPhone());
        List<HnslUser> list = hnslUserService.list(userLambdaQueryWrapper);

        // 对下级用户列表进行脱敏处理
        if (list != null && !list.isEmpty()) {
            for (HnslUser user : list) {
                user.setUserName(DesensitizationUtil.desensitizedName(user.getUserName()));
                user.setUserSfz(DesensitizationUtil.idCardDesensitization(user.getUserSfz()));
                user.setUserPhone(DesensitizationUtil.mobilePhoneDesensitization(user.getUserPhone()));
                user.setUserPhoneTwo(DesensitizationUtil.mobilePhoneDesensitization(user.getUserPhoneTwo()));
                user.setUserSite(DesensitizationUtil.desensitizedAddress(user.getUserSite()));
                user.setUserMail(DesensitizationUtil.emailDesensitization(user.getUserMail()));
                user.setStatusSuperior(DesensitizationUtil.mobilePhoneDesensitization(user.getStatusSuperior()));
                user.setUserManger(DesensitizationUtil.mobilePhoneDesensitization(user.getUserManger()));
                user.setReferrerId(DesensitizationUtil.mobilePhoneDesensitization(user.getReferrerId()));
            }
        }

        jsonObject.put("userJuniorList",list);

        //查询学校消息
        LambdaQueryWrapper<HnslUserSchool> userSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userSchoolLambdaQueryWrapper.eq(HnslUserSchool::getUserPhone,hnslUser.getUserPhone());
        List<HnslUserSchool> hnslUserSchools = hnslUserSchoolService.list(userSchoolLambdaQueryWrapper);
        List<String> collect = hnslUserSchools.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());
        LambdaQueryWrapper<HnslSchool> hnslSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslSchoolLambdaQueryWrapper.in(HnslSchool::getSchoolCode,collect);
        List<HnslSchool> hnslSchoolList = hnslSchoolService.list(hnslSchoolLambdaQueryWrapper);
        jsonObject.put("hnslSchoolList",hnslSchoolList);

        //获取管理楼栋信息
        HnslBuilding hnslBuildingEntity = new HnslBuilding();
        hnslBuildingEntity.setUserId(hnslUser.getUserPhone());
        List<HnslBuilding> buildingList = hnslBuildingService.queryBuilding(hnslBuildingEntity);
        jsonObject.put("hnslBuildingList",buildingList);

        //根据手机号码获得银行对象
        LambdaQueryWrapper<HnslUserBankcard> userBankcardLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userBankcardLambdaQueryWrapper.eq(HnslUserBankcard::getUserId,hnslUser.getUserPhone());
        HnslUserBankcard one = hnslUserBankcardService.getOne(userBankcardLambdaQueryWrapper);

        // 对银行卡信息进行脱敏
        if (one != null) {
            // 假设HnslUserBankcard有银行卡号等敏感字段，使用acctNoDesensitization方法脱敏
            if (one.getBankNumber() != null) {
                one.setBankNumber(DesensitizationUtil.acctNoDesensitization(one.getBankNumber()));
            }
        }

        jsonObject.put("hnslUserBankcard",one);
        return success(jsonObject);
        // 使用关联查询
        //return success(hnslUserService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:save')")
    @OperationLog
    @ApiOperation("添加用户管理表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslUser hnslUser) {
        if(StringUtil.isEmpty(hnslUser.getUserName())){
            return fail("人员姓名不能为空");
        }
        String userPhone = hnslUser.getUserPhone();
        if(hnslUser.getChannelType()==0){
            return fail("来源渠道不能为空");
        }
        if (StringUtil.isEmpty(hnslUser.getCityCode()) || "0".equals(hnslUser.getCityCode())) {
            return fail("地市不能为空");
        }
        if (StringUtil.isEmpty(userPhone)) {
            return fail( "手机号不能为空");
        } else {
            //todo 因为没找到合适的正则表达式 暂时只对输入手机号做是否为数字限制
            /*// 校验手机号
            String regExp = "^[1](([3|5|8][\\d])|([4][4,5,6,7,8,9])|([6][2,5,6,7])|([7][^9])|([9][1,8,9]))[\\d]{8}$";*/
            if (!CheckStringUtils.isNumeric(userPhone)) {
                return fail("手机号不符合要求");
            }
            if(!StringUtil.isEmpty(hnslUser.getStatusSuperior())){
                if(!CheckStringUtils.isNumeric(hnslUser.getStatusSuperior())){
                    return fail( "上级手机号不符合要求");
                }
            }
        }
        LambdaQueryWrapper<HnslUser> hnslUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslUserLambdaQueryWrapper.eq(HnslUser::getUserPhone,hnslUser.getUserPhone());
        int count = hnslUserService.count(hnslUserLambdaQueryWrapper);
        if (count > 0) {
            return fail( "该用户已存在,新增失败");
        }

        if (StringUtil.isEmpty(hnslUser.getSchoolName()) && StringUtil.isEmpty(hnslUser.getSchoolCode())) {
            return fail( "人员无关联学校,新增失败");
        }
        //上级判断
        HnslUser userPojo=null;//上级合伙人信息
        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {
            hnslUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslUserLambdaQueryWrapper.eq(HnslUser::getUserPhone,hnslUser.getStatusSuperior());
            List<HnslUser> UserSj = hnslUserService.list(hnslUserLambdaQueryWrapper);
            if(UserSj!=null && UserSj.size()>0){
                userPojo=UserSj.get(0);
                if("2".equals(userPojo.getStatusSf()) || "3".equals(userPojo.getStatusSf())){
                    int sjLevel=userPojo.getStatusSf();
                    int level=hnslUser.getStatusSf();
                    if(sjLevel<level){
                        LambdaQueryWrapper<HnslUserSchool> hnslUserSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        hnslUserSchoolLambdaQueryWrapper.eq(HnslUserSchool::getSchoolCode,hnslUser.getSchoolCode())
                                .eq(HnslUserSchool::getUserPhone,hnslUser.getUserPhone());
                        List<HnslUserSchool> schoolList=hnslUserSchoolService.list(hnslUserSchoolLambdaQueryWrapper);
                        if(schoolList.size()==0){
                            return fail("上级和下级学校不一致");
                        }
                    }else  if("1".equals(userPojo.getStatusSf()) || "5".equals(userPojo.getStatusSf()) || "6".equals(userPojo.getStatusSf())){
                        return fail("上级只能为一二级合伙人");
                    }else{
                        return fail("该合伙人比上级合伙人等级高，无法新建账户");
                    }
                }else{
                    return fail("上级只能为一二级合伙人");
                }
                //查询团队是否存在
                if(!Objects.equals("4",userPojo.getStatusSf())){
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior())
                            .eq(HnslTeamMember::getTeamIdentityLevel,2);
                    List<HnslTeamMember> teamInfo = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                    if(teamInfo==null && teamInfo.isEmpty()){
                        return fail("上级合伙人无团队");
                    }
                }else{
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior());
                    List<HnslTeamMember> hnslTeamMemberEntity = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                    if(hnslTeamMemberEntity==null && hnslTeamMemberEntity.isEmpty()){
                        return fail("上级合伙人无团队");
                    }
                }
            }else{
                return fail("未查询到到该上级用户信息");
            }
        }
        SimpleDateFormat sdfCode = new SimpleDateFormat("yyyyMMddHHmmss");
        String userCode="SLID"+sdfCode.format(new Date())+String.valueOf((int)((Math.random()*6+1)*100000));;
        hnslUser.setUserCode(userCode);
        boolean save = hnslUserService.save(hnslUser);
        if (!save) {
            return fail("添加失败");
        }
        //插入用户学校关联数据
        HnslUserSchool hnslUserSchool = new HnslUserSchool();
        hnslUserSchool.setUserPhone(hnslUser.getUserPhone());
        String[] schoolCode = hnslUser.getSchoolCode().split(",");
        for (int i = 0; i < schoolCode.length; i++) {
            hnslUserSchool.setSchoolCode(schoolCode[i]);
            logger.info("用户学校关联对象" + hnslUserSchool);
            hnslUserSchoolService.save(hnslUserSchool);
        }
        //添加团队
        if("3".equals(hnslUser.getStatusSf()) ||
                "2".equals(hnslUser.getStatusSf())){//不是三级合伙人创建自己团队
            Random random = new Random();
            HnslTeam t=new HnslTeam();
            DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            String teamCode="SLTEAM"+format.format(new Date())+ random.nextInt(4)
                    + String.format("%03d", random.nextInt(1000));
            t.setAgentPoint(0);
            t.setCreatedDate(new Date());
            t.setStatus(1);
            t.setTeamCode(teamCode);
            if("2".equals(hnslUser.getStatusSf())) {
                t.setTeamLevel(1);
            }else if("3".equals(hnslUser.getStatusSf())) {
                t.setTeamLevel(2);
            }
            t.setTeamName("我的团队"+random.nextInt(5));
            t.setTeamType(3);
            t.setCreatedUser(hnslUser.getStatusSuperior());
            hnslTeamService.save(t);
            HnslTeamMember tm = new HnslTeamMember();
            tm.setStatus(1);
            tm.setTeamIdentityLevel(2);
            tm.setCreatedDate(new Date());
            tm.setCreatedUser(hnslUser.getUserPhone());
            tm.setTeamCode(teamCode);
            if("2".equals(hnslUser.getStatusSf())) {
                tm.setTeamIdentity(2);
            }else if("3".equals(hnslUser.getStatusSf())) {
                tm.setTeamIdentity(3);
            }
            tm.setTeamReferrer("");
            tm.setUserCode(hnslUser.getUserPhone());
            hnslTeamMemberService.save(tm);

            hnslUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslUserLambdaQueryWrapper.eq(HnslUser::getStatusSuperior,hnslUser.getUserPhone());
            List<HnslUser> hnslUserEntities = hnslUserService.list(hnslUserLambdaQueryWrapper);
            for(HnslUser users:hnslUserEntities){
                tm=new HnslTeamMember();
                tm.setCreatedDate(new Date());
                tm.setCreatedUser("admin");
                tm.setStatus(1);
                tm.setTeamIdentityLevel(1);
                tm.setTeamCode(teamCode);
                tm.setTeamIdentity(1);
                tm.setTeamIdentityLevel(1);
                tm.setTeamReferrer(users.getReferrerId());
                tm.setUserCode(users.getUserPhone());
                hnslTeamMemberService.save(tm);
            }
        }
        String teamCodes="";
        HashMap<String, Object> pMap = new HashMap<>();

        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {
            if("2".equals(userPojo.getStatusSf()) || "4".equals(userPojo.getStatusSf())
                    || "3".equals(userPojo.getStatusSf())){ //校园经理一上不添加团队
                if("4".equals(userPojo.getStatusSf())){ //上级是三级合伙人邀请
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior())
                            .eq(HnslTeamMember::getTeamIdentityLevel,1);
                    List<HnslTeamMember> teamInfo = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                    teamCodes=teamInfo.get(0).getTeamCode();
                }else{
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior());
                    List<HnslTeamMember> hnslTeamMemberEntity = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                    HnslTeamMember upTeam=hnslTeamMemberEntity.get(0);
                    teamCodes=upTeam.getTeamCode();
                }
                HnslTeamMember hnslTeamMemberEntity = new HnslTeamMember();
                hnslTeamMemberEntity.setStatus(1);
                hnslTeamMemberEntity.setCreatedDate(new Date());
                hnslTeamMemberEntity.setCreatedUser(userPojo.getUserPhone());
                hnslTeamMemberEntity.setTeamCode(teamCodes);
                hnslTeamMemberEntity.setTeamIdentity(1);
                hnslTeamMemberEntity.setTeamIdentityLevel(1);
                hnslTeamMemberEntity.setTeamReferrer(userPojo.getUserPhone());
                hnslTeamMemberEntity.setUserCode(hnslUser.getUserPhone());
                hnslTeamMemberService.save(hnslTeamMemberEntity);
            }
        }
        return success("添加成功");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:update')")
    @OperationLog
    @ApiOperation("修改用户管理表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslUser hnslUser) {
        User loginUser = getLoginUser();
        hnslUser.setUpdatedUser(loginUser.getUsername());
        hnslUser.setUpdatedDate(new Date());
        if(hnslUser.getChannelType()==0){
            return fail("来源渠道不能为空");
        }
        HnslUser hnslUserInit = hnslUserService.getById(hnslUser.getId());
        //修改前处理脱敏字段
        processDes(hnslUser, hnslUserInit);
        HnslUser userPojo=null;
        if (!StringUtil.isEmpty(hnslUser.getStatusSuperior())) {

            LambdaQueryWrapper<HnslUser> hnslUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslUserLambdaQueryWrapper.eq(HnslUser::getUserPhone,hnslUser.getStatusSuperior());
            List<HnslUser> UserSj = hnslUserService.list(hnslUserLambdaQueryWrapper);
            if(UserSj!=null && UserSj.size()>0){
                userPojo=UserSj.get(0);
                if("2".equals(userPojo.getStatusSf()) || "3".equals(userPojo.getStatusSf())){
                    int sjLevel=userPojo.getStatusSf();
                    int level=hnslUser.getStatusSf();
                    if(sjLevel<level){
                        Map<String,Object> towMap=new HashMap<String,Object>();
                        towMap.put("phone1",userPojo.getUserPhone());
                        towMap.put("phone2",hnslUserInit.getUserPhone());
                        int twoIndex=hnslUserService.querySchoolByUserPhoneTwo(towMap);
                        if(twoIndex==0){
                            return fail( "上级和下级学校不一致");
                        }
                    }else  if("1".equals(userPojo.getStatusSf()) || "5".equals(userPojo.getStatusSf()) || "6".equals(userPojo.getStatusSf())  || "4".equals(userPojo.getStatusSf())){
                        return fail( "上级只能为一二级合伙人");
                    }else{
                        return fail( "该合伙人比上级合伙人等级高，无法新建账户");
                    }
                }else{
                    return fail( "上级等级不能低于下级");
                }

                //查询团队是否存在
                if(!Objects.equals("4",userPojo.getStatusSf())){
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior())
                            .eq(HnslTeamMember::getTeamIdentityLevel,2);
                    List<HnslTeamMember> teamInfo = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                    if(teamInfo==null && teamInfo.isEmpty()){
                        return fail("上级合伙人无团队");
                    }
                }else{
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior());
                    List<HnslTeamMember> hnslTeamMemberEntity = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                    if(hnslTeamMemberEntity==null && hnslTeamMemberEntity.isEmpty()){
                        return fail("上级合伙人无团队");
                    }
                }
            }else{
                return fail( "未查询到到该上级用户信息");
            }

            if(hnslUser.getUserPhone().equals(hnslUser.getStatusSuperior())){
                return fail( "用户号码不能与上级号码一样");
            }
               /* if (!hnslUser.getUserPhone().equals(hnslUser.getUserPhoneTwo()) && !ADMIN.equals(userPhone)) {
                    return fail( "无权限修改用户账户！");
                }*/
            //修改上级的情况下调整团队
            if (!hnslUser.getStatusSuperior().equals(hnslUserInit.getStatusSuperior())) {
                //删除合伙人在其他团队的信息
                LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getUserCode,hnslUser.getUserPhone());
                hnslTeamMemberService.remove(hnslTeamMemberLambdaQueryWrapper);
                //切换上级团队信息
                String teamCodes="";
                HashMap<String, Object> pMap = new HashMap<>();
                if("2".equals(userPojo.getStatusSf()) || "4".equals(userPojo.getStatusSf())
                        || "3".equals(userPojo.getStatusSf())){ //校园经理一上不添加团队
                    if("4".equals(userPojo.getStatusSf())){ //上级是三级合伙人邀请
                        hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior())
                                .eq(HnslTeamMember::getTeamIdentityLevel,1);
                        List<HnslTeamMember> teamInfo = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                        teamCodes=teamInfo.get(0).getTeamCode();
                    }else{
                        hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslUser.getStatusSuperior());
                        List<HnslTeamMember> hnslTeamMemberEntity = hnslTeamMemberService.list(hnslTeamMemberLambdaQueryWrapper);
                        HnslTeamMember upTeam=hnslTeamMemberEntity.get(0);
                        teamCodes=upTeam.getTeamCode();
                    }
                    HnslTeamMember hnslTeamMemberEntity1 = new HnslTeamMember();
                    hnslTeamMemberEntity1.setStatus(1);
                    hnslTeamMemberEntity1.setCreatedDate(new Date());
                    hnslTeamMemberEntity1.setCreatedUser(hnslUser.getUserPhone());
                    hnslTeamMemberEntity1.setTeamCode(teamCodes);
                    hnslTeamMemberEntity1.setTeamIdentity(1);
                    hnslTeamMemberEntity1.setTeamIdentityLevel(1);
                    hnslTeamMemberEntity1.setTeamReferrer(hnslUser.getUserPhone());
                    hnslTeamMemberEntity1.setUserCode(hnslUser.getUserPhone());
                    hnslTeamMemberService.save(hnslTeamMemberEntity1);
                }
            }
            //   }
        }else{
            //删除合伙人在其他团队的信息
            LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getUserCode,hnslUser.getUserPhone());
            hnslTeamMemberService.remove(hnslTeamMemberLambdaQueryWrapper);
        }

        //判断合伙人等级是否修改
        if(!hnslUser.getStatusSf().equals(hnslUserInit.getStatusSf())){

            //删除 大于等于 合伙人当前等级的合伙人上级记录
            if(Objects.equals("3",hnslUser.getStatusSf())
                    || Objects.equals("4",hnslUser.getStatusSf())
                    || Objects.equals("2",hnslUser.getStatusSf())){
                HashMap<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("phone",hnslUser.getUserPhone());
                if(Objects.equals("3",hnslUser.getStatusSf())){
                    objectHashMap.put("array",new Integer[]{1,2,3});
                }else if(Objects.equals("4",hnslUser.getStatusSf())){
                    objectHashMap.put("array",new Integer[]{1,2,3,4});
                }else if(Objects.equals("2",hnslUser.getStatusSf())){
                    objectHashMap.put("array",new Integer[]{1,2});
                }
                hnslUserService.updateBySuperior(objectHashMap);
            }
            //修改团队信息
            LambdaQueryWrapper<HnslTeamMember> hnslUserLambdaQueryWrapper1 = new LambdaQueryWrapper<>();
            hnslUserLambdaQueryWrapper1.eq(HnslTeamMember::getUserCode,hnslUser.getUserPhone())
                    .eq(HnslTeamMember::getTeamIdentityLevel,2);
            HnslTeamMember hnslTeamMemberEntity = hnslTeamMemberService.getOne(hnslUserLambdaQueryWrapper1);
            if(hnslTeamMemberEntity!=null){//有团队
                //除一级 和二级以外的合伙人删除团队
                if(!(Objects.equals("2",hnslUser.getStatusSf())
                        || Objects.equals("3",hnslUser.getStatusSf()))){
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("teamCode",hnslTeamMemberEntity.getTeamCode());
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    LambdaQueryWrapper<HnslTeam> hnslTeamLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslTeamMemberEntity.getTeamCode());
                    hnslTeamLambdaQueryWrapper.eq(HnslTeam::getTeamCode,hnslTeamMemberEntity.getTeamCode());
                    hnslTeamMemberService.remove(hnslTeamMemberLambdaQueryWrapper);
                    hnslTeamService.remove(hnslTeamLambdaQueryWrapper);
                }else{
                    UpdateWrapper<HnslTeam> hnslTeamUpdateWrapper = new UpdateWrapper<>();
                    if("2".equals(hnslUser.getStatusSf())){
                        hnslTeamUpdateWrapper.set("TEAM_LEVEL",1);
                    }else if("3".equals(hnslUser.getStatusSf())){
                        hnslTeamUpdateWrapper.set("TEAM_LEVEL",2);
                    }else if("4".equals(hnslUser.getStatusSf())){
                        hnslTeamUpdateWrapper.set("TEAM_LEVEL",3);
                    }else{
                        hnslTeamUpdateWrapper.set("TEAM_LEVEL",0);
                    }
                    hnslTeamUpdateWrapper.eq("TEAM_CODE", hnslTeamMemberEntity.getTeamCode())
                                    .set("UPDATED_USER", loginUser.getUsername())
                                            .set("UPDATED_DATE",new Date());
                    hnslTeamService.update(hnslTeamUpdateWrapper);

                    //删除之前团队成员
                    HashMap<String, Object> hashMap1 = new HashMap<>();
                    hashMap1.put("teamCode",hnslTeamMemberEntity.getTeamCode());
                    hashMap1.put("teamIdentityLevel",1);
                    LambdaQueryWrapper<HnslTeamMember> hnslTeamMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslTeamMemberLambdaQueryWrapper.eq(HnslTeamMember::getTeamCode,hnslTeamMemberEntity.getTeamCode())
                                    .eq(HnslTeamMember::getTeamIdentityLevel,1);
                    hnslTeamMemberService.remove(hnslTeamMemberLambdaQueryWrapper);
                    //重新配置团队下级
                    LambdaQueryWrapper<HnslUser> hnslUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    hnslUserLambdaQueryWrapper.eq(HnslUser::getStatusSuperior,hnslUser.getUserPhone());
                    List<HnslUser> hnslUserEntities = hnslUserService.list(hnslUserLambdaQueryWrapper);
                    for(HnslUser users:hnslUserEntities){
                        HnslTeamMember tm=new HnslTeamMember();
                        tm.setCreatedDate(new Date());
                        tm.setCreatedUser("admin");
                        tm.setStatus(1);
                        tm.setTeamIdentityLevel(1);
                        tm.setTeamCode(hnslTeamMemberEntity.getTeamCode());
                        tm.setTeamIdentity(1);
                        tm.setTeamIdentityLevel(1);
                        tm.setTeamReferrer(users.getReferrerId());
                        tm.setUserCode(users.getUserPhone());
                        hnslTeamMemberService.save(tm);
                    }
                }
            }else{
                //无团队
                if(Objects.equals("2",hnslUser.getStatusSf())
                        || Objects.equals("3",hnslUser.getStatusSf())){//合伙人创建自己团队
                    Random random = new Random();
                    HnslTeam t=new HnslTeam();
                    DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
                    String teamCode="SLTEAM"+format.format(new Date())+ random.nextInt(4)
                            + String.format("%03d", random.nextInt(1000));
                    t.setAgentPoint(0);
                    t.setCreatedDate(new Date());
                    t.setStatus(1);
                    t.setTeamCode(teamCode);
                    if("2".equals(hnslUser.getStatusSf())) {
                        t.setTeamLevel(1);
                    }else if("3".equals(hnslUser.getStatusSf())) {
                        t.setTeamLevel(2);
                    }
                    t.setTeamName("我的团队"+format.format(new Date())+random.nextInt(5));
                    t.setTeamType(3);
                    t.setCreatedUser(hnslUser.getStatusSuperior());
                    hnslTeamService.save(t);
                    HnslTeamMember tm = new HnslTeamMember();
                    tm.setStatus(1);
                    tm.setTeamIdentityLevel(2);
                    tm.setCreatedDate(new Date());
                    tm.setCreatedUser(hnslUser.getUserPhone());
                    tm.setTeamCode(teamCode);
                    if("2".equals(hnslUser.getStatusSf())) {
                        tm.setTeamIdentity(2);
                    }else if("3".equals(hnslUser.getStatusSf())) {
                        tm.setTeamIdentity(3);
                    }
                    tm.setTeamReferrer("");
                    tm.setUserCode(hnslUser.getUserPhone());
                    hnslTeamMemberService.save(tm);
                    if("2".equals(hnslUser.getStatusSf())
                            || "3".equals(hnslUser.getStatusSf())){
                        LambdaQueryWrapper<HnslUser> hnslUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        hnslUserLambdaQueryWrapper.eq(HnslUser::getStatusSuperior,hnslUser.getUserPhone());
                        List<HnslUser> hnslUserEntities = hnslUserService.list(hnslUserLambdaQueryWrapper);
                        for(HnslUser users:hnslUserEntities){
                            HnslTeamMember tm1=new HnslTeamMember();
                            tm1.setCreatedDate(new Date());
                            tm1.setCreatedUser("admin");
                            tm1.setStatus(1);
                            tm1.setTeamIdentityLevel(1);
                            tm1.setTeamCode(teamCode);
                            tm1.setTeamIdentity(1);
                            tm1.setTeamIdentityLevel(1);
                            tm1.setTeamReferrer(users.getReferrerId());
                            tm1.setUserCode(users.getUserPhone());
                            hnslTeamMemberService.save(tm1);
                        }
                    }
                }
            }
        }

        if (!hnslUserService.updateById(hnslUser)) {
            return fail("修改失败");
        }

        if (!hnslUser.getUserPhone().equals(hnslUser.getUserPhoneTwo())) {
            Map<String, Object> map = new HashMap<>();
            // 用户已登陆 调用存储过程全修改
            map.put("newPhone", hnslUser.getUserPhone());
            map.put("userPhone", hnslUser.getUserPhoneTwo());
            map.put("v_user_name", loginUser.getUsername());
            hnslUserService.updateBYPhone(map);
        }


        // 修改关联学校 SchoolCode不为空则修改 否则修改失败
        if (!StringUtil.isEmpty(hnslUser.getSchoolCode())) {
            if (!hnslUser.getSchoolNameUpdate().equals(hnslUser.getSchoolName())) {
                LambdaQueryWrapper<HnslUserSchool> hnslUserSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
                hnslUserSchoolLambdaQueryWrapper.eq(HnslUserSchool::getUserPhone,hnslUser.getUserPhone());
                hnslUserSchoolService.remove(hnslUserSchoolLambdaQueryWrapper);
                HnslUserSchool userSchool = new HnslUserSchool();
                userSchool.setUserPhone(hnslUser.getUserPhone());
                String[] schoolCode = hnslUser.getSchoolCode().split(",");
                for (int i = 0; i < schoolCode.length; i++) {
                    userSchool.setSchoolCode(schoolCode[i]);
                    hnslUserSchoolService.save(userSchool);
                }
            }
        } else {
            return fail("修改失败,所属学校为空");
        }

        return success("修改成功");
    }

    private static void processDes(HnslUser hnslUser, HnslUser hnslUserInit) {
        // 处理敏感字段脱敏问题，如果传入的是带*号的脱敏数据，则使用原始值
        // 1. 检查姓名
        if (hnslUser.getUserName() != null && hnslUser.getUserName().contains("*")) {
            hnslUser.setUserName(hnslUserInit.getUserName());
        }

        // 2. 检查身份证号
        if (hnslUser.getUserSfz() != null && hnslUser.getUserSfz().contains("*")) {
            hnslUser.setUserSfz(hnslUserInit.getUserSfz());
        }

        // 3. 检查手机号码
        if (hnslUser.getUserPhone() != null && hnslUser.getUserPhone().contains("*")) {
            hnslUser.setUserPhone(hnslUserInit.getUserPhone());
        }

        // 4. 检查第二联系电话
        if (hnslUser.getUserPhoneTwo() != null && hnslUser.getUserPhoneTwo().contains("*")) {
            hnslUser.setUserPhoneTwo(hnslUserInit.getUserPhoneTwo());
        }

        // 5. 检查地址
        if (hnslUser.getUserSite() != null && hnslUser.getUserSite().contains("*")) {
            hnslUser.setUserSite(hnslUserInit.getUserSite());
        }

        // 6. 检查邮箱
        if (hnslUser.getUserMail() != null && hnslUser.getUserMail().contains("*")) {
            hnslUser.setUserMail(hnslUserInit.getUserMail());
        }

        // 7. 检查上级手机号码
        if (hnslUser.getStatusSuperior() != null && hnslUser.getStatusSuperior().contains("*")) {
            hnslUser.setStatusSuperior(hnslUserInit.getStatusSuperior());
        }

        // 8. 检查校园经理手机号码
        if (hnslUser.getUserManger() != null && hnslUser.getUserManger().contains("*")) {
            hnslUser.setUserManger(hnslUserInit.getUserManger());
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:remove')")
    @OperationLog
    @ApiOperation("删除用户管理表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslUserService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:save')")
    @OperationLog
    @ApiOperation("批量添加用户管理表")
    @PostMapping("/decode/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslUser> list) {
        if (hnslUserService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:update')")
    @OperationLog
    @ApiOperation("批量修改用户管理表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslUser> batchParam) {
        if (batchParam.update(hnslUserService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:remove')")
    @OperationLog
    @ApiOperation("批量删除用户管理表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        for(Integer id:ids){
            HnslUser hnslUserEntity = hnslUserService.getById(id);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("userPhone",hnslUserEntity.getUserPhone());
            hnslUserService.deleteBYPhone(hashMap);
        }
        if (hnslUserService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:importUser')")
    @OperationLog
    @ApiOperation("导入用户")
    @PostMapping("/decode/importUser")
    public ApiResult<?> importUser(MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        String image = "xls,xlsx";

        if (!file.isEmpty()) {
            ServletContext servletContext = request.getSession().getServletContext();
            String uploadPath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXls(uploadPath, newname, 6, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 6, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);

                if (numberList != null) {
                    //将数据转化成user对象
                    List<HnslUser> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslUser userPojo = new HnslUser();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            /*if (StringUtil.isEmpty(name)) {
                                continue;
                            }else */
                            //姓名
                            if ("0".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户姓名不能为空");
                                }
                                userPojo.setUserName(StringUtil.trimString(userMap.get(name)));
                            }
                            //联系电话
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户联系电话不能为空");
                                }
                                if(StringUtil.trimString(userMap.get(name)).length()!=11){
                                    return fail("导入用户联系电话位数错误");
                                }
                                userPojo.setUserPhone(StringUtil.trimString(userMap.get(name)));
                            }
                            //揽机工号
                            else if ("2".equalsIgnoreCase(name.trim())) {
                               /* if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户揽机工号不能为空");
                                }*/
                                userPojo.setNumbers(StringUtil.trimString(userMap.get(name)));
                            }
                            //地址
                            else if ("3".equalsIgnoreCase(name.trim())) {
                                userPojo.setUserSite(StringUtil.trimString(userMap.get(name)));
                            }
                            //销售员编码
                            else if ("4".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户销售员编码不能为空");
                                }
                                Pattern pattern =Pattern.compile("^Y\\d{11}$",Pattern.CASE_INSENSITIVE);
                                Matcher matcher = pattern.matcher(StringUtil.trimString(userMap.get(name)));
                                if(!matcher.matches()){
                                    return fail("导入用户销售员编码格式错误，格式为Y12345678910");
                                }
                                userPojo.setSalesCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //身份
                            else if ("5".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户身份不能为空");
                                }
                                if(!("1".equals(StringUtil.trimString(userMap.get(name)))
                                        || "2".equals(StringUtil.trimString(userMap.get(name)))
                                        || "3".equals(StringUtil.trimString(userMap.get(name)))
                                        || "4".equals(StringUtil.trimString(userMap.get(name)))
                                        || "5".equals(StringUtil.trimString(userMap.get(name)))
                                        || "6".equals(StringUtil.trimString(userMap.get(name))))){
                                    return fail("导入用户身份等级错误");
                                }
                                // 限制非admin用户不能导入省级管理员
//                                if (!"admin".equals(userName)) {
//                                    if ("5".equals(StringUtil.trimString(userMap.get(name)))) {
//                                        return fail("导入用户身份权限不足");
//                                    }
//                                }
//                                if (flag) {
//                                    if (Integer.valueOf(StringUtil.trimString(userMap.get(name))) > Integer.valueOf(statusSf)) {
//                                        return fail("导入用户身份权限不足");
//                                    }
//                                }
                                userPojo.setStatusSf(Integer.parseInt(StringUtil.trimString(userMap.get(name))));
                            }
                            //地市ID
                            else if ("6".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户地市ID不能为空");
                                }
                                userPojo.setCityCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //上级ID
                            else if ("7".equalsIgnoreCase(name.trim())) {
                                userPojo.setStatusSuperior(StringUtil.trimString(userMap.get(name)));
                            }
                            //推荐人ID
                            else if ("8".equalsIgnoreCase(name.trim())) {
                                userPojo.setReferrerId(StringUtil.trimString(userMap.get(name)));
                            }
                            //所属校园经理ID
                            else if ("9".equalsIgnoreCase(name.trim())) {
                                userPojo.setUserManger(StringUtil.trimString(userMap.get(name)));
                            }
                            //学校名称
                            else if ("10".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户学校名称不能为空");
                                }
                                userPojo.setSchoolName(StringUtil.trimString(userMap.get(name)));
                            }
                            //学校编码
                            else if ("11".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户学校编码不能为空");
                                }
                                userPojo.setSchoolCode(StringUtil.trimString(userMap.get(name)));
                            }//合伙人身份证
                            else if ("12".equalsIgnoreCase(name.trim())) {
                                if (StringUtil.isEmpty(StringUtil.trimString(userMap.get(name)))) {
                                    return fail("导入用户身份证不能为空");
                                }
                                userPojo.setUserSfz(StringUtil.trimString(userMap.get(name)));
                            }else //来源渠道
                                if ("13".equalsIgnoreCase(name.trim())) {
                                    String channelType=StringUtil.trimString(userMap.get(name));
                                    if (StringUtil.isEmpty(channelType)) {
                                        return fail("导入渠道类型不能为空");
                                    }
                                    boolean typeBool=true;
                                    String[] types={"1","2","3","4","5"};
                                    for(String type:types){
                                        if(Objects.equals(type,channelType)){
                                            typeBool=false;
                                        }
                                    }
                                    if(typeBool){
                                        return fail("请输入指定的渠道编号");
                                    }
                                    userPojo.setChannelType(Integer.parseInt(channelType));
                                }else //团队名称
                                    if ("14".equalsIgnoreCase(name.trim())) {
                                        userPojo.setTeamName(StringUtil.trimString(userMap.get(name)));
                                    }
                        }
                        if(userPojo.getUserPhone().equals(userPojo.getStatusSuperior())){
                            return fail("上级账号不能相同，无上级可不填");
                        }
                        if (StringUtil.isEmpty(userPojo.getUserSfz())) {
                            return fail("导入用户身份证不能为空");
                        }
                        if (StringUtil.isEmpty(String.valueOf(userPojo.getChannelType())) || userPojo.getChannelType()==0) {
                            return fail("导入渠道类型不能为空");
                        }
                        hnslUserList.add(userPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslUserService.saveUserArray(hnslUserList, request);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            HashMap<String, String> r = new HashMap<>();
                            r.put("mes", "exportDaoUsers");
                            r.put("resultCode", "6");
                            r.put("fileName", saveUserArray.get("fileName"));
                            return success(r);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        logger.error("批量插入用户信息失败:" + e);
                        return fail("批量插入用户信息失败:" + e);
                    }
                } else {
                    return fail("文件内容为空，或者解析失败");
                }
                return fail("文件内容为空，或者解析失败");
            } catch (Exception e) {
                return fail("文件上传接口上传异常:" + e);
            }
        } else {
            return fail("上传文件为空");
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:update')")
    @OperationLog
    @ApiOperation("禁用或允许用户")
    @PostMapping("/decode/disableUser")
    public ApiResult<?> outputUserJiFei(@RequestBody HnslUser hnslUser) {
//        HnslUser hnslUserDes = hnslUserService.getById(hnslUser.getId());
        hnslUserService.updateById(hnslUser);
//        User loginUser = getLoginUser();
//        HnslUserupdateRecord userUpdate = new HnslUserupdateRecord();
//        userUpdate.setUpdatedUser(userPhone);
//        userUpdate.setUserupdatePhone(hnslUserDes.getUserPhone());
//        userUpdate.setUpdatedDate(new Date());
//        userUpdate.setStatus(1L);
//        String detailsName = null;
//        if (hnslUser.getStatus() == 1) {
//            detailsName = "管理员:{" + loginUser.getUsername() + "}允许用户:{" + hnslUserDes.getUserName() + "}";
//        } else {
//            detailsName = "管理员:{" + loginUser.getUsername() + "}禁用用户:{" + hnslUserDes.getUserName() + "}";
//        }
//        userUpdate.setUserupdateDetails(detailsName);
//        hnslUserupdateRecordService.save(userUpdate);
        return success("修改成功");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:exportUser')")
    @OperationLog
    @ApiOperation("根据condition导出扫楼用户数据（新版已迁移）")
    @PostMapping("/exportUserIntegrate")
    public ApiResult<?> exportUser(@RequestBody JSONObject conditionStr, HttpServletRequest request, HttpServletResponse response) {
        logger.info("导出用户查询条件: " + conditionStr.toString());
        Map<String, Object> conditionMap = (Map<String, Object>) JSON.parse(conditionStr.toString());

        User user = getLoginUser();
        String UserPhone = (String) redisUtil.get("thisUserPhone" + user.getPhone());
        String UserSf = (String) redisUtil.get("thisUserSf" + user.getPhone());
        String cityCode = (String) redisUtil.get("cityCode" + user.getPhone());
        String hnslType = (String) redisUtil.get("hnslChannel" + user.getPhone());
        conditionMap.put("loginUserSf", UserSf); // 查询语句判断各个身份导出数据权限
        // 如果是地市管理员 则能够导出所属地市的全部人员
        if ("6".equals(UserSf)) {
            conditionMap.put("hnslChannel", hnslType);
            if (!StringUtil.isEmpty(cityCode)) {
                conditionMap.put("cityCode", cityCode);
            }
        }
        // 如果是校园经理 则能够导出其管理下的校园所有人员
        if ("1".equals(UserSf)) {
            // 根据手机号查询管理校园编码
//            String schoolCode = hnslUserService.querySchoolCodeByPhone(UserPhone);
//            if (!StringUtil.isEmpty(schoolCode)) {
//                conditionMap.put("schoolCode", schoolCode);
//            }
//            conditionMap.put("hnslChannel", hnslType);
            List<String> schoolCode = hnslUserService.querySchoolCodeByPhone(UserPhone);
            if (!schoolCode.isEmpty() && schoolCode.size() > 0) {
                conditionMap.put("schoolCode", schoolCode);
            }
            conditionMap.put("hnslChannel", hnslType);
        }
        try {
            //根据条件查询出数据
            conditionMap.put("page", null);
            List<Map<String, String>> resultList = hnslIntegralService.queryExportIntegralList(conditionMap);
            if (resultList == null || resultList.size() == 0) {
                logger.info("exportUser --> resultList is null");
                HashMap<String, Object> resultMap = new HashMap<>();
                resultMap.put("code", "-2");
                return success(resultMap);
            }
            XSSFWorkbook wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
            XSSFRow row = sheet.createRow((int) 0);
            XSSFCellStyle style = wb.createCellStyle();
            XSSFCell cell = row.createCell(0);
            // 设置字体
            XSSFFont font = wb.createFont();
            //设置20列及列宽
            for (int i = 0; i < 22; i++) {
                if (i == 2 || i == 5 || i == 7 || i == 9 || i == 10 || i == 14) {
                    sheet.setColumnWidth(i, 20 * 300);
                    continue;
                }
                sheet.setColumnWidth(i, 20 * 140);
            }

            // 在sheet表中添加第一行表头
            // 合并第一行单元格
            CellRangeAddress region = new CellRangeAddress(0, 0, 0, 21);
            sheet.addMergedRegion(region);
            // 创建单元格，设置值表头，设置表头居中

            style.setAlignment(HorizontalAlignment.CENTER);

            // 设置字体
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 11);
            font.setBold(true);

            style.setFont(font);

            cell.setCellValue("智慧扫楼积分信息详表");
            cell.setCellStyle(style);

            //设置第二行 行最后单元格显示月份
            XSSFRow row1 = sheet.createRow(1);
            XSSFCell cell1 = row1.createCell(19);

            XSSFCellStyle style1 = wb.createCellStyle();
            //设置字体,黄色背景 及居中显示
            XSSFFont font1 = wb.createFont();
            font1.setFontName("宋体");
            font1.setFontHeightInPoints((short) 9);
            style1.setFont(font1);
            style1.setAlignment(HorizontalAlignment.CENTER);
            Calendar c = Calendar.getInstance();
            cell1.setCellValue("月份:" + c.get(Calendar.YEAR) + "." + (c.get(Calendar.MONTH) + 1));
            cell1.setCellStyle(style1);


            // 设置第三行数据字段 添加至excel
            Map<String, String> columnMap = new HashMap<>();
            columnMap.put("0", "序号");
            columnMap.put("1", "姓名");
            columnMap.put("2", "合伙人级别");
            columnMap.put("3", "合伙人身份");
            columnMap.put("4", "身份证号");
            columnMap.put("5", "手机号码");
            columnMap.put("6", "本月新装积分");
            columnMap.put("7", "本月扫楼积分");
            columnMap.put("8", "本月任务积分");
            columnMap.put("9", "本月奖励积分");
            columnMap.put("10", "本月积分小计");
            columnMap.put("11", "累计新装积分");
            columnMap.put("12", "累计扫楼积分");
            columnMap.put("13", "累计任务积分");
            columnMap.put("14", "累计奖励积分");
            columnMap.put("15", "累计下级积分");
            columnMap.put("16", "累计总积分");
            columnMap.put("17", "上级工号");
            columnMap.put("18", "上级姓名");
            columnMap.put("19", "来源渠道");

            XSSFRow row2 = sheet.createRow(2);

            XSSFCellStyle style2 = wb.createCellStyle();

            // 居中格式
            style2.setAlignment(HorizontalAlignment.CENTER);
            for (int i = 0; i < 20; i++) {
                XSSFCell cell2 = row2.createCell(i);
                cell2.setCellValue(columnMap.get(i + ""));
                cell2.setCellStyle(style2);
            }


            // 将数据写入excel
            if (null != resultList && resultList.size() != 0) {
                // 遍历结果集
                for (int i = 0; i < resultList.size(); i++) {
                    Map<String, String> resultMap = resultList.get(i);
                    // 填写数据从第三行开始
                    row = sheet.createRow((int) i + 3);
                    // 序列
                    row.createCell(0).setCellValue(String.valueOf(i + 1));
                    // 姓名
                    if (!StringUtil.isEmpty(resultMap.get("USER_NAME"))) {
                        // row.createCell(1).setCellValue(DesensitizationUtil.desensitizedName(resultMap.get("USER_NAME")));
                        row.createCell(1).setCellValue(resultMap.get("USER_NAME"));
                    }
                    // 合伙人级别
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("STATUS_SF")))) {
                        if ("1".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(2).setCellValue("校园经理");
                        } else if ("2".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(2).setCellValue("一级合伙人");
                        } else if ("3".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(2).setCellValue("二级合伙人");
                        } else if ("4".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(2).setCellValue("三级合伙人");
                        } else if ("5".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(2).setCellValue("省级管理员");
                        } else if ("6".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(2).setCellValue("地市管理员");
                        }
                    }
                    // 合伙人身份
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("BL")))) {
                        if ("0".equals(String.valueOf(resultMap.get("BL")))) {
                            row.createCell(3).setCellValue("非楼长");
                        } else {
                            row.createCell(3).setCellValue("楼长");
                        }
                    }
                    // 身份证号
                    if (!StringUtil.isEmpty(resultMap.get("USER_SFZ"))) {
                        // row.createCell(4).setCellValue(DesensitizationUtil.acctNoDesensitization(resultMap.get("USER_SFZ")));
                        row.createCell(4).setCellValue(resultMap.get("USER_SFZ"));
                    }
                    // 手机号码
                    if (!StringUtil.isEmpty(resultMap.get("USER_PHONE"))) {
                        row.createCell(5).setCellValue(resultMap.get("USER_PHONE"));
                        //  row.createCell(5).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(resultMap.get("USER_PHONE")));
                    }
                    // 本月新装积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S1")))) {
                        row.createCell(6).setCellValue(String.valueOf(resultMap.get("S1")));
                    }
                    // 本月扫楼积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S3")))) {
                        row.createCell(7).setCellValue(String.valueOf(resultMap.get("S3")));
                    }
                    // 本月任务积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S4")))) {
                        row.createCell(8).setCellValue(String.valueOf(resultMap.get("S4")));
                    }
                    // 本月奖励积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S5")))) {
                        row.createCell(9).setCellValue(String.valueOf(resultMap.get("S5")));
                    }
                    // 本月积分小计
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("ZJ")))) {
                        row.createCell(10).setCellValue(String.valueOf(resultMap.get("ZJ")));
                    }
                    // 累计新装积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S1P")))) {
                        row.createCell(11).setCellValue(String.valueOf(resultMap.get("S1P")));
                    }
                    // 本月扫楼积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S3P")))) {
                        row.createCell(12).setCellValue(String.valueOf(resultMap.get("S3P")));
                    }
                    //累计任务积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S4P")))) {
                        row.createCell(13).setCellValue(String.valueOf(resultMap.get("S4P")));
                    }
                    // 累计奖励积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S5P")))) {
                        row.createCell(14).setCellValue(String.valueOf(resultMap.get("S5P")));
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("userPhone", String.valueOf(resultMap.get("USER_PHONE")));

                    // 累计下级积分
                    if ("4".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                        if (!StringUtil.isEmpty(String.valueOf(resultMap.get("INTEGRAL")))) {
                            row.createCell(15).setCellValue(0);
                            row.createCell(16).setCellValue(String.valueOf(resultMap.get("INTEGRAL")));
                        }
                    } else {
                        if (!StringUtil.isEmpty(String.valueOf(resultMap.get("BL"))) && !"0".equals(String.valueOf(resultMap.get("BL")))) {
                            map.put("type", "2");
                            List<Map<String, String>> byJF = hnslUserService.queryALLStatusSuperior(map);
                            int zise = 0;
                            for (Map<String, String> list : byJF) {
                                if ("0".equals(String.valueOf(list.get("SL")))
                                        || StringUtil.isEmpty(String.valueOf(list.get("SL")))) {
                                    if (!StringUtil.isEmpty(String.valueOf(list.get("INTEGRALS")))) {
                                        zise += Integer.parseInt(String.valueOf(list.get("INTEGRALS")));
                                    }
                                }
                            }
                            row.createCell(15).setCellValue(zise);
                            if (!StringUtil.isEmpty(String.valueOf(resultMap.get("INTEGRAL")))) {
                                row.createCell(16).setCellValue(Integer.parseInt(String.valueOf(resultMap.get("INTEGRAL"))) + zise);
                            } else {
                                row.createCell(16).setCellValue(zise);
                            }

                        } else {
                            if (!StringUtil.isEmpty(String.valueOf(resultMap.get("INTEGRAL")))) {
                                row.createCell(15).setCellValue(0);
                                row.createCell(16).setCellValue(String.valueOf(resultMap.get("INTEGRAL")));
                            }
                        }
                    }

                    // 上级号码
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("STATUS_SUPERIOR"))))) {
                        row.createCell(17).setCellValue(String.valueOf(resultMap.get("STATUS_SUPERIOR")));
                    }
                    // 上级姓名
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("NAME_SUPERIOR"))))) {
                        row.createCell(18).setCellValue(String.valueOf(resultMap.get("NAME_SUPERIOR")));
                    }
                    // 渠道-1 中小学2 高校（校园经理/管理员/学子支撑）3 学子公司（学子合伙人）4 校园店（门店代理商）5 泛渠道（合作直销
                    if (!StringUtil.isEmpty(String.valueOf(String.valueOf(resultMap.get("CHANNEL_TYPE"))))) {
                        String channelType = String.valueOf(resultMap.get("CHANNEL_TYPE"));
                        if ("1".equals(channelType)) {
                            row.createCell(19).setCellValue("中小学");
                        } else if ("2".equals(channelType)) {
                            row.createCell(19).setCellValue("高校");
                        } else if ("3".equals(channelType)) {
                            row.createCell(19).setCellValue("学子公司");
                        } else if ("4".equals(channelType)) {
                            row.createCell(19).setCellValue("校园店");
                        } else if ("5".equals(channelType)) {
                            row.createCell(19).setCellValue("泛渠道");
                        }

                    }
                }
            }
            try {
                WaterMarkUtil.insertWaterMarkTextToXlsxEntrance(wb, user.getUsername() + " " + user.getNickname());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            // 修改为存放到webapps/downloads目录下
            String webappsPath = System.getProperty("user.dir") + File.separator + "src" + File.separator + "main" + File.separator + "webapps" + File.separator + "downloads" + File.separator;
            // 确保目录存在
            File dir = new File(webappsPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            logger.info("智慧扫楼导出积分信息详表file文件路径:" + webappsPath);
            String fileName = "智慧扫楼积分信息详表";
            File file = new File(webappsPath + fileName + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                
                // 上传到S3文件服务器
                String s3FilePath = fileName + "_" + System.currentTimeMillis() + ".xls";
                awsS3Utils.putFile(s3FilePath, wb);
                logger.info("文件已上传到S3文件服务器，路径：" + s3FilePath);
                
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) {//判断是否是文件
                    Map<String, Object> map = new HashedMap();
                    map.put("code", "6");
                    map.put("msg", "downloadExportFileIg");
                    map.put("fileName", fileName);
                    return success(map);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("hnsluser exportUser exception error : " + e.getMessage());
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("code", "-1");
            return success(resultMap);
        }
        Map<String, Object> map = new HashedMap();
        map.put("code", "6");
        map.put("msg", "downloadExportFileIg");
        map.put("fileName", "智慧扫楼积分信息详表");
        return success(map);
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:exportUser')")
    @OperationLog
    @ApiOperation("用户详情导出")
    @PostMapping("/exportUser")
    public ApiResult<?> exportUserIntegrate(@RequestBody JSONObject conditionStr, HttpServletRequest request, HttpServletResponse response) {
        logger.info("导出查询积分条件: " + conditionStr.toString());
        ServletContext servletContext = request.getSession().getServletContext();
        Map<String, Object> conditionMap = (Map<String, Object>) JSON.parse(conditionStr.toString());

        // 获取当前用户信息并设置条件
        User user = DesensitizationUtil.getUser();
        
        // TODO: 如需恢复用户权限检查，可以取消下面注释
        /*
        String UserPhone = redisUtils.get("thisUserPhone" + user.getMobile());
        String UserSf = redisUtils.get("thisUserSf" + user.getMobile());
        String cityCode = redisUtils.get("cityCode" + user.getMobile());
        String hnslType = redisUtils.get("hnslChannel" + user.getMobile());
        conditionMap.put("loginUserSf", UserSf); // 查询语句判断各个身份导出数据权限
        conditionMap.put("hnslChannel", hnslType);
        
        // 如果是地市管理员 则能够导出所属地市的全部人员
        if ("6".equals(UserSf)) {
            if (!StringUtil.isEmpty(cityCode)) {
                conditionMap.put("cityCode", cityCode);
            }
        }
        
        // 如果是校园经理 则能够导出其管理下的校园所有人员
        if ("1".equals(UserSf)) {
            // 根据手机号查询管理校园编码
            List<String> schoolCode = hnslUserService.querySchoolCodeByPhone(UserPhone);
            if (!schoolCode.isEmpty() && schoolCode.size() > 0) {
                conditionMap.put("schoolCode", schoolCode);
            }
        }
        */
        
        try {
            //根据条件查询出数据
            conditionMap.put("page", null);
            List<Map<String, String>> resultList = hnslUserService.queryExportUserList(conditionMap);
            if (resultList == null || resultList.size() == 0) {
                logger.info("exportUser --> resultList is null");
                return success("exportUser --> resultList is null");
            }
            
            // 生成一条记录
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
            String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
            String fileName = "合伙人信息" + batchCode;
            String filePath = "uploads/" + fileName;
            logger.info("导出路径" + filePath);
            Integer activeEvent = 7;
            Integer appSettingId = (Integer) conditionMap.get("appSettingId");
            Integer list = resultList.size();
            queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, list, appSettingId);

            HSSFWorkbook wb = new HSSFWorkbook();
            HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));

            //设置列宽，扩展到31列
            for (int i = 0; i < 31; i++) {
                if (i == 2 || i == 5 || i == 7 || i == 9 || i == 10 || i == 14) {
                    sheet.setColumnWidth(i, 20 * 300);
                    continue;
                }
                sheet.setColumnWidth(i, 20 * 140);
            }

            // 在sheet表中添加第一行表头
            HSSFRow row = sheet.createRow((int) 0);
            // 合并第一行单元格
            CellRangeAddress region = new CellRangeAddress(0, 0, 0, 30);
            sheet.addMergedRegion(region);
            // 创建单元格，设置值表头，设置表头居中
            HSSFCellStyle style = wb.createCellStyle();

            style.setAlignment(HorizontalAlignment.CENTER);

            // 设置字体
            HSSFFont font = wb.createFont();
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 11);
            font.setBold(true);

            style.setFont(font);

            HSSFCell cell = row.createCell(0);
            cell.setCellValue("智慧扫楼人员信息详表");
            cell.setCellStyle(style);

            //设置第二行 行最后单元格显示月份
            HSSFRow row1 = sheet.createRow(1);
            HSSFCell cell1 = row1.createCell(19);

            HSSFCellStyle style1 = wb.createCellStyle();
            //设置字体,黄色背景 及居中显示
            HSSFFont font1 = wb.createFont();
            font1.setFontName("宋体");
            font1.setFontHeightInPoints((short) 9);
            style1.setFont(font1);
            style.setAlignment(HorizontalAlignment.CENTER);
            Calendar c = Calendar.getInstance();
            cell1.setCellValue("月份:" + c.get(Calendar.YEAR) + "." + (c.get(Calendar.MONTH) + 1));
            cell1.setCellStyle(style1);

            // 设置第三行数据字段 添加至excel
            Map<String, String> columnMap = new HashMap<>();
            columnMap.put("0", "序号");
            columnMap.put("1", "地市");
            columnMap.put("2", "学校");
            columnMap.put("3", "学校编号");
            columnMap.put("4", "合伙人用户ID");
            columnMap.put("5", "姓名");
            columnMap.put("6", "手机号码");
            columnMap.put("7", "性别");
            columnMap.put("8", "身份证号");
            columnMap.put("9", "年龄");
            columnMap.put("10", "来源渠道");
            columnMap.put("11", "注册时间");
            columnMap.put("12", "地址");
            columnMap.put("13", "工行银行卡号");
            columnMap.put("14", "身份（合伙人级别）");
            columnMap.put("15", "管理楼栋");
            columnMap.put("16", "服务QQ群号");
            columnMap.put("17", "QQ群人数");
            columnMap.put("18", "销售员编码");
            columnMap.put("19", "本月开卡数");
            columnMap.put("20", "本月积分");
            columnMap.put("21", "本月绩效");
            columnMap.put("22", "累计开卡数");
            columnMap.put("23", "上级联系号码");
            columnMap.put("24", "上级合伙人用户ID");
            columnMap.put("25", "上级姓名");
            columnMap.put("26", "是否有效");
            columnMap.put("27", "上上级合伙人ID");
            columnMap.put("28", "上上级姓名");
            columnMap.put("29", "上上级手机号");
            columnMap.put("30", "最近登录时间");

            HSSFRow row2 = sheet.createRow(2);
            HSSFCellStyle style2 = wb.createCellStyle();
            // 居中格式
            style2.setAlignment(HorizontalAlignment.CENTER);
            
            for (int i = 0; i < 31; i++) {
                HSSFCell cell2 = row2.createCell(i);
                cell2.setCellValue(columnMap.get(i + ""));
                cell2.setCellStyle(style2);
            }

            // 将数据写入excel
            if (null != resultList && resultList.size() != 0) {
                // 遍历结果集
                for (int i = 0; i < resultList.size(); i++) {
                    Map<String, String> resultMap = resultList.get(i);

                    // 填写数据从第三行开始
                    row = sheet.createRow((int) i + 3);
                    // 序列
                    row.createCell(0).setCellValue(String.valueOf(i + 1));
                    // 城市 CITY_CODE
                    if (!StringUtil.isEmpty(resultMap.get("CITY_CODE"))) {
                        // 使用城市代码转换为城市名称
                        row.createCell(1).setCellValue(InterfaceUtil.getCodeToName(resultMap.get("CITY_CODE")));
                    }
                    // 学校 school_name
                    if (!StringUtil.isEmpty(resultMap.get("SCHOOL_NAME"))) {
                        row.createCell(2).setCellValue(StringUtil.filtration(resultMap.get("SCHOOL_NAME")));
                    }
                    // 学校编码 school_code
                    if (!StringUtil.isEmpty(resultMap.get("SCHOOL_CODE"))) {
                        row.createCell(3).setCellValue(StringUtil.filtration(resultMap.get("SCHOOL_CODE")));
                    }
                    // 合伙人用户ID
                    if (null != resultMap.get("id")) {
                        row.createCell(4).setCellValue(String.valueOf(resultMap.get("id")));
                    }
                    // 姓名 user_name
                    if (!StringUtil.isEmpty(resultMap.get("USER_NAME"))) {
                        row.createCell(5).setCellValue(StringUtil.filtration(resultMap.get("USER_NAME")));
                    }
                    // 手机号码 user_phone
                    if (!StringUtil.isEmpty(resultMap.get("USER_PHONE"))) {
                        row.createCell(6).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.filtration(resultMap.get("USER_PHONE"))));
                    }
                    // 性别 user_gender
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("USER_GENDER")))) {
                        if ("2".equals(String.valueOf(resultMap.get("USER_GENDER")))) {
                            row.createCell(7).setCellValue("女");
                        } else if ("1".equals(String.valueOf(resultMap.get("USER_GENDER")))) {
                            row.createCell(7).setCellValue("男");
                        } else {
                            row.createCell(7).setCellValue(" ");
                        }
                    }
                    // 身份证号 user_sfz
                    if (!StringUtil.isEmpty(resultMap.get("USER_SFZ"))) {
                        String userSfz = StringUtil.filtration(resultMap.get("USER_SFZ"));
                        row.createCell(8).setCellValue(DesensitizationUtil.acctNoDesensitization(userSfz));
                        
                        // 年龄
                        if (userSfz.length() == 18) {
                            row.createCell(9).setCellValue(getAgeFromIDCard(userSfz.substring(6, 14)));
                        } else {
                            row.createCell(9).setCellValue("");
                        }
                    }
                    
                    // 来源渠道
                    String channelType = String.valueOf(resultMap.get("CHANNEL_TYPE"));
                    if ("1".equals(channelType)) {
                        row.createCell(10).setCellValue("中小学");
                    } else if ("2".equals(channelType)) {
                        row.createCell(10).setCellValue("高校");
                    } else if ("3".equals(channelType)) {
                        row.createCell(10).setCellValue("学子公司");
                    } else if ("4".equals(channelType)) {
                        row.createCell(10).setCellValue("校园店");
                    } else if ("5".equals(channelType)) {
                        row.createCell(10).setCellValue("泛渠道");
                    } else {
                        row.createCell(10).setCellValue("");
                    }
                    
                    // 注册时间
                    row.createCell(11).setCellValue(String.valueOf(resultMap.get("CREATED_DATE")));
                    
                    // 地址 user_site
                    if (!StringUtil.isEmpty(resultMap.get("USER_SITE"))) {
                        row.createCell(12).setCellValue(StringUtil.filtration(resultMap.get("USER_SITE")));
                    }
                    // 工行银行卡号 Bank_Number
                    if (!StringUtil.isEmpty(resultMap.get("BANK_NUMBER"))) {
                        row.createCell(13).setCellValue(StringUtil.filtration(resultMap.get("BANK_NUMBER")));
                    }
                    // 身份（合伙人级别）status_sf
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("STATUS_SF")))) {
                        if ("1".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(14).setCellValue("校园经理");
                        } else if ("2".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(14).setCellValue("一级合伙人");
                        } else if ("3".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(14).setCellValue("二级合伙人");
                        } else if ("4".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(14).setCellValue("三级合伙人");
                        } else if ("5".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(14).setCellValue("省级管理员");
                        } else if ("6".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(14).setCellValue("地市管理员");
                        }
                    }
                    // 管理楼栋 Building
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("BUILDING")))) {
                        if ("0".equals(String.valueOf(resultMap.get("BUILDING")))) {
                            row.createCell(15).setCellValue("否");
                        } else if ("1".equals(String.valueOf(resultMap.get("BUILDING")))) {
                            row.createCell(15).setCellValue("是");
                        }
                    }
                    // 服务QQ群号 qqflock
                    if (!StringUtil.isEmpty(resultMap.get("QQFLOCK"))) {
                        row.createCell(16).setCellValue(StringUtil.filtration(resultMap.get("QQFLOCK")));
                    }
                    // QQ群人数 qqflock_number
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("QQFLOCK_NUMBER")))) {
                        row.createCell(17).setCellValue(StringUtil.filtration(String.valueOf(resultMap.get("QQFLOCK_NUMBER"))));
                    }
                    // 销售员编码 sales_code
                    if (!StringUtil.isEmpty(resultMap.get("SALES_CODE"))) {
                        row.createCell(18).setCellValue(StringUtil.filtration(resultMap.get("SALES_CODE")));
                    }
                    // 本月开卡数 monthcount
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("MONTHCOUNT")))) {
                        row.createCell(19).setCellValue(StringUtil.filtration(String.valueOf(resultMap.get("MONTHCOUNT"))));
                    }

                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("userPhone", String.valueOf(resultMap.get("USER_PHONE")));

                    // 本月积分 monthintegral
                    if ("4".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                        if (!StringUtil.isEmpty(String.valueOf(resultMap.get("MONTHINTEGRAL")))) {
                            row.createCell(20).setCellValue(StringUtil.filtration(String.valueOf(resultMap.get("MONTHINTEGRAL"))));
                        }
                    } else {
                        if (!"0".equals(String.valueOf(resultMap.get("SL"))) || !StringUtil.isEmpty(String.valueOf(resultMap.get("SL")))) {
                            row.createCell(20).setCellValue("关闭查询");
                        } else {
                            if (!StringUtil.isEmpty(String.valueOf(resultMap.get("MONTHINTEGRAL")))) {
                                row.createCell(20).setCellValue(StringUtil.filtration(String.valueOf(resultMap.get("MONTHINTEGRAL"))));
                            }
                        }
                    }

                    // 本月绩效 performance
                    if (!StringUtil.isEmpty(resultMap.get("PERFORMANCE"))) {
                        row.createCell(21).setCellValue(StringUtil.filtration(resultMap.get("PERFORMANCE")));
                    }
                    // 累计开卡数 totalcount
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("TOTALCOUNT")))) {
                        row.createCell(22).setCellValue(StringUtil.filtration(String.valueOf(resultMap.get("TOTALCOUNT"))));
                    }
                    
                    // 上级联系号码
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("STATUS_SUPERIOR")))) {
                        row.createCell(23).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.filtration(String.valueOf(resultMap.get("STATUS_SUPERIOR")))));
                    }
                    // 上级合伙人用户ID
                    if (null != resultMap.get("sjHhrId")) {
                        row.createCell(24).setCellValue(String.valueOf(resultMap.get("sjHhrId")));
                    }
                    // 上级姓名
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("NAME_SUPERIOR")))) {
                        row.createCell(25).setCellValue(DesensitizationUtil.desensitizedName(StringUtil.filtration(String.valueOf(resultMap.get("NAME_SUPERIOR")))));
                    }
                    // 是否有效
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("status")))) {
                        if ("1".equals(String.valueOf(resultMap.get("status")))) {
                            row.createCell(26).setCellValue("有效");
                        } else {
                            row.createCell(26).setCellValue("无效");
                        }
                    }
                    // 上上级合伙人ID
                    if (null != resultMap.get("ssjHhrId")) {
                        row.createCell(27).setCellValue(String.valueOf(resultMap.get("ssjHhrId")));
                    }
                    // 上上级姓名
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("ssjHhr")))) {
                        row.createCell(28).setCellValue(DesensitizationUtil.desensitizedName(StringUtil.filtration(String.valueOf(resultMap.get("ssjHhr")))));
                    }
                    // 上上级手机号
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("ssjPhone")))) {
                        row.createCell(29).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.filtration(String.valueOf(resultMap.get("ssjPhone")))));
                    }
                    // 最近登录时间
                    row.createCell(30).setCellValue(String.valueOf(resultMap.get("loginDate")));
                }
            }

            // 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            
            logger.info("智慧扫楼导出人员信息详表file文件路径:" + filePath);
             fileName = "智慧扫楼人员信息详表";
            
            // 上传到S3文件服务器
            //String s3FilePath = fileName + "_" + System.currentTimeMillis() + ".xls";
            awsS3Utils.putFile(filePath, wb);
            logger.info("文件已上传到S3文件服务器，路径：" + filePath);
            
            File file = new File(filePath + fileName + ".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    return fail("写入文件失败: " + e.getMessage());
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
                return fail("创建文件失败: " + e.getMessage());
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) {//判断是否是文件
                    Map<String, Object> map = new HashedMap();
                    map.put("code", "6");
                    map.put("msg", "downloadExportFile");
                    map.put("fileName", fileName);
                    return success(map);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return fail("hnsluser exportUser exception : " + e.getMessage());
        }
        return fail("导出失败");
    }


    /**
     * 根据身份证生日计算年龄
     */
    private String getAgeFromIDCard(String birthDate) {
        try {
            // 假设birthDate格式为YYYYMMDD
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Calendar birth = Calendar.getInstance();
            birth.setTime(sdf.parse(birthDate));

            Calendar now = Calendar.getInstance();
            int age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
            if (now.get(Calendar.DAY_OF_YEAR) < birth.get(Calendar.DAY_OF_YEAR)) {
                age--;
            }
            return String.valueOf(age);
        } catch (Exception e) {
            return "";
        }
    }




    /**
     * 导出用户积分
     *
     * @return
     */
    @RequestMapping("/outputUserJiFei")
    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:exportUser')")
    @OperationLog
    @ApiOperation("用户积分列表导出")
    public ApiResult<?> outputUserJiFei(@RequestBody HnslUser hnslUser, HttpServletRequest request, HttpServletResponse response) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (hnslUser.getAppSettingId() == null) {
            return fail("请选择审批人");
        }
        User user = getLoginUser();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("userPhone", hnslUser.getUserPhone());
        requestMap.put("statusSf", hnslUser.getStatusSf());
        requestMap.put("userSfz", hnslUser.getUserSfz());
        String UserPhone = redisUtil.getString("thisUserPhone" + user.getPhone());
        String UserSf = redisUtil.getString("thisUserSf" + user.getPhone());
        String cityCode = redisUtil.getString("cityCode" + user.getPhone());
        requestMap.put("loginUserSf", UserSf); // 查询语句判断各个身份导出数据权限
        // 如果是地市管理员 则能够导出所属地市的全部人员
        if ("6".equals(UserSf)) {
            if (!StringUtil.isEmpty(hnslUser.getCityCode())) {
                requestMap.put("cityCode", hnslUser.getCityCode());
                requestMap.put("hnslChannel", hnslUser.getChannelType());
            }
        }
        // 如果是校园经理 则能够导出其管理下的校园所有人员
        if ("1".equals(UserSf)) {
            // 根据手机号查询管理校园编码
//            String schoolCode = hnslUserService.querySchoolCodeByPhone(UserPhone);
//            if (!StringUtil.isEmpty(schoolCode)) {
//                requestMap.put("schoolCode", schoolCode);
//                requestMap.put("hnslChannel", hnslType);
            }
            List<String> schoolCode = hnslUserService.querySchoolCodeByPhone(hnslUser.getUserPhone());
            if (!schoolCode.isEmpty() && schoolCode.size() > 0) {
                requestMap.put("schoolCode", schoolCode);
                requestMap.put("hnslChannel", hnslUser.getChannelType());
            }
        List<HnslUser> queryList = hnslUserService.queryList(requestMap);
        if (CollectionUtils.isEmpty(queryList)){
            return fail("当前用户无可导出数据");
        }

        // 生成一条记录
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
        String batchCode = sdf.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
        String fileName = "积分表" + batchCode;
        String filePath = "uploads/" + fileName;
        logger.info("导出路径" + filePath + filePath);
        Integer activeEvent = 7;
        Integer appSettingId = Integer.valueOf(hnslUser.getAppSettingId());
        Integer list = queryList.size();
        queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, list, appSettingId);

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));
        HSSFRow row = sheet.createRow((int) 0);
        HSSFCellStyle style = wb.createCellStyle();
        HSSFCell cell = row.createCell(0);
        // 设置字体
        HSSFFont font = wb.createFont();
        //设置列宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 300);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 20 * 256);

        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        cell.setCellValue("姓名");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("身份");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("身份证号");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("手机号码");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("积分");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("揽机工号");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("销售员编码");
        cell.setCellStyle(style);

        // 循环将数据写入Excel
        if (null != queryList && queryList.size() != 0) {

            for (int i = 0; i < queryList.size(); i++) {

                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                if ("1".equals(queryList.get(i).getStatusSf())) {
                    row.createCell(1).setCellValue("校园经理");
                } else if ("2".equals(queryList.get(i).getStatusSf())) {
                    row.createCell(1).setCellValue("一级合伙人");
                } else if ("3".equals(queryList.get(i).getStatusSf())) {
                    row.createCell(1).setCellValue("二级合伙人");
                } else if ("4".equals(queryList.get(i).getStatusSf())) {
                    row.createCell(1).setCellValue("三级合伙人");
                }
                row.createCell(0).setCellValue(DesensitizationUtil.desensitizedName(queryList.get(i).getUserName()));
                row.createCell(2).setCellValue(DesensitizationUtil.idCardDesensitization(StringUtil.isNotNull(queryList.get(i).getUserSfz()) ? StringUtil.filtration(queryList.get(i).getUserSfz()) : ""));
                row.createCell(3).setCellValue(DesensitizationUtil.mobilePhoneDesensitization(StringUtil.isNotNull(queryList.get(i).getUserPhone()) ? StringUtil.filtration(queryList.get(i).getUserPhone()) : ""));

//                row.createCell(0).setCellValue(queryList.get(i).getUserName());
//                row.createCell(2).setCellValue(StringUtil.isNotNull(queryList.get(i).getUserSfz()) ? StringUtil.filtration(queryList.get(i).getUserSfz()) : "");
//                row.createCell(3).setCellValue(StringUtil.isNotNull(queryList.get(i).getUserPhone()) ? StringUtil.filtration(queryList.get(i).getUserPhone()) : "");


                row.createCell(4).setCellValue(queryList.get(i).getIntegral());
                row.createCell(5).setCellValue(StringUtil.isNotNull(queryList.get(i).getNumbers()) ? StringUtil.filtration(queryList.get(i).getNumbers()) : "");
                row.createCell(6).setCellValue(StringUtil.isNotNull(queryList.get(i).getSalesCode()) ? StringUtil.filtration(queryList.get(i).getSalesCode()) : "");
            }
            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("用户积分列表导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        }
        return fail("导出失败");
    }
    
    
    
    @PreAuthorize("hasAuthority('hnzhsl:hnslUser:exportUser')")
    @OperationLog
    @ApiOperation("用户积分详情导出")
    @PostMapping("/exportUsers")
    public ApiResult<?> exportUserIntegrates(@RequestBody JSONObject conditionStr, HttpServletRequest request, HttpServletResponse response) {
        logger.info("导出用户详情积分条件: " + conditionStr.toString());
        ServletContext servletContext = request.getSession().getServletContext();
        Map<String, Object> conditionMap = (Map<String, Object>) JSON.parse(conditionStr.toString());

//        SysUserEntity user = getUser();
//        String UserPhone = redisUtils.get("thisUserPhone" + user.getMobile());
//        String UserSf = redisUtils.get("thisUserSf" + user.getMobile());
//        String cityCode = redisUtils.get("cityCode" + user.getMobile());
//        //conditionMap.put("loginUserSf", UserSf); // 查询语句判断各个身份导出数据权限
//        // 如果是地市管理员 则能够导出所属地市的全部人员
//        if ("6".equals(UserSf)) {
//            if (!StringUtil.isEmpty(cityCode)) {
//                conditionMap.put("cityCode", cityCode);
//            }
//        }
//        
//        // 如果是校园经理 则能够导出其管理下的校园所有人员
//        if ("1".equals(UserSf)) {
//            // 根据手机号查询管理校园编码
//            List<String> schoolCode = hnslUserService.querySchoolCodeByPhone(UserPhone);
//            if (!schoolCode.isEmpty() && schoolCode.size() > 0) {
//                conditionMap.put("schoolCode", schoolCode);
//            }
//        }
        try {
            //根据条件查询出数据
            List<Map<String, String>> resultList = hnslUserService.queryExportUserIntegral(conditionMap);
            if (resultList == null || resultList.size() == 0) {
                return fail("未查询到导出数据");
            }
            // 生成一条记录
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMDD");
            String batchCode = sdf1.format(new Date()) + ((int) ((Math.random() * 9 + 1) * 10));
            String fileName = "用户积分详情" + batchCode;
            String filePath = "uploads/" + fileName;
            logger.info("导出路径" + filePath);
            Integer activeEvent = 11;
            Integer appSettingId = (Integer) conditionStr.get("appSettingId");
            Integer lists = resultList.size();
            User user = getLoginUser();
            queryUserManagerUtil.exportRecord(fileName, filePath, user, activeEvent, lists, appSettingId);

            HSSFWorkbook wb = new HSSFWorkbook();
            HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(), DateUtils.STRING_DATE_FORMAT));

            //设置21列及列宽
            for (int i = 0; i <= 27; i++) {
                sheet.setColumnWidth(i, 20 * 250);
            }
            // 在sheet表中添加第一行表头
            HSSFRow row = sheet.createRow((int) 0);
            // 合并第一行单元格
            CellRangeAddress region = new CellRangeAddress(0, 0, 0, 21);
            sheet.addMergedRegion(region);
            // 创建单元格，设置值表头，设置表头居中
            HSSFCellStyle style = wb.createCellStyle();

            style.setAlignment(HorizontalAlignment.CENTER);

            // 设置字体
            HSSFFont font = wb.createFont();
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 11);
            font.setBold(true);

            style.setFont(font);

            HSSFCell cell = row.createCell(0);
            cell.setCellValue("智慧扫楼人员信息详表");
            cell.setCellStyle(style);

            //设置第二行 行最后单元格显示月份
            HSSFRow row1 = sheet.createRow(1);
            HSSFCell cell1 = row1.createCell(19);

            HSSFCellStyle style1 = wb.createCellStyle();
            //设置字体,黄色背景 及居中显示
            HSSFFont font1 = wb.createFont();
            font1.setFontName("宋体");
            font1.setFontHeightInPoints((short) 9);
            style1.setFont(font1);
            style.setAlignment(HorizontalAlignment.CENTER);
            Calendar c = Calendar.getInstance();
            cell1.setCellValue("月份:" + c.get(Calendar.YEAR) + "." + (c.get(Calendar.MONTH) + 1));
            cell1.setCellStyle(style1);


            // 设置第三行数据字段 添加至excel
            Map<String, String> columnMap = new HashMap<>();
            columnMap.put("0", "姓名");
            columnMap.put("1", "身份证");
            columnMap.put("2", "手机号");
            columnMap.put("3", "地市");
            columnMap.put("4", "学校");
            columnMap.put("5", "注册时间");
            columnMap.put("6", "级别（校园经理/123级合伙人）");
            columnMap.put("7", "面对面订单数");
            columnMap.put("8", "面对面业务积分");
            columnMap.put("9", "一人一码订单数");
            columnMap.put("10", "一人一码积分");
            columnMap.put("11", "加装业务订单数");
            columnMap.put("12", "加装业务积分");
            columnMap.put("13", "5G业务订单数");
            columnMap.put("14", "5G业务积分");
            columnMap.put("15", "熟卡业务订单数");
            columnMap.put("16", "熟卡业务积分");
            columnMap.put("17", "充值订单数");
            columnMap.put("18", "充值业务积分");
            columnMap.put("19", "其他积分");
            columnMap.put("20", "总计数量");
            columnMap.put("21", "总计积分");
            columnMap.put("22", "身份（是否楼长）");
            columnMap.put("23", "上级");
            columnMap.put("24", "上级工号");
            columnMap.put("25", "揽机工号");
            columnMap.put("26", "销售员编码");
            columnMap.put("27", "人员状态");
            HSSFRow row2 = sheet.createRow(2);

            HSSFCellStyle style2 = wb.createCellStyle();

            // 居中格式
            style.setAlignment(HorizontalAlignment.CENTER);
            for (int i = 0; i < 28; i++) {
                HSSFCell cell2 = row2.createCell(i);
                cell2.setCellValue(columnMap.get(i + ""));
                cell2.setCellStyle(style2);
            }


            // 将数据写入excel
            if (null != resultList && resultList.size() != 0) {
                // 遍历结果集
                for (int i = 0; i < resultList.size(); i++) {
                    Map<String, String> resultMap = resultList.get(i);
                    // 填写数据从第三行开始
                    row = sheet.createRow((int) i + 3);
                    // 姓名
                    if (!StringUtil.isEmpty(resultMap.get("USER_NAME"))) {
                        row.createCell(0).setCellValue(resultMap.get("USER_NAME"));
                    }
                    // 身份证
                    if (!StringUtil.isEmpty(resultMap.get("USER_SFZ"))) {
                        row.createCell(1).setCellValue(resultMap.get("USER_SFZ"));
                    }
                    // 手机号
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("USER_PHONE")))) {
                        row.createCell(2).setCellValue(resultMap.get("USER_PHONE"));
                    }
                    // 地市
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("CITY_CODE")))) {
                        row.createCell(3).setCellValue(resultMap.get("CITY_CODE"));
                    }
                    // 学校
                    if (!StringUtil.isEmpty(resultMap.get("SCHOOL_NAME"))) {
                        row.createCell(4).setCellValue(resultMap.get("SCHOOL_NAME"));
                    }
                    // 注册时间
                    if (!StringUtil.isEmpty(resultMap.get("CREATED_DATES"))) {
                        row.createCell(5).setCellValue(resultMap.get("CREATED_DATES"));
                    }
                    // 级别（校园经理/123级合伙人）
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("STATUS_SF")))) {
                        if ("1".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(6).setCellValue("经理");
                        } else if ("2".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(6).setCellValue("一级");
                        }else if ("3".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(6).setCellValue("二级");
                        }else if ("4".equals(String.valueOf(resultMap.get("STATUS_SF")))) {
                            row.createCell(6).setCellValue("三级");
                        } else {
                            row.createCell(6).setCellValue("无");
                        }
                    }
                    // 面对面订单数
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S1")))) {
                        row.createCell(7).setCellValue(String.valueOf(resultMap.get("S1")));
                    }
                    // 面对面业务积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S1F")))) {
                        row.createCell(8).setCellValue(String.valueOf(resultMap.get("S1F")));
                    }
                    // 一人一码订单数
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S2")))) {
                        row.createCell(9).setCellValue(String.valueOf(resultMap.get("S2")));
                    }
                    // 一人一码积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S2F")))) {
                        row.createCell(10).setCellValue(String.valueOf(resultMap.get("S2F")));
                    }
                    // 加装业务订单数
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S4")))) {
                        row.createCell(11).setCellValue(String.valueOf(resultMap.get("S4")));
                    }
                    // 加装业务积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S4F")))) {
                        row.createCell(12).setCellValue(String.valueOf(resultMap.get("S4F")));
                    }
                    // 5G业务订单数
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S5")))) {
                        row.createCell(13).setCellValue(String.valueOf(resultMap.get("S5")));
                    }
                    // 5G业务积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S5F")))) {
                        row.createCell(14).setCellValue(String.valueOf(resultMap.get("S5F")));
                    }
                    // 熟卡业务订单数
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S3")))) {
                        row.createCell(15).setCellValue(String.valueOf(resultMap.get("S3")));
                    }

                    // 熟卡业务积分
                    if(!StringUtil.isEmpty(String.valueOf(resultMap.get("S3F")))){
                        row.createCell(16).setCellValue(String.valueOf(resultMap.get("S3F")));
                    }

                    // 充值业务订单数
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S6")))) {
                        row.createCell(17).setCellValue(String.valueOf(resultMap.get("S6")));
                    }

                    // 充值业务积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("S6F")))) {
                        row.createCell(18).setCellValue(String.valueOf(resultMap.get("S6F")));
                    }

                    // 其他积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("QT")))) {
                        row.createCell(19).setCellValue(String.valueOf(resultMap.get("QT")));
                    }
                    // 总共数量
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("ZJ")))) {
                        row.createCell(20).setCellValue(String.valueOf(resultMap.get("ZJ")));
                    }
                    // 总共积分
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("ZJF")))) {
                        row.createCell(21).setCellValue(String.valueOf(resultMap.get("ZJF")));
                    }
                    // 身份（是否楼长）
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("LDC")))) {
                        if(Objects.equals("0",String.valueOf(resultMap.get("LDC")))){
                            row.createCell(22).setCellValue("否");
                        }else{
                            row.createCell(22).setCellValue("是");
                        }
                    }
                    // 上级
                    if (!StringUtil.isEmpty((String.valueOf(resultMap.get("SUPERIOR_NAME"))))) {
                        row.createCell(23).setCellValue(String.valueOf(resultMap.get("SUPERIOR_NAME")));
                    }

                    // 上级工号
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("SUPERIOR_PHONE")))) {
                        row.createCell(24).setCellValue(String.valueOf(resultMap.get("SUPERIOR_PHONE")));
                    }
                    // 揽机工号
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("NUMBERS")))) {
                        row.createCell(25).setCellValue(String.valueOf(resultMap.get("NUMBERS")));
                    }
                    // 销售员编码
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("SALES_CODE")))) {
                        row.createCell(26).setCellValue(String.valueOf(resultMap.get("SALES_CODE")));
                    }
                    // 人员状态
                    if (!StringUtil.isEmpty(String.valueOf(resultMap.get("STATUS")))) {
                        if(Objects.equals("1",String.valueOf(resultMap.get("STATUS")))){
                            row.createCell(27).setCellValue("有效");
                        }else{
                            row.createCell(27).setCellValue("失效");
                        }
                    }
                }
            }

            // 1. 添加水印
            try {
                WaterMarkUtil.insertWaterMarkTextToHssfEntrance(wb, user.getUsername() + " " + user.getPhone());
            } catch (Exception e) {
                logger.error("添加水印失败", e);
            }
            // 2. 确保目录存在
            File dir = new File(filePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    logger.info("成功创建目录: " + filePath);
                } else {
                    logger.error("无法创建目录: " + filePath);
                    return fail("无法创建导出目录");
                }
            }
            logger.info("派单报表导出file文件路径:" + filePath);
            // 3. 上传到S3文件服务器
            boolean uploadSuccess = ExportUtil.addWatermarkAndUploadToS3(wb, user.getUsername() + " " + user.getPhone(), filePath, awsS3Utils, logger);
            if (!uploadSuccess) {
                return fail("上传文件到S3服务器失败");
            }
            // 4. 返回结构
            Map<String, Object> map = new HashedMap();
            map.put("code", "6");
            map.put("msg", "downloadExportFile");
            map.put("fileName", fileName);
            return success(map);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出积分详情表异常 : " + e.getMessage());
            return fail("导出积分详情表异常 : " + e.getMessage());
        }
    }
}
