package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserSwitch;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserSwitchParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户数据开关表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
public interface HnslUserSwitchMapper extends BaseMapper<HnslUserSwitch> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserSwitch>
     */
    List<HnslUserSwitch> selectPageRel(@Param("page") IPage<HnslUserSwitch> page,
                             @Param("param") HnslUserSwitchParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserSwitch> selectListRel(@Param("param") HnslUserSwitchParam param);

}
