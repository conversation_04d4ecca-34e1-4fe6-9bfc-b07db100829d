package com.hlkj.yxsAdminApi.common.core.utils;

import com.amazonaws.services.s3.model.S3ObjectInputStream;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 解析excel 只用于1行1列的excel
 * 示例：从EXCEL导入到数据库
 * @FileName ObjectExcelRead.java
 * <AUTHOR>
 * @date 2018年4月27日 上午9:42:18
 */
public class ObjectExcelRead {

	private static final Logger logger= LoggerFactory.getLogger(ObjectExcelRead.class);
	
	
	/**
	 * 读取xlsx
	 * @param filepath 文件路径
	 * @param filename 文件名
	 * @param startrow 开始行号  从0开始
	 * @param startcol 开始列号 从0开始
	 * @param sheetnum sheet 从0开始
	 * @return list
	 */
	public static List<Map<String, String>> readExcelXlsx(String filepath, String filename, int startrow, int startcol, int sheetnum) {
		List<Map<String, String>> varList = new ArrayList<Map<String, String>>();

		try {
			File target = new File(filepath, filename);
			FileInputStream fi = new FileInputStream(target);
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
			XSSFWorkbook wb = new XSSFWorkbook(fi);
			XSSFSheet sheet = wb.getSheetAt(sheetnum); 					//sheet 从0开始
			
			int rowNum = sheet.getLastRowNum() + 1; 					//取得最后一行的行号
			for (int i = startrow; i < rowNum; i++) {					//行循环开始
				Map<String, String> map = new HashMap<String, String>();
				XSSFRow row = sheet.getRow(i);
				boolean rowEmpty = InterfaceUtil.isRowEmpty(row);//行
				if(rowEmpty){//本行为空跳过
					continue;
				}
				int cellNum = row.getLastCellNum(); 					//每行的最后一个单元格位置
				for (int j = startcol; j < cellNum; j++) {				//列循环开始
					
					XSSFCell cell = row.getCell(Short.parseShort(j + ""));
					CellType cellType = cell.getCellType();
					String cellValue = null;
                    DecimalFormat df = new DecimalFormat("#");
                    switch (cellType) { 					// 判断excel单元格内容的格式，并对其进行转换，以便插入数据库
                    //CELL_TYPE_NUMERIC 数值型 0
                    case NUMERIC:
                        if(HSSFDateUtil.isCellDateFormatted(cell)){
                            cellValue = simpleDateFormat.format(HSSFDateUtil.getJavaDate(cell.getNumericCellValue()));
                        }else{
                            cellValue = df.format(cell.getNumericCellValue()).toString();
                        }
                        break;
                    //CELL_TYPE_STRING 字符串型 1
                    case STRING:
                        cellValue = cell.getStringCellValue().trim();
                        break;
                    //CELL_TYPE_FORMULA 公式型 2
                    case FORMULA:
                        cellValue = cell.getCellFormula();
                        break;
                    //CELL_TYPE_BLANK 空值 3
                    case BLANK:
                        cellValue = cell.getStringCellValue().trim();
                        break;
                    //CELL_TYPE_BOOLEAN 布尔型 4
                    case BOOLEAN:
                        cellValue = String.valueOf(cell.getBooleanCellValue()).trim();
                        break;
                    //CELL_TYPE_ERROR 错误 5
                    case ERROR:
                        cellValue = cell.getStringCellValue();
                        break;
                    }
                    map.put(j + "", cellValue);
					logger.info(""+map);
				}
				varList.add(map);
			}

		} catch (Exception e) {
			logger.error("解析xls格式的excel文件失败",e);
		}
		
		return varList;
	}
	
	
	/**
	 * 读取xlsx(积分)
	 * @param filepath 文件路径
	 * @param filename 文件名
	 * @param startrow 开始行号  从0开始
	 * @param startcol 开始列号 从0开始
	 * @param sheetnum sheet 从0开始
	 * @return list
	 */
	public static List<Map<String, String>> readExcelXlsx2(String filepath, String filename, int startrow, int startcol, int sheetnum) {
		List<Map<String, String>> varList = new ArrayList<Map<String, String>>();

		try {
			File target = new File(filepath, filename);
			FileInputStream fi = new FileInputStream(target);
			
			XSSFWorkbook wb = new XSSFWorkbook(fi);
			XSSFSheet sheet = wb.getSheetAt(sheetnum); 					//sheet 从0开始
			
			int rowNum = sheet.getLastRowNum() + 1; 					//取得最后一行的行号
			for (int i = startrow; i < rowNum; i++) {					//行循环开始
				Map<String, String> map = new HashMap<String, String>();
				XSSFRow row = sheet.getRow(i); 							//行
				int cellNum = row.getLastCellNum(); 					//每行的最后一个单元格位置

				for (int j = startcol; j < cellNum; j++) {				//列循环开始
					
					XSSFCell cell = row.getCell(Short.parseShort(j + ""));
					String cellValue = getString(cell);
					map.put(j + "", cellValue);
					logger.info(""+map);
				}
				varList.add(map);
			}

		} catch (Exception e) {
			logger.error("解析xls格式的excel文件失败",e);
		}

		return varList;
	}

	private static String getString(XSSFCell cell) {
		CellType cellType = cell.getCellType();
		String cellValue = null;
		if (null != cell) {
			DecimalFormat df = new DecimalFormat("#");
			switch (cellType) { 					// 判断excel单元格内容的格式，并对其进行转换，以便插入数据库
			//CELL_TYPE_NUMERIC 数值型 0
			case NUMERIC:
				cellValue=df.format(cell.getNumericCellValue()).toString();
				break;
			//CELL_TYPE_STRING 字符串型 1
			case STRING:
				cellValue = cell.getStringCellValue().trim();
				break;
			//CELL_TYPE_FORMULA 公式型 2
			case FORMULA:
				cellValue = cell.getCellFormula();
				break;
			//CELL_TYPE_BLANK 空值 3
			case BLANK:
				cellValue = cell.getStringCellValue().trim();
				break;
			//CELL_TYPE_BOOLEAN 布尔型 4
			case BOOLEAN:
				cellValue = String.valueOf(cell.getBooleanCellValue()).trim();
				break;
			//CELL_TYPE_ERROR 错误 5
			case ERROR:
				cellValue = cell.getStringCellValue();
				break;
			}
		} else {
			cellValue = "";
		}
		return cellValue;
	}

	/**
	 * 读取xls
	 * @param filepath 文件路径
	 * @param filename 文件名
	 * @param startrow 开始行号 从0开始
	 * @param startcol 开始列号 从0开始
	 * @param sheetnum sheet 从0开始
	 * @return list
	 */
	@SuppressWarnings("deprecation")
	public static List<Map<String, String>> readExcelXls2(String filepath, String filename, int startrow, int startcol, int sheetnum) {
		List<Map<String, String>> varList = new ArrayList<Map<String, String>>();

		try {
			File target = new File(filepath, filename);
			FileInputStream fi = new FileInputStream(target);

			HSSFWorkbook wb = new HSSFWorkbook(fi);
			HSSFSheet sheet = wb.getSheetAt(sheetnum); 					//sheet 从0开始

			int rowNum = sheet.getLastRowNum() + 1; 					//取得最后一行的行号
			for (int i = startrow; i < rowNum; i++) {					//行循环开始
				Map<String, String> map = new HashMap<String, String>();
				HSSFRow row = sheet.getRow(i); 							//行
				int cellNum = row.getLastCellNum(); 					//每行的最后一个单元格位置
				String cellValue = null;
				for (int j = startcol; j < cellNum; j++) {				//列循环开始
					HSSFCell cell = row.getCell(Short.parseShort(j + ""));
					cellValue = getString(map, cellValue, j, cell);
				}
				varList.add(map);
			}

		} catch (Exception e) {
			logger.error("解析xls格式的excel文件失败",e);
		}
		
		return varList;
	}
	
	/**
	 * 读取xls
	 * @param filepath 文件路径
	 * @param filename 文件名
	 * @param startrow 开始行号 从0开始
	 * @param startcol 开始列号 从0开始
	 * @param sheetnum sheet 从0开始
	 * @return list
	 */
	@SuppressWarnings("deprecation")
	public static List<Map<String, String>> readExcelXls(String filepath, String filename, int startrow, int startcol, int sheetnum) {
		List<Map<String, String>> varList = new ArrayList<Map<String, String>>();

		try {
			File target = new File(filepath, filename);
			FileInputStream fi = new FileInputStream(target);
			
			HSSFWorkbook wb = new HSSFWorkbook(fi);
			HSSFSheet sheet = wb.getSheetAt(sheetnum); 					//sheet 从0开始
			
			int rowNum = sheet.getLastRowNum() + 1; 					//取得最后一行的行号
			for (int i = startrow; i < rowNum; i++) {					//行循环开始
				Map<String, String> map = new HashMap<String, String>();
				HSSFRow row = sheet.getRow(i);//行
				boolean rowEmpty = InterfaceUtil.isRowEmpty(row);
				if(rowEmpty){//本行为空跳过
					continue;
				}
				int cellNum = row.getLastCellNum(); 					//每行的最后一个单元格位置
				String cellValue = null;
				for (int j = startcol; j < cellNum; j++) {				//列循环开始
					System.out.println(row.getCell(j));
					HSSFCell cell = row.getCell(j);
					cellValue = getString(map, cellValue, j, cell);
					//varpd.put("var"+j, cellValue);
					//varList.add(cellValue);
				}
				varList.add(map);
			}

		} catch (Exception e) {
			logger.error("解析xls格式的excel文件失败",e);
		}
		
		return varList;
	}

	private static String getString(Map<String, String> map, String cellValue, int j, HSSFCell cell) {
		CellType cellType = cell.getCellType();
		DecimalFormat df = new DecimalFormat("#");
		switch (cellType) { 					// 判断excel单元格内容的格式，并对其进行转换，以便插入数据库
		//CELL_TYPE_NUMERIC 数值型 0
		case NUMERIC:
			cellValue = df.format(cell.getNumericCellValue()).toString();
			break;
		//CELL_TYPE_STRING 字符串型 1
		case STRING:
			cellValue = cell.getStringCellValue().trim();
			break;
		//CELL_TYPE_FORMULA 公式型 2
		case FORMULA:
			cellValue = cell.getCellFormula();
			break;
		//CELL_TYPE_BLANK 空值 3
		case BLANK:
			cellValue = cell.getStringCellValue().trim();
			break;
		//CELL_TYPE_BOOLEAN 布尔型 4
		case BOOLEAN:
			cellValue = String.valueOf(cell.getBooleanCellValue()).trim();
			break;
		//CELL_TYPE_ERROR 错误 5
		case ERROR:
			cellValue = cell.getStringCellValue();
			break;
		}
		logger.info("单个值j"+cellValue);
		map.put(j + "", cellValue);
		return cellValue;
	}


	@SuppressWarnings("deprecation")
	public static List<Map<String, String>> readWhiteExcelXls(S3ObjectInputStream inputStream, int startrow, int startcol, int sheetnum) {
		List<Map<String, String>> varList = new ArrayList<Map<String, String>>();

		try {
			XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
			XSSFSheet sheet = workbook.getSheetAt(0);
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
			int rowNum = sheet.getLastRowNum() + 1;
			for (int i = 1; i < rowNum; i++) {
				Map<String, String> map = new HashMap<String, String>();
				XSSFRow row = sheet.getRow(i);
				boolean rowEmpty = InterfaceUtil.isRowEmpty(row);
				if(rowEmpty){
					continue;
				}
				int cellNum = row.getLastCellNum();
				for (int j = 0; j < cellNum; j++) {

					XSSFCell cell = row.getCell(Short.parseShort(j + ""));
					String cellValue = null;
//					if (null != cell) {
//						DecimalFormat df = new DecimalFormat("#");
//						CellType cellType = cell.getCellType();
//						if (cellType == Cell.CELL_TYPE_NUMERIC) {
//							if (HSSFDateUtil.isCellDateFormatted(cell)) {
//								cellValue = simpleDateFormat.format(HSSFDateUtil.getJavaDate(cell.getNumericCellValue()));
//							} else {
//								cellValue = df.format(cell.getNumericCellValue()).toString();
//							}
//						} else if (cellType == Cell.CELL_TYPE_STRING) {
//							cellValue = cell.getStringCellValue().trim();
//						} else if (cellType == Cell.CELL_TYPE_FORMULA) {
//							cellValue = cell.getCellFormula();
//						} else if (cellType == Cell.CELL_TYPE_BLANK) {
//							cellValue = cell.getStringCellValue().trim();
//						} else if (cellType == Cell.CELL_TYPE_BOOLEAN) {
//							cellValue = String.valueOf(cell.getBooleanCellValue()).trim();
//						} else if (cellType == Cell.CELL_TYPE_ERROR) {
//							cellValue = cell.getStringCellValue();
//						} else {
//							cellValue = "";
//						}
//					} else {
//						cellValue = "";
//					}
					map.put(j + "", cellValue);
					logger.info(""+map);
				}
				varList.add(map);
			}

		} catch (Exception e) {
			logger.error("解析xls格式的excel文件失败",e);
		}

		return varList;
	}


}
