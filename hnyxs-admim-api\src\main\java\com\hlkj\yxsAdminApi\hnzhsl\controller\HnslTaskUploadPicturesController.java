package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.DateUtils;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTaskUploadPicturesService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTaskUploadPictures;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTaskUploadPicturesParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 分享图片表控制器
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Api(tags = "分享图片表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslTaskUploadPictures")
public class HnslTaskUploadPicturesController extends BaseController {
    @Autowired
    private HnslTaskUploadPicturesService hnslTaskUploadPicturesService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:list')")
    @OperationLog
    @ApiOperation("分页查询分享图片表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTaskUploadPictures>> page(@RequestBody HnslTaskUploadPicturesParam param) {
        PageParam<HnslTaskUploadPictures, HnslTaskUploadPicturesParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskUploadPicturesService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTaskUploadPicturesService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:list')")
    @OperationLog
    @ApiOperation("查询全部分享图片表")
    @PostMapping("/list")
    public ApiResult<List<HnslTaskUploadPictures>> list(@RequestBody HnslTaskUploadPicturesParam param) {
        PageParam<HnslTaskUploadPictures, HnslTaskUploadPicturesParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTaskUploadPicturesService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTaskUploadPicturesService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:list')")
    @OperationLog
    @ApiOperation("根据id查询分享图片表")
    @GetMapping("/{id}")
    public ApiResult<HnslTaskUploadPictures> get(@PathVariable("id") Integer id) {
        return success(hnslTaskUploadPicturesService.getById(id));
        // 使用关联查询
        //return success(hnslTaskUploadPicturesService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:save')")
    @OperationLog
    @ApiOperation("添加分享图片表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTaskUploadPictures hnslTaskUploadPictures) {
        if (hnslTaskUploadPicturesService.save(hnslTaskUploadPictures)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:update')")
    @OperationLog
    @ApiOperation("修改分享图片表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTaskUploadPictures hnslTaskUploadPictures) {
        if (hnslTaskUploadPicturesService.updateById(hnslTaskUploadPictures)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:remove')")
    @OperationLog
    @ApiOperation("删除分享图片表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTaskUploadPicturesService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:save')")
    @OperationLog
    @ApiOperation("批量添加分享图片表")
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTaskUploadPictures> list) {
        if (hnslTaskUploadPicturesService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:update')")
    @OperationLog
    @ApiOperation("批量修改分享图片表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTaskUploadPictures> batchParam) {
        if (batchParam.update(hnslTaskUploadPicturesService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:remove')")
    @OperationLog
    @ApiOperation("批量删除分享图片表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTaskUploadPicturesService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslTaskUploadPictures:output')")
    @OperationLog
    @ApiOperation("导出截图信息")
    @PostMapping("/outTaskUploadPictures")
    public ApiResult<?> outputOrder(@RequestBody HnslTaskUploadPictures hnslTaskUploadPicturesEntity, HttpServletRequest request,
                         HttpServletResponse response) {
        logger.info("导出截图信息入参"+hnslTaskUploadPicturesEntity);

        ServletContext servletContext = request.getSession().getServletContext();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //查询列表数据
//        Map<String, Object> requestMap = new HashMap<>();
//        requestMap.put("beginTime", hnslTaskUploadPicturesEntity.getBeginTime());
//        requestMap.put("endTime", hnslTaskUploadPicturesEntity.getEndTime());
//        requestMap.put("userPhone", hnslTaskUploadPicturesEntity.getUserPhone());
//        requestMap.put("uploadType", hnslTaskUploadPicturesEntity.getUploadType());


        LambdaQueryWrapper<HnslTaskUploadPictures> hnslTaskUploadPicturesLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslTaskUploadPicturesLambdaQueryWrapper.eq(HnslTaskUploadPictures::getUserPhone,hnslTaskUploadPicturesEntity.getUserPhone())
                .eq(HnslTaskUploadPictures::getUploadType,hnslTaskUploadPicturesEntity.getUploadType())
                .apply("UPLOAD_DATE>={0} and UPLOAD_DATE<={1}",hnslTaskUploadPicturesEntity.getBeginTime(),hnslTaskUploadPicturesEntity.getEndTime());
        List<HnslTaskUploadPictures> hnslTaskUploadPicturesList = hnslTaskUploadPicturesService.list(hnslTaskUploadPicturesLambdaQueryWrapper);

        logger.info("截图"+hnslTaskUploadPicturesList);

        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(DateUtils.getDateString(new Date(),DateUtils.STRING_DATE_FORMAT));
        //设置列宽
        sheet.setColumnWidth(0, 20 * 346);
        sheet.setColumnWidth(1, 20 * 266);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 266);
        sheet.setColumnWidth(4, 20 * 356);
        sheet.setColumnWidth(5, 20 * 256);

        // 3.在sheet中添加表头第0行，老版本poi对excel行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 4.创建单元格，设置值表头，设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        // 居中格式
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置表头
        HSSFCell cell = row.createCell(0);
        cell.setCellValue("截图类型");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("上传时间");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("点赞数");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("QQ群号码");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("QQ群人数");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("QQ群昵称");
        cell.setCellStyle(style);

        // 循环将数据写入Excel
        if(null!=hnslTaskUploadPicturesList && hnslTaskUploadPicturesList.size()!=0){

            for(int i = 0;i<hnslTaskUploadPicturesList.size();i++){
                row = sheet.createRow((int) i + 1);
                // 创建单元格，设置值
                if(1==hnslTaskUploadPicturesList.get(i).getUploadType()){
                    row.createCell(0).setCellValue("朋友圈截图");
                }else if(2==hnslTaskUploadPicturesList.get(i).getUploadType()) {
                    row.createCell(0).setCellValue("QQ群截图");
                }else if(3==hnslTaskUploadPicturesList.get(i).getUploadType()) {
                    row.createCell(0).setCellValue("服务截图");
                }
                row.createCell(1).setCellValue(simpleDateFormat.format(hnslTaskUploadPicturesList.get(i).getUploadDate()));

                System.out.println(hnslTaskUploadPicturesList.get(i).getThumbsUp());
                if(!"".equals(hnslTaskUploadPicturesList.get(i).getThumbsUp()) && hnslTaskUploadPicturesList.get(i).getThumbsUp()!=null) {
                    row.createCell(2).setCellValue(hnslTaskUploadPicturesList.get(i).getThumbsUp());
                }
                if(!"".equals(hnslTaskUploadPicturesList.get(i).getQqGroupNumber()) && hnslTaskUploadPicturesList.get(i).getQqGroupNumber()!=null ){
                    row.createCell(3).setCellValue(hnslTaskUploadPicturesList.get(i).getQqGroupNumber());
                }
                if(!"".equals(hnslTaskUploadPicturesList.get(i).getQqGroupPeoples()) && hnslTaskUploadPicturesList.get(i).getQqGroupPeoples()!=null) {
                    row.createCell(4).setCellValue(hnslTaskUploadPicturesList.get(i).getQqGroupPeoples());
                }
                if(!"".equals(hnslTaskUploadPicturesList.get(i).getQqGroupName()) && hnslTaskUploadPicturesList.get(i).getQqGroupName()!=null) {
                    row.createCell(5).setCellValue(hnslTaskUploadPicturesList.get(i).getQqGroupName());
                }

            }
            String filepath =servletContext.getRealPath("/") + "uploads"+ File.separator+"file"+File.separator;;
            logger.info("file文件"+filepath);
            String fileName="截图信息表";
            File file = new File(filepath +fileName+".xls");
            OutputStream ouputStream;
            try {
                ouputStream = new FileOutputStream(file);
                try {
                    wb.write(ouputStream);
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            if (file.exists()) {//判断文件是否存在
                if (file.isFile()) {//判断是否是文件
                    Map<String, Object> map = new HashedMap<>();
                    map.put("code","6");
                    map.put("fileName", fileName);
                    map.put("msg", "exportDaoUsers");
                    return success(map);
                }
            }
        }
        return fail("无导出数据");
    }
}
