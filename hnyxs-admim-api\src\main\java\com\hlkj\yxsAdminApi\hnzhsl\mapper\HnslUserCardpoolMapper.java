package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslUserCardpool;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslUserCardpoolParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私人号池Mapper
 *
 * <AUTHOR>
 * @since 2023-06-21 09:08:40
 */
public interface HnslUserCardpoolMapper extends BaseMapper<HnslUserCardpool> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslUserCardpool>
     */
    List<HnslUserCardpool> selectPageRel(@Param("page") IPage<HnslUserCardpool> page,
                             @Param("param") HnslUserCardpoolParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslUserCardpool> selectListRel(@Param("param") HnslUserCardpoolParam param);

}
