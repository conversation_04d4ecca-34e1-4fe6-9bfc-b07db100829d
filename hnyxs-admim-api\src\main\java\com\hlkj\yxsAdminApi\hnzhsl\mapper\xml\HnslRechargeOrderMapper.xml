<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hlkj.yxsAdminApi.hnzhsl.mapper.HnslRechargeOrderMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        select a.*,t1.user_name,t2.school_name,t1.status_sf,t1.city_code
        from hnsl_recharge_order a
        left join hnsl_user t1 on a.partnerid=t1.user_phone
        left join hnsl_school t2 on a.school_id=t2.school_code
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.phone != null">
                AND a.PHONE LIKE CONCAT('%', #{param.phone}, '%')
            </if>
            <if test="param.money != null">
                AND a.MONEY LIKE CONCAT('%', #{param.money}, '%')
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.createdUser != null">
                AND a.CREATED_USER LIKE CONCAT('%', #{param.createdUser}, '%')
            </if>
            <if test="param.createdDate != null">
                AND a.CREATED_DATE LIKE CONCAT('%', #{param.createdDate}, '%')
            </if>
            <if test="param.updatedUser != null">
                AND a.UPDATED_USER LIKE CONCAT('%', #{param.updatedUser}, '%')
            </if>
            <if test="param.updatedDate != null">
                AND a.UPDATED_DATE LIKE CONCAT('%', #{param.updatedDate}, '%')
            </if>
            <if test="param.timestamp != null">
                AND a.TIMESTAMP LIKE CONCAT('%', #{param.timestamp}, '%')
            </if>
            <if test="param.requestid != null">
                AND a.REQUESTID LIKE CONCAT('%', #{param.requestid}, '%')
            </if>
            <if test="param.orderid != null">
                AND a.ORDERID LIKE CONCAT('%', #{param.orderid}, '%')
            </if>
            <if test="param.partnerid != null">
                AND a.PARTNERID LIKE CONCAT('%', #{param.partnerid}, '%')
            </if>
            <if test="param.channel != null">
                AND a.CHANNEL LIKE CONCAT('%', #{param.channel}, '%')
            </if>
            <if test="param.sign != null">
                AND a.SIGN LIKE CONCAT('%', #{param.sign}, '%')
            </if>
            <if test="param.noncestr != null">
                AND a.NONCESTR LIKE CONCAT('%', #{param.noncestr}, '%')
            </if>
            <if test="param.schoolId != null">
                AND a.SCHOOL_ID LIKE CONCAT('%', #{param.schoolId}, '%')
            </if>
            <if test="param.beginTime != null and param.beginTime.trim() != ''">
                AND t.CREATED_DATE between
                #{param.beginTime} and
                #{param.endTime}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRechargeOrder">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.hlkj.yxsAdminApi.hnzhsl.entity.HnslRechargeOrder">
        <include refid="selectSql"></include>
    </select>

</mapper>
