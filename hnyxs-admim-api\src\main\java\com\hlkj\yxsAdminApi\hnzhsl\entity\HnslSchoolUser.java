package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学生信息表
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSchoolUser对象", description = "学生信息表")
public class HnslSchoolUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户姓名")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "性别（0:女  1:男")
    @TableField("SEX")
    private Integer sex;

    @ApiModelProperty(value = "身份证")
    @TableField("IDENTITY_CARD")
    private String identityCard;

    @ApiModelProperty(value = "手机号")
    @TableField("PHONE")
    private String phone;

    @ApiModelProperty(value = "本异网（0:异网   1:本网")
    @TableField("NETWORK")
    private Integer network;

    @ApiModelProperty(value = "入学时间")
    @TableField("ENROLTIME")
    private String enroltime;

    @ApiModelProperty(value = "籍贯")
    @TableField("NATIVEPLACE")
    private String nativeplace;

    @ApiModelProperty(value = "年龄")
    @TableField("AGE")
    private Integer age;

    @ApiModelProperty(value = "第二联系电话")
    @TableField("PHONE_TWO")
    private String phoneTwo;

    @ApiModelProperty(value = "手机品牌")
    @TableField("PHONE_BRAND")
    private String phoneBrand;

    @ApiModelProperty(value = "月套餐费用")
    @TableField("SETMEAL")
    private String setmeal;

    @ApiModelProperty(value = "语音套餐")
    @TableField("VOICE")
    private String voice;

    @ApiModelProperty(value = "宽带运营商")
    @TableField("OPERATOR")
    private String operator;

    @ApiModelProperty(value = "宽带速率")
    @TableField("NETWORK_VELOCITY")
    private String networkVelocity;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "房间ID")
    @TableField("ROOM_ID")
    private String roomId;

    @ApiModelProperty(value = "楼栋ID")
    @TableField("BUILDING_ID")
    private String buildingId;

    @ApiModelProperty(value = "身份证地址")
    @TableField("CLIENT_SFZ_SITE")
    private String clientSfzSite;

    @ApiModelProperty(value = "身份证时间")
    @TableField("CLIENT_SFZ_TIME")
    private String clientSfzTime;

    @ApiModelProperty(value = "学生编码")
    @TableField("CLIENT_NUMBER")
    private String clientNumber;

    @ApiModelProperty(value = "流量套餐")
    @TableField("FLOW")
    private String flow;

    @ApiModelProperty(value = "账户余额")
    @TableField("BALANCE")
    private Integer balance;

    @ApiModelProperty(value = "翼支付状态（0未开通 1：已开通）")
    @TableField("YIPAY")
    private Integer yipay;

    @ApiModelProperty(value = "学校编码")
    @TableField("SCHOOL_CODE")
    private String schoolCode;

    @ApiModelProperty(value = "备注")
    @TableField("CLIENT_REMARK")
    private String clientRemark;

    @ApiModelProperty(value = "宽带加装包备注栏")
    @TableField("BROADBAND_REMARK")
    private String broadbandRemark;

    @ApiModelProperty(value = "是否毕业（0:未毕业 1:已毕业）")
    @TableField("FINISH_SCHOOL")
    private Integer finishSchool;

    @ApiModelProperty(value = "院系")
    @TableField("SCHOOL_DEPARTMENT")
    private String schoolDepartment;

    @ApiModelProperty(value = "专业")
    @TableField("SCHOOL_SPECIALTY")
    private String schoolSpecialty;

}
