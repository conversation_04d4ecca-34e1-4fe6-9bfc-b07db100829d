package com.hlkj.yxsAdminApi.common.core.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.system.entity.DictionaryData;
import com.hlkj.yxsAdminApi.common.system.entity.SysDictEntity;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.SysDictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.concurrent.TimeUnit;


/**
 * redis操作类
 * @Title: RedisUtil.java
 * <AUTHOR>
 * @date 2019年5月27日
 * @version V1.0
 */
@Component
public class RedisUtil {
	private static Logger log = LoggerFactory.getLogger(RedisUtil.class);
	
	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private  SysDictService sysDictService;
    /** 
     * 指定缓存失效时间 (秒)
     * @param key 键 
     * @return 
     */  
    public boolean expire(String key,long time){  
        try {
            if(time>0){  
                redisTemplate.expire(key, time, TimeUnit.SECONDS);  
            }  
            return true;  
        } catch (Exception e) {  
        	log.error("redis中expire异常",e);
            return false;  
        }  
    }
    
    /**
     * 指定key失效时间
     * <AUTHOR>
     * @date 2019年7月29日
     * @version V1.0
     */
    public boolean expire(String key,long time,TimeUnit unit){  
        try {
            if(time>0){  
                redisTemplate.expire(key, time, unit);
                return true;
            }
            return false;
        } catch (Exception e) {  
        	log.error("redis中expire异常",e);
            return false;  
        }  
    }
    
    /** 
     * 根据key 获取过期时间 
     * @return 时间(秒) 返回0代表为永久有效 
     */  
    public long getExpire(String key){
        try {
			return redisTemplate.getExpire(key,TimeUnit.SECONDS);
		} catch (Exception e) {
			log.error("redis中getExpire异常", e);
			return -1;
		}  
    }
    
    /** 
     * 根据key 获取过期时间 
     * @return 时间(秒) 返回0代表为永久有效 
     */  
    public long getExpire(String key,TimeUnit timeUnit){
        try {
			return redisTemplate.getExpire(key,timeUnit);
		} catch (Exception e) {
			log.error("redis中getExpire异常", e);
			return -1;
		}
    }

    /** 
     * 判断key是否存在 
     * @param key 键 
     * @return true 存在 false不存在 
     */  
    public boolean hasKey(String key){  
        try {  
            return redisTemplate.hasKey(key);
        } catch (Exception e) {  
        	log.error("redis中hasKey异常",e);
            return false;  
        }  
    }  

    /** 
     * 删除缓存 
     * @param key 可以传一个值 或多个 
     */  
    @SuppressWarnings("unchecked")
	public void del(String ... key){  
        try {
			if(key!=null&&key.length>0){  
			    if(key.length==1){  
			        redisTemplate.delete(key[0]);  
			    }else{
			        redisTemplate.delete((Collection<String>) CollectionUtils.arrayToList(key));
			    }
			}
		} catch (Exception e) {
			log.error("redis中del异常",e);
		}  
    }  

    /** 
     * 普通缓存获取 
     * @param key 键 
     * @return 值 
     */  
    public Object get(String key){
    	try {
			if(null == key){
				return null;
			}else{
				return redisTemplate.opsForValue().get(key); 
			}
		} catch (Exception e) {
			log.error("redis中get异常",e);
		}
    	return null;
    }
    
    
    /**
     * 返回String类型
     * <AUTHOR>
     * @date 2019年8月9日
     * @param key
     * @return
     * @version V1.0
     */
    public String getString(String key){
    	try {
			if(null == key){
				return null;
			}else{
				Object object = redisTemplate.opsForValue().get(key);
				if(null != object){
					return object.toString();
				}
			}
		} catch (Exception e) {
			log.error("redis中get异常",e);
		}
    	return null;
    }

    /** 
     * 普通缓存放入 (没有失效时间)
     * @return true成功 false失败 
     */  
    public boolean set(String key,Object value) {  
         try {  
            redisTemplate.opsForValue().set(key, value);  
            return true;  
        } catch (Exception e) {  
        	log.error("redis中set异常",e);
            return false;  
        }  

    }  

    /** 
     * 普通缓存放入并设置时间 指定失效时间
     * @return true成功 false 失败 
     */  
    public boolean set(String key,Object value,long time,TimeUnit unit){  
        try {  
            if(time>0){  
                redisTemplate.opsForValue().set(key, value, time, unit);
                return true;
            }else{
            	return set(key,value);
            }
        } catch (Exception e) {  
        	log.error("redis中set异常",e);
            return false;  
        }  
    }
    
    /**
     * 保存信息在缓存中(秒)
     * <AUTHOR>
     * @date 2019年7月29日
     * @version V1.0
     */
    public boolean set(String key,Object value,long time){  
        try {  
            if(time>0){  
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
                return true;
            }else{
            	return set(key,value);
            }
        } catch (Exception e) {  
        	log.error("redis中set异常",e);
            return false;  
        }  
    }  

    /** 
     * 递增 
     * @param key 键 
     * @param delta 要增加几(大于0)
     * @return 
     */  
    public long incr(String key, long delta){    
        try {
			if(delta<0){  
			    return 0;
			}  
			return redisTemplate.opsForValue().increment(key, delta);
		} catch (Exception e) {
			log.error("redis递增异常", e);
			return 0;
		}  
    }  

    /** 
     * 递减 
     * @param key 键 
     * @param delta 要减少几(小于0)
     * @return 
     */  
    public long decr(String key, long delta){    
        try {
			if(delta<0){  
				return -1;
			}  
			return redisTemplate.opsForValue().increment(key, -delta);
		} catch (Exception e) {
			log.error("redis递减异常", e);
			return -1;
		}    
    }

	/**
	 * 高风险查询限制
	 * @return
	 */
	public boolean limitRateCount(String key, User loginUser) {
		LambdaQueryWrapper<SysDictEntity> dictionaryQueryWrapper = new LambdaQueryWrapper<>();
		dictionaryQueryWrapper.eq(SysDictEntity::getType, "limit_rate");
		SysDictEntity dictionary = sysDictService.getOne(dictionaryQueryWrapper);
		if (dictionary == null) {
			return false;
		}
		int limit = Integer.parseInt(dictionary.getCode());
		boolean checkLimit = incrAndCheckLimit(key + loginUser.getUserId(), limit);
		if (checkLimit) {
			return true;
		}
		return false;
	}
	/**
	 * 增加调用次数并判断是否超过限制
	 */
	public boolean incrAndCheckLimit(String key, int limit) {
		Long count = redisTemplate.opsForValue().increment(key);
		if (count == null) {
			return false; // 增加失败
		}

		if (count == 1) {
			// 第一次调用，设置过期时间到明天凌晨0点
			long expireTimestamp = getNextResetTimestamp();
			long nowTimestamp = ZonedDateTime.now(ZoneId.systemDefault()).toEpochSecond();
			long expireSeconds = expireTimestamp - nowTimestamp;
			redisTemplate.expire(key, expireSeconds, TimeUnit.SECONDS);
		}

		// 判断是否超出限制
		return count >= limit;
	}
	/**
	 * 获取当天凌晨12点的时间戳（秒）
	 */
	private long getNextResetTimestamp() {
		ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
		ZonedDateTime midnightTomorrow = now.truncatedTo(ChronoUnit.DAYS).plusDays(1);
		return midnightTomorrow.toEpochSecond();
	}
}
