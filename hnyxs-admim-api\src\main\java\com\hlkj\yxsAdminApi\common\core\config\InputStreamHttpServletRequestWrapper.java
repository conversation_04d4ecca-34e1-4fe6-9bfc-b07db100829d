package com.hlkj.yxsAdminApi.common.core.config;




import com.alibaba.fastjson.JSONObject;
import com.hlkj.yxsAdminApi.common.core.utils.RSA2Util;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;


/**
 * 请求流支持多次获取
 * @ClassName InputStreamHttpServletRequestWrapper
 * @Description TODO
 * @<NAME_EMAIL>
 * @Date 2023/4/11 12:59
 * @Version 1.0
 */
public class InputStreamHttpServletRequestWrapper  extends HttpServletRequestWrapper {
    private final String body;

    public InputStreamHttpServletRequestWrapper(HttpServletRequest request){
        super(request);
        //创建字符缓冲区
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        InputStream inputStream = null;
        try {
            inputStream = request.getInputStream();
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                //将输入流里面的参数读取到字符缓冲区
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
        } catch (IOException ex) {

        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //s为接口请求参数字符串类型
        String s = stringBuilder.toString();
        if(!"".equals(s)){
            //开始解密字符串类型参数
            JSONObject json = RSA2Util.decryptJson( JSONObject.parseObject(stringBuilder.toString()));
            body = json.getString("requestData").toString();
        }else{
            body=s;
        }
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(body.getBytes());
        ServletInputStream servletInputStream = new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }
            @Override
            public boolean isReady() {
                return false;
            }
            @Override
            public void setReadListener(ReadListener readListener) {
            }
            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }
        };
        return servletInputStream;

    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    public String getBody() {
        return this.body;
    }

}