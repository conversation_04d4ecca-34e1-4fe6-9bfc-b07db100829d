package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 派单每月统计表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslSendOrdersMonthRecord对象", description = "派单每月统计表")
public class HnslSendOrdersMonthRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动唯一标识")
    @TableField("ACTIVITY_LOGO")
    private String activityLogo;

    @ApiModelProperty(value = "活动名称")
    @TableField("ACTIVITY_NAME")
    private String activityName;

    @ApiModelProperty(value = "月份")
    @TableField("MONTH")
    private String month;

    @ApiModelProperty(value = "地市")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty(value = "派单量")
    @TableField("STATISTIC1")
    private Integer statistic1;

    @ApiModelProperty(value = "外呼量")
    @TableField("STATISTIC2")
    private Integer statistic2;

    @ApiModelProperty(value = "回收量")
    @TableField("STATISTIC3")
    private Integer statistic3;

    @ApiModelProperty(value = "回单量")
    @TableField("STATISTIC4")
    private Integer statistic4;

    @ApiModelProperty(value = "逾期量")
    @TableField("STATISTIC5")
    private Integer statistic5;

    @ApiModelProperty(value = "营销成功量")
    @TableField("STATISTIC6")
    private Integer statistic6;

    @ApiModelProperty(value = "营销失败量")
    @TableField("STATISTIC7")
    private Integer statistic7;

    @ApiModelProperty(value = "外呼率")
    @TableField("CHANCE1")
    private String chance1;

    @ApiModelProperty(value = "逾期率")
    @TableField("CHANCE2")
    private String chance2;

    @ApiModelProperty(value = "成功率")
    @TableField("CHANCE3")
    private String chance3;

    @ApiModelProperty(value = "营销失败率")
    @TableField("CHANCE4")
    private String chance4;

}
