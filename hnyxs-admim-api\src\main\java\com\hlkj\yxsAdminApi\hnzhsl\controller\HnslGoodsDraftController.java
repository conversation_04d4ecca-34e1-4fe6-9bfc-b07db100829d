package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsDraftService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslGoodsDraft;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsDraftParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 扫楼商品草稿表控制器
 *
 * <AUTHOR>
 * @since 2023-06-17 16:07:58
 */
@Api(tags = "扫楼商品草稿表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoodsDraft")
public class HnslGoodsDraftController extends BaseController {
    @Autowired
    private HnslGoodsDraftService hnslGoodsDraftService;

    @Autowired
    private HnslGoodsService hnslGoodsService;
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:list')")
    @OperationLog
    @ApiOperation("分页查询扫楼商品草稿表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoodsDraft>> page(@RequestBody HnslGoodsDraftParam param) {
        PageParam<HnslGoodsDraft, HnslGoodsDraftParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsDraftService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslGoodsDraftService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:list')")
    @OperationLog
    @ApiOperation("查询全部扫楼商品草稿表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoodsDraft>> list(@RequestBody HnslGoodsDraftParam param) {
        PageParam<HnslGoodsDraft, HnslGoodsDraftParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsDraftService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsDraftService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:list')")
    @OperationLog
    @ApiOperation("根据id查询扫楼商品草稿表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoodsDraft> get(@PathVariable("id") Integer id) {
        return success(hnslGoodsDraftService.getById(id));
        // 使用关联查询
        //return success(hnslGoodsDraftService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:save')")
    @OperationLog
    @ApiOperation("添加扫楼商品草稿表")
    @PostMapping("/decode/save")
    public ApiResult<?> save(@RequestBody HnslGoodsDraft hnslGoodsDraft) {
        Integer id = hnslGoodsDraft.getId();
        hnslGoodsDraft.setId(null);
        User loginUser = getLoginUser();
        hnslGoodsDraft.setConfigurationName(loginUser.getNickname());
        hnslGoodsDraft.setConfigurationNumber(loginUser.getUsername());
        hnslGoodsDraft.setSubmitDate(new Date());
        hnslGoodsDraft.setSubmitType("1");

        if (hnslGoodsDraftService.save(hnslGoodsDraft)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:update')")
    @OperationLog
    @ApiOperation("修改扫楼商品草稿表")
    @PostMapping("/decode/update")
    public ApiResult<?> update(@RequestBody HnslGoodsDraft hnslGoodsDraft) {
        if (hnslGoodsDraftService.updateById(hnslGoodsDraft)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:remove')")
    @OperationLog
    @ApiOperation("删除扫楼商品草稿表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsDraftService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:save')")
    @OperationLog
    @ApiOperation("批量添加扫楼商品草稿表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoodsDraft> list) {
        if (hnslGoodsDraftService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:update')")
    @OperationLog
    @ApiOperation("批量修改扫楼商品草稿表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoodsDraft> batchParam) {
        if (batchParam.update(hnslGoodsDraftService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoodsDraft:remove')")
    @OperationLog
    @ApiOperation("批量删除扫楼商品草稿表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsDraftService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
