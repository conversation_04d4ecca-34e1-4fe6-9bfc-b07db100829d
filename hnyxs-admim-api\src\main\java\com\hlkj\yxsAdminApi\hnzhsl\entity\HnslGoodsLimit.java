package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品限制对应表
 *
 * <AUTHOR>
 * @since 2023-07-31 16:59:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslGoodsLimit对象", description = "商品限制对应表")
public class HnslGoodsLimit implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "商品编码")
    @TableField("GOODS_NUMBER")
    private String goodsNumber;

    @ApiModelProperty(value = "商品限制编码")
    @TableField("GOODS_LIMIT_NUMBER")
    private String goodsLimitNumber;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "限制类型 1:默认商品关联限制 2：加装互斥限制 3:生卡数量限制")
    @TableField("LIMIT_TYPE")
    private Integer limitType;

    @ApiModelProperty(value = "商品限制编码对应限制下单数量")
    @TableField("LIMIT_ORDER_COUNT")
    private Integer limitOrderCount;

}
