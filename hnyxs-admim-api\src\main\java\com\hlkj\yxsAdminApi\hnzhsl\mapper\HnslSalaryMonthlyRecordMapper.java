package com.hlkj.yxsAdminApi.hnzhsl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSalaryMonthlyRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSalaryMonthlyRecordParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 扫楼薪资每月记录表Mapper
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
public interface HnslSalaryMonthlyRecordMapper extends BaseMapper<HnslSalaryMonthlyRecord> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<HnslSalaryMonthlyRecord>
     */
    List<HnslSalaryMonthlyRecord> selectPageRel(@Param("page") IPage<HnslSalaryMonthlyRecord> page,
                             @Param("param") HnslSalaryMonthlyRecordParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<HnslSalaryMonthlyRecord> selectListRel(@Param("param") HnslSalaryMonthlyRecordParam param);

}
