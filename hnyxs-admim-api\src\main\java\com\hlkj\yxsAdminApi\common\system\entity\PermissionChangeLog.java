package com.hlkj.yxsAdminApi.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限变更日志
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@ApiModel(description = "权限变更日志")
@TableName("hnyxs_permission_change_log")
public class PermissionChangeLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("操作人ID")
    private String operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("操作IP")
    private String operationIp;

    @ApiModelProperty("操作时间")
    private Date operationTime;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作模块")
    private String operationModule;

    @ApiModelProperty("目标对象ID")
    private String targetId;

    @ApiModelProperty("目标对象名称")
    private String targetName;

    @ApiModelProperty("变更详情")
    private String changeDetails;

    @ApiModelProperty("变更前内容")
    private String beforeChange;

    @ApiModelProperty("变更后内容")
    private String afterChange;

    @ApiModelProperty("审核工单号")
    private String approvalOrderNo;

    @ApiModelProperty("状态")
    private Integer status;
} 