package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslTeamRecordService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslTeamRecord;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslTeamRecordParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 团队月记录表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "团队月记录表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-team-record")
public class HnslTeamRecordController extends BaseController {
    @Autowired
    private HnslTeamRecordService hnslTeamRecordService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:list')")
    @OperationLog
    @ApiOperation("分页查询团队月记录表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslTeamRecord>> page(@RequestBody HnslTeamRecordParam param) {
        PageParam<HnslTeamRecord, HnslTeamRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamRecordService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslTeamRecordService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:list')")
    @OperationLog
    @ApiOperation("查询全部团队月记录表")
    @PostMapping("/list")
    public ApiResult<List<HnslTeamRecord>> list(@RequestBody HnslTeamRecordParam param) {
        PageParam<HnslTeamRecord, HnslTeamRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslTeamRecordService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslTeamRecordService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:list')")
    @OperationLog
    @ApiOperation("根据id查询团队月记录表")
    @GetMapping("/{id}")
    public ApiResult<HnslTeamRecord> get(@PathVariable("id") Integer id) {
        return success(hnslTeamRecordService.getById(id));
        // 使用关联查询
        //return success(hnslTeamRecordService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:save')")
    @OperationLog
    @ApiOperation("添加团队月记录表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslTeamRecord hnslTeamRecord) {
        if (hnslTeamRecordService.save(hnslTeamRecord)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:update')")
    @OperationLog
    @ApiOperation("修改团队月记录表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslTeamRecord hnslTeamRecord) {
        if (hnslTeamRecordService.updateById(hnslTeamRecord)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:remove')")
    @OperationLog
    @ApiOperation("删除团队月记录表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslTeamRecordService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:save')")
    @OperationLog
    @ApiOperation("批量添加团队月记录表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslTeamRecord> list) {
        if (hnslTeamRecordService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:update')")
    @OperationLog
    @ApiOperation("批量修改团队月记录表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslTeamRecord> batchParam) {
        if (batchParam.update(hnslTeamRecordService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslTeamRecord:remove')")
    @OperationLog
    @ApiOperation("批量删除团队月记录表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslTeamRecordService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
