package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.utils.ObjectExcelRead;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.hnzhsl.entity.*;
import com.hlkj.yxsAdminApi.hnzhsl.service.*;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslGoodsParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 商品表控制器
 *
 * <AUTHOR>
 * @since 2023-04-21 10:50:16
 */
@Api(tags = "商品表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnslGoods")
public class HnslGoodsController extends BaseController {

    @Resource
    private HnslGoodsService hnslGoodsService;

    @Resource
    private HnslGoodsBelongService hnslGoodsBelongService;

    @Resource
    private HnslGoodsPosterService hnslGoodsPosterService;

    @Resource
    private HnslGoodsRelService hnslGoodsRelService;
    @Resource
    private HnslGoodsSaleService hnslGoodsSaleService;
    @Resource
    private HnslIntegralDescriptionService hnslIntegralDescriptionService;

    @Resource
    private HnslGoodsLimitService hnslGoodsLimitService;
    @Resource
    private HnslSchoolService hnslSchoolService;
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:list')")
    @OperationLog
    @ApiOperation("分页查询商品表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslGoods>> page(@RequestBody HnslGoodsParam param) {
        //PageParam<HnslGoods, HnslGoodsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        User loginUser = getLoginUser();
        return success(hnslGoodsService.pageRel(param));
        // 使用关联查询
        //return success(hnslGoodsService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:list')")
    @OperationLog
    @ApiOperation("查询全部商品表")
    @PostMapping("/list")
    public ApiResult<List<HnslGoods>> list(@RequestBody HnslGoodsParam param) {
        PageParam<HnslGoods, HnslGoodsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslGoodsService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslGoodsService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:list')")
    @OperationLog
    @ApiOperation("根据id查询商品表")
    @GetMapping("/{id}")
    public ApiResult<HnslGoods> get(@PathVariable("id") Integer id) {
        HnslGoods hnslGoodsServiceById = hnslGoodsService.getById(id);
        LambdaQueryWrapper<HnslGoodsRel> hnslGoodsRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslGoodsRelLambdaQueryWrapper.eq(HnslGoodsRel::getStatus,1)
                .eq(HnslGoodsRel::getGoodsNumber,hnslGoodsServiceById.getGoodsNumber());
        List<HnslGoodsRel> list = hnslGoodsRelService.list(hnslGoodsRelLambdaQueryWrapper);
        List<String> collect = list.stream().map(w -> w.getGoodsNumberRel()).collect(Collectors.toList());
        LambdaQueryWrapper<HnslGoodsSale> hnslGoodsSaleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslGoodsSaleLambdaQueryWrapper
                .in(HnslGoodsSale::getGoodsSaleId,collect);
        List<HnslGoodsSale> list1 = hnslGoodsSaleService.list(hnslGoodsSaleLambdaQueryWrapper);
        ArrayList<HnslGoodsSale> hnslGoodsSales1 = new ArrayList<>();
        ArrayList<HnslGoodsSale> hnslGoodsSales3 = new ArrayList<>();
        list1.forEach(w -> {
            switch (w.getGoodsSaleType()){
                case 1:
                    hnslGoodsSales1.add(w);
                    break;
                case 2:
                    hnslGoodsSales3.add(w);
                    break;
                case 3:
                    hnslGoodsServiceById.getMinPrice4().add(w);
                    break;
                case 4:
                    hnslGoodsServiceById.setMinPrice10(w);
                    break;
                case 5:
                    hnslGoodsServiceById.setMinPrice9(w);
                    break;
            }
        });
        hnslGoodsServiceById.setMinPrice1(hnslGoodsSales1);
        hnslGoodsServiceById.setMinPrice3(hnslGoodsSales3);
        //查询关联学校
        LambdaQueryWrapper<HnslGoodsBelong> hnslGoodsBelongLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslGoodsBelongLambdaQueryWrapper.eq(HnslGoodsBelong::getGoodsNumber,hnslGoodsServiceById.getGoodsNumber())
                .eq(HnslGoodsBelong::getStatus,1);
        List<HnslGoodsBelong> list3 = hnslGoodsBelongService.list(hnslGoodsBelongLambdaQueryWrapper);
        List<String> collect1 = list3.stream().map(w -> w.getSchoolCode()).collect(Collectors.toList());
        LambdaQueryWrapper<HnslSchool> hnslSchoolLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hnslSchoolLambdaQueryWrapper.in(HnslSchool::getSchoolCode,collect1);
        List<HnslSchool> list2 = hnslSchoolService.list(hnslSchoolLambdaQueryWrapper);
        hnslGoodsServiceById.setSchoolList(list2);
        return success(hnslGoodsServiceById);
        // 使用关联查询
        //return success(hnslGoodsService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:save')")
    @OperationLog
    @ApiOperation("添加商品表")
    @PostMapping("/decode/save")
    public ApiResult<?> save(@RequestBody HnslGoods hnslGoods) {
        if(hnslGoods.getSaflType()==2 && StringUtil.isNull(hnslGoods.getGoodsNumber())){
            return fail("商品编码不能为空");
        }
        if(StringUtil.isNull(hnslGoods.getGoodsName())){
            return fail("商品名称不能为空");
        }
        User loginUser = getLoginUser();
        hnslGoods.setCreatedUser(loginUser.getUsername());
        hnslGoods.setCreatedDate(new Date());
        hnslGoods.setStatus("0");
        try {
            if(StringUtil.isNull(hnslGoods.getProductionPrice())){
                hnslGoods.setProductionPrice("0");
            }
            //合并所有销售品信息
            List<HnslGoodsSale> hnslGoodsSales = new ArrayList<>();
            hnslGoodsSales.addAll(hnslGoods.getMinPrice1());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice2());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice3());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice4());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice5());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice6());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice7());
            hnslGoodsSales.addAll(hnslGoods.getMinPrice8());
            hnslGoodsSales.add(hnslGoods.getMinPrice9());
            hnslGoodsSales.add(hnslGoods.getMinPrice10());

            //计算总积分
            IntSummaryStatistics summaryStatistics = hnslGoodsSales.stream().mapToInt((s) -> s.getGoodsSaleIntegral()).summaryStatistics();
            hnslGoods.setGoodIntegral(summaryStatistics.getSum()+"");
            //计算总价
            String prestore=hnslGoods.getPrestore();
            String productionPrice=hnslGoods.getProductionPrice();
            BigDecimal b3 = new BigDecimal(prestore);
            BigDecimal b2 = new BigDecimal(productionPrice);
            Double goodsPrice=b3.add(b2).doubleValue();
            hnslGoods.setGoodsPrice(goodsPrice);
            //生成商品编码
            if(StringUtil.isNull(hnslGoods.getGoodsNumber())){
                String goodsNumber="";
                switch (hnslGoods.getSaflType()){
                    case 1:
                        goodsNumber+="XZ";
                        break;
                    case 2:
                        goodsNumber+="QR";
                        break;
                    case 3:
                        goodsNumber+="JZ";
                        break;
                    case 4:
                        goodsNumber+="CFSF";
                        break;
                    default:
                        goodsNumber+="QT";
                }
                LambdaQueryWrapper<HnslGoods> hnslGoodsLambdaQueryWrapper = new LambdaQueryWrapper<>();
                hnslGoodsLambdaQueryWrapper.eq(HnslGoods::getSaflType,hnslGoods.getSaflType());
                int count = hnslGoodsService.count(hnslGoodsLambdaQueryWrapper);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYY");
                String number=null;
                //加装生成规则不一样
                if(hnslGoods.getSaflType()==3){
                    Integer goodsType = Integer.parseInt(hnslGoods.getGoodsType());
                    switch (goodsType){
                        case 1:
                            number="qyb";
                            break;
                        case 2:
                            number="ycb";
                            break;
                        case 3:
                            number="xfb";
                            break;
                        case 4:
                            number="llb";
                            break;
                        case 5:
                            number="kdb";
                            break;
                        case 6:
                            number="yyb";
                            break;
                        case 7:
                            number="zzb";
                            break;
                        default:
                            number="qtb";
                    }
                }else{
                    number=""+(goodsPrice.intValue()>99?goodsPrice.intValue():(goodsPrice.intValue()>9?"0"+goodsPrice.intValue():"00"+goodsPrice.intValue()));
                }
                goodsNumber+=loginUser.getHnslUser().getCityCode()+""+
                        number
                        +""+simpleDateFormat.format(new Date()).substring(2)
                        +(count>99?count:(count>9?"0"+count:"00"+count));
                hnslGoods.setGoodsNumber(goodsNumber);
            }
            hnslGoodsService.save(hnslGoods);
            HnslGoodsRel gRel=new HnslGoodsRel();
            gRel.setStatus("1");
            gRel.setGoodsNumber(hnslGoods.getGoodsNumber());
            if(hnslGoods.getMinPrice1()!=null && hnslGoods.getMinPrice1().size()>0){
                hnslGoods.getMinPrice1().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("1");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }
            //添加学校
            if(hnslGoods.getSchoolList()!=null && hnslGoods.getSchoolList().size()>0){
                List<HnslSchool> schoolList = hnslGoods.getSchoolList();
                ArrayList<HnslGoodsBelong> hnslGoodsBelongs = new ArrayList<>();
                for(HnslSchool school:schoolList){
                    HnslGoodsBelong gBelong=new HnslGoodsBelong();
                    gBelong.setCreatedUser(loginUser.getUsername());
                    gBelong.setCreatedDate(new Date());
                    gBelong.setGoodsNumber(hnslGoods.getGoodsNumber().trim());
                    gBelong.setStatus(1);
                    gBelong.setSchoolCode(school.getSchoolCode());
                    hnslGoodsBelongs.add(gBelong);
                }
                hnslGoodsBelongService.saveBatch(hnslGoodsBelongs);
            }
            //关联海报保存
            if(hnslGoods.getGoodsPosterImg()!=null && hnslGoods.getGoodsPosterImg().length>0){
                for(String hb:hnslGoods.getGoodsPosterImg()){
                    HnslGoodsPoster hnslGoodsPosterEntity = new HnslGoodsPoster();
                    hnslGoodsPosterEntity.setCreatedDate(new Date());
                    hnslGoodsPosterEntity.setCreatedUser(loginUser.getUsername());
                    hnslGoodsPosterEntity.setPosterUrl("uploads/picture/goods/"+hb);
                    hnslGoodsPosterEntity.setStatus(1);
                    hnslGoodsPosterEntity.setGoodsId(hnslGoods.getId());
                    hnslGoodsPosterService.save(hnslGoodsPosterEntity);
                }
            }

            if(hnslGoods.getMinPrice9()!=null){
                    gRel.setGoodsNumberRel(hnslGoods.getMinPrice9().getGoodsSaleId());
                    gRel.setRelType("8");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(hnslGoods.getMinPrice9().getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
            }

            if(hnslGoods.getMinPrice8()!=null && hnslGoods.getMinPrice8().size()>0){
                hnslGoods.getMinPrice8().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("7");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice2()!=null && hnslGoods.getMinPrice2().size()>0){
                hnslGoods.getMinPrice2().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("2");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice2()!=null && hnslGoods.getMinPrice2().size()>0){
                hnslGoods.getMinPrice2().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("2");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice3()!=null && hnslGoods.getMinPrice3().size()>0){
                hnslGoods.getMinPrice3().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("3");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice10()!=null){
                    gRel.setGoodsNumberRel(hnslGoods.getMinPrice10().getGoodsSaleId());
                    gRel.setRelType("9");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(hnslGoods.getMinPrice10().getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
            }

            return success("添加成功");
        }catch (Exception e){
            logger.error("添加商品异常{}",e.getMessage());
            return fail("添加异常");
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:update')")
    @OperationLog
    @ApiOperation("修改商品表及关联信息")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslGoods hnslGoods) {
        User loginUser = getLoginUser();
        String userName=loginUser.getUsername();
        hnslGoods.setUpdatedUser(loginUser.getUsername());
        hnslGoods.setUpdatedDate(new Date());
//        if (hnslGoodsService.updateById(hnslGoods)) {
//            return success("修改成功");
//        }
//        if(!StringUtil.isEmpty(hnslGoods.getBandwidth())){ // 如果带宽不为空而带宽销售编码是空值的话 修改失败
//            if(StringUtil.isEmpty(hnslGoods.getMinPrice9()) && hnslGoods.getSaflType()!=3){
//                return fail("修改失败<带宽销售编码为空>");
//            }
//        }
//        if(!StringUtil.isEmpty(hnslGoods.getMinPrice9())){ // 反向判断
//            if(StringUtil.isEmpty(hnslGoods.getBandwidth())  && hnslGoods.getSaflType()!=3){
//                return fail("修改失败<带宽为空>");
//            }
//        }
//        //TODO 如果带宽及带宽销售编码都有值 保存套餐类型为号卡新装 方便前台展示套餐
//        if(!(StringUtil.isEmpty(hnslGoods.getMinPrice9()) && StringUtil.isEmpty(hnslGoods.getBandwidth())) && hnslGoods.getSaflType()!=3){
//            hnslGoods.setSaflType(1);
//        }

//        if(!StringUtil.isEmpty(hnslGoods.getGoodsMianNumber())){
//            hnslGoods.setGoodsMianNumber(hnslGoods.getGoodsMianNumber().trim());
//        }
//        if(!StringUtil.isEmpty(hnslGoods.getPrestore())){
//            hnslGoodsService.uodateHnslGoodsEntity(hnslGoods);
//        }else{
//            String upload= "uploads" + File.separator + "picture" + File.separator+ "goods" + File.separator;
//            if(hnslGoods.getGoodsImg()!=null && !"".equals(hnslGoods.getGoodsImg())){
//                if(hnslGoods.getGoodsImg().indexOf("uploads")== -1){
//                    hnslGoods.setGoodsImg(upload+hnslGoods.getGoodsImg());
//                }
//            }
//            if(hnslGoods.getGoodsDetailsImg()!=null && !"".equals(hnslGoods.getGoodsDetailsImg())){
//                if(hnslGoods.getGoodsDetailsImg().indexOf("uploads")== -1){
//                    hnslGoods.setGoodsDetailsImg(upload+hnslGoods.getGoodsDetailsImg());
//                }
//            }
//            if(hnslGoods.getGoodsServiceUrl()!=null && !"".equals(hnslGoods.getGoodsServiceUrl())){
//                if(hnslGoods.getGoodsServiceUrl().indexOf("uploads")== -1){
//                    hnslGoods.setGoodsServiceUrl(upload+hnslGoods.getGoodsServiceUrl());
//                }
//            }
//            if(hnslGoods.getGoodsShareImg()!=null && !"".equals(hnslGoods.getGoodsShareImg())){
//                if(hnslGoods.getGoodsShareImg().indexOf("uploads")== -1){
//                    hnslGoods.setGoodsShareImg(upload+hnslGoods.getGoodsShareImg());
//                }
//            }
//        }

        try{
            //更新修改前置参数
            hnslGoodsService.uodateHnslGoodsEntity(hnslGoods);
            //添加修改时间 修改人
            hnslGoodsService.updateById(hnslGoods);

            //更新关联学校数据
            LambdaQueryWrapper<HnslGoodsBelong> hnslGoodsBelongLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslGoodsBelongLambdaQueryWrapper.eq(HnslGoodsBelong::getGoodsNumber,hnslGoods.getGoodsNumber());
            hnslGoodsBelongService.remove(hnslGoodsBelongLambdaQueryWrapper);
            if(hnslGoods.getSchoolList()!=null && hnslGoods.getSchoolList().size()>0){
                List<HnslSchool> schoolList = hnslGoods.getSchoolList();
                ArrayList<HnslGoodsBelong> hnslGoodsBelongs = new ArrayList<>();
                for(HnslSchool school:schoolList){
                    HnslGoodsBelong gBelong=new HnslGoodsBelong();
                    gBelong.setCreatedUser(loginUser.getUsername());
                    gBelong.setCreatedDate(new Date());
                    gBelong.setGoodsNumber(hnslGoods.getGoodsNumber().trim());
                    gBelong.setStatus(1);
                    gBelong.setSchoolCode(school.getSchoolCode());
                    hnslGoodsBelongs.add(gBelong);
                }
                hnslGoodsBelongService.saveBatch(hnslGoodsBelongs);
            }

            //更新关联海报数据
            LambdaQueryWrapper<HnslGoodsPoster> hnslGoodsPosterLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslGoodsPosterLambdaQueryWrapper.eq(HnslGoodsPoster::getGoodsId,hnslGoods.getId());
            hnslGoodsPosterService.remove(hnslGoodsPosterLambdaQueryWrapper);
            if(hnslGoods.getGoodsPosterImg()!=null && hnslGoods.getGoodsPosterImg().length>0){
                for(String hb:hnslGoods.getGoodsPosterImg()){
                    HnslGoodsPoster hnslGoodsPosterEntity = new HnslGoodsPoster();
                    hnslGoodsPosterEntity.setCreatedDate(new Date());
                    hnslGoodsPosterEntity.setCreatedUser(loginUser.getUsername());
                    hnslGoodsPosterEntity.setPosterUrl(hb);
                    hnslGoodsPosterEntity.setStatus(1);
                    hnslGoodsPosterEntity.setGoodsId(hnslGoods.getId());
                    hnslGoodsPosterService.save(hnslGoodsPosterEntity);
                }
            }

            //更新关联销售数据
            LambdaQueryWrapper<HnslGoodsRel> hnslGoodsRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hnslGoodsRelLambdaQueryWrapper.eq(HnslGoodsRel::getGoodsNumber,hnslGoods.getGoodsNumber());
            hnslGoodsRelService.remove(hnslGoodsRelLambdaQueryWrapper);

            HnslGoodsRel gRel=new HnslGoodsRel();
            gRel.setStatus("1");
            gRel.setGoodsNumber(hnslGoods.getGoodsNumber());
            gRel.setGoodsNumberRelMsg("");
            if(hnslGoods.getMinPrice1()!=null && hnslGoods.getMinPrice1().size()>0){
                hnslGoods.getMinPrice1().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("1");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice9()!=null){
                gRel.setGoodsNumberRel(hnslGoods.getMinPrice9().getGoodsSaleId());
                gRel.setRelType("8");
                gRel.setGoodsNumberRelMsg("");
                gRel.setGoodsSchoolDiscern(hnslGoods.getMinPrice9().getCampusTrafficSwitch());
                hnslGoodsRelService.save(gRel);
            }

            if(hnslGoods.getMinPrice8()!=null && hnslGoods.getMinPrice8().size()>0){
                hnslGoods.getMinPrice8().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("7");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice2()!=null && hnslGoods.getMinPrice2().size()>0){
                hnslGoods.getMinPrice2().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("2");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice2()!=null && hnslGoods.getMinPrice2().size()>0){
                hnslGoods.getMinPrice2().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("2");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice3()!=null && hnslGoods.getMinPrice3().size()>0){
                hnslGoods.getMinPrice3().forEach( w ->{
                    gRel.setGoodsNumberRel(w.getGoodsSaleId());
                    gRel.setRelType("3");
                    gRel.setGoodsNumberRelMsg("");
                    gRel.setGoodsSchoolDiscern(w.getCampusTrafficSwitch());
                    hnslGoodsRelService.save(gRel);
                });
            }

            if(hnslGoods.getMinPrice10()!=null){
                gRel.setGoodsNumberRel(hnslGoods.getMinPrice10().getGoodsSaleId());
                gRel.setRelType("9");
                gRel.setGoodsNumberRelMsg("");
                gRel.setGoodsSchoolDiscern(hnslGoods.getMinPrice10().getCampusTrafficSwitch());
                hnslGoodsRelService.save(gRel);
            }
            return success("添加成功");
        }catch(Exception e){
            return fail("修改失败"+e.getMessage());
        }
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:update')")
    @OperationLog
    @ApiOperation("修改单独商品信息")
    @PostMapping("/decode/updateInfo")
    public ApiResult<?> updateInfo(@RequestBody HnslGoods hnslGoods) {
        User loginUser = getLoginUser();
        hnslGoods.setUpdatedUser(loginUser.getUsername());
        hnslGoods.setUpdatedDate(new Date());
        UpdateWrapper<HnslGoods> hnslGoodsUpdateWrapper = new UpdateWrapper<>();
        hnslGoodsUpdateWrapper.set("status", hnslGoods.getStatus())
                .eq("id",hnslGoods.getId());
        if (hnslGoodsService.update(hnslGoodsUpdateWrapper)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }
    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:remove')")
    @OperationLog
    @ApiOperation("删除商品表")
    @GetMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslGoodsService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:save')")
    @OperationLog
    @ApiOperation("批量添加商品表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslGoods> list) {
        if (hnslGoodsService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:update')")
    @OperationLog
    @ApiOperation("批量修改商品表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslGoods> batchParam) {
        if (batchParam.update(hnslGoodsService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:remove')")
    @OperationLog
    @ApiOperation("批量删除商品表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslGoodsService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:importUser')")
    @OperationLog
    @ApiOperation("导入熟卡积分")
    @PostMapping("/decode/importIntegralExplain")
    public ApiResult<?> importIntegralExplain(MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        User loginUser = getLoginUser();
        String userName = loginUser.getUsername();

        JSONObject result = new JSONObject();
        String image = "xls,xlsx";

        if (!file.isEmpty()) {
            ServletContext servletContext = request.getSession().getServletContext();
            String uploadPath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXls(uploadPath, newname, 1, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 1, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                if (numberList != null) {
                    //将数据转化成user对象
                    List<HnslIntegralDescription> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslIntegralDescription listPojo = new HnslIntegralDescription();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //商品编码
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                listPojo.setGoodsCode(StringUtil.trimString(userMap.get(name)));
                            }
                            //商品名称
                            else if ("1".equalsIgnoreCase(name.trim())) {

                                listPojo.setGoodsName(StringUtil.trimString(userMap.get(name)));
                            }//商品价格
                            else if ("2".equalsIgnoreCase(name.trim())) {
                                if(!InterfaceUtil.isInteger(userMap.get(name))){
                                    return fail("请输入整数");
                                }
                                listPojo.setGoodsPrice(Integer.parseInt(userMap.get(name)));
                            }//商品类型
                            else if ("3".equalsIgnoreCase(name.trim())) {
                                if(!InterfaceUtil.isInteger(userMap.get(name))){
                                    return fail("请输入整数");
                                }
                                listPojo.setGoodsType(Integer.parseInt(userMap.get(name)));
                            }
                        }
                        listPojo.setCreatedDate(new Date());
                        listPojo.setStatus(1);
                        listPojo.setCreatedUser(userName);
                        hnslUserList.add(listPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslIntegralDescriptionService.saveUserArray(hnslUserList, request);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            HashMap<Object, Object> r = new HashMap<>();
                            r.put("mes", "exportDaoUser");
                            r.put("resultCode", "6");
                            r.put("fileName", saveUserArray.get("fileName"));
                            return success(r);
                        }
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        return fail("批量插入用户信息失败:" + e);
                    }
                } else {
                    return fail("文件内容为空，或者解析失败");
                }
                return fail("导出文件失败");
            } catch (Exception e) {
                return fail("文件上传接口上传异常:" + e);
            }
        } else {
            return fail("上传文件为空" );
        }
    }


    @PreAuthorize("hasAuthority('hnzhsl:hnslGoods:importUser')")
    @OperationLog
    @ApiOperation("导入加装限制编码")
    @PostMapping("/decode/importAstrictCode")
    public ApiResult<?> importAstrictCode(MultipartFile file, HttpServletRequest request, HttpServletResponse response) {
        User loginUser = getLoginUser();
        String userName = loginUser.getUsername();

        JSONObject result = new JSONObject();
        String image = "xls,xlsx";

        if (!file.isEmpty()) {
            ServletContext servletContext = request.getSession().getServletContext();
            String uploadPath = servletContext.getRealPath("/") + "uploads" + File.separator + "file" + File.separator;
            logger.info("uploadPath:  " + uploadPath);
            // 文件上传大小5M
            long fileSize = 5 * 1024 * 1024;
            if (file.getSize() > fileSize) {
                return fail("上传文件大小大于5M");
            }
            String OriginalFilename = file.getOriginalFilename();
            String fileSuffix = OriginalFilename.substring(OriginalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!Arrays.asList(image.split(",")).contains(fileSuffix)) {
                return fail("上传文件格式不正确");
            }

            if (!ServletFileUpload.isMultipartContent(request)) {
                return fail("文件上传格式不正确");
            }

            // 检查上传文件的目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.isDirectory()) {
                if (!uploadDir.mkdirs()) {
                    return fail("文件所在目录创建失败");
                }
            }
            SimpleDateFormat sf_ = new SimpleDateFormat("yyyyMMddHHmmss");
            String times = sf_.format(new Date());
            String newname = times + (int) (Math.random() * (99999 - 10000) + 10000) + "." + fileSuffix;
            File saveFile = new File(uploadPath, newname);
            try {
                file.transferTo(saveFile);
                List<Map<String, String>> numberList = null;
                if (fileSuffix.endsWith("xls")) {
                    numberList = ObjectExcelRead.readExcelXlsx2(uploadPath, newname, 1, 0, 0);
                } else {
                    numberList = ObjectExcelRead.readExcelXlsx(uploadPath, newname, 1, 0, 0);
                }
                logger.info("文件解析结果numberList：" + numberList);
                if (numberList != null) {
                    //将数据转化成user对象
                    List<HnslGoodsLimit> hnslUserList = new ArrayList<>();
                    for (int i = 0; i < numberList.size(); i++) {
                        HnslGoodsLimit listPojo = new HnslGoodsLimit();
                        Map<String, String> userMap = numberList.get(i);
                        Set<String> nameset = userMap.keySet();
                        Iterator<String> namelist = nameset.iterator();
                        while (namelist.hasNext()) {
                            String name = namelist.next();
                            if (StringUtil.isEmpty(name)) {
                                continue;
                            }
                            //主编码
                            else if ("0".equalsIgnoreCase(name.trim())) {
                                listPojo.setGoodsNumber(StringUtil.trimString(userMap.get(name)));
                            }
                            //限制编码
                            else if ("1".equalsIgnoreCase(name.trim())) {
                                listPojo.setGoodsLimitNumber(StringUtil.trimString(userMap.get(name)));
                            }
                        }
                        listPojo.setCreatedDate(new Date());
                        listPojo.setStatus(1);
                        listPojo.setCreatedUser(userName);
                        hnslUserList.add(listPojo);
                    }
                    try {
                        logger.info("解析结果装入用户集合" + hnslUserList);
                        Map<String, String> saveUserArray = hnslGoodsLimitService.saveUserArray(hnslUserList, request);
                        if (null != saveUserArray & saveUserArray.get("resultCode").equals("1")) {
                            HashMap<String, String> r = new HashMap<>();
                            r.put("mes", "exportDaoUser");
                            r.put("resultCode", "6");
                            r.put("fileName", saveUserArray.get("fileName"));
                            return success(r);
                        }
                    } catch (Exception e) {
                        return fail("批量插入用户信息失败:" + e);
                    }
                } else {
                    return fail("文件内容为空，或者解析失败");
                }
                return fail("导入文件为空");
            } catch (Exception e) {
                return fail("文件上传接口上传异常:" + e);
            }
        } else {
            return fail("上传文件为空");
        }
    }

}
