package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务完成进度表
 *
 * <AUTHOR>
 * @since 2023-06-28 19:58:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslTaskPlan对象", description = "任务完成进度表")
public class HnslTaskPlan implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户手机号码")
    @TableField("USER_PHONE")
    private String userPhone;

    @ApiModelProperty(value = "任务编码")
    @TableField("TASK_CODE")
    private String taskCode;

    @ApiModelProperty(value = "状态(1:在架 0：下架)")
    @TableField("PLAN_STATUS")
    private Integer planStatus;

    @ApiModelProperty(value = "学生编码")
    @TableField("CLIENT_NUMBER")
    private String clientNumber;

    @ApiModelProperty(value = "完成时间")
    @TableField("PLAN_DATE")
    private Date planDate;

}
