package com.hlkj.yxsAdminApi.hnzhsl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 房间表
 *
 * <AUTHOR>
 * @since 2023-06-20 11:24:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HnslRoom对象", description = "房间表")
public class HnslRoom implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "房间名称(号)")
    @TableField("ROOM_NAME")
    private String roomName;

    @ApiModelProperty(value = "现有登记入住人数")
    @TableField("ROOM_REGISTER_EXISTING")
    private Integer roomRegisterExisting;

    @ApiModelProperty(value = "本网手机用户现有人数")
    @TableField("NETWORK_PHONE_EXISTING")
    private Integer networkPhoneExisting;

    @ApiModelProperty(value = "本网宽带用户现有人数")
    @TableField("NETWORK_EXISTING")
    private Integer networkExisting;

    @ApiModelProperty(value = "楼栋ID")
    @TableField("BUILDING_ID")
    private String buildingId;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_USER")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;

    @ApiModelProperty(value = "修改人")
    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATED_DATE")
    private Date updatedDate;

    @ApiModelProperty(value = "是否可用状态（0:否 1:是")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "房间编码")
    @TableField("ROOM_NUMBER")
    private String roomNumber;

}
