package com.hlkj.yxsAdminApi.common.system.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.sql.Timestamp;

/**门户人员信息表
 */
@Data
@ApiModel(description = "组织机构")
@TableName("portal_personnel_info")
public class PortalPersonnelInfo {
    private static final long serialVersionUID = 1L;
    private String personnelId;

    private String hrCode;

    private String account;

    private String uid;

    private String username;

    private String officePhone;

    private String email;

    private String sex;

    private String certNum;

    private Long status;

    private String sort;

    private String ctUserCode;

    private String ctpositiontype;

    private String cttitle;

    private String ctpositionname;

    private String ctposleveltype;

    private String ctpositionlevel;

    private String ctposlayertype;

    private String ctpositionlayer;

    private String ctpositionsequence;

    private Long ptype;

    private Long property;

    private String orgMartCode;

    private String fullName;

    private Timestamp createDate;

    private Timestamp updateDate;

    private String mobilephone;
}
