package com.hlkj.yxsAdminApi.hnzhsl.controller;

import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzhsl.service.HnslSchoolOrdersService;
import com.hlkj.yxsAdminApi.hnzhsl.entity.HnslSchoolOrders;
import com.hlkj.yxsAdminApi.hnzhsl.param.HnslSchoolOrdersParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 派单学校关联表控制器
 *
 * <AUTHOR>
 * @since 2023-06-21 15:28:37
 */
@Api(tags = "派单学校关联表管理")
@RestController
@RequestMapping("/api/hnzhsl/hnsl-school-orders")
public class HnslSchoolOrdersController extends BaseController {
    @Autowired
    private HnslSchoolOrdersService hnslSchoolOrdersService;

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:list')")
    @OperationLog
    @ApiOperation("分页查询派单学校关联表")
    @PostMapping("/page")
    public ApiResult<PageResult<HnslSchoolOrders>> page(@RequestBody HnslSchoolOrdersParam param) {
        PageParam<HnslSchoolOrders, HnslSchoolOrdersParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSchoolOrdersService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnslSchoolOrdersService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:list')")
    @OperationLog
    @ApiOperation("查询全部派单学校关联表")
    @PostMapping("/list")
    public ApiResult<List<HnslSchoolOrders>> list(@RequestBody HnslSchoolOrdersParam param) {
        PageParam<HnslSchoolOrders, HnslSchoolOrdersParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnslSchoolOrdersService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnslSchoolOrdersService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:list')")
    @OperationLog
    @ApiOperation("根据id查询派单学校关联表")
    @GetMapping("/{id}")
    public ApiResult<HnslSchoolOrders> get(@PathVariable("id") Integer id) {
        return success(hnslSchoolOrdersService.getById(id));
        // 使用关联查询
        //return success(hnslSchoolOrdersService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:save')")
    @OperationLog
    @ApiOperation("添加派单学校关联表")
    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody HnslSchoolOrders hnslSchoolOrders) {
        if (hnslSchoolOrdersService.save(hnslSchoolOrders)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:update')")
    @OperationLog
    @ApiOperation("修改派单学校关联表")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody HnslSchoolOrders hnslSchoolOrders) {
        if (hnslSchoolOrdersService.updateById(hnslSchoolOrders)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:remove')")
    @OperationLog
    @ApiOperation("删除派单学校关联表")
    @PostMapping("/remove/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (hnslSchoolOrdersService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:save')")
    @OperationLog
    @ApiOperation("批量添加派单学校关联表")
    @PostMapping("/saveBatch")
    public ApiResult<?> saveBatch(@RequestBody List<HnslSchoolOrders> list) {
        if (hnslSchoolOrdersService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:update')")
    @OperationLog
    @ApiOperation("批量修改派单学校关联表")
    @PostMapping("/updateBatch")
    public ApiResult<?> updateBatch(@RequestBody BatchParam<HnslSchoolOrders> batchParam) {
        if (batchParam.update(hnslSchoolOrdersService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @PreAuthorize("hasAuthority('hnzhsl:hnslSchoolOrders:remove')")
    @OperationLog
    @ApiOperation("批量删除派单学校关联表")
    @PostMapping("/removeBatch")
    public ApiResult<?> removeBatch(@RequestBody List<Integer> ids) {
        if (hnslSchoolOrdersService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
